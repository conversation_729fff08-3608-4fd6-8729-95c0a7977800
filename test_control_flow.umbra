// Test control flow with correct syntax

println!("=== Control Flow Test ===")

// Basic when statement
let age: Integer := 25

when age >= 18:
    println!("You are an adult")
otherwise:
    println!("You are a minor")

// When statement with multiple conditions
let score: Integer := 85

when score >= 90:
    println!("Grade: A")
otherwise:
    when score >= 80:
        println!("Grade: B")
    otherwise:
        when score >= 70:
            println!("Grade: C")
        otherwise:
            println!("Grade: F")

println!("=== Test Completed ===")
