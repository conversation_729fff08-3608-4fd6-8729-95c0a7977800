// Test Module System with 'bring' Keyword
// Verifies that module loading and importing works correctly

show("Module System Test")
show("==================")

// Test importing standard library modules
show("Testing standard library imports...")

bring std.math
show("Math module imported")

// Test using imported functions
show("Testing imported functions...")

let pi_value: Float := PI
show("PI constant: ", pi_value)

let e_value: Float := E
show("E constant: ", e_value)

// Test string removed since we're only testing math module
let absolute_value: Integer := abs(-42)
show("Absolute value of -42: ", absolute_value)

show("Module system test completed successfully!")
