// Test Module System with 'bring' Keyword
// Verifies that module loading and importing works correctly

show("Module System Test")
show("==================")

// Test importing standard library modules
show("Testing standard library imports...")

bring std.math
show("Math module imported")

bring std.string
show("String module imported")

bring std.io
show("I/O module imported")

// Test using imported functions
show("Testing imported functions...")

let pi_value: Float := PI
show("PI constant: ", pi_value)

let e_value: Float := E
show("E constant: ", e_value)

let test_string: String := "Hello, Umbra!"
let string_length: Integer := len(test_string)
show("String length: ", string_length)

let absolute_value: Integer := abs(-42)
show("Absolute value of -42: ", absolute_value)

// Test print functions
print("Testing print function: ")
println("This is a test!")

show("Module system test completed successfully!")
