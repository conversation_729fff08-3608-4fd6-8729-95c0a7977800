// Production Data Processing Pipeline
// Real-world data ingestion, cleaning, and preprocessing system

println!("🔄 Starting Production Data Processing Pipeline")
println!("=" * 60)

// Configuration and Setup
let config: Map[String, Any] := {
    "input_file": "data/customer_data.csv",
    "output_file": "data/processed_customer_data.csv",
    "validation_rules": {
        "age": {"min": 18, "max": 120},
        "income": {"min": 0, "max": 1000000},
        "credit_score": {"min": 300, "max": 850}
    },
    "missing_value_strategy": "median_fill",
    "outlier_threshold": 3.0,
    "feature_scaling": "standard_scaler"
}

println!("📋 Configuration loaded:")
println!("  Input: {}", config["input_file"])
println!("  Output: {}", config["output_file"])
println!("  Missing value strategy: {}", config["missing_value_strategy"])

// Data Loading with Error Handling
fn load_dataset(file_path: String) -> Result[DataFrame, String]:
    println!("📂 Loading dataset from: {}", file_path)
    
    when file_exists(file_path):
        let data: DataFrame := read_csv(file_path)
        println!("✅ Dataset loaded successfully")
        println!("  Rows: {}", data.shape()[0])
        println!("  Columns: {}", data.shape()[1])
        return Ok(data)
    otherwise:
        let error_msg: String := "File not found: " + file_path
        println!("❌ Error: {}", error_msg)
        return Err(error_msg)

// Data Validation and Quality Assessment
fn validate_data_quality(data: DataFrame) -> DataQualityReport:
    println!("🔍 Performing data quality assessment...")
    
    let report: DataQualityReport := DataQualityReport::new()
    let total_rows: Integer := data.shape()[0]
    
    // Check for missing values
    repeat column in data.columns():
        let missing_count: Integer := data[column].null_count()
        let missing_percentage: Float := (missing_count as Float / total_rows as Float) * 100.0
        
        report.add_missing_value_info(column, missing_count, missing_percentage)
        
        when missing_percentage > 20.0:
            println!("⚠️  High missing values in {}: {:.2f}%", column, missing_percentage)
        otherwise:
            when missing_percentage > 0.0:
                println!("ℹ️  Missing values in {}: {:.2f}%", column, missing_percentage)
    
    // Check for duplicates
    let duplicate_count: Integer := data.duplicate_count()
    when duplicate_count > 0:
        println!("⚠️  Found {} duplicate rows", duplicate_count)
        report.set_duplicates(duplicate_count)
    
    // Data type validation
    repeat column in data.columns():
        let expected_type: String := infer_column_type(column)
        let actual_type: String := data[column].dtype()
        
        when expected_type != actual_type:
            println!("⚠️  Type mismatch in {}: expected {}, got {}", column, expected_type, actual_type)
    
    println!("✅ Data quality assessment completed")
    return report

// Advanced Data Cleaning
fn clean_dataset(data: DataFrame, config: Map[String, Any]) -> DataFrame:
    println!("🧹 Starting data cleaning process...")
    let mut cleaned_data: DataFrame := data.copy()
    
    // Remove duplicates
    cleaned_data := cleaned_data.drop_duplicates()
    println!("  Removed {} duplicate rows", data.shape()[0] - cleaned_data.shape()[0])
    
    // Handle missing values based on strategy
    let strategy: String := config["missing_value_strategy"] as String
    
    when strategy == "median_fill":
        repeat column in cleaned_data.numeric_columns():
            let median_value: Float := cleaned_data[column].median()
            cleaned_data[column] := cleaned_data[column].fill_null(median_value)
            println!("  Filled missing values in {} with median: {:.2f}", column, median_value)
    
    otherwise when strategy == "mean_fill":
        repeat column in cleaned_data.numeric_columns():
            let mean_value: Float := cleaned_data[column].mean()
            cleaned_data[column] := cleaned_data[column].fill_null(mean_value)
            println!("  Filled missing values in {} with mean: {:.2f}", column, mean_value)
    
    otherwise when strategy == "mode_fill":
        repeat column in cleaned_data.categorical_columns():
            let mode_value: String := cleaned_data[column].mode()
            cleaned_data[column] := cleaned_data[column].fill_null(mode_value)
            println!("  Filled missing values in {} with mode: {}", column, mode_value)
    
    // Outlier detection and handling
    let outlier_threshold: Float := config["outlier_threshold"] as Float
    
    repeat column in cleaned_data.numeric_columns():
        let z_scores: Series := calculate_z_scores(cleaned_data[column])
        let outlier_mask: Series := z_scores.abs() > outlier_threshold
        let outlier_count: Integer := outlier_mask.sum()
        
        when outlier_count > 0:
            println!("  Found {} outliers in {} (z-score > {})", outlier_count, column, outlier_threshold)
            
            // Cap outliers at 95th and 5th percentiles
            let p95: Float := cleaned_data[column].quantile(0.95)
            let p5: Float := cleaned_data[column].quantile(0.05)
            
            cleaned_data[column] := cleaned_data[column].clip(p5, p95)
            println!("  Capped outliers in {} to range [{:.2f}, {:.2f}]", column, p5, p95)
    
    println!("✅ Data cleaning completed")
    return cleaned_data

// Feature Engineering and Preprocessing
fn engineer_features(data: DataFrame) -> DataFrame:
    println!("⚙️  Starting feature engineering...")
    let mut engineered_data: DataFrame := data.copy()

    // Create age groups
    when "age" in engineered_data.columns():
        engineered_data["age_group"] := when engineered_data["age"] < 25:
            "young"
        otherwise when engineered_data["age"] < 45:
            "middle_aged"
        otherwise when engineered_data["age"] < 65:
            "mature"
        otherwise:
            "senior"

        println!("  Created age_group feature")

    // Create income categories
    when "income" in engineered_data.columns():
        let income_q1: Float := engineered_data["income"].quantile(0.25)
        let income_q2: Float := engineered_data["income"].quantile(0.50)
        let income_q3: Float := engineered_data["income"].quantile(0.75)

        engineered_data["income_category"] := when engineered_data["income"] <= income_q1:
            "low"
        otherwise when engineered_data["income"] <= income_q2:
            "medium_low"
        otherwise when engineered_data["income"] <= income_q3:
            "medium_high"
        otherwise:
            "high"

        println!("  Created income_category feature")

    // Create interaction features
    when "age" in engineered_data.columns() && "income" in engineered_data.columns():
        engineered_data["age_income_ratio"] := engineered_data["age"] / engineered_data["income"] * 1000
        println!("  Created age_income_ratio interaction feature")

    // Encode categorical variables
    repeat column in engineered_data.categorical_columns():
        when column != "target":  // Don't encode target variable
            let encoded_column: String := column + "_encoded"
            engineered_data[encoded_column] := label_encode(engineered_data[column])
            println!("  Label encoded {} -> {}", column, encoded_column)

    println!("✅ Feature engineering completed")
    return engineered_data

// Data Scaling and Normalization
fn scale_features(data: DataFrame, config: Map[String, Any]) -> Tuple[DataFrame, Scaler]:
    println!("📏 Scaling features...")
    let scaling_method: String := config["feature_scaling"] as String
    let mut scaled_data: DataFrame := data.copy()

    let numeric_columns: List[String] := scaled_data.numeric_columns()
    let scaler: Scaler := when scaling_method == "standard_scaler":
        StandardScaler::new()
    otherwise when scaling_method == "min_max_scaler":
        MinMaxScaler::new()
    otherwise when scaling_method == "robust_scaler":
        RobustScaler::new()
    otherwise:
        StandardScaler::new()  // Default

    // Fit and transform numeric columns
    repeat column in numeric_columns:
        when column != "target":  // Don't scale target variable
            let original_values: Series := scaled_data[column]
            scaled_data[column] := scaler.fit_transform(original_values)

            let mean_val: Float := scaled_data[column].mean()
            let std_val: Float := scaled_data[column].std()
            println!("  Scaled {}: mean={:.3f}, std={:.3f}", column, mean_val, std_val)

    println!("✅ Feature scaling completed using {}", scaling_method)
    return (scaled_data, scaler)

// Main Pipeline Execution
fn main() -> Void:
    // Load dataset
    let dataset_result: Result[DataFrame, String] := load_dataset(config["input_file"] as String)

    let raw_data: DataFrame := when dataset_result:
        Ok(data) => data
        Err(error) => {
            println!("❌ Pipeline failed: {}", error)
            return
        }

    // Validate data quality
    let quality_report: DataQualityReport := validate_data_quality(raw_data)
    quality_report.print_summary()

    // Clean dataset
    let cleaned_data: DataFrame := clean_dataset(raw_data, config)

    // Engineer features
    let engineered_data: DataFrame := engineer_features(cleaned_data)

    // Scale features
    let (final_data, scaler): Tuple[DataFrame, Scaler] := scale_features(engineered_data, config)

    // Save processed data
    let output_path: String := config["output_file"] as String
    final_data.to_csv(output_path)
    println!("💾 Processed data saved to: {}", output_path)

    // Save scaler for future use
    let scaler_path: String := "models/data_scaler.pkl"
    scaler.save(scaler_path)
    println!("💾 Scaler saved to: {}", scaler_path)

    // Final summary
    println!("\n🎉 Data Processing Pipeline Completed Successfully!")
    println!("📊 Final Dataset Statistics:")
    println!("  Rows: {}", final_data.shape()[0])
    println!("  Columns: {}", final_data.shape()[1])
    println!("  Features: {}", final_data.columns().len() - 1)  // Exclude target
    println!("  Memory usage: {:.2f} MB", final_data.memory_usage() / 1024.0 / 1024.0)

main()
