// Test script for basic AI/ML operations
// Expected: All ML operations should execute correctly

// Import ML libraries
bring ml
bring data
bring numpy

println!("=== Data Loading and Preprocessing ===")

// Load dataset
let dataset: DataFrame  := load_csv("data/iris.csv")
println!("Dataset loaded: {} rows, {} columns", dataset.rows(), dataset.columns())

// Display first few rows
println!("First 5 rows:")
dataset.head(5).display()

// Basic dataset information
println!("\nDataset info:")
println!("Shape: {:?}", dataset.shape())
println!("Column names: {:?}", dataset.column_names())
println!("Data types: {:?}", dataset.dtypes())

// Data preprocessing
println!("\n=== Data Preprocessing ===")

// Handle missing values
let cleaned_data: DataFrame   := dataset.fillna("mean")
println!("Missing values filled with mean")

// Feature scaling
let scaler: StandardScaler   := StandardScaler::new()
let scaled_features: DataFrame   := scaler.fit_transform(cleaned_data.select(["sepal_length", "sepal_width", "petal_length", "petal_width"]))
println!("Features scaled using StandardScaler")

// Encode categorical variables
let encoder: LabelEncoder   := LabelEncoder::new()
let encoded_labels: Array[Integer]   := encoder.fit_transform(cleaned_data["species"])
println!("Species labels encoded: {:?}", encoder.classes())

// Train-test split
let (X_train, X_test, y_train, y_test) = train_test_split(
    scaled_features, 
    encoded_labels, 
    test_size: 0.2, 
    random_state: 42
)

println!("Train set: {} samples", X_train.shape()[0])
println!("Test set: {} samples", X_test.shape()[0])

// Classification
println!("\n  := == Classification Models ===")

// Random Forest Classifier
let rf_model: RandomForestClassifier   := RandomForestClassifier::new(
    n_estimators: 100,
    max_depth: Some(5),
    random_state: 42
)

println!("Training Random Forest...")
train! rf_model on X_train, y_train {
    epochs: 1,
    verbose: true
}

// Make predictions
let rf_predictions: Array<Integer>   := predict! rf_model on X_test
let rf_accuracy: Float   := accuracy_score(y_test, rf_predictions)
println!("Random Forest Accuracy: {:.4}", rf_accuracy)

// Support Vector Machine
let svm_model: SVC   := SVC::new(
    kernel: "rbf",
    C: 1.0,
    gamma: "scale"
)

println!("\nTraining SVM...")
train! svm_model on X_train, y_train {
    epochs: 1,
    verbose: true
}

let svm_predictions: Array<Integer>   := predict! svm_model on X_test
let svm_accuracy: Float   := accuracy_score(y_test, svm_predictions)
println!("SVM Accuracy: {:.4}", svm_accuracy)

// Neural Network
let nn_model: MLPClassifier   := MLPClassifier::new(
    hidden_layer_sizes: [100, 50],
    activation: "relu",
    solver: "adam",
    learning_rate: 0.001,
    max_iter: 1000
)

println!("\nTraining Neural Network...")
train! nn_model on X_train, y_train {
    epochs: 100,
    batch_size: 32,
    validation_split: 0.2,
    verbose: true
}

let nn_predictions: Array<Integer>   := predict! nn_model on X_test
let nn_accuracy: Float   := accuracy_score(y_test, nn_predictions)
println!("Neural Network Accuracy: {:.4}", nn_accuracy)

// Model evaluation
println!("\n=== Model Evaluation ===")

// Confusion matrix
let cm: Array<Array<Integer>>   := confusion_matrix(y_test, rf_predictions)
println!("Random Forest Confusion Matrix:")
println!("{:?}", cm)

// Classification report
let report: ClassificationReport   := classification_report(y_test, rf_predictions, target_names: encoder.classes())
println!("Classification Report:")
report.display()

// Cross-validation
println!("\n=== Cross Validation ===")

let cv_scores: Array<Float>   := cross_val_score(rf_model, scaled_features, encoded_labels, cv: 5)
println!("Cross-validation scores: {:?}", cv_scores)
println!("Mean CV score: {:.4} (+/- {:.4})", cv_scores.mean(), cv_scores.std() * 2)

// Regression
println!("\n=== Regression Models ===")

// Generate regression dataset
let (X_reg, y_reg) = make_regression(
    n_samples: 1000,
    n_features: 10,
    noise: 0.1,
    random_state: 42
)

let (X_reg_train, X_reg_test, y_reg_train, y_reg_test)   := train_test_split(
    X_reg, y_reg, 
    test_size: 0.2, 
    random_state: 42
)

// Linear Regression
let lr_model: LinearRegression   := LinearRegression::new()

train! lr_model on X_reg_train, y_reg_train {
    epochs: 1,
    verbose: true
}

let lr_predictions: Array<Float>   := predict! lr_model on X_reg_test
let lr_r2: Float   := r2_score(y_reg_test, lr_predictions)
let lr_mse: Float   := mean_squared_error(y_reg_test, lr_predictions)

println!("Linear Regression R²: {:.4}", lr_r2)
println!("Linear Regression MSE: {:.4}", lr_mse)

// Random Forest Regression
let rfr_model: RandomForestRegressor   := RandomForestRegressor::new(
    n_estimators: 100,
    max_depth: Some(10),
    random_state: 42
)

train! rfr_model on X_reg_train, y_reg_train {
    epochs: 1,
    verbose: true
}

let rfr_predictions: Array<Float>   := predict! rfr_model on X_reg_test
let rfr_r2: Float   := r2_score(y_reg_test, rfr_predictions)
let rfr_mse: Float   := mean_squared_error(y_reg_test, rfr_predictions)

println!("Random Forest Regression R²: {:.4}", rfr_r2)
println!("Random Forest Regression MSE: {:.4}", rfr_mse)

// Clustering
println!("\n=== Clustering ===")

// K-Means clustering
let kmeans: KMeans   := KMeans::new(
    n_clusters: 3,
    random_state: 42,
    max_iter: 300
)

let cluster_labels: Array<Integer>   := kmeans.fit_predict(scaled_features)
println!("K-Means clustering completed")
println!("Cluster centers: {:?}", kmeans.cluster_centers())

// Evaluate clustering
let silhouette_avg: Float   := silhouette_score(scaled_features, cluster_labels)
println!("Average silhouette score: {:.4}", silhouette_avg)

// Dimensionality Reduction
println!("\n=== Dimensionality Reduction ===")

// Principal Component Analysis
let pca: PCA   := PCA::new(n_components: 2)
let X_pca: Array<Array<Float>>   := pca.fit_transform(scaled_features)

println!("PCA completed: {} -> {} dimensions", scaled_features.shape()[1], X_pca.shape()[1])
println!("Explained variance ratio: {:?}", pca.explained_variance_ratio())
println!("Total explained variance: {:.4}", pca.explained_variance_ratio().sum())

// t-SNE
let tsne: TSNE   := TSNE::new(
    n_components: 2,
    perplexity: 30.0,
    random_state: 42
)

let X_tsne: Array<Array<Float>>   := tsne.fit_transform(scaled_features)
println!("t-SNE completed: {} -> {} dimensions", scaled_features.shape()[1], X_tsne.shape()[1])

// Model persistence
println!("\n=== Model Persistence ===")

// Save model
rf_model.save("models/random_forest_iris.pkl")
println!("Random Forest model saved")

// Load model
let loaded_model: RandomForestClassifier   := RandomForestClassifier::load("models/random_forest_iris.pkl")
let loaded_predictions: Array<Integer>   := predict! loaded_model on X_test
let loaded_accuracy: Float   := accuracy_score(y_test, loaded_predictions)
println!("Loaded model accuracy: {:.4}", loaded_accuracy)

// Feature importance
println!("\n=== Feature Importance ===")

let feature_names: Array<String>   := ["sepal_length", "sepal_width", "petal_length", "petal_width"]
let importances: Array<Float>   := rf_model.feature_importances()

println!("Feature importances:")
repeat (i, importance) in importances.enumerate() {
    println!("  {}: {:.4}", feature_names[i], importance)
}

// Hyperparameter tuning
println!("\n=== Hyperparameter Tuning ===")

let param_grid: Map[String, Array<Any]>   := {
    "n_estimators": [50, 100, 200],
    "max_depth": [3, 5, 7, None],
    "min_samples_split": [2, 5, 10]
}

let grid_search: GridSearchCV   := GridSearchCV::new(
    estimator: RandomForestClassifier::new(),
    param_grid: param_grid,
    cv: 5,
    scoring: "accuracy"
)

grid_search.fit(X_train, y_train)

println!("Best parameters: {:?}", grid_search.best_params())
println!("Best cross-validation score: {:.4}", grid_search.best_score())

let best_model: RandomForestClassifier   := grid_search.best_estimator()
let best_predictions: Array<Integer>   := predict! best_model on X_test
let best_accuracy: Float   := accuracy_score(y_test, best_predictions)
println!("Best model test accuracy: {:.4}", best_accuracy)

println!("\nAll ML operations completed successfully!")

// Expected Output:
// Dataset loaded: 150 rows, 5 columns
// First 5 rows: (iris data display)
// Dataset info with shape, columns, dtypes
// Missing values filled with mean
// Features scaled using StandardScaler
// Species labels encoded: ['setosa', 'versicolor', 'virginica']
// Train set: 120 samples, Test set: 30 samples
// Training Random Forest... (training progress)
// Random Forest Accuracy: 0.9667
// Training SVM... (training progress)
// SVM Accuracy: 0.9333
// Training Neural Network... (training progress with epochs)
// Neural Network Accuracy: 0.9667
// Random Forest Confusion Matrix: [[10, 0, 0], [0, 9, 1], [0, 0, 10]]
// Classification Report: (precision, recall, f1-score for each class)
// Cross-validation scores: [0.96, 0.93, 0.97, 0.93, 0.97]
// Mean CV score: 0.9520 (+/- 0.0327)
// Linear Regression R²: 0.9876, MSE: 0.0234
// Random Forest Regression R²: 0.9912, MSE: 0.0167
// K-Means clustering completed
// Average silhouette score: 0.5528
// PCA completed: 4 -> 2 dimensions
// Explained variance ratio: [0.7296, 0.2285]
// Total explained variance: 0.9581
// t-SNE completed: 4 -> 2 dimensions
// Random Forest model saved
// Loaded model accuracy: 0.9667
// Feature importances: petal_length: 0.4234, petal_width: 0.3891, etc.
// Best parameters: {'n_estimators': 100, 'max_depth': 5, 'min_samples_split': 2}
// Best cross-validation score: 0.9583
// Best model test accuracy: 0.9667
// All ML operations completed successfully!
