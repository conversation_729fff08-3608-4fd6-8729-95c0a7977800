# Umbra Language Test Scripts Index

This document provides a comprehensive index of all test scripts in the Umbra language test suite.

## 📊 Test Coverage Overview

| Category | Scripts | Features Tested | Status |
|----------|---------|-----------------|--------|
| Basic | 3 | Variables, Data Types, Operators | ✅ Complete |
| Control Flow | 2 | Conditionals, Loops, Pattern Matching | ✅ Complete |
| Functions | 1 | Function Definitions, Calls, Closures | ✅ Complete |
| OOP | 2 | Structures, Traits, Implementations | ✅ Complete |
| Advanced | 1 | Generics, Type Parameters | ✅ Complete |
| Async | 1 | Async/Await, Concurrency | ✅ Complete |
| Database | 1 | Database Operations, Queries | ✅ Complete |
| AI/ML | 1 | Machine Learning Features | ✅ Complete |
| Error Handling | 1 | Error Types, Try/Catch | ✅ Complete |
| Macros | 1 | Macro System, Expansion | ✅ Complete |

**Total: 14 test scripts covering 100+ language features**

## 📁 Detailed Test Script Breakdown

### 🔤 Basic Language Features (`basic/`)

#### `01_variables.umbra`
- **Purpose**: Test variable declarations and assignments
- **Features Tested**:
  - Basic variable declarations (`let x: Integer = 42`)
  - Mutable variables (`let mut counter: Integer = 0`)
  - Type inference (`let auto_int = 100`)
  - Constants and immutable variables
  - Variable shadowing
  - Multiple variable declarations
- **Expected Output**: All variables declared and assigned correctly
- **Runtime**: ~1 second

#### `02_data_types.umbra`
- **Purpose**: Test all basic data types in Umbra
- **Features Tested**:
  - Numeric types (Integer, Float, scientific notation)
  - String types (simple, empty, multiline, escaped)
  - Boolean type
  - Collection types (List, Map, Set)
  - Option and Result types
  - Tuple types
  - Type checking and conversion
- **Expected Output**: All data types properly handled and displayed
- **Runtime**: ~2 seconds

#### `03_operators.umbra`
- **Purpose**: Test all operators in Umbra
- **Features Tested**:
  - Arithmetic operators (`+`, `-`, `*`, `/`, `%`, `**`)
  - Comparison operators (`==`, `!=`, `>`, `<`, `>=`, `<=`)
  - Logical operators (`&&`, `||`, `!`)
  - Bitwise operators (`&`, `|`, `^`, `~`, `<<`, `>>`)
  - Assignment operators (`+=`, `-=`, `*=`, `/=`, `%=`)
  - String operators (concatenation)
  - Range operators (`..`, `..=`)
  - Null coalescing (`??`)
  - Ternary operator (`? :`)
  - Operator precedence
- **Expected Output**: All operators work correctly with proper precedence
- **Runtime**: ~1 second

### 🔀 Control Flow (`control_flow/`)

#### `01_conditionals.umbra`
- **Purpose**: Test conditional statements (when/otherwise)
- **Features Tested**:
  - Basic when statements
  - Multiple conditions with otherwise when
  - Complex conditions with logical operators
  - Nested when statements
  - When expressions (returning values)
  - Pattern matching with Option and Result
  - Guards in pattern matching
  - Multiple patterns with `|`
  - Range patterns
- **Expected Output**: All conditional branches execute correctly
- **Runtime**: ~2 seconds

#### `02_loops.umbra`
- **Purpose**: Test loop constructs
- **Features Tested**:
  - Repeat loops (for-each style)
  - Repeat with ranges and collections
  - While loops with complex conditions
  - Do-while loops
  - For loops (C-style)
  - Nested loops
  - Break and continue statements
  - Labeled break and continue
  - Infinite loops with break conditions
  - Pattern matching in loops
- **Expected Output**: All loops execute correctly with proper control flow
- **Runtime**: ~3 seconds

### 🔧 Functions (`functions/`)

#### `01_basic_functions.umbra`
- **Purpose**: Test function definitions and calls
- **Features Tested**:
  - Simple functions with no parameters
  - Functions with multiple parameters
  - Default parameters
  - Early returns and multiple return points
  - Reference parameters
  - Expression body functions
  - Recursive functions (Fibonacci)
  - Higher-order functions
  - Closures and function factories
  - Variadic functions
  - Generic functions
  - Constrained generics
- **Expected Output**: All functions defined and called correctly
- **Runtime**: ~2 seconds

### 🏗️ Object-Oriented Programming (`oop/`)

#### `01_structures.umbra`
- **Purpose**: Test structure definitions and usage
- **Features Tested**:
  - Basic structure definitions
  - Structures with methods and constructors
  - Generic structures
  - Structures with lifetime parameters
  - Nested structures
  - Optional fields
  - Default values
  - Structure update syntax
  - Structure destructuring
- **Expected Output**: All structures defined and used correctly
- **Runtime**: ~2 seconds

#### `02_traits.umbra`
- **Purpose**: Test trait definitions and implementations
- **Features Tested**:
  - Basic trait definitions
  - Traits with default implementations
  - Associated types
  - Generic traits
  - Multiple methods in traits
  - Trait inheritance
  - Trait implementations for structures
  - Generic functions with trait bounds
  - Multiple trait bounds
  - Trait objects (dynamic dispatch)
  - Cross-validation and model evaluation
- **Expected Output**: All traits defined and implemented correctly
- **Runtime**: ~3 seconds

### 🚀 Advanced Features (`advanced/`)

#### `01_generics.umbra`
- **Purpose**: Test generic types and functions
- **Features Tested**:
  - Generic functions with single/multiple type parameters
  - Generic functions with constraints
  - Generic structures and implementations
  - Generic traits and enums
  - Higher-kinded types simulation
  - Complex generic constraints with where clauses
  - Type inference with generics
  - Generic trait objects
  - Associated types in generics
- **Expected Output**: All generic constructs work correctly
- **Runtime**: ~3 seconds

### ⚡ Async Programming (`async/`)

#### `01_basic_async.umbra`
- **Purpose**: Test async/await functionality
- **Features Tested**:
  - Basic async functions
  - Async functions with parameters
  - Sequential vs concurrent async calls
  - Async function composition
  - Timeout handling with race conditions
  - Async generators/streams
  - Async error handling with try/catch
  - Async combinators (join, select)
  - Async loops and iteration
- **Expected Output**: All async operations execute correctly
- **Runtime**: ~10 seconds (includes delays)

### 🗄️ Database Operations (`database/`)

#### `01_basic_database.umbra`
- **Purpose**: Test database integration features
- **Features Tested**:
  - Database connection configuration
  - Table creation with DDL
  - INSERT operations (single, multiple, with variables)
  - SELECT operations (all, with WHERE, single record, with parameters)
  - UPDATE operations (single, multiple records)
  - DELETE operations with conditions
  - JOIN operations (INNER JOIN)
  - Aggregate functions (COUNT, AVG, MIN, MAX)
  - Subqueries
  - Database connection management
- **Expected Output**: All database operations execute correctly
- **Runtime**: ~3 seconds

### 🤖 AI/ML Features (`ai_ml/`)

#### `01_basic_ml.umbra`
- **Purpose**: Test AI/ML specific features
- **Features Tested**:
  - Data loading and preprocessing
  - Feature scaling and encoding
  - Train-test splitting
  - Classification models (Random Forest, SVM, Neural Network)
  - Model evaluation and metrics
  - Cross-validation
  - Regression models
  - Clustering (K-Means)
  - Dimensionality reduction (PCA, t-SNE)
  - Model persistence (save/load)
  - Feature importance analysis
  - Hyperparameter tuning with GridSearch
- **Expected Output**: All ML operations execute correctly
- **Runtime**: ~15 seconds (model training)

### ❌ Error Handling (`error_handling/`)

#### `01_basic_errors.umbra`
- **Purpose**: Test error handling mechanisms
- **Features Tested**:
  - Custom error types
  - Result type handling
  - Try-catch blocks
  - Error propagation with `?` operator
  - Multiple error types in functions
  - Option type error handling
  - Nested error handling
  - Finally blocks for cleanup
  - Error chaining and composition
  - Pattern matching with errors
- **Expected Output**: All error handling constructs work correctly
- **Runtime**: ~2 seconds

### 🔧 Macro System (`macros/`)

#### `01_basic_macros.umbra`
- **Purpose**: Test macro system functionality
- **Features Tested**:
  - Built-in macros (println!, format!, assert!, debug!)
  - Custom macro definitions with macro_rules!
  - Macros with parameters and multiple patterns
  - Macros with repetition
  - Macros for code generation
  - Advanced macro features (conditional compilation)
  - Procedural macro simulation
  - Macro hygiene
  - Recursive macros
  - Compile-time computation
- **Expected Output**: All macros expand and execute correctly
- **Runtime**: ~2 seconds

## 🚀 Running the Tests

### Run All Tests
```bash
./test_scripts/run_all_tests.sh
```

### Run Specific Category
```bash
./test_scripts/run_all_tests.sh basic
./test_scripts/run_all_tests.sh oop
./test_scripts/run_all_tests.sh async
```

### Run Single Test
```bash
cargo run -- test_scripts/basic/01_variables.umbra
```

### List Available Categories
```bash
./test_scripts/run_all_tests.sh --list
```

### Get Help
```bash
./test_scripts/run_all_tests.sh --help
```

## 📈 Expected Test Results

When all tests pass, you should see:
- **Total Tests**: 14
- **Passed**: 14
- **Failed**: 0
- **Skipped**: 0
- **Success Rate**: 100%
- **Total Runtime**: ~40 seconds

## 🔍 Test Validation

Each test script includes:
- **Comprehensive comments** explaining expected behavior
- **Expected output** documented in comments
- **Error cases** to test failure scenarios
- **Edge cases** to ensure robustness
- **Performance considerations** for timing-sensitive tests

## 🛠️ Extending the Test Suite

To add new tests:
1. Create a new `.umbra` file in the appropriate category directory
2. Follow the naming convention: `##_feature_name.umbra`
3. Include comprehensive comments and expected output
4. Test both success and failure cases
5. Update this index file

## 📝 Notes

- Tests are designed to be independent and can run in any order
- Each test includes cleanup to avoid side effects
- Database tests use mock operations for portability
- AI/ML tests use simulated data for consistency
- Async tests include appropriate delays for timing validation
- All tests include comprehensive error handling validation
