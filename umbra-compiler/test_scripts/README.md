# Umbra Language Test Scripts

This directory contains comprehensive test scripts for all Umbra language features. Each subdirectory focuses on a specific aspect of the language.

## Directory Structure

### 📁 `basic/`
Basic language features and syntax
- Variables and data types
- Literals and expressions
- Basic arithmetic and operations
- Comments and documentation

### 📁 `control_flow/`
Control flow constructs
- Conditional statements (`when`/`otherwise`)
- Loops (`repeat`, `while`, `for`)
- Pattern matching (`match`)
- Break and continue statements

### 📁 `functions/`
Function-related features
- Function definitions and calls
- Parameters and return values
- Closures and lambda expressions
- Higher-order functions
- Recursion

### 📁 `oop/`
Object-oriented programming features
- Structures and classes
- Traits and interfaces
- Implementations and methods
- Inheritance and polymorphism
- Access modifiers

### 📁 `advanced/`
Advanced language features
- Generics and type parameters
- Memory ownership and borrowing
- Modules and imports
- Custom operators
- Metaprogramming

### 📁 `database/`
Database integration features
- Database connections
- Query execution
- Transactions
- Migrations
- ORM functionality

### 📁 `ai_ml/`
AI/ML specific features
- Model training
- Data preprocessing
- Evaluation and metrics
- Prediction and inference
- Integration with Python libraries

### 📁 `async/`
Asynchronous programming
- Async functions
- Await expressions
- Task spawning
- Concurrent execution
- Async streams

### 📁 `macros/`
Macro system
- Built-in macros
- Custom macro definitions
- Procedural macros
- Compile-time code generation

### 📁 `error_handling/`
Error handling mechanisms
- Try/catch blocks
- Error propagation (`?` operator)
- Custom error types
- Result and Option types
- Exception handling

## Running Tests

To run a specific test script:
```bash
cargo run -- test_scripts/basic/variables.umbra
```

To run all tests in a category:
```bash
# Run all basic tests
find test_scripts/basic -name "*.umbra" -exec cargo run -- {} \;
```

To run all test scripts:
```bash
./run_all_tests.sh
```

## Test Script Naming Convention

- `01_feature_name.umbra` - Basic feature test
- `02_feature_name_advanced.umbra` - Advanced feature test
- `test_feature_name.umbra` - Comprehensive feature test
- `example_feature_name.umbra` - Example usage demonstration

## Expected Output

Each test script includes comments indicating expected behavior and output. Some scripts may require specific setup or dependencies.

## Contributing

When adding new test scripts:
1. Follow the naming convention
2. Include comprehensive comments
3. Test both success and failure cases
4. Document expected output
5. Keep scripts focused on specific features
