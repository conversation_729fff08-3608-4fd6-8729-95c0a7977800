// Test script for basic database operations
// Expected: All database operations should execute correctly

// Database connection configuration
let db_config: DatabaseConfig = {
    host: "localhost",
    port: 5432,
    database: "test_db",
    username: "user",
    password: "password",
    pool_size: 10
}

// Connect to database
let db: Database = connect(db_config)

println!("=== Database Connection ===")
println!("Connected to database: {}", db_config.database)

// Basic table creation
println!("\n=== Table Creation ===")

query! {
    CREATE TABLE users (
        id SERIAL PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        email VARCHAR(150) UNIQUE NOT NULL,
        age INTEGER,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
}

query! {
    CREATE TABLE posts (
        id SERIAL PRIMARY KEY,
        user_id INTEGER REFERENCES users(id),
        title VARCHAR(200) NOT NULL,
        content TEXT,
        published BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
}

println!("Tables created successfully")

// Basic INSERT operations
println!("\n=== Insert Operations ===")

// Single insert
let user_id1: Integer = query! {
    INSERT INTO users (name, email, age) 
    VALUES ('Alice Johnson', '<EMAIL>', 28)
    RETURNING id
}.fetch_one().id

println!("Inserted user with ID: {}", user_id1)

// Multiple inserts
query! {
    INSERT INTO users (name, email, age) VALUES
    ('Bob Smith', '<EMAIL>', 35),
    ('Charlie Brown', '<EMAIL>', 42),
    ('Diana Prince', '<EMAIL>', 30)
}

println!("Inserted multiple users")

// Insert with variables
let new_user_name: String = "Eve Wilson"
let new_user_email: String = "<EMAIL>"
let new_user_age: Integer = 26

query! {
    INSERT INTO users (name, email, age) 
    VALUES (${new_user_name}, ${new_user_email}, ${new_user_age})
}

println!("Inserted user with variables: {}", new_user_name)

// SELECT operations
println!("\n=== Select Operations ===")

// Select all users
let all_users = query! {
    SELECT id, name, email, age FROM users
}.fetch_all()

println!("All users:")
repeat user in all_users {
    println!("  ID: {}, Name: {}, Email: {}, Age: {}", user.id, user.name, user.email, user.age)
}

// Select with WHERE clause
let adult_users = query! {
    SELECT name, age FROM users WHERE age >= 30
}.fetch_all()

println!("\nAdult users (age >= 30):")
repeat user in adult_users {
    println!("  Name: {}, Age: {}", user.name, user.age)
}

// Select single record
let specific_user = query! {
    SELECT * FROM users WHERE email = '<EMAIL>'
}.fetch_one()

println!("\nSpecific user: {} ({})", specific_user.name, specific_user.email)

// Select with parameters
let min_age: Integer = 25
let users_above_age = query! {
    SELECT name, age FROM users WHERE age > ${min_age} ORDER BY age DESC
}.fetch_all()

println!("\nUsers above age {}:", min_age)
repeat user in users_above_age {
    println!("  {}: {} years old", user.name, user.age)
}

// UPDATE operations
println!("\n=== Update Operations ===")

// Update single record
let updated_rows: Integer = query! {
    UPDATE users SET age = 29 WHERE name = 'Alice Johnson'
}.execute()

println!("Updated {} row(s)", updated_rows)

// Update with variables
let new_email: String = "<EMAIL>"
query! {
    UPDATE users SET email = ${new_email} WHERE name = 'Alice Johnson'
}

println!("Updated Alice's email to: {}", new_email)

// Update multiple records
query! {
    UPDATE users SET age = age + 1 WHERE age < 30
}

println!("Incremented age for users under 30")

// DELETE operations
println!("\n=== Delete Operations ===")

// Delete with condition
let deleted_count: Integer = query! {
    DELETE FROM users WHERE age > 40
}.execute()

println!("Deleted {} user(s) over 40", deleted_count)

// Verify remaining users
let remaining_users = query! {
    SELECT COUNT(*) as count FROM users
}.fetch_one()

println!("Remaining users: {}", remaining_users.count)

// JOIN operations
println!("\n=== Join Operations ===")

// Insert some posts first
query! {
    INSERT INTO posts (user_id, title, content, published) VALUES
    (${user_id1}, 'First Post', 'This is my first blog post', true),
    (${user_id1}, 'Second Post', 'Another interesting post', false)
}

// Inner join
let user_posts = query! {
    SELECT u.name, p.title, p.published
    FROM users u
    INNER JOIN posts p ON u.id = p.user_id
    WHERE u.id = ${user_id1}
}.fetch_all()

println!("Posts by user ID {}:", user_id1)
repeat post in user_posts {
    let status: String = when post.published { "Published" } otherwise { "Draft" }
    println!("  '{}' by {} ({})", post.title, post.name, status)
}

// Aggregate functions
println!("\n=== Aggregate Functions ===")

let stats = query! {
    SELECT 
        COUNT(*) as total_users,
        AVG(age) as average_age,
        MIN(age) as youngest,
        MAX(age) as oldest
    FROM users
}.fetch_one()

println!("User statistics:")
println!("  Total users: {}", stats.total_users)
println!("  Average age: {:.1}", stats.average_age)
println!("  Youngest: {}", stats.youngest)
println!("  Oldest: {}", stats.oldest)

// Subqueries
println!("\n=== Subqueries ===")

let users_with_posts = query! {
    SELECT name, email 
    FROM users 
    WHERE id IN (SELECT DISTINCT user_id FROM posts)
}.fetch_all()

println!("Users with posts:")
repeat user in users_with_posts {
    println!("  {} ({})", user.name, user.email)
}

// Close database connection
db.close()
println!("\nDatabase connection closed")

// Expected Output:
// Connected to database: test_db
// Tables created successfully
// Inserted user with ID: 1
// Inserted multiple users
// Inserted user with variables: Eve Wilson
// All users:
//   ID: 1, Name: Alice Johnson, Email: <EMAIL>, Age: 28
//   ID: 2, Name: Bob Smith, Email: <EMAIL>, Age: 35
//   ID: 3, Name: Charlie Brown, Email: <EMAIL>, Age: 42
//   ID: 4, Name: Diana Prince, Email: <EMAIL>, Age: 30
//   ID: 5, Name: Eve Wilson, Email: <EMAIL>, Age: 26
// Adult users (age >= 30):
//   Name: Bob Smith, Age: 35
//   Name: Charlie Brown, Age: 42
//   Name: Diana Prince, Age: 30
// Specific user: Alice Johnson (<EMAIL>)
// Users above age 25:
//   Charlie Brown: 42 years old
//   Bob Smith: 35 years old
//   Diana Prince: 30 years old
//   Alice Johnson: 28 years old
// Updated 1 row(s)
// Updated Alice's email to: <EMAIL>
// Incremented age for users under 30
// Deleted 1 user(s) over 40
// Remaining users: 4
// Posts by user ID 1:
//   'First Post' by Alice Johnson (Published)
//   'Second Post' by Alice Johnson (Draft)
// User statistics:
//   Total users: 4
//   Average age: 30.5
//   Youngest: 27
//   Oldest: 35
// Users with posts:
//   Alice Johnson (<EMAIL>)
// Database connection closed
