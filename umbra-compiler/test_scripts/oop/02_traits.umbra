// Test script for trait definitions and implementations
// Expected: All traits should be defined and implemented correctly

// Basic trait definition
trait Display {
    fn display(self) -> String
}

// Trait with default implementation
trait Greetable {
    fn greet(self) -> String {
        return "Hello!"
    }
    
    fn formal_greet(self) -> String  // Abstract method
}

// Trait with associated types
trait Iterator {
    type Item
    
    fn next(mut self) -> Option[Self::Item]
    fn has_next(self) -> <PERSON><PERSON>an
}

// Trait with generic parameters
trait Comparable<T> {
    fn compare(self, other: T) -> Integer  // -1, 0, 1
}

// Trait with multiple methods
trait Shape {
    fn area(self) -> Float
    fn perimeter(self) -> Float
    fn scale(mut self, factor: Float)
    
    // Default implementation using other methods
    fn describe(self) -> String {
        return "Shape with area " + self.area().to_string() + " and perimeter " + self.perimeter().to_string()
    }
}

// Trait inheritance
trait Drawable : Display {
    fn draw(self) -> String
    fn color(self) -> String
}

// Structure definitions
struct Person {
    name: String,
    age: Integer
}

struct Rectangle {
    width: Float,
    height: Float
}

struct Circle {
    radius: Float
}

struct Point {
    x: Float,
    y: Float
}

// Implement Display trait for Person
impl Display for Person {
    fn display(self) -> String {
        return self.name + " (" + self.age.to_string() + " years old)"
    }
}

// Implement Greetable trait for Person
impl Greetable for Person {
    fn formal_greet(self) -> String {
        return "Good day, " + self.name + "!"
    }
    
    // Override default implementation
    fn greet(self) -> String {
        return "Hi, I'm " + self.name + "!"
    }
}

// Implement Shape trait for Rectangle
impl Shape for Rectangle {
    fn area(self) -> Float {
        return self.width * self.height
    }
    
    fn perimeter(self) -> Float {
        return 2.0 * (self.width + self.height)
    }
    
    fn scale(mut self, factor: Float) {
        self.width *= factor
        self.height *= factor
    }
}

// Implement Shape trait for Circle
impl Shape for Circle {
    fn area(self) -> Float {
        return 3.14159 * self.radius * self.radius
    }
    
    fn perimeter(self) -> Float {
        return 2.0 * 3.14159 * self.radius
    }
    
    fn scale(mut self, factor: Float) {
        self.radius *= factor
    }
}

// Implement Display for shapes
impl Display for Rectangle {
    fn display(self) -> String {
        return "Rectangle(" + self.width.to_string() + "x" + self.height.to_string() + ")"
    }
}

impl Display for Circle {
    fn display(self) -> String {
        return "Circle(radius=" + self.radius.to_string() + ")"
    }
}

// Implement Drawable for Rectangle (trait inheritance)
impl Drawable for Rectangle {
    fn draw(self) -> String {
        return "Drawing a " + self.color() + " rectangle"
    }
    
    fn color(self) -> String {
        return "blue"
    }
}

// Implement Comparable for Integer (built-in type)
impl Comparable<Integer> for Integer {
    fn compare(self, other: Integer) -> Integer {
        when self > other {
            return 1
        } otherwise when self < other {
            return -1
        } otherwise {
            return 0
        }
    }
}

// Generic function that uses traits
fn print_display<T: Display>(item: T) {
    println!("Display: {}", item.display())
}

fn greet_all<T: Greetable>(items: List[T]) {
    repeat item in items {
        println!("{}", item.greet())
        println!("{}", item.formal_greet())
    }
}

fn calculate_total_area<T: Shape>(shapes: List[T]) -> Float {
    let mut total: Float   := 0.0
    repeat shape in shapes {
        total += shape.area()
    }
    return total
}

// Function with multiple trait bounds
fn display_and_draw<T: Display + Drawable>(item: T) {
    println!("Item: {}", item.display())
    println!("Drawing: {}", item.draw())
}

// Main execution
println!("=== Basic Trait Usage ===")

let person1: Person   := Person { name: "Alice", age: 25 }
let person2: Person   := Person { name: "Bob", age: 30 }

print_display(person1)
print_display(person2)

println!("\n=== Trait with Default Implementation ===")

let people: List[Person]   := [
    Person { name: "Charlie", age: 35 },
    Person { name: "Diana", age: 28 }
]

greet_all(people)

println!("\n=== Shape Trait Implementation ===")

let rect: Rectangle   := Rectangle { width: 10.0, height: 5.0 }
let circle: Circle   := Circle { radius: 3.0 }

println!("Rectangle: {}", rect.display())
println!("Area: {}, Perimeter: {}", rect.area(), rect.perimeter())
println!("Description: {}", rect.describe())

println!("\nCircle: {}", circle.display())
println!("Area: {}, Perimeter: {}", circle.area(), circle.perimeter())
println!("Description: {}", circle.describe())

println!("\n=== Trait Inheritance ===")

display_and_draw(rect)

println!("\n=== Generic Trait Implementation ===")

let a: Integer   := 10
let b: Integer   := 5
let comparison: Integer   := a.compare(b)

println!("Comparing {} and {}: {}", a, b, comparison)

println!("\n=== Trait Objects (Dynamic Dispatch) ===")

// Create a list of trait objects
let shapes: List[Box<dyn Shape]>   := [
    Box::new(Rectangle { width: 4.0, height: 6.0 }),
    Box::new(Circle { radius: 2.5 }),
    Box::new(Rectangle { width: 3.0, height: 3.0 })
]

repeat shape in shapes {
    println!("Shape area: {}", shape.area())
}

println!("\n=== Associated Types ===")

struct NumberIterator {
    current: Integer,
    max: Integer
}

impl Iterator for NumberIterator {
    type Item = Integer
    
    fn next(mut self) -> Option[Self::Item] {
        when self.current <= self.max {
            let result: Integer   := self.current
            self.current += 1
            return Some(result)
        } otherwise {
            return None
        }
    }
    
    fn has_next(self) -> Boolean {
        return self.current <= self.max
    }
}

let mut iter: NumberIterator   := NumberIterator { current: 1, max: 5 }

while iter.has_next() {
    when let Some(value) = iter.next() {
        println!("Iterator value: {}", value)
    }
}

// Expected Output:
// Display: Alice (25 years old)
// Display: Bob (30 years old)
// Hi, I'm Charlie!
// Good day, Charlie!
// Hi, I'm Diana!
// Good day, Diana!
// Rectangle: Rectangle(10x5)
// Area: 50, Perimeter: 30
// Description: Shape with area 50 and perimeter 30
// Circle: Circle(radius  := 3)
// Area: 28.27, Perimeter: 18.85
// Description: Shape with area 28.27 and perimeter 18.85
// Item: Rectangle(4x6)
// Drawing: Drawing a blue rectangle
// Comparing 10 and 5: 1
// Shape area: 24
// Shape area: 19.63
// Shape area: 9
// Iterator value: 1
// Iterator value: 2
// Iterator value: 3
// Iterator value: 4
// Iterator value: 5
