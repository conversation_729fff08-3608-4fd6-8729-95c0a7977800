// Test script for structure definitions and usage
// Expected: All structures should be defined and used correctly

// Basic structure definition
struct Person {
    name: String,
    age: Integer,
    email: String
}

// Structure with methods
struct Rectangle {
    width: Float,
    height: Float
}

impl Rectangle {
    // Constructor method
    fn new(width: Float, height: Float) -> Rectangle {
        return Rectangle { width: width, height: height }
    }
    
    // Instance method
    fn area(self) -> Float {
        return self.width * self.height
    }
    
    // Instance method with mutable self
    fn scale(mut self, factor: Float) {
        self.width *= factor
        self.height *= factor
    }
    
    // Method that returns a new instance
    fn scaled(self, factor: Float) -> Rectangle {
        return Rectangle {
            width: self.width * factor,
            height: self.height * factor
        }
    }
    
    // Static method
    fn square(side: Float) -> Rectangle {
        return Rectangle::new(side, side)
    }
}

// Structure with generic parameters
struct Container<T> {
    value: T,
    label: String
}

impl<T> Container<T> {
    fn new(value: T, label: String) -> Container<T> {
        return Container { value: value, label: label }
    }
    
    fn get_value(self) -> T {
        return self.value
    }
    
    fn get_label(self) -> String {
        return self.label
    }
}

// Structure with lifetime parameters
struct Reference<'a> {
    data: &'a String
}

// Nested structures
struct Address {
    street: String,
    city: String,
    zip_code: String
}

struct Employee {
    person: Person,
    address: Address,
    employee_id: Integer,
    salary: Float
}

// Structure with optional fields
struct Config {
    host: String,
    port: Integer,
    ssl_enabled: Boolean,
    timeout: Option<Integer>,
    max_connections: Option<Integer>
}

// Structure with default values
struct DatabaseConfig {
    host: String = "localhost",
    port: Integer = 5432,
    database: String,
    username: String,
    password: String
}

// Main execution
println!("=== Basic Structure Usage ===")

// Create instances
let person1: Person = Person {
    name: "Alice Johnson",
    age: 28,
    email: "<EMAIL>"
}

let person2: Person = Person {
    name: "Bob Smith",
    age: 35,
    email: "<EMAIL>"
}

println!("Person 1: {} (age {})", person1.name, person1.age)
println!("Person 2: {} (age {})", person2.name, person2.age)

println!("\n=== Structure with Methods ===")

// Using constructor
let rect1: Rectangle = Rectangle::new(10.0, 5.0)
println!("Rectangle 1: {}x{}, Area: {}", rect1.width, rect1.height, rect1.area())

// Using static method
let square: Rectangle = Rectangle::square(7.0)
println!("Square: {}x{}, Area: {}", square.width, square.height, square.area())

// Method chaining
let scaled_rect: Rectangle = rect1.scaled(2.0)
println!("Scaled rectangle: {}x{}, Area: {}", scaled_rect.width, scaled_rect.height, scaled_rect.area())

println!("\n=== Generic Structures ===")

let int_container: Container<Integer> = Container::new(42, "Answer")
let string_container: Container<String> = Container::new("Hello", "Greeting")

println!("Int container: {} = {}", int_container.get_label(), int_container.get_value())
println!("String container: {} = {}", string_container.get_label(), string_container.get_value())

println!("\n=== Nested Structures ===")

let address: Address = Address {
    street: "123 Main St",
    city: "Anytown",
    zip_code: "12345"
}

let employee: Employee = Employee {
    person: Person {
        name: "Charlie Brown",
        age: 30,
        email: "<EMAIL>"
    },
    address: address,
    employee_id: 1001,
    salary: 75000.0
}

println!("Employee: {}", employee.person.name)
println!("ID: {}, Salary: ${}", employee.employee_id, employee.salary)
println!("Address: {}, {}, {}", employee.address.street, employee.address.city, employee.address.zip_code)

println!("\n=== Optional Fields ===")

let config1: Config = Config {
    host: "api.example.com",
    port: 443,
    ssl_enabled: true,
    timeout: Some(30),
    max_connections: None
}

let config2: Config = Config {
    host: "localhost",
    port: 8080,
    ssl_enabled: false,
    timeout: None,
    max_connections: Some(100)
}

println!("Config 1: {}:{}, SSL: {}", config1.host, config1.port, config1.ssl_enabled)
println!("Timeout: {:?}, Max connections: {:?}", config1.timeout, config1.max_connections)

println!("Config 2: {}:{}, SSL: {}", config2.host, config2.port, config2.ssl_enabled)
println!("Timeout: {:?}, Max connections: {:?}", config2.timeout, config2.max_connections)

println!("\n=== Structure Update Syntax ===")

// Create a new person based on existing one with some fields changed
let updated_person: Person = Person {
    age: 29,
    ..person1  // Copy other fields from person1
}

println!("Updated person: {} (age {})", updated_person.name, updated_person.age)

println!("\n=== Structure Destructuring ===")

// Destructure a structure
let Person { name, age, email } = person2
println!("Destructured - Name: {}, Age: {}, Email: {}", name, age, email)

// Partial destructuring
let Person { name: person_name, .. } = person1
println!("Person name: {}", person_name)

// Expected Output:
// Person 1: Alice Johnson (age 28)
// Person 2: Bob Smith (age 35)
// Rectangle 1: 10x5, Area: 50
// Square: 7x7, Area: 49
// Scaled rectangle: 20x10, Area: 200
// Int container: Answer = 42
// String container: Greeting = Hello
// Employee: Charlie Brown
// ID: 1001, Salary: $75000
// Address: 123 Main St, Anytown, 12345
// Config 1: api.example.com:443, SSL: true
// Timeout: Some(30), Max connections: None
// Config 2: localhost:8080, SSL: false
// Timeout: None, Max connections: Some(100)
// Updated person: Alice Johnson (age 29)
// Destructured - Name: Bob Smith, Age: 35, Email: <EMAIL>
// Person name: Alice Johnson
