#!/bin/bash

# Umbra Language Test Runner
# This script runs all test scripts and reports results

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Test statistics
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0
SKIPPED_TESTS=0

# Function to print colored output
print_color() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to print test header
print_header() {
    local title=$1
    echo
    print_color $BLUE "=================================="
    print_color $BLUE "  $title"
    print_color $BLUE "=================================="
    echo
}

# Function to run a single test
run_test() {
    local test_file=$1
    local test_name=$(basename "$test_file" .umbra)
    
    echo -n "Running $test_name... "
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    # Check if test file exists
    if [ ! -f "$test_file" ]; then
        print_color $YELLOW "SKIPPED (file not found)"
        SKIPPED_TESTS=$((SKIPPED_TESTS + 1))
        return
    fi
    
    # Run the test
    if timeout 30s cargo run -- "$test_file" > /dev/null 2>&1; then
        print_color $GREEN "PASSED"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        print_color $RED "FAILED"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        
        # Show error output for debugging
        echo "Error output:"
        cargo run -- "$test_file" 2>&1 | head -10
        echo "..."
    fi
}

# Function to run tests in a category
run_category_tests() {
    local category=$1
    local description=$2
    
    print_header "$description"
    
    if [ -d "test_scripts/$category" ]; then
        for test_file in test_scripts/$category/*.umbra; do
            if [ -f "$test_file" ]; then
                run_test "$test_file"
            fi
        done
    else
        print_color $YELLOW "Category directory not found: test_scripts/$category"
    fi
}

# Main execution
main() {
    print_color $PURPLE "🚀 Umbra Language Test Suite Runner"
    print_color $PURPLE "====================================="
    
    # Check if we're in the right directory
    if [ ! -f "Cargo.toml" ]; then
        print_color $RED "Error: Please run this script from the umbra-compiler root directory"
        exit 1
    fi
    
    # Check if cargo is available
    if ! command -v cargo &> /dev/null; then
        print_color $RED "Error: Cargo is not installed or not in PATH"
        exit 1
    fi
    
    # Build the project first
    print_header "Building Umbra Compiler"
    if cargo build --release; then
        print_color $GREEN "Build successful!"
    else
        print_color $RED "Build failed! Cannot run tests."
        exit 1
    fi
    
    # Start timer
    START_TIME=$(date +%s)
    
    # Run test categories
    run_category_tests "basic" "Basic Language Features"
    run_category_tests "control_flow" "Control Flow Constructs"
    run_category_tests "functions" "Function Features"
    run_category_tests "oop" "Object-Oriented Programming"
    run_category_tests "advanced" "Advanced Features"
    run_category_tests "async" "Async Programming"
    run_category_tests "database" "Database Operations"
    run_category_tests "ai_ml" "AI/ML Features"
    run_category_tests "error_handling" "Error Handling"
    run_category_tests "macros" "Macro System"
    
    # Calculate execution time
    END_TIME=$(date +%s)
    EXECUTION_TIME=$((END_TIME - START_TIME))
    
    # Print final results
    print_header "Test Results Summary"
    
    echo "Total Tests:   $TOTAL_TESTS"
    print_color $GREEN "Passed:        $PASSED_TESTS"
    print_color $RED "Failed:        $FAILED_TESTS"
    print_color $YELLOW "Skipped:       $SKIPPED_TESTS"
    echo "Execution Time: ${EXECUTION_TIME}s"
    
    # Calculate success rate
    if [ $TOTAL_TESTS -gt 0 ]; then
        SUCCESS_RATE=$(( (PASSED_TESTS * 100) / TOTAL_TESTS ))
        echo "Success Rate:  ${SUCCESS_RATE}%"
        
        if [ $FAILED_TESTS -eq 0 ]; then
            print_color $GREEN "🎉 All tests passed!"
            exit 0
        else
            print_color $RED "❌ Some tests failed."
            exit 1
        fi
    else
        print_color $YELLOW "⚠️  No tests were found to run."
        exit 1
    fi
}

# Handle command line arguments
case "${1:-}" in
    --help|-h)
        echo "Umbra Language Test Runner"
        echo ""
        echo "Usage: $0 [OPTIONS] [CATEGORY]"
        echo ""
        echo "OPTIONS:"
        echo "  --help, -h     Show this help message"
        echo "  --list, -l     List available test categories"
        echo "  --verbose, -v  Run with verbose output"
        echo ""
        echo "CATEGORY:"
        echo "  basic          Run only basic language tests"
        echo "  control_flow   Run only control flow tests"
        echo "  functions      Run only function tests"
        echo "  oop            Run only OOP tests"
        echo "  advanced       Run only advanced feature tests"
        echo "  async          Run only async programming tests"
        echo "  database       Run only database tests"
        echo "  ai_ml          Run only AI/ML tests"
        echo "  error_handling Run only error handling tests"
        echo "  macros         Run only macro tests"
        echo ""
        echo "Examples:"
        echo "  $0                    # Run all tests"
        echo "  $0 basic              # Run only basic tests"
        echo "  $0 --verbose oop      # Run OOP tests with verbose output"
        exit 0
        ;;
    --list|-l)
        echo "Available test categories:"
        echo "  basic          - Variables, data types, operators"
        echo "  control_flow   - Conditionals, loops, pattern matching"
        echo "  functions      - Function definitions, calls, closures"
        echo "  oop            - Structures, traits, implementations"
        echo "  advanced       - Generics, ownership, modules"
        echo "  async          - Async/await, concurrency"
        echo "  database       - Database operations, queries"
        echo "  ai_ml          - Machine learning features"
        echo "  error_handling - Error types, try/catch"
        echo "  macros         - Macro definitions and expansion"
        exit 0
        ;;
    --verbose|-v)
        set -x  # Enable verbose mode
        shift
        ;;
esac

# If a specific category is provided, run only that category
if [ $# -eq 1 ]; then
    CATEGORY=$1
    case $CATEGORY in
        basic|control_flow|functions|oop|advanced|async|database|ai_ml|error_handling|macros)
            print_color $PURPLE "🚀 Running $CATEGORY tests only"
            
            # Build first
            print_header "Building Umbra Compiler"
            if cargo build --release; then
                print_color $GREEN "Build successful!"
            else
                print_color $RED "Build failed! Cannot run tests."
                exit 1
            fi
            
            START_TIME=$(date +%s)
            run_category_tests "$CATEGORY" "$(echo $CATEGORY | tr '_' ' ' | sed 's/.*/\L&/; s/[a-z]*/\u&/g') Tests"
            END_TIME=$(date +%s)
            EXECUTION_TIME=$((END_TIME - START_TIME))
            
            print_header "Test Results Summary"
            echo "Total Tests:   $TOTAL_TESTS"
            print_color $GREEN "Passed:        $PASSED_TESTS"
            print_color $RED "Failed:        $FAILED_TESTS"
            print_color $YELLOW "Skipped:       $SKIPPED_TESTS"
            echo "Execution Time: ${EXECUTION_TIME}s"
            
            if [ $FAILED_TESTS -eq 0 ]; then
                print_color $GREEN "🎉 All $CATEGORY tests passed!"
                exit 0
            else
                print_color $RED "❌ Some $CATEGORY tests failed."
                exit 1
            fi
            ;;
        *)
            print_color $RED "Unknown category: $CATEGORY"
            print_color $YELLOW "Use --list to see available categories"
            exit 1
            ;;
    esac
fi

# Run all tests
main
