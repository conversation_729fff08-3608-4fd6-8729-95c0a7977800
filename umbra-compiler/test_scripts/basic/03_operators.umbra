// Test script repeat all operators in Umbra
// Expected: All operators should work correctly

// Arithmetic operators
let a: Integer   := 10
let b: Integer   := 3

println!("=== Arithmetic Operators ===")
println!("a = {}, b = {}", a, b)
println!("a + b = {}", a + b)    // Addition: 13
println!("a - b = {}", a - b)    // Subtraction: 7
println!("a * b = {}", a * b)    // Multiplication: 30
println!("a / b = {}", a / b)    // Division: 3 (integer division)
println!("a % b = {}", a % b)    // Modulo: 1
println!("a ** b = {}", a ** b)  // Exponentiation: 1000

// Float arithmetic
let x: Float   := 10.5
let y: Float   := 3.2

println!("\n=== Float Arithmetic ===")
println!("x = {}, y = {}", x, y)
println!("x + y = {}", x + y)
println!("x - y = {}", x - y)
println!("x * y = {}", x * y)
println!("x / y = {}", x / y)

// Comparison operators
println!("\n=== Comparison Operators ===")
println!("a == b: {}", a == b)   // Equal: false
println!("a != b: {}", a != b)   // Not equal: true
println!("a > b: {}", a > b)     // Greater than: true
println!("a < b: {}", a < b)     // Less than: false
println!("a >= b: {}", a >= b)   // Greater or equal: true
println!("a <= b: {}", a <= b)   // Less or equal: false

// Logical operators
let p: Boolean   := true
let q: Boolean   := false

println!("\n=== Logical Operators ===")
println!("p = {}, q = {}", p, q)
println!("p && q: {}", p && q)   // Logical AND: false
println!("p || q: {}", p || q)   // Logical OR: true
println!("!p: {}", !p)           // Logical NOT: false
println!("!q: {}", !q)           // Logical NOT: true

// Bitwise operators (for integers)
let m: Integer   := 12  // Binary: 1100
let n: Integer   := 10  // Binary: 1010

println!("\n=== Bitwise Operators ===")
println!("m = {} (binary: {:b})", m, m)
println!("n = {} (binary: {:b})", n, n)
println!("m & n = {} (binary: {:b})", m & n, m & n)   // AND: 8 (1000)
println!("m | n = {} (binary: {:b})", m | n, m | n)   // OR: 14 (1110)
println!("m ^ n = {} (binary: {:b})", m ^ n, m ^ n)   // XOR: 6 (0110)
println!("~m = {} (binary: {:b})", ~m, ~m)           // NOT: -13
println!("m << 2 = {}", m << 2)  // Left shift: 48
println!("m >> 2 = {}", m >> 2)  // Right shift: 3

// Assignment operators
let mut value: Integer   := 5

println!("\n=== Assignment Operators ===")
println!("Initial value: {}", value)

value += 3
println!("After += 3: {}", value)  // 8

value -= 2
println!("After -= 2: {}", value)  // 6

value *= 4
println!("After *= 4: {}", value)  // 24

value /= 3
println!("After /= 3: {}", value)  // 8

value %= 5
println!("After %= 5: {}", value)  // 3

// String operators
let str1: String   := "Hello"
let str2: String   := "World"

println!("\n=== String Operators ===")
println!("str1 = '{}', str2 = '{}'", str1, str2)
println!("str1 + ' ' + str2 = '{}'", str1 + " " + str2)  // Concatenation

let mut greeting: String   := "Hi"
greeting += " there!"
println!("After greeting += ' there!': '{}'", greeting)

// Range operators
println!("\n=== Range Operators ===")
println!("1..5 = {:?}", 1..5)      // Exclusive range: [1, 2, 3, 4]
println!("1..=5 = {:?}", 1..=5)    // Inclusive range: [1, 2, 3, 4, 5]

// Null coalescing operator
let optional_value: Option[Integer]   := None
let default_value: Integer   := 42

println!("\n=== Null Coalescing ===")
println!("optional_value ?? default_value = {}", optional_value ?? default_value)

// Ternary operator (conditional expression)
let condition: Boolean   := true
let result: String   := condition ? "yes" : "no"

println!("\n=== Ternary Operator ===")
println!("condition ? 'yes' : 'no' = '{}'", result)

// Operator precedence test
let precedence_result: Integer   := 2 + 3 * 4  // Should be 14, not 20

println!("\n=== Operator Precedence ===")
println!("2 + 3 * 4 = {} (should be 14)", precedence_result)
println!("(2 + 3) * 4 = {}", (2 + 3) * 4)  // Should be 20
