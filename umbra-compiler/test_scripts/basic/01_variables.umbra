// Test script for basic variable declarations and assignments
// Expected: All variables should be declared and assigned correctly

// Basic variable declarations
let x: Integer := 42
let name: String := "Umbra"
let pi: Float := 3.14159
let is_active: Boolean := true

// Mutable variables
let mut counter: Integer : : := 0
let mut message: String : : := "Hello"

// Variable assignments
counter := counter + 1
message := message + " World"

// Type inference
let auto_int: Auto : := 100        // Should infer Integer
let auto_string: Auto : := "test"  // Should infer String
let auto_float: Auto : := 2.5      // Should infer Float
let auto_bool: Auto : := false     // Should infer Boolean

// Constants (immutable by default)
let MAX_SIZE: Integer : := 1000
let APP_NAME: String : := "Umbra Compiler"

// Multiple variable declarations
let a: Integer : := 1
let b: Integer : := 2
let c: Integer : := a + b

// Shadowing (redeclaring variables in same scope)
let value: Integer : := 10
let value: String : := "ten"  // Shadows the previous value

// Print all variables to verify
println!("x = {}", x)
println!("name = {}", name)
println!("pi = {}", pi)
println!("is_active = {}", is_active)
println!("counter = {}", counter)
println!("message = {}", message)
println!("auto_int = {}", auto_int)
println!("auto_string = {}", auto_string)
println!("auto_float = {}", auto_float)
println!("auto_bool = {}", auto_bool)
println!("MAX_SIZE = {}", MAX_SIZE)
println!("APP_NAME = {}", APP_NAME)
println!("c = a + b = {}", c)
println!("shadowed value = {}", value)

// Expected Output:
// x = 42
// name = Umbra
// pi = 3.14159
// is_active = true
// counter = 1
// message = Hello World
// auto_int = 100
// auto_string = test
// auto_float = 2.5
// auto_bool = false
// MAX_SIZE = 1000
// APP_NAME = Umbra Compiler
// c = a + b = 3
// shadowed value = ten
