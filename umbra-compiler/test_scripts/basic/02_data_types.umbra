// Test script for all basic data types in Umbra
// Expected: All data types should be properly handled

// Numeric types
let small_int: Integer = 42
let large_int: Integer = 1000000
let negative_int: Integer = -123
let zero: Integer = 0

let simple_float: Float = 3.14
let scientific: Float = 1.23e-4
let negative_float: Float = -99.99

// String types
let simple_string: String = "Hello, Umbra!"
let empty_string: String = ""
let multiline_string: String = "Line 1\nLine 2\nLine 3"
let escaped_string: String = "Quote: \"Hello\" and backslash: \\"

// Boolean type
let true_value: Boolean = true
let false_value: Boolean = false

// Collection types
let number_list: List<Integer> = [1, 2, 3, 4, 5]
let string_list: List<String> = ["apple", "banana", "cherry"]
let mixed_list: List<Any> = [1, "hello", 3.14, true]

let empty_list: List<Integer> = []

// Map/Dictionary type
let person_map: Map<String, Any> = {
    "name": "<PERSON>",
    "age": 30,
    "active": true,
    "score": 95.5
}

let empty_map: Map<String, Integer> = {}

// Set type
let unique_numbers: Set<Integer> = {1, 2, 3, 4, 5, 1, 2}  // Duplicates removed
let unique_strings: Set<String> = {"red", "green", "blue"}

// Option type (nullable values)
let some_value: Option<Integer> = Some(42)
let no_value: Option<Integer> = None

// Result type (for error handling)
let success_result: Result<String, String> = Ok("Success!")
let error_result: Result<String, String> = Err("Something went wrong")

// Tuple type
let coordinates: (Float, Float) = (10.5, 20.3)
let person_info: (String, Integer, Boolean) = ("Bob", 25, true)

// Print all values
println!("=== Numeric Types ===")
println!("small_int: {}", small_int)
println!("large_int: {}", large_int)
println!("negative_int: {}", negative_int)
println!("zero: {}", zero)
println!("simple_float: {}", simple_float)
println!("scientific: {}", scientific)
println!("negative_float: {}", negative_float)

println!("\n=== String Types ===")
println!("simple_string: {}", simple_string)
println!("empty_string: '{}'", empty_string)
println!("multiline_string: {}", multiline_string)
println!("escaped_string: {}", escaped_string)

println!("\n=== Boolean Type ===")
println!("true_value: {}", true_value)
println!("false_value: {}", false_value)

println!("\n=== Collection Types ===")
println!("number_list: {:?}", number_list)
println!("string_list: {:?}", string_list)
println!("mixed_list: {:?}", mixed_list)
println!("empty_list: {:?}", empty_list)

println!("\n=== Map Type ===")
println!("person_map: {:?}", person_map)
println!("empty_map: {:?}", empty_map)

println!("\n=== Set Type ===")
println!("unique_numbers: {:?}", unique_numbers)
println!("unique_strings: {:?}", unique_strings)

println!("\n=== Option Type ===")
println!("some_value: {:?}", some_value)
println!("no_value: {:?}", no_value)

println!("\n=== Result Type ===")
println!("success_result: {:?}", success_result)
println!("error_result: {:?}", error_result)

println!("\n=== Tuple Type ===")
println!("coordinates: {:?}", coordinates)
println!("person_info: {:?}", person_info)

// Type checking and conversion
println!("\n=== Type Information ===")
println!("Type of small_int: {}", typeof(small_int))
println!("Type of simple_string: {}", typeof(simple_string))
println!("Type of number_list: {}", typeof(number_list))
println!("Type of person_map: {}", typeof(person_map))
