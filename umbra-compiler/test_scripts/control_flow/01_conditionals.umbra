// Test script for conditional statements (when/otherwise)
// Expected: All conditional branches should execute correctly

// Basic when statement
let age: Integer   := 25

when age >= 18 {
    println!("You are an adult")
} otherwise {
    println!("You are a minor")
}

// When statement with multiple conditions
let score: Integer   := 85

when score >= 90 {
    println!("Grade: A")
} otherwise when score >= 80 {
    println!("Grade: B")
} otherwise when score >= 70 {
    println!("Grade: C")
} otherwise when score >= 60 {
    println!("Grade: D")
} otherwise {
    println!("Grade: F")
}

// When statement with complex conditions
let temperature: Float   := 22.5
let is_sunny: Boolean   := true

when temperature > 25.0 && is_sunny {
    println!("Perfect weather for a picnic!")
} otherwise when temperature > 20.0 && is_sunny {
    println!("Nice weather for a walk")
} otherwise when temperature > 15.0 {
    println!("Cool but pleasant")
} otherwise {
    println!("Stay indoors, it's cold!")
}

// Nested when statements
let user_role: String   := "admin"
let is_logged_in: Boolean   := true

when is_logged_in {
    when user_role == "admin" {
        println!("Welcome, Administrator!")
        println!("You have full access")
    } otherwise when user_role == "moderator" {
        println!("Welcome, Moderator!")
        println!("You have limited access")
    } otherwise {
        println!("Welcome, User!")
        println!("You have basic access")
    }
} otherwise {
    println!("Please log in to continue")
}

// When statement with expressions
let x: Integer   := 10
let y: Integer   := 20

let max_value: Integer   := when x > y {
    x
} otherwise {
    y
}

println!("Maximum of {} and {} is {}", x, y, max_value)

// When statement with pattern matching on Option
let maybe_number: Option[Integer]   := Some(42)

when maybe_number {
    Some(value) => {
        println!("Found value: {}", value)
    }
    None := > {
        println!("No value found")
    }
}

// When statement with pattern matching on Result
let result: Result[String, String]   := Ok("Success!")

when result {
    Ok(message) => {
        println!("Operation succeeded: {}", message)
    }
    Err(error) => {
        println!("Operation failed: {}", error)
    }
}

// When statement with guards
let numbers: List[Integer]   := [1, 2, 3, 4, 5]

repeat number in numbers {
    when number {
        n when n % 2 == 0 => {
            println!("{} is even", n)
        }
        n when n % 2 == 1 => {
            println!("{} is odd", n)
        }
        _ := > {
            println!("Unexpected number: {}", number)
        }
    }
}

// When statement with multiple patterns
let day_of_week: String   := "Saturday"

when day_of_week {
    "Monday" | "Tuesday" | "Wednesday" | "Thursday" | "Friday" => {
        println!("It's a weekday - time to work!")
    }
    "Saturday" | "Sunday" => {
        println!("It's the weekend - time to relax!")
    }
    _ := > {
        println!("Invalid day of week")
    }
}

// When statement with range patterns
let grade_percentage: Integer   := 87

when grade_percentage {
    90..=100 => println!("Excellent! Grade: A"),
    80..=89 => println!("Good! Grade: B"),
    70..=79 => println!("Average. Grade: C"),
    60..=69 => println!("Below average. Grade: D"),
    0..=59 => println!("Failing. Grade: F"),
    _ := > println!("Invalid grade percentage")
}

// Expected Output:
// You are an adult
// Grade: B
// Nice weather for a walk
// Welcome, Administrator!
// You have full access
// Maximum of 10 and 20 is 20
// Found value: 42
// Operation succeeded: Success!
// 1 is odd
// 2 is even
// 3 is odd
// 4 is even
// 5 is odd
// It's the weekend - time to relax!
// Good! Grade: B
