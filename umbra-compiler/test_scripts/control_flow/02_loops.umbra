// Test script for loop constructs
// Expected: All loops should execute correctly

println!("=== Repeat Loop (for-each style) ===")

// Basic repeat loop with list
let numbers: List[Integer]   := [1, 2, 3, 4, 5]

repeat item in numbers {
    println!("Number: {}", item)
}

// Repeat loop with range
println!("\n=== Repeat with Range ===")
repeat i in 1..=5 {
    println!("Count: {}", i)
}

// Repeat loop with string characters
println!("\n=== Repeat with String ===")
let word: String   := "Hello"
repeat char in word {
    println!("Character: {}", char)
}

// Repeat loop with map/dictionary
println!("\n=== Repeat with Map ===")
let person: Map[String, Any]   := {
    "name": "Alice",
    "age": 30,
    "city": "New York"
}

repeat (key, value) in person {
    println!("{}: {}", key, value)
}

// While loop
println!("\n=== While Loop ===")
let mut counter: Integer   := 0

while counter < 5 {
    println!("Counter: {}", counter)
    counter += 1
}

// While loop with complex condition
println!("\n=== While with Complex Condition ===")
let mut x: Integer   := 1
let mut sum: Integer   := 0

while x <= 10 && sum < 30 {
    sum += x
    println!("x: {}, sum: {}", x, sum)
    x += 1
}

// Do-while loop (executes at least once)
println!("\n=== Do-While Loop ===")
let mut value: Integer   := 10

do {
    println!("Value: {}", value)
    value -= 2
} while value > 0

// For loop (C-style)
println!("\n=== For Loop ===")
for i in 0..5 {
    println!("For loop iteration: {}", i)
}

// For loop with step
println!("\n=== For Loop with Step ===")
for i in (0..10).step_by(2) {
    println!("Even number: {}", i)
}

// Nested loops
println!("\n=== Nested Loops ===")
for i in 1..=3 {
    repeat j in 1..=3 {
        println!("({}, {})", i, j)
    }
}

// Loop with break statement
println!("\n=== Loop with Break ===")
let mut search_value: Integer   := 7
let search_list: List[Integer]   := [1, 3, 5, 7, 9, 11, 13]

repeat item in search_list {
    when item == search_value {
        println!("Found {} at position", search_value)
        break
    }
    println!("Checking: {}", item)
}

// Loop with continue statement
println!("\n=== Loop with Continue ===")
repeat num in 1..=10 {
    when num % 2 == 0 {
        continue  // Skip even numbers
    }
    println!("Odd number: {}", num)
}

// Loop with labeled break
println!("\n=== Labeled Break ===")
'outer: for i in 1..=3 {
    repeat j in 1..=3 {
        when i == 2 && j == 2 {
            println!("Breaking out of both loops at ({}, {})", i, j)
            break 'outer
        }
        println!("Inner loop: ({}, {})", i, j)
    }
}

// Loop with labeled continue
println!("\n=== Labeled Continue ===")
'outer: for i in 1..=3 {
    repeat j in 1..=3 {
        when j == 2 {
            println!("Skipping to next outer iteration at ({}, {})", i, j)
            continue 'outer
        }
        println!("Processing: ({}, {})", i, j)
    }
}

// Infinite loop with break condition
println!("\n=== Infinite Loop with Break ===")
let mut attempts: Integer   := 0

loop {
    attempts += 1
    println!("Attempt: {}", attempts)
    
    when attempts >= 3 {
        println!("Max attempts reached, breaking")
        break
    }
}

// Loop with pattern matching
println!("\n=== Loop with Pattern Matching ===")
let mixed_data: List[Any]   := [1, "hello", 3.14, true, 42]

repeat item in mixed_data {
    match item    {
        Integer(n) => println!("Integer: {}", n),
        String(s) => println!("String: {}", s),
        Float(f) => println!("Float: {}", f),
        Boolean(b) => println!("Boolean: {}", b),
        _ := > println!("Unknown type")
    }
}

// Expected Output:
// Number: 1, Number: 2, Number: 3, Number: 4, Number: 5
// Count: 1, Count: 2, Count: 3, Count: 4, Count: 5
// Character: H, Character: e, Character: l, Character: l, Character: o
// name: Alice, age: 30, city: New York
// Counter: 0, Counter: 1, Counter: 2, Counter: 3, Counter: 4
// Various while loop outputs
// Do-while outputs
// For loop iterations
// Nested loop combinations
// Break and continue demonstrations
// Labeled break/continue examples
// Infinite loop with controlled break
// Pattern matching in loops
