// Test script for error handling mechanisms
// Expected: All error handling constructs should work correctly

// Custom error types
error NetworkError {
    message: String,
    status_code: Integer
}

error ValidationError {
    field: String,
    message: String
}

error DatabaseError {
    query: String,
    error_message: String
}

// Function that can throw errors
fn divide(a: Float, b: Float) -> Result<Float, String> {
    when b == 0.0 {
        return Err("Division by zero")
    }
    return Ok(a / b)
}

// Function with custom error
fn validate_email(email: String) -> Result<String, ValidationError> {
    when !email.contains("@") {
        return Err(ValidationError {
            field: "email",
            message: "Email must contain @ symbol"
        })
    }
    when email.len() < 5 {
        return Err(ValidationError {
            field: "email", 
            message: "Email too short"
        })
    }
    return Ok(email)
}

// Function that can throw multiple error types
fn fetch_user_data(user_id: Integer) -> Result<String, NetworkError | DatabaseError> {
    when user_id < 0 {
        return Err(NetworkError {
            message: "Invalid user ID",
            status_code: 400
        })
    }
    when user_id > 1000 {
        return Err(DatabaseError {
            query: "SELECT * FROM users WHERE id = " + user_id.to_string(),
            error_message: "User not found in database"
        })
    }
    return Ok("User data for ID " + user_id.to_string())
}

// Function that propagates errors with ?
fn process_user_email(user_id: Integer, email: String) -> Result<String, ValidationError | NetworkError | DatabaseError> {
    let user_data: String = fetch_user_data(user_id)?  // Propagate error if any
    let valid_email: String = validate_email(email)?   // Propagate error if any
    return Ok("Processed: " + user_data + " with email " + valid_email)
}

println!("=== Basic Error Handling ===")

// Try-catch blocks
try {
    let result: Float = divide(10.0, 2.0).unwrap()
    println!("Division result: {}", result)
} catch error: String {
    println!("Caught error: {}", error)
}

try {
    let result: Float = divide(10.0, 0.0).unwrap()  // This will throw
    println!("This won't be printed")
} catch error: String {
    println!("Caught division error: {}", error)
}

println!("\n=== Result Type Handling ===")

// Pattern matching with Result
let division_results: List<Result<Float, String>> = [
    divide(20.0, 4.0),
    divide(15.0, 3.0),
    divide(10.0, 0.0),
    divide(8.0, 2.0)
]

repeat result in division_results {
    match result {
        Ok(value) => println!("Success: {}", value),
        Err(error) => println!("Error: {}", error)
    }
}

// Using unwrap_or for default values
let safe_division: Float = divide(10.0, 0.0).unwrap_or(0.0)
println!("Safe division with default: {}", safe_division)

// Using unwrap_or_else for computed defaults
let computed_default: Float = divide(10.0, 0.0).unwrap_or_else(|error| {
    println!("Computing default due to error: {}", error)
    return -1.0
})
println!("Computed default: {}", computed_default)

println!("\n=== Custom Error Types ===")

// Test email validation
let email_tests: List<String> = [
    "<EMAIL>",
    "invalid-email",
    "a@b",
    "<EMAIL>"
]

repeat email in email_tests {
    match validate_email(email) {
        Ok(valid_email) => println!("Valid email: {}", valid_email),
        Err(ValidationError { field, message }) => {
            println!("Validation error in {}: {}", field, message)
        }
    }
}

println!("\n=== Multiple Error Types ===")

let user_tests: List<Integer> = [-5, 42, 1500, 100]

repeat user_id in user_tests {
    match fetch_user_data(user_id) {
        Ok(data) => println!("Fetched: {}", data),
        Err(NetworkError { message, status_code }) => {
            println!("Network error ({}): {}", status_code, message)
        },
        Err(DatabaseError { query, error_message }) => {
            println!("Database error: {} (Query: {})", error_message, query)
        }
    }
}

println!("\n=== Error Propagation ===")

// Test error propagation with ?
let test_cases: List<(Integer, String)> = [
    (50, "<EMAIL>"),
    (-1, "<EMAIL>"),      // Network error
    (100, "invalid-email"),       // Validation error
    (2000, "<EMAIL>")     // Database error
]

repeat (user_id, email) in test_cases {
    match process_user_email(user_id, email) {
        Ok(result) => println!("Success: {}", result),
        Err(ValidationError { field, message }) => {
            println!("Validation failed for {}: {}", field, message)
        },
        Err(NetworkError { message, status_code }) => {
            println!("Network error ({}): {}", status_code, message)
        },
        Err(DatabaseError { query, error_message }) => {
            println!("Database error: {}", error_message)
        }
    }
}

println!("\n=== Option Type Error Handling ===")

fn find_in_list(list: List<Integer>, target: Integer) -> Option<Integer> {
    repeat (index, value) in list.enumerate() {
        when value == target {
            return Some(index)
        }
    }
    return None
}

let numbers: List<Integer> = [10, 20, 30, 40, 50]
let search_targets: List<Integer> = [30, 60, 10]

repeat target in search_targets {
    match find_in_list(numbers, target) {
        Some(index) => println!("Found {} at index {}", target, index),
        None => println!("{} not found in list", target)
    }
}

// Using Option with unwrap_or
let found_index: Integer = find_in_list(numbers, 25).unwrap_or(-1)
println!("Index of 25 (or -1 if not found): {}", found_index)

println!("\n=== Nested Error Handling ===")

fn complex_operation() -> Result<String, String> {
    try {
        let step1: Float = divide(100.0, 5.0)?
        println!("Step 1 completed: {}", step1)
        
        let step2: Float = divide(step1, 2.0)?
        println!("Step 2 completed: {}", step2)
        
        let step3: Float = divide(step2, 0.0)?  // This will fail
        println!("Step 3 completed: {}", step3)
        
        return Ok("All steps completed")
    } catch error: String {
        return Err("Complex operation failed: " + error)
    }
}

match complex_operation() {
    Ok(message) => println!("Complex operation: {}", message),
    Err(error) => println!("Complex operation error: {}", error)
}

println!("\n=== Finally Blocks ===")

fn operation_with_cleanup() -> Result<String, String> {
    println!("Starting operation...")
    
    try {
        // Simulate some work that might fail
        let risky_result: Float = divide(10.0, 0.0)?
        return Ok("Operation succeeded: " + risky_result.to_string())
    } catch error: String {
        println!("Operation failed: {}", error)
        return Err(error)
    } finally {
        println!("Cleanup: Operation finished (success or failure)")
    }
}

let _ = operation_with_cleanup()

println!("\n=== Error Chaining ===")

fn chain_errors() -> Result<String, String> {
    let result1: Result<Float, String> = divide(10.0, 2.0)
    let result2: Result<Float, String> = divide(20.0, 0.0)
    
    // Chain operations
    let final_result: Result<Float, String> = result1.and_then(|val1| {
        result2.map(|val2| val1 + val2)
    })
    
    match final_result {
        Ok(sum) => Ok("Sum: " + sum.to_string()),
        Err(error) => Err("Chained operation failed: " + error)
    }
}

match chain_errors() {
    Ok(result) => println!("Chained result: {}", result),
    Err(error) => println!("Chained error: {}", error)
}

println!("\nAll error handling tests completed!")

// Expected Output:
// Division result: 5
// Caught division error: Division by zero
// Success: 5, Success: 5, Error: Division by zero, Success: 4
// Safe division with default: 0
// Computing default due to error: Division by zero
// Computed default: -1
// Valid email: <EMAIL>
// Validation error in email: Email must contain @ symbol
// Validation error in email: Email too short
// Valid email: <EMAIL>
// Network error (400): Invalid user ID
// Fetched: User data for ID 42
// Database error: User not found in database (Query: SELECT * FROM users WHERE id = 1500)
// Fetched: User data for ID 100
// Success: Processed: User data for ID 50 <NAME_EMAIL>
// Network error (400): Invalid user ID
// Validation failed for email: Email must contain @ symbol
// Database error: User not found in database
// Found 30 at index 2
// 60 not found in list
// Found 10 at index 0
// Index of 25 (or -1 if not found): -1
// Step 1 completed: 20
// Step 2 completed: 10
// Complex operation error: Complex operation failed: Division by zero
// Starting operation...
// Operation failed: Division by zero
// Cleanup: Operation finished (success or failure)
// Chained error: Chained operation failed: Division by zero
// All error handling tests completed!
