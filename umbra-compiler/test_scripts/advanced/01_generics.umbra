// Test script for generic types and functions
// Expected: All generic constructs should work correctly

// Generic function with single type parameter
fn identity<T>(value: T) -> T {
    return value
}

// Generic function with multiple type parameters
fn pair<T, U>(first: T, second: U) -> (T, U) {
    return (first, second)
}

// Generic function with constraints
fn max<T: Comparable>(a: T, b: T) -> T {
    when a.compare(b) >= 0 {
        return a
    } otherwise {
        return b
    }
}

// Generic structure
struct Box<T> {
    value: T
}

impl<T> Box<T> {
    fn new(value: T) -> Box<T> {
        return Box { value: value }
    }
    
    fn get(self) -> T {
        return self.value
    }
    
    fn set(mut self, value: T) {
        self.value = value
    }
}

// Generic structure with multiple parameters
struct Pair<T, U> {
    first: T,
    second: U
}

impl<T, U> Pair<T, U> {
    fn new(first: T, second: U) -> Pair<T, U> {
        return Pair { first: first, second: second }
    }
    
    fn swap(self) -> Pair<U, T> {
        return Pair { first: self.second, second: self.first }
    }
}

// Generic structure with constraints
struct SortedList[T: Comparable + Clone] {
    items: List[T]
}

impl<T: Comparable + Clone> SortedList[T] {
    fn new() -> SortedList[T] {
        return SortedList { items: [] }
    }
    
    fn add(mut self, item: T) {
        // Insert in sorted order
        let mut inserted: Boolean   := false
        let mut new_items: List[T]   := []
        
        repeat existing_item in self.items {
            when !inserted && item.compare(existing_item) <= 0 {
                new_items.push(item.clone())
                inserted := true
            }
            new_items.push(existing_item)
        }
        
        when !inserted {
            new_items.push(item)
        }
        
        self.items = new_items
    }
    
    fn get_items(self) -> List[T] {
        return self.items
    }
}

// Generic trait
trait Container<T> {
    fn put(mut self, item: T)
    fn get(mut self) -> Option[T]
    fn is_empty(self) -> Boolean
}

// Stack implementation using generic trait
struct Stack<T> {
    items: List[T]
}

impl<T> Stack<T> {
    fn new() -> Stack<T> {
        return Stack { items: [] }
    }
}

impl<T> Container<T> for Stack<T> {
    fn put(mut self, item: T) {
        self.items.push(item)
    }
    
    fn get(mut self) -> Option[T] {
        when self.items.len() > 0 {
            return Some(self.items.pop())
        } otherwise {
            return None
        }
    }
    
    fn is_empty(self) -> Boolean {
        return self.items.len() == 0
    }
}

// Generic enum
enum Result[T, E] {
    Ok(T),
    Err(E)
}

// Generic function that works with Result
fn map_result<T, U, E>(result: Result[T, E], mapper: fn(T) -> U) -> Result[U, E] {
    match result    {
        Ok(value) => Ok(mapper(value)),
        Err(error) => Err(error)
    }
}

// Higher-kinded types simulation
trait Functor<T> {
    type Output<U>
    
    fn map<U>(self, f: fn(T) -> U) -> Self::Output<U>
}

impl<T> Functor<T> for Option[T] {
    type Output<U> = Option[U]
    
    fn map<U>(self, f: fn(T) -> U) -> Option[U] {
        match self    {
            Some(value) => Some(f(value)),
            None := > None
        }
    }
}

// Generic function with where clabring fn complex_function<T, U, V>(a: T, b: U) -> V 
where 
    T: Clone + Display,
    U: Into<String>,
    V: From<String>
{
    let combined: String   := a.display() + " " + b.into()
    return V::from(combined)
}

// Main execution
println!("=== Basic Generic Functions ===")

let int_result: Integer   := identity(42)
let string_result: String   := identity("Hello")
let bool_result: Boolean   := identity(true)

println!("identity(42): {}", int_result)
println!("identity('Hello'): {}", string_result)
println!("identity(true): {}", bool_result)

let number_pair: (Integer, Float)   := pair(10, 3.14)
let mixed_pair: (String, Boolean)   := pair("test", false)

println!("pair(10, 3.14): {:?}", number_pair)
println!("pair('test', false): {:?}", mixed_pair)

println!("\n=== Generic Structures ===")

let int_box: Box<Integer>   := Box::new(100)
let string_box: Box<String>   := Box::new("boxed string")

println!("int_box.get(): {}", int_box.get())
println!("string_box.get(): {}", string_box.get())

let coordinate_pair: Pair<Float, Float>   := Pair::new(10.5, 20.3)
let swapped: Pair<Float, Float>   := coordinate_pair.swap()

println!("coordinate_pair: ({}, {})", coordinate_pair.first, coordinate_pair.second)
println!("swapped: ({}, {})", swapped.first, swapped.second)

println!("\n=== Constrained Generics ===")

let mut sorted_numbers: SortedList[Integer]   := SortedList::new()
sorted_numbers.add(5)
sorted_numbers.add(2)
sorted_numbers.add(8)
sorted_numbers.add(1)
sorted_numbers.add(9)

println!("sorted_numbers: {:?}", sorted_numbers.get_items())

println!("\n=== Generic Traits ===")

let mut stack: Stack<String>   := Stack::new()
stack.put("first")
stack.put("second")
stack.put("third")

println!("stack.is_empty(): {}", stack.is_empty())

while !stack.is_empty() {
    when let Some(item) = stack.get() {
        println!("popped: {}", item)
    }
}

println!("stack.is_empty(): {}", stack.is_empty())

println!("\n  := == Generic Enums ===")

let success: Result[Integer, String]   := Result::Ok(42)
let failure: Result[Integer, String]   := Result::Err("Something went wrong")

let doubled_success: Result[Integer, String]   := map_result(success, fn(x: Integer) -> Integer { x * 2 })
let doubled_failure: Result[Integer, String]   := map_result(failure, fn(x: Integer) -> Integer { x * 2 })

println!("doubled_success: {:?}", doubled_success)
println!("doubled_failure: {:?}", doubled_failure)

println!("\n=== Higher-Kinded Types ===")

let some_value: Option[Integer]   := Some(10)
let no_value: Option[Integer]   := None

let mapped_some: Option[String]   := some_value.map(fn(x: Integer) -> String { x.to_string() + "!" })
let mapped_none: Option[String]   := no_value.map(fn(x: Integer) -> String { x.to_string() + "!" })

println!("mapped_some: {:?}", mapped_some)
println!("mapped_none: {:?}", mapped_none)

println!("\n=== Type Inference ===")

// Type inference with generics
let inferred_box = Box: :new(42)  // Box<Integer> inferred
let inferred_pair: Auto  := pair("hello", 123)

println!("inferred_box type: Box<Integer>, value: {}", inferred_box.get())
println!("inferred_pair type: (String, Integer), value: {:?}", inferred_pair)

// Expected Output:
// identity(42): 42
// identity('Hello'): Hello
// identity(true): true
// pair(10, 3.14): (10, 3.14)
// pair('test', false): (test, false)
// int_box.get(): 100
// string_box.get(): boxed string
// coordinate_pair: (10.5, 20.3)
// swapped: (20.3, 10.5)
// sorted_numbers: [1, 2, 5, 8, 9]
// stack.is_empty(): false
// popped: third
// popped: second
// popped: first
// stack.is_empty(): true
// doubled_success: Ok(84)
// doubled_failure: Err(Something went wrong)
// mapped_some: Some(10!)
// mapped_none: None
// inferred_box type: Box<Integer>, value: 42
// inferred_pair type: (String, Integer), value: (hello, 123)
