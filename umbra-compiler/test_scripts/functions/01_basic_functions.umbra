// Test script for basic function definitions and calls
// Expected: All functions should be defined and called correctly

// Simple function with no parameters
fn greet() -> String {
    return "Hello, Umbra!"
}

// Function with parameters
fn add(a: Integer, b: Integer) -> Integer {
    return a + b
}

// Function with multiple parameters of different types
fn introduce(name: String, age: Integer, is_student: <PERSON><PERSON><PERSON>) -> String {
    let status: String   := when is_student {
        true := > "student"
        false := > "professional"
    }
    return "Hi, I'm " + name + ", " + age.to_string() + " years old, and I'm a " + status
}

// Function with default parameters
fn power(base: Integer, exponent: Integer = 2) -> Integer {
    let mut result: Integer   := 1
    repeat _ in 1..=exponent {
        result *= base
    }
    return result
}

// Function that returns early
fn find_first_even(numbers: List[Integer]) -> Option[Integer] {
    repeat num in numbers {
        when num % 2 == 0 {
            return Some(num)
        }
    }
    return None
}

// Function with multiple return points
fn classify_number(n: Integer) -> String {
    when n > 0 {
        return "positive"
    }
    when n < 0 {
        return "negative"
    }
    return "zero"
}

// Function that modifies parameters (by reference)
fn increment_by_ref(mut value: Integer) -> Integer {
    value += 1
    return value
}

// Function with expression body (no explicit return)
fn multiply(x: Integer, y: Integer) -> Integer = x * y

// Function with complex logic
fn fibonacci(n: Integer) -> Integer {
    when n <= 1 {
        return n
    }
    return fibonacci(n - 1) + fibonacci(n - 2)
}

// Function that takes a function as parameter (higher-order function)
fn apply_operation(a: Integer, b: Integer, operation: fn(Integer, Integer) -> Integer) -> Integer {
    return operation(a, b)
}

// Function that returns a function (closure)
fn make_multiplier(factor: Integer) -> fn(Integer) -> Integer {
    return fn(x: Integer) -> Integer {
        return x * factor
    }
}

// Variadic function (takes variable number of arguments)
fn sum_all(numbers: ...Integer) -> Integer {
    let mut total: Integer   := 0
    repeat num in numbers {
        total += num
    }
    return total
}

// Generic function
fn swap<T>(a: T, b: T) -> (T, T) {
    return (b, a)
}

// Function with constraints
fn compare<T: Comparable>(a: T, b: T) -> String {
    when a > b {
        return "first is greater"
    } otherwise when a < b {
        return "second is greater"
    } otherwise {
        return "they are equal"
    }
}

// Main execution
println!("=== Basic Function Calls ===")
println!("greet(): {}", greet())
println!("add(5, 3): {}", add(5, 3))
println!("introduce('Alice', 25, true): {}", introduce("Alice", 25, true))

println!("\n=== Default Parameters ===")
println!("power(3): {}", power(3))        // Uses default exponent = 2
println!("power(3, 4): {}", power(3, 4))  // Uses provided exponent = 4

println!("\n=== Early Return ===")
let numbers: List[Integer]   := [1, 3, 5, 8, 9, 12]
println!("find_first_even([1,3,5,8,9,12]): {:?}", find_first_even(numbers))

println!("\n=== Multiple Return Points ===")
println!("classify_number(5): {}", classify_number(5))
println!("classify_number(-3): {}", classify_number(-3))
println!("classify_number(0): {}", classify_number(0))

println!("\n=== Reference Parameters ===")
let original_value: Integer   := 10
println!("increment_by_ref({}): {}", increment_by_ref(original_value))

println!("\n=== Expression Body ===")
println!("multiply(4, 7): {}", multiply(4, 7))

println!("\n=== Recursive Function ===")
println!("fibonacci(8): {}", fibonacci(8))

println!("\n=== Higher-Order Functions ===")
println!("apply_operation(10, 5, add): {}", apply_operation(10, 5, add))
println!("apply_operation(10, 5, multiply): {}", apply_operation(10, 5, multiply))

println!("\n=== Closures ===")
let double: fn(Integer) -> Integer   := make_multiplier(2)
let triple: fn(Integer) -> Integer   := make_multiplier(3)
println!("double(7): {}", double(7))
println!("triple(7): {}", triple(7))

println!("\n=== Variadic Functions ===")
println!("sum_all(1, 2, 3, 4, 5): {}", sum_all(1, 2, 3, 4, 5))
println!("sum_all(10, 20): {}", sum_all(10, 20))

println!("\n=== Generic Functions ===")
let (x, y) = swap(1, 2)
println!("swap(1, 2): ({}, {})", x, y)

let (a, b)   := swap("hello", "world")
println!("swap('hello', 'world'): ('{}', '{}')", a, b)

println!("\n=== Constrained Generics ===")
println!("compare(10, 5): {}", compare(10, 5))
println!("compare('apple', 'banana'): {}", compare("apple", "banana"))

// Expected Output:
// greet(): Hello, Umbra!
// add(5, 3): 8
// introduce('Alice', 25, true): Hi, I'm Alice, 25 years old, and I'm a student
// power(3): 9
// power(3, 4): 81
// find_first_even([1,3,5,8,9,12]): Some(8)
// classify_number(5): positive
// classify_number(-3): negative
// classify_number(0): zero
// increment_by_ref(10): 11
// multiply(4, 7): 28
// fibonacci(8): 21
// apply_operation(10, 5, add): 15
// apply_operation(10, 5, multiply): 50
// double(7): 14
// triple(7): 21
// sum_all(1, 2, 3, 4, 5): 15
// sum_all(10, 20): 30
// swap(1, 2): (2, 1)
// swap('hello', 'world'): ('world', 'hello')
// compare(10, 5): first is greater
// compare('apple', 'banana'): second is greater
