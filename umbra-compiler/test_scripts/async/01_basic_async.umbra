// Test script for basic async/await functionality
// Expected: All async operations should execute correctly

// Basic async function
async fn simple_async_task() -> String {
    println!("Starting async task...")
    await sleep(1000)  // Sleep for 1 second
    println!("Async task completed!")
    return "Task result"
}

// Async function with parameters
async fn fetch_data(url: String, timeout_ms: Integer) -> Result<String, String> {
    println!("Fetching data from: {}", url)
    
    // Simulate network delay
    await sleep(timeout_ms)
    
    // Simulate success/failure based on URL
    when url.contains("error") {
        return Err("Network error occurred")
    } otherwise {
        return Ok("Data fetched successfully from " + url)
    }
}

// Async function that calls other async functions
async fn process_multiple_requests() -> List<String> {
    println!("Processing multiple requests...")
    
    let mut results: List<String> = []
    
    // Sequential async calls
    let result1: Result<String, String> = await fetch_data("https://api1.example.com", 500)
    let result2: Result<String, String> = await fetch_data("https://api2.example.com", 300)
    let result3: Result<String, String> = await fetch_data("https://api3.example.com", 700)
    
    // Process results
    match result1 {
        Ok(data) => results.push(data),
        Err(error) => results.push("API1 failed: " + error)
    }
    
    match result2 {
        Ok(data) => results.push(data),
        Err(error) => results.push("API2 failed: " + error)
    }
    
    match result3 {
        Ok(data) => results.push(data),
        Err(error) => results.push("API3 failed: " + error)
    }
    
    return results
}

// Async function with concurrent execution
async fn concurrent_requests() -> List<String> {
    println!("Making concurrent requests...")
    
    // Start all requests concurrently
    let task1 = spawn(fetch_data("https://fast-api.com", 200))
    let task2 = spawn(fetch_data("https://slow-api.com", 800))
    let task3 = spawn(fetch_data("https://error-api.com/error", 400))
    
    // Wait for all to complete
    let result1: Result<String, String> = await task1
    let result2: Result<String, String> = await task2
    let result3: Result<String, String> = await task3
    
    let mut results: List<String> = []
    
    // Collect results
    results.push(result1.unwrap_or("Task1 failed"))
    results.push(result2.unwrap_or("Task2 failed"))
    results.push(result3.unwrap_or("Task3 failed"))
    
    return results
}

// Async function with timeout
async fn timed_operation() -> Result<String, String> {
    println!("Starting timed operation...")
    
    let operation = spawn(async {
        await sleep(2000)  // 2 second operation
        return "Operation completed"
    })
    
    // Race between operation and timeout
    let result = await race([
        operation,
        spawn(async {
            await sleep(1500)  // 1.5 second timeout
            return "Operation timed out"
        })
    ])
    
    return Ok(result)
}

// Async generator/stream simulation
async fn number_stream(count: Integer) -> AsyncIterator<Integer> {
    return async_generator(async {
        repeat i in 1..=count {
            println!("Generating number: {}", i)
            await sleep(100)  // Small delay between numbers
            yield i
        }
    })
}

// Async function that processes a stream
async fn process_stream() {
    println!("Processing async stream...")
    
    let stream = await number_stream(5)
    
    await for number in stream {
        println!("Received number: {}", number)
        
        // Process each number
        let doubled: Integer = number * 2
        println!("Doubled: {}", doubled)
    }
    
    println!("Stream processing completed")
}

// Main async function
async fn main() {
    println!("=== Basic Async Function ===")
    let simple_result: String = await simple_async_task()
    println!("Result: {}", simple_result)
    
    println!("\n=== Sequential Async Calls ===")
    let sequential_results: List<String> = await process_multiple_requests()
    repeat result in sequential_results {
        println!("Sequential result: {}", result)
    }
    
    println!("\n=== Concurrent Async Calls ===")
    let concurrent_results: List<String> = await concurrent_requests()
    repeat result in concurrent_results {
        println!("Concurrent result: {}", result)
    }
    
    println!("\n=== Async with Timeout ===")
    let timed_result: Result<String, String> = await timed_operation()
    match timed_result {
        Ok(message) => println!("Timed operation: {}", message),
        Err(error) => println!("Timed operation error: {}", error)
    }
    
    println!("\n=== Async Streams ===")
    await process_stream()
    
    println!("\n=== Async Error Handling ===")
    
    // Try-catch with async
    try {
        let error_result: Result<String, String> = await fetch_data("https://error-api.com/error", 100)
        match error_result {
            Ok(data) => println!("Unexpected success: {}", data),
            Err(error) => {
                println!("Expected error caught: {}", error)
                throw error  // Re-throw for demonstration
            }
        }
    } catch error: String {
        println!("Caught async error: {}", error)
    }
    
    println!("\n=== Async Combinators ===")
    
    // Join multiple async operations
    let (result_a, result_b, result_c) = await join([
        fetch_data("https://api-a.com", 300),
        fetch_data("https://api-b.com", 200),
        fetch_data("https://api-c.com", 400)
    ])
    
    println!("Joined results:")
    println!("A: {:?}", result_a)
    println!("B: {:?}", result_b)
    println!("C: {:?}", result_c)
    
    // Select first completed operation
    let first_result = await select([
        fetch_data("https://fast.com", 100),
        fetch_data("https://medium.com", 300),
        fetch_data("https://slow.com", 500)
    ])
    
    println!("First completed: {:?}", first_result)
    
    println!("\n=== Async Loops ===")
    
    // Async for loop
    let urls: List<String> = [
        "https://service1.com",
        "https://service2.com",
        "https://service3.com"
    ]
    
    repeat url in urls {
        let result: Result<String, String> = await fetch_data(url, 200)
        match result {
            Ok(data) => println!("Success for {}: {}", url, data),
            Err(error) => println!("Error for {}: {}", url, error)
        }
    }
    
    println!("All async operations completed!")
}

// Run the main async function
await main()

// Expected Output:
// Starting async task...
// (1 second delay)
// Async task completed!
// Result: Task result
// Processing multiple requests...
// Fetching data from: https://api1.example.com
// (0.5 second delay)
// Fetching data from: https://api2.example.com
// (0.3 second delay)
// Fetching data from: https://api3.example.com
// (0.7 second delay)
// Sequential result: Data fetched successfully from https://api1.example.com
// Sequential result: Data fetched successfully from https://api2.example.com
// Sequential result: Data fetched successfully from https://api3.example.com
// Making concurrent requests...
// (All requests start simultaneously)
// Concurrent result: Data fetched successfully from https://fast-api.com
// Concurrent result: Data fetched successfully from https://slow-api.com
// Concurrent result: Task3 failed
// Starting timed operation...
// Timed operation: Operation timed out
// Processing async stream...
// Generating number: 1, Received number: 1, Doubled: 2
// (continues for numbers 2-5)
// Stream processing completed
// Expected error caught: Network error occurred
// Caught async error: Network error occurred
// Joined results: (success messages for A, B, C)
// First completed: (result from fastest API)
// Success/Error messages for each URL
// All async operations completed!
