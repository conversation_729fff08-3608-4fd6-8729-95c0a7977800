#!/usr/bin/env python3
"""
Umbra Language Syntax Fixer
Fixes common syntax issues in Umbra test scripts
"""

import os
import re
import sys
from pathlib import Path

def fix_umbra_syntax(content):
    """Fix common Umbra syntax issues"""
    
    # Fix variable declarations: = to :=
    content = re.sub(r'let\s+([^:]+):\s*([^=]+)\s*=\s*', r'let \1: \2 := ', content)
    content = re.sub(r'let\s+mut\s+([^:]+):\s*([^=]+)\s*=\s*', r'let mut \1: \2 := ', content)
    
    # Fix assignments: = to :=
    content = re.sub(r'^(\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*([^=])', r'\1\2 := \3', content, flags=re.MULTILINE)
    
    # Fix generic types: < > to [ ]
    content = re.sub(r'List<([^>]+)>', r'List[\1]', content)
    content = re.sub(r'Map<([^,]+),\s*([^>]+)>', r'Map[\1, \2]', content)
    content = re.sub(r'Set<([^>]+)>', r'Set[\1]', content)
    content = re.sub(r'Option<([^>]+)>', r'Option[\1]', content)
    content = re.sub(r'Result<([^,]+),\s*([^>]+)>', r'Result[\1, \2]', content)
    content = re.sub(r'Vec<([^>]+)>', r'List[\1]', content)
    content = re.sub(r'HashMap<([^,]+),\s*([^>]+)>', r'Map[\1, \2]', content)
    
    # Fix import statements
    content = re.sub(r'use\s+([^;]+);?', r'bring \1', content)
    content = re.sub(r'import\s+([^;]+);?', r'bring \1', content)
    
    # Fix function definitions
    content = re.sub(r'fn\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\(', r'fn \1(', content)
    
    # Fix type inference
    content = re.sub(r'let\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*:=\s*([^/\n]+)(?://.*)?$', 
                     lambda m: f'let {m.group(1)}: Auto := {m.group(2).strip()}', content, flags=re.MULTILINE)
    
    # Fix when statements (if -> when)
    content = re.sub(r'\bif\b', 'when', content)
    content = re.sub(r'\belse\b', 'otherwise', content)
    
    # Fix loops
    content = re.sub(r'\bfor\s+([^i]+)\s+in\s+', r'repeat \1 in ', content)
    
    # Fix match statements
    content = re.sub(r'\bmatch\s+([^{]+)\s*{', r'match \1 {', content)
    
    # Fix string formatting
    content = re.sub(r'println!\s*\(\s*"([^"]*)",\s*([^)]+)\)', r'println!("\1", \2)', content)
    
    # Fix macro calls
    content = re.sub(r'([a-zA-Z_][a-zA-Z0-9_]*!)(\s*)\(', r'\1\2(', content)
    
    return content

def fix_file(file_path):
    """Fix syntax in a single file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        fixed_content = fix_umbra_syntax(content)
        
        if fixed_content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(fixed_content)
            print(f"✅ Fixed: {file_path}")
            return True
        else:
            print(f"⚪ No changes: {file_path}")
            return False
            
    except Exception as e:
        print(f"❌ Error fixing {file_path}: {e}")
        return False

def main():
    """Main function to fix all Umbra test scripts"""
    
    if len(sys.argv) > 1:
        target_path = sys.argv[1]
    else:
        target_path = "test_scripts"
    
    if not os.path.exists(target_path):
        print(f"❌ Path not found: {target_path}")
        return 1
    
    print("🔧 Umbra Syntax Fixer")
    print("=" * 50)
    
    fixed_count = 0
    total_count = 0
    
    # Find all .umbra files
    for root, dirs, files in os.walk(target_path):
        for file in files:
            if file.endswith('.umbra'):
                file_path = os.path.join(root, file)
                total_count += 1
                if fix_file(file_path):
                    fixed_count += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Summary: Fixed {fixed_count}/{total_count} files")
    
    if fixed_count > 0:
        print("🎉 Syntax fixes applied successfully!")
    else:
        print("✨ All files already have correct syntax!")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
