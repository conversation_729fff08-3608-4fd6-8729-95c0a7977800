#!/usr/bin/env python3
"""
Fix double colon syntax errors in Umbra test scripts
"""

import os
import re
import sys

def fix_double_colon_syntax(content):
    """Fix double colon syntax issues"""
    
    # Fix multiple colons before :=
    content = re.sub(r':\s*:\s*:\s*:=', r' :=', content)
    content = re.sub(r':\s*:\s*:=', r' :=', content)
    content = re.sub(r':\s*:\s*:\s*:\s*:=', r' :=', content)
    
    # Fix spacing issues
    content = re.sub(r':\s+:=', r' :=', content)
    
    return content

def fix_file(file_path):
    """Fix syntax in a single file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        fixed_content = fix_double_colon_syntax(content)
        
        if fixed_content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(fixed_content)
            print(f"✅ Fixed: {file_path}")
            return True
        else:
            print(f"⚪ No changes: {file_path}")
            return False
            
    except Exception as e:
        print(f"❌ Error fixing {file_path}: {e}")
        return False

def main():
    """Main function"""
    
    target_path = "test_scripts"
    
    print("🔧 Double Colon Syntax Fixer")
    print("=" * 50)
    
    fixed_count = 0
    total_count = 0
    
    # Find all .umbra files
    for root, dirs, files in os.walk(target_path):
        for file in files:
            if file.endswith('.umbra'):
                file_path = os.path.join(root, file)
                total_count += 1
                if fix_file(file_path):
                    fixed_count += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Summary: Fixed {fixed_count}/{total_count} files")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
