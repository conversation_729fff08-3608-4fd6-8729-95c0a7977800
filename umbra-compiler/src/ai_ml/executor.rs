/// AI/ML execution engine for Umbra
/// 
/// This module provides the runtime execution engine for AI/ML statements
/// including train, evaluate, predict, and visualize operations.

use crate::error::{UmbraError, UmbraResult};
use crate::parser::ast::{TrainStatement, EvaluateStatement, PredictStatement, VisualizeStatement, Expression};
use crate::runtime::RuntimeValue;
use crate::ai_ml::{Dataset, Model, TrainingManager, EvaluationManager, PredictionManager};
use std::collections::HashMap;
use std::process::Command;
use std::fs::{File, create_dir_all};
use std::io::Write;

/// AI/ML execution engine
pub struct AIMLExecutor {
    /// Training manager
    training_manager: TrainingManager,
    /// Evaluation manager
    evaluation_manager: EvaluationManager,
    /// Prediction manager
    prediction_manager: PredictionManager,
    /// Active models
    models: HashMap<String, Model>,
    /// Active datasets
    datasets: HashMap<String, Dataset>,
    /// Working directory for temporary files
    working_dir: String,
    /// Python executable path
    python_executable: String,
}

impl AIMLExecutor {
    pub fn new(working_dir: String) -> Self {
        Self {
            training_manager: TrainingManager::new(working_dir.clone()),
            evaluation_manager: EvaluationManager::new(working_dir.clone()),
            prediction_manager: PredictionManager::new(working_dir.clone()),
            models: HashMap::new(),
            datasets: HashMap::new(),
            working_dir,
            python_executable: "python3".to_string(),
        }
    }

    /// Execute a train statement
    pub fn execute_train(&mut self, train_stmt: &TrainStatement) -> UmbraResult<RuntimeValue> {
        println!("🚀 Executing train statement...");

        // Get model and dataset names
        let model_name = train_stmt.model.clone();
        let dataset_name = train_stmt.dataset.clone();

        // Extract training parameters first (before borrowing models)
        let mut training_params = HashMap::new();
        for param in &train_stmt.config {
            let value = self.evaluate_expression(&param.value)?;
            training_params.insert(param.name.clone(), value);
        }

        // Check if model and dataset exist
        if !self.models.contains_key(&model_name) {
            return Err(UmbraError::Runtime(format!("Model '{}' not found", model_name)));
        }
        if !self.datasets.contains_key(&dataset_name) {
            return Err(UmbraError::Runtime(format!("Dataset '{}' not found", dataset_name)));
        }

        // Set default parameters if not provided
        if !training_params.contains_key("epochs") {
            training_params.insert("epochs".to_string(), RuntimeValue::Integer(100));
        }
        if !training_params.contains_key("learning_rate") {
            training_params.insert("learning_rate".to_string(), RuntimeValue::Float(0.001));
        }
        if !training_params.contains_key("batch_size") {
            training_params.insert("batch_size".to_string(), RuntimeValue::Integer(32));
        }

        // Generate and execute training script
        let output_path = format!("{}/models/{}.model", self.working_dir, model_name);
        create_dir_all(format!("{}/models", self.working_dir))
            .map_err(|e| UmbraError::Runtime(format!("Failed to create models directory: {}", e)))?;

        // Get references after parameter extraction
        let model = self.models.get(&model_name).unwrap();
        let dataset = self.datasets.get(&dataset_name).unwrap();
        let training_script = self.generate_training_script(model, dataset, &training_params, &output_path)?;
        let script_path = format!("{}/train_{}.py", self.working_dir, model_name);

        // Write training script
        let mut script_file = File::create(&script_path)
            .map_err(|e| UmbraError::Runtime(format!("Failed to create training script: {}", e)))?;
        script_file.write_all(training_script.as_bytes())
            .map_err(|e| UmbraError::Runtime(format!("Failed to write training script: {}", e)))?;

        // Execute training
        println!("🔄 Training model '{}'...", model_name);
        let output = Command::new(&self.python_executable)
            .arg(&script_path)
            .output()
            .map_err(|e| UmbraError::Runtime(format!("Failed to execute training script: {}", e)))?;

        if !output.status.success() {
            let error_msg = String::from_utf8_lossy(&output.stderr);
            return Err(UmbraError::Runtime(format!("Training failed: {}", error_msg)));
        }

        // Parse training results
        let stdout = String::from_utf8_lossy(&output.stdout);
        let metrics = self.parse_training_output(&stdout)?;

        println!("✅ Training completed successfully!");
        println!("📊 Final metrics: {:?}", metrics);

        // Update model state
        let model = self.models.get_mut(&model_name).unwrap();
        model.trained = true;
        model.model_path = Some(output_path);

        Ok(RuntimeValue::Map(metrics))
    }

    /// Execute an evaluate statement
    pub fn execute_evaluate(&mut self, eval_stmt: &EvaluateStatement) -> UmbraResult<RuntimeValue> {
        println!("📊 Executing evaluate statement...");

        let model_name = &eval_stmt.model;
        let dataset_name = &eval_stmt.dataset;

        let model = self.models.get(model_name)
            .ok_or_else(|| UmbraError::Runtime(format!("Model '{}' not found", model_name)))?;

        let dataset = self.datasets.get(dataset_name)
            .ok_or_else(|| UmbraError::Runtime(format!("Dataset '{}' not found", dataset_name)))?;

        if !model.trained {
            return Err(UmbraError::Runtime(format!("Model '{}' is not trained", model_name)));
        }

        // Generate evaluation script
        let evaluation_script = self.generate_evaluation_script(model, dataset_name)?;
        let script_path = format!("{}/evaluate_{}.py", self.working_dir, model_name);

        // Write evaluation script
        let mut script_file = File::create(&script_path)
            .map_err(|e| UmbraError::Runtime(format!("Failed to create evaluation script: {}", e)))?;
        script_file.write_all(evaluation_script.as_bytes())
            .map_err(|e| UmbraError::Runtime(format!("Failed to write evaluation script: {}", e)))?;

        // Execute evaluation
        println!("🔄 Evaluating model '{}'...", model_name);
        let output = Command::new(&self.python_executable)
            .arg(&script_path)
            .output()
            .map_err(|e| UmbraError::Runtime(format!("Failed to execute evaluation script: {}", e)))?;

        if !output.status.success() {
            let error_msg = String::from_utf8_lossy(&output.stderr);
            return Err(UmbraError::Runtime(format!("Evaluation failed: {}", error_msg)));
        }

        // Parse evaluation results
        let stdout = String::from_utf8_lossy(&output.stdout);
        let stderr = String::from_utf8_lossy(&output.stderr);

        // Display the actual Python output
        println!("🔍 Python script output:");
        for line in stdout.lines() {
            println!("{}", line);
        }

        if !stderr.is_empty() {
            println!("⚠️  Python stderr:");
            for line in stderr.lines() {
                println!("{}", line);
            }
        }

        let metrics = self.parse_evaluation_output(&stdout)?;

        println!("✅ Evaluation completed successfully!");
        println!("📊 Parsed metrics: {:?}", metrics);

        Ok(RuntimeValue::Map(metrics))
    }

    /// Execute a predict statement
    pub fn execute_predict(&mut self, predict_stmt: &PredictStatement) -> UmbraResult<RuntimeValue> {
        println!("🔮 Executing predict statement...");

        let model_name = &predict_stmt.model;
        let input_data = &predict_stmt.sample;

        let model = self.models.get(model_name)
            .ok_or_else(|| UmbraError::Runtime(format!("Model '{}' not found", model_name)))?;

        if !model.trained {
            return Err(UmbraError::Runtime(format!("Model '{}' is not trained", model_name)));
        }

        // For now, create a simple runtime value from the input string
        // In a real implementation, this would parse the input data properly
        let input_value = RuntimeValue::String(input_data.clone());

        // Generate prediction script
        let prediction_script = self.generate_prediction_script(model, &input_value)?;
        let script_path = format!("{}/predict_{}.py", self.working_dir, model_name);

        // Write prediction script
        let mut script_file = File::create(&script_path)
            .map_err(|e| UmbraError::Runtime(format!("Failed to create prediction script: {}", e)))?;
        script_file.write_all(prediction_script.as_bytes())
            .map_err(|e| UmbraError::Runtime(format!("Failed to write prediction script: {}", e)))?;

        // Execute prediction
        println!("🔄 Making prediction with model '{}'...", model_name);
        let output = Command::new(&self.python_executable)
            .arg(&script_path)
            .output()
            .map_err(|e| UmbraError::Runtime(format!("Failed to execute prediction script: {}", e)))?;

        if !output.status.success() {
            let error_msg = String::from_utf8_lossy(&output.stderr);
            return Err(UmbraError::Runtime(format!("Prediction failed: {}", error_msg)));
        }

        // Parse prediction results
        let stdout = String::from_utf8_lossy(&output.stdout);
        let prediction = self.parse_prediction_output(&stdout)?;

        println!("✅ Prediction completed successfully!");
        println!("🎯 Prediction result: {:?}", prediction);

        Ok(prediction)
    }

    /// Execute a visualize statement
    pub fn execute_visualize(&mut self, viz_stmt: &VisualizeStatement) -> UmbraResult<RuntimeValue> {
        println!("📈 Executing visualize statement...");

        let data_name = &viz_stmt.metric;
        let viz_type = &viz_stmt.dimension;

        // Get data to visualize
        let data = if let Some(dataset) = self.datasets.get(data_name) {
            RuntimeValue::String(format!("dataset:{}", data_name))
        } else if let Some(model) = self.models.get(data_name) {
            RuntimeValue::String(format!("model:{}", data_name))
        } else {
            return Err(UmbraError::Runtime(format!("Data '{}' not found", data_name)));
        };

        // Generate visualization script
        let viz_script = self.generate_visualization_script(&data, viz_type)?;
        let script_path = format!("{}/visualize_{}_{}.py", self.working_dir, data_name, viz_type);

        // Write visualization script
        let mut script_file = File::create(&script_path)
            .map_err(|e| UmbraError::Runtime(format!("Failed to create visualization script: {}", e)))?;
        script_file.write_all(viz_script.as_bytes())
            .map_err(|e| UmbraError::Runtime(format!("Failed to write visualization script: {}", e)))?;

        // Execute visualization
        println!("🔄 Generating {} visualization for '{}'...", viz_type, data_name);
        let output = Command::new(&self.python_executable)
            .arg(&script_path)
            .output()
            .map_err(|e| UmbraError::Runtime(format!("Failed to execute visualization script: {}", e)))?;

        if !output.status.success() {
            let error_msg = String::from_utf8_lossy(&output.stderr);
            return Err(UmbraError::Runtime(format!("Visualization failed: {}", error_msg)));
        }

        println!("✅ Visualization completed successfully!");
        let output_path = format!("{}/visualizations/{}_{}.png", self.working_dir, data_name, viz_type);
        
        Ok(RuntimeValue::String(output_path))
    }

    /// Register a model
    pub fn register_model(&mut self, name: String, model: Model) {
        self.models.insert(name, model);
    }

    /// Register a dataset
    pub fn register_dataset(&mut self, name: String, dataset: Dataset) {
        self.datasets.insert(name, dataset);
    }

    /// Load a dataset from file
    pub fn load_dataset(&mut self, name: String, path: &str) -> UmbraResult<()> {
        // Use Python-based loading for real dataset information
        let python_script = format!(r#"
import pandas as pd
import numpy as np
import sys

try:
    # Load CSV file
    df = pd.read_csv('{}')

    # Print real dataset information
    print(f"📊 Dataset loaded: {{df.shape[0]}} samples, {{df.shape[1]}} features")
    print(f"📋 Features: {{', '.join(df.columns.tolist())}}")

    # Check for target column
    target_col = None
    for col in ['target', 'churn', 'label', 'y']:
        if col in df.columns:
            target_col = col
            break

    if target_col:
        print(f"🎯 Target column: {{target_col}}")
        if df[target_col].dtype in ['int64', 'int32', 'object', 'bool']:
            print(f"📊 Target distribution: {{df[target_col].value_counts().to_dict()}}")

    print("✅ Dataset loading completed")

except Exception as e:
    print(f"❌ Error loading dataset: {{e}}", file=sys.stderr)
    sys.exit(1)
"#, path);

        // Write and execute Python script
        let script_path = format!("{}/load_dataset_{}.py", self.working_dir, name);
        let mut script_file = File::create(&script_path)
            .map_err(|e| UmbraError::Runtime(format!("Failed to create dataset loading script: {}", e)))?;
        script_file.write_all(python_script.as_bytes())
            .map_err(|e| UmbraError::Runtime(format!("Failed to write dataset loading script: {}", e)))?;

        // Execute Python script to get real dataset info
        let output = Command::new(&self.python_executable)
            .arg(&script_path)
            .output()
            .map_err(|e| UmbraError::Runtime(format!("Failed to execute dataset loading script: {}", e)))?;

        if !output.status.success() {
            let error_msg = String::from_utf8_lossy(&output.stderr);
            return Err(UmbraError::Runtime(format!("Dataset loading failed: {}", error_msg)));
        }

        // Print the real dataset information
        let stdout = String::from_utf8_lossy(&output.stdout);
        let stderr = String::from_utf8_lossy(&output.stderr);

        // Display Python output (dataset info)
        for line in stdout.lines() {
            println!("{}", line);
        }

        // Display Python stderr (additional info)
        for line in stderr.lines() {
            println!("{}", line);
        }

        // Create dataset object with real path
        let dataset = Dataset::from_csv(path)?;
        self.datasets.insert(name, dataset);
        Ok(())
    }

    /// Create a model
    pub fn create_model(&mut self, name: String, model_type: &str, config: HashMap<String, RuntimeValue>) -> UmbraResult<()> {
        // Convert config to TrainParameter vector
        let config_vec = config.into_iter().map(|(key, value)| {
            use crate::parser::ast::{TrainParameter, Expression, Literal};
            use crate::error::SourceLocation;
            let expr = match value {
                RuntimeValue::Integer(i) => Expression::Literal(Literal::Integer(i)),
                RuntimeValue::Float(f) => Expression::Literal(Literal::Float(f)),
                RuntimeValue::String(s) => Expression::Literal(Literal::String(s)),
                RuntimeValue::Boolean(b) => Expression::Literal(Literal::Boolean(b)),
                _ => Expression::Literal(Literal::String(format!("{:?}", value))),
            };
            TrainParameter {
                name: key,
                value: expr,
                location: SourceLocation { line: 0, column: 0 },
            }
        }).collect();

        let model = Model::new(name.clone(), model_type.to_string(), config_vec)?;
        self.models.insert(name, model);
        Ok(())
    }

    /// Evaluate an expression to runtime value
    fn evaluate_expression(&self, expr: &Expression) -> UmbraResult<RuntimeValue> {
        match expr {
            Expression::Literal(lit) => {
                match lit {
                    crate::parser::ast::Literal::Integer(i) => Ok(RuntimeValue::Integer(*i)),
                    crate::parser::ast::Literal::Float(f) => Ok(RuntimeValue::Float(*f)),
                    crate::parser::ast::Literal::String(s) => Ok(RuntimeValue::String(s.clone())),
                    crate::parser::ast::Literal::Boolean(b) => Ok(RuntimeValue::Boolean(*b)),
                }
            }
            Expression::Identifier(id) => {
                // Look up identifier in context
                Ok(RuntimeValue::String(id.name.clone()))
            }
            _ => Ok(RuntimeValue::Null),
        }
    }

    // Helper methods for script generation and output parsing would be implemented here...
    // These are simplified for brevity but would contain full Python script generation logic

    fn generate_training_script(
        &self,
        model: &Model,
        dataset: &Dataset,
        params: &HashMap<String, RuntimeValue>,
        output_path: &str,
    ) -> UmbraResult<String> {
        // Generate Python training script based on model type and parameters
        let script = format!(r#"
import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, mean_squared_error
import joblib

# Load dataset
data = pd.read_csv('{}')
X = data.drop('target', axis=1)
y = data['target']

# Split data
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

# Create and train model
{}

# Save model
joblib.dump(model, '{}')

# Print metrics
print(f"Training completed. Model saved to {}")
"#, 
            format!("{}/training_data.csv", self.working_dir),
            self.generate_model_code(model, params)?,
            output_path,
            output_path
        );

        Ok(script)
    }

    fn generate_model_code(&self, model: &Model, params: &HashMap<String, RuntimeValue>) -> UmbraResult<String> {
        match model.model_type.as_str() {
            "linear_regression" => Ok(format!(r#"
from sklearn.linear_model import LinearRegression
model = LinearRegression()
model.fit(X_train, y_train)
predictions = model.predict(X_test)
mse = mean_squared_error(y_test, predictions)
print(f"MSE: {{mse}}")
"#)),
            "random_forest" => {
                let n_estimators = params.get("n_estimators")
                    .and_then(|v| match v { RuntimeValue::Integer(i) => Some(*i), _ => None })
                    .unwrap_or(100);
                Ok(format!(r#"
from sklearn.ensemble import RandomForestRegressor
model = RandomForestRegressor(n_estimators={})
model.fit(X_train, y_train)
predictions = model.predict(X_test)
mse = mean_squared_error(y_test, predictions)
print(f"MSE: {{mse}}")
"#, n_estimators))
            }
            _ => Err(UmbraError::Runtime(format!("Unsupported model type: {}", model.model_type)))
        }
    }

    fn generate_evaluation_script(&self, model: &Model, dataset_name: &str) -> UmbraResult<String> {
        // Find the dataset file path based on the dataset name
        let dataset_path = match dataset_name {
            name if name.contains("customer_churn") => "data/customer_churn.csv",
            name if name.contains("training") => "data/training_data.csv",
            name if name.contains("validation") => "data/validation_data.csv",
            name if name.contains("test") => "data/test_data.csv",
            _ => "data/training_data.csv" // default fallback
        };

        Ok(format!(r#"
import joblib
import pandas as pd
import numpy as np
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, mean_squared_error, r2_score, classification_report

# Load model and data
model = joblib.load('{}')
data = pd.read_csv('{}')

# Print dataset information
print(f"📊 Dataset loaded: {{len(data)}} samples, {{len(data.columns)-1}} features")

# Prepare features and target
if 'target' in data.columns:
    X = data.drop('target', axis=1)
    y = data['target']
elif 'churn' in data.columns:
    X = data.drop('churn', axis=1)
    y = data['churn']
else:
    # Use last column as target
    X = data.iloc[:, :-1]
    y = data.iloc[:, -1]

# Make predictions
predictions = model.predict(X)

# Determine if classification or regression
is_classification = len(np.unique(y)) <= 10 and y.dtype in ['int64', 'int32', 'object', 'bool']

if is_classification:
    # Classification metrics
    accuracy = accuracy_score(y, predictions)
    precision = precision_score(y, predictions, average='weighted', zero_division=0)
    recall = recall_score(y, predictions, average='weighted', zero_division=0)
    f1 = f1_score(y, predictions, average='weighted', zero_division=0)

    print(f"  Accuracy: {{accuracy:.4f}}")
    print(f"  Precision: {{precision:.4f}}")
    print(f"  Recall: {{recall:.4f}}")
    print(f"  F1-Score: {{f1:.4f}}")
else:
    # Regression metrics
    mse = mean_squared_error(y, predictions)
    rmse = np.sqrt(mse)
    r2 = r2_score(y, predictions)

    print(f"  MSE: {{mse:.4f}}")
    print(f"  RMSE: {{rmse:.4f}}")
    print(f"  R² Score: {{r2:.4f}}")
"#,
            model.model_path.as_ref().unwrap_or(&"model.pkl".to_string()),
            dataset_path
        ))
    }

    fn generate_prediction_script(&self, model: &Model, input_data: &RuntimeValue) -> UmbraResult<String> {
        Ok(format!(r#"
import joblib
import numpy as np

# Load model
model = joblib.load('{}')

# Prepare input data
input_data = {}

# Make prediction
prediction = model.predict([input_data])
print(f"Prediction: {{prediction[0]}}")
"#, 
            model.model_path.as_ref().unwrap_or(&"model.pkl".to_string()),
            self.runtime_value_to_python(input_data)?
        ))
    }

    fn generate_visualization_script(&self, data: &RuntimeValue, viz_type: &str) -> UmbraResult<String> {
        Ok(format!(r#"
import matplotlib.pyplot as plt
import pandas as pd
import seaborn as sns

# Load data
data = pd.read_csv('data.csv')

# Create visualization
plt.figure(figsize=(10, 6))
{}

# Save plot
plt.savefig('visualization.png')
print("Visualization saved to visualization.png")
"#, 
            match viz_type {
                "histogram" => "plt.hist(data.iloc[:, 0])",
                "scatter" => "plt.scatter(data.iloc[:, 0], data.iloc[:, 1])",
                "line" => "plt.plot(data.iloc[:, 0])",
                _ => "plt.plot(data)"
            }
        ))
    }

    fn runtime_value_to_python(&self, value: &RuntimeValue) -> UmbraResult<String> {
        match value {
            RuntimeValue::Integer(i) => Ok(i.to_string()),
            RuntimeValue::Float(f) => Ok(f.to_string()),
            RuntimeValue::String(s) => Ok(format!("'{}'", s)),
            RuntimeValue::Boolean(b) => Ok(if *b { "True" } else { "False" }.to_string()),
            RuntimeValue::List(list) => {
                let items: Result<Vec<String>, UmbraError> = list.iter()
                    .map(|v| self.runtime_value_to_python(v))
                    .collect();
                Ok(format!("[{}]", items?.join(", ")))
            }
            _ => Ok("None".to_string()),
        }
    }

    fn parse_training_output(&self, output: &str) -> UmbraResult<HashMap<String, RuntimeValue>> {
        let mut metrics = HashMap::new();
        
        // Parse metrics from training output
        for line in output.lines() {
            if line.contains("MSE:") {
                if let Some(value_str) = line.split("MSE:").nth(1) {
                    if let Ok(value) = value_str.trim().parse::<f64>() {
                        metrics.insert("mse".to_string(), RuntimeValue::Float(value));
                    }
                }
            }
            if line.contains("Accuracy:") {
                if let Some(value_str) = line.split("Accuracy:").nth(1) {
                    if let Ok(value) = value_str.trim().parse::<f64>() {
                        metrics.insert("accuracy".to_string(), RuntimeValue::Float(value));
                    }
                }
            }
        }
        
        Ok(metrics)
    }

    fn parse_evaluation_output(&self, output: &str) -> UmbraResult<HashMap<String, RuntimeValue>> {
        let mut metrics = HashMap::new();

        // Parse real metrics from Python evaluation output
        for line in output.lines() {
            // Parse accuracy
            if line.contains("Accuracy:") {
                if let Some(value_str) = line.split("Accuracy:").nth(1) {
                    if let Ok(value) = value_str.trim().parse::<f64>() {
                        metrics.insert("accuracy".to_string(), RuntimeValue::Float(value));
                    }
                }
            }
            // Parse precision
            if line.contains("Precision:") {
                if let Some(value_str) = line.split("Precision:").nth(1) {
                    if let Ok(value) = value_str.trim().parse::<f64>() {
                        metrics.insert("precision".to_string(), RuntimeValue::Float(value));
                    }
                }
            }
            // Parse recall
            if line.contains("Recall:") {
                if let Some(value_str) = line.split("Recall:").nth(1) {
                    if let Ok(value) = value_str.trim().parse::<f64>() {
                        metrics.insert("recall".to_string(), RuntimeValue::Float(value));
                    }
                }
            }
            // Parse F1-Score
            if line.contains("F1-Score:") {
                if let Some(value_str) = line.split("F1-Score:").nth(1) {
                    if let Ok(value) = value_str.trim().parse::<f64>() {
                        metrics.insert("f1_score".to_string(), RuntimeValue::Float(value));
                    }
                }
            }
            // Parse MSE
            if line.contains("MSE:") {
                if let Some(value_str) = line.split("MSE:").nth(1) {
                    if let Ok(value) = value_str.trim().parse::<f64>() {
                        metrics.insert("mse".to_string(), RuntimeValue::Float(value));
                    }
                }
            }
            // Parse RMSE
            if line.contains("RMSE:") {
                if let Some(value_str) = line.split("RMSE:").nth(1) {
                    if let Ok(value) = value_str.trim().parse::<f64>() {
                        metrics.insert("rmse".to_string(), RuntimeValue::Float(value));
                    }
                }
            }
            // Parse R² Score
            if line.contains("R² Score:") {
                if let Some(value_str) = line.split("R² Score:").nth(1) {
                    if let Ok(value) = value_str.trim().parse::<f64>() {
                        metrics.insert("r2_score".to_string(), RuntimeValue::Float(value));
                    }
                }
            }
        }

        Ok(metrics)
    }

    fn parse_prediction_output(&self, output: &str) -> UmbraResult<RuntimeValue> {
        for line in output.lines() {
            if line.contains("Prediction:") {
                if let Some(value_str) = line.split("Prediction:").nth(1) {
                    let value_str = value_str.trim();
                    if let Ok(value) = value_str.parse::<f64>() {
                        return Ok(RuntimeValue::Float(value));
                    }
                    if let Ok(value) = value_str.parse::<i64>() {
                        return Ok(RuntimeValue::Integer(value));
                    }
                    return Ok(RuntimeValue::String(value_str.to_string()));
                }
            }
        }
        
        Err(UmbraError::Runtime("Could not parse prediction output".to_string()))
    }
}

impl Default for AIMLExecutor {
    fn default() -> Self {
        Self::new("/tmp/umbra_aiml".to_string())
    }
}
