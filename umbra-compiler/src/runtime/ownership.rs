use std::collections::{HashMap, HashSet};
use std::rc::{Rc, Weak};
use std::cell::RefCell;

use crate::error::{UmbraError, UmbraResult};
use crate::runtime::RuntimeValue;

/// Ownership mode for values
#[derive(Debug, Clone, PartialEq)]
pub enum OwnershipMode {
    /// Value is owned by current scope
    Owned,
    /// Value is borrowed immutably
    Borrowed,
    /// Value is borrowed mutably
    BorrowedMut,
    /// Value is moved (no longer accessible)
    Moved,
    /// Value is shared (reference counted)
    Shared,
}

/// Reference to a value with ownership information
#[derive(Debug, Clone)]
pub struct ValueRef {
    pub value: RuntimeValue,
    pub ownership: OwnershipMode,
    pub borrow_count: usize,
    pub mutable_borrow: bool,
    pub lifetime_id: u64,
}

/// Memory ownership manager
pub struct OwnershipManager {
    /// Value references by variable name
    values: HashMap<String, ValueRef>,
    /// Borrow checker state
    borrows: HashMap<String, Vec<BorrowInfo>>,
    /// Lifetime counter
    lifetime_counter: u64,
    /// Active lifetimes
    lifetimes: HashMap<u64, LifetimeInfo>,
    /// Move tracking
    moved_values: HashSet<String>,
}

/// Information about a borrow
#[derive(Debug, Clone)]
pub struct BorrowInfo {
    pub borrower: String,
    pub is_mutable: bool,
    pub lifetime_id: u64,
}

/// Lifetime information
#[derive(Debug, Clone)]
pub struct LifetimeInfo {
    pub id: u64,
    pub scope_depth: usize,
    pub is_active: bool,
}

impl OwnershipManager {
    pub fn new() -> Self {
        Self {
            values: HashMap::new(),
            borrows: HashMap::new(),
            lifetime_counter: 0,
            lifetimes: HashMap::new(),
            moved_values: HashSet::new(),
        }
    }

    /// Create a new lifetime
    pub fn create_lifetime(&mut self, scope_depth: usize) -> u64 {
        self.lifetime_counter += 1;
        let lifetime_id = self.lifetime_counter;
        
        self.lifetimes.insert(lifetime_id, LifetimeInfo {
            id: lifetime_id,
            scope_depth,
            is_active: true,
        });
        
        lifetime_id
    }

    /// End a lifetime
    pub fn end_lifetime(&mut self, lifetime_id: u64) -> UmbraResult<()> {
        if let Some(lifetime) = self.lifetimes.get_mut(&lifetime_id) {
            lifetime.is_active = false;
            
            // Clean up borrows associated with this lifetime
            self.borrows.retain(|_, borrows| {
                borrows.retain(|borrow| borrow.lifetime_id != lifetime_id);
                !borrows.is_empty()
            });
            
            Ok(())
        } else {
            Err(UmbraError::Runtime(format!("Lifetime {} not found", lifetime_id)))
        }
    }

    /// Store a value with ownership
    pub fn store_value(&mut self, name: String, value: RuntimeValue, ownership: OwnershipMode) -> UmbraResult<()> {
        // Check if value was already moved
        if self.moved_values.contains(&name) {
            return Err(UmbraError::Runtime(format!("Cannot use moved value '{}'", name)));
        }

        let lifetime_id = self.create_lifetime(0); // Default scope depth
        
        let value_ref = ValueRef {
            value,
            ownership,
            borrow_count: 0,
            mutable_borrow: false,
            lifetime_id,
        };
        
        self.values.insert(name, value_ref);
        Ok(())
    }

    /// Get a value reference
    pub fn get_value(&self, name: &str) -> UmbraResult<&ValueRef> {
        if self.moved_values.contains(name) {
            return Err(UmbraError::Runtime(format!("Value '{}' has been moved", name)));
        }
        
        self.values.get(name)
            .ok_or_else(|| UmbraError::Runtime(format!("Variable '{}' not found", name)))
    }

    /// Borrow a value immutably
    pub fn borrow_immutable(&mut self, name: &str, borrower: String, lifetime_id: u64) -> UmbraResult<RuntimeValue> {
        if self.moved_values.contains(name) {
            return Err(UmbraError::Runtime(format!("Cannot borrow moved value '{}'", name)));
        }

        let value_ref = self.values.get_mut(name)
            .ok_or_else(|| UmbraError::Runtime(format!("Variable '{}' not found", name)))?;

        // Check borrow rules
        if value_ref.mutable_borrow {
            return Err(UmbraError::Runtime(format!("Cannot borrow '{}' immutably while mutably borrowed", name)));
        }

        // Add borrow info
        let borrow_info = BorrowInfo {
            borrower,
            is_mutable: false,
            lifetime_id,
        };
        
        self.borrows.entry(name.to_string()).or_insert_with(Vec::new).push(borrow_info);
        value_ref.borrow_count += 1;

        Ok(value_ref.value.clone())
    }

    /// Borrow a value mutably
    pub fn borrow_mutable(&mut self, name: &str, borrower: String, lifetime_id: u64) -> UmbraResult<RuntimeValue> {
        if self.moved_values.contains(name) {
            return Err(UmbraError::Runtime(format!("Cannot borrow moved value '{}'", name)));
        }

        let value_ref = self.values.get_mut(name)
            .ok_or_else(|| UmbraError::Runtime(format!("Variable '{}' not found", name)))?;

        // Check borrow rules
        if value_ref.borrow_count > 0 || value_ref.mutable_borrow {
            return Err(UmbraError::Runtime(format!("Cannot borrow '{}' mutably while already borrowed", name)));
        }

        // Add borrow info
        let borrow_info = BorrowInfo {
            borrower,
            is_mutable: true,
            lifetime_id,
        };
        
        self.borrows.entry(name.to_string()).or_insert_with(Vec::new).push(borrow_info);
        value_ref.mutable_borrow = true;

        Ok(value_ref.value.clone())
    }

    /// Move a value (transfer ownership)
    pub fn move_value(&mut self, name: &str) -> UmbraResult<RuntimeValue> {
        if self.moved_values.contains(name) {
            return Err(UmbraError::Runtime(format!("Value '{}' has already been moved", name)));
        }

        let value_ref = self.values.get(name)
            .ok_or_else(|| UmbraError::Runtime(format!("Variable '{}' not found", name)))?;

        // Check if value is currently borrowed
        if value_ref.borrow_count > 0 || value_ref.mutable_borrow {
            return Err(UmbraError::Runtime(format!("Cannot move '{}' while borrowed", name)));
        }

        let value = value_ref.value.clone();
        self.moved_values.insert(name.to_string());
        
        Ok(value)
    }

    /// Release a borrow
    pub fn release_borrow(&mut self, name: &str, borrower: &str) -> UmbraResult<()> {
        if let Some(borrows) = self.borrows.get_mut(name) {
            if let Some(pos) = borrows.iter().position(|b| b.borrower == borrower) {
                let borrow_info = borrows.remove(pos);
                
                if let Some(value_ref) = self.values.get_mut(name) {
                    if borrow_info.is_mutable {
                        value_ref.mutable_borrow = false;
                    } else {
                        value_ref.borrow_count = value_ref.borrow_count.saturating_sub(1);
                    }
                }
            }
        }
        
        Ok(())
    }

    /// Check if a value can be accessed
    pub fn can_access(&self, name: &str) -> bool {
        !self.moved_values.contains(name) && self.values.contains_key(name)
    }

    /// Check if a value can be borrowed immutably
    pub fn can_borrow_immutable(&self, name: &str) -> bool {
        if self.moved_values.contains(name) {
            return false;
        }
        
        if let Some(value_ref) = self.values.get(name) {
            !value_ref.mutable_borrow
        } else {
            false
        }
    }

    /// Check if a value can be borrowed mutably
    pub fn can_borrow_mutable(&self, name: &str) -> bool {
        if self.moved_values.contains(name) {
            return false;
        }
        
        if let Some(value_ref) = self.values.get(name) {
            value_ref.borrow_count == 0 && !value_ref.mutable_borrow
        } else {
            false
        }
    }

    /// Check if a value can be moved
    pub fn can_move(&self, name: &str) -> bool {
        if self.moved_values.contains(name) {
            return false;
        }
        
        if let Some(value_ref) = self.values.get(name) {
            value_ref.borrow_count == 0 && !value_ref.mutable_borrow
        } else {
            false
        }
    }

    /// Get ownership information for a value
    pub fn get_ownership_info(&self, name: &str) -> Option<OwnershipMode> {
        if self.moved_values.contains(name) {
            Some(OwnershipMode::Moved)
        } else {
            self.values.get(name).map(|v| v.ownership.clone())
        }
    }

    /// Clean up expired lifetimes
    pub fn cleanup_expired_lifetimes(&mut self, current_scope_depth: usize) {
        let expired_lifetimes: Vec<u64> = self.lifetimes
            .iter()
            .filter(|(_, lifetime)| lifetime.scope_depth > current_scope_depth)
            .map(|(id, _)| *id)
            .collect();

        for lifetime_id in expired_lifetimes {
            let _ = self.end_lifetime(lifetime_id);
        }
    }

    /// Clear all ownership information (for scope exit)
    pub fn clear_scope(&mut self) {
        self.values.clear();
        self.borrows.clear();
        self.moved_values.clear();
        self.lifetimes.clear();
    }
}

impl Default for OwnershipManager {
    fn default() -> Self {
        Self::new()
    }
}
