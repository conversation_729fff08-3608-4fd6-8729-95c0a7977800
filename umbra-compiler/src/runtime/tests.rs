#[cfg(test)]
mod tests {
    use super::*;
    use crate::parser::ast::*;
    use crate::error::SourceLocation;
    use crate::runtime::{Runtime, RuntimeValue};

    fn create_test_runtime() -> Runtime {
        let mut runtime = Runtime::new();
        runtime.register_builtins();
        runtime
    }

    #[test]
    fn test_basic_arithmetic() {
        let mut runtime = create_test_runtime();

        // Test addition
        let expr = Expression::Binary(BinaryOp {
            left: Box::new(Expression::Literal(Literal::Integer(5))),
            operator: BinaryOperator::Add,
            right: Box::new(Expression::Literal(Literal::Integer(3))),
            location: SourceLocation { line: 1, column: 1 },
        });

        let result = runtime.execute_expression(&expr).unwrap();
        assert_eq!(result, RuntimeValue::Integer(8));
    }

    #[test]
    fn test_variable_assignment() {
        let mut runtime = create_test_runtime();

        // Test variable assignment
        let stmt = Statement::Variable(VariableDecl {
            name: "x".to_string(),
            type_annotation: Type::Basic(BasicType::Integer),
            value: Expression::Literal(Literal::Integer(42)),
            location: SourceLocation { line: 1, column: 1 },
        });

        runtime.execute_statement(&stmt).unwrap();

        // Test variable access
        let expr = Expression::Identifier(Identifier {
            name: "x".to_string(),
            location: SourceLocation { line: 1, column: 1 },
        });
        let result = runtime.execute_expression(&expr).unwrap();
        assert_eq!(result, RuntimeValue::Integer(42));
    }

    #[test]
    fn test_function_definition_and_call() {
        let mut runtime = create_test_runtime();

        // Define a function
        let func_def = FunctionDef {
            name: "add_two".to_string(),
            parameters: vec![
                Parameter {
                    name: "x".to_string(),
                    type_annotation: Type::Basic(BasicType::Integer),
                    location: SourceLocation { line: 1, column: 1 },
                }
            ],
            return_type: Type::Basic(BasicType::Integer),
            body: vec![
                Statement::Return(ReturnStatement {
                    value: Some(Expression::Binary(BinaryOp {
                        left: Box::new(Expression::Identifier(Identifier {
                            name: "x".to_string(),
                            location: SourceLocation { line: 1, column: 1 },
                        })),
                        operator: BinaryOperator::Add,
                        right: Box::new(Expression::Literal(Literal::Integer(2))),
                        location: SourceLocation { line: 1, column: 1 },
                    })),
                    location: SourceLocation { line: 1, column: 1 },
                })
            ],
            visibility: Visibility::Public,
            type_params: vec![],
            constraints: vec![],
            location: SourceLocation { line: 1, column: 1 },
        };

        runtime.functions.insert("add_two".to_string(), func_def);

        // Call the function
        let call_expr = Expression::Call(FunctionCall {
            name: "add_two".to_string(),
            arguments: vec![Expression::Literal(Literal::Integer(5))],
            location: SourceLocation { line: 1, column: 1 },
        });

        let result = runtime.execute_expression(&call_expr).unwrap();
        assert_eq!(result, RuntimeValue::Integer(7));
    }

    #[test]
    fn test_when_statement() {
        let mut runtime = create_test_runtime();

        // Set up a variable
        runtime.set_variable("x", RuntimeValue::Integer(10)).unwrap();

        // Test when statement
        let when_stmt = Statement::When(WhenStatement {
            condition: Expression::Binary(BinaryOp {
                left: Box::new(Expression::Identifier(Identifier {
                    name: "x".to_string(),
                    location: SourceLocation { line: 1, column: 1 },
                })),
                operator: BinaryOperator::Greater,
                right: Box::new(Expression::Literal(Literal::Integer(5))),
                location: SourceLocation { line: 1, column: 1 },
            }),
            then_body: vec![
                Statement::Variable(VariableDecl {
                    name: "result".to_string(),
                    type_annotation: Type::Basic(BasicType::String),
                    value: Expression::Literal(Literal::String("greater".to_string())),
                    location: SourceLocation { line: 1, column: 1 },
                })
            ],
            else_body: Some(vec![
                Statement::Variable(VariableDecl {
                    name: "result".to_string(),
                    type_annotation: Type::Basic(BasicType::String),
                    value: Expression::Literal(Literal::String("not greater".to_string())),
                    location: SourceLocation { line: 1, column: 1 },
                })
            ]),
            location: SourceLocation { line: 1, column: 1 },
        });

        runtime.execute_statement(&when_stmt).unwrap();

        let result = runtime.get_variable("result").unwrap();
        assert_eq!(result, &RuntimeValue::String("greater".to_string()));
    }

    #[test]
    fn test_repeat_loop() {
        let mut runtime = create_test_runtime();

        // Initialize counter
        runtime.set_variable("counter", RuntimeValue::Integer(0)).unwrap();

        // Create a list to iterate over (since RepeatStatement expects an iterable)
        let list_expr = Expression::List(ListLiteral {
            elements: vec![
                Expression::Literal(Literal::Integer(1)),
                Expression::Literal(Literal::Integer(2)),
                Expression::Literal(Literal::Integer(3)),
            ],
            location: SourceLocation { line: 1, column: 1 },
        });

        // Test repeat loop
        let repeat_stmt = Statement::Repeat(RepeatStatement {
            variable: "i".to_string(),
            iterable: list_expr,
            body: vec![
                Statement::Assignment(Assignment {
                    name: "counter".to_string(),
                    operator: AssignmentOperator::Assign,
                    value: Expression::Binary(BinaryOp {
                        left: Box::new(Expression::Identifier(Identifier {
                            name: "counter".to_string(),
                            location: SourceLocation { line: 1, column: 1 },
                        })),
                        operator: BinaryOperator::Add,
                        right: Box::new(Expression::Literal(Literal::Integer(1))),
                        location: SourceLocation { line: 1, column: 1 },
                    }),
                    location: SourceLocation { line: 1, column: 1 },
                })
            ],
            location: SourceLocation { line: 1, column: 1 },
        });

        runtime.execute_statement(&repeat_stmt).unwrap();

        let result = runtime.get_variable("counter").unwrap();
        assert_eq!(result, &RuntimeValue::Integer(3));
    }

    #[test]
    fn test_struct_creation_and_access() {
        let mut runtime = create_test_runtime();

        // Create a struct literal
        let struct_expr = Expression::Struct(StructLiteral {
            struct_name: "Person".to_string(),
            fields: vec![
                StructFieldInit {
                    name: "name".to_string(),
                    value: Expression::Literal(Literal::String("Alice".to_string())),
                    location: SourceLocation { line: 1, column: 1 },
                },
                StructFieldInit {
                    name: "age".to_string(),
                    value: Expression::Literal(Literal::Integer(30)),
                    location: SourceLocation { line: 1, column: 1 },
                }
            ],
            location: SourceLocation { line: 1, column: 1 },
        });

        let struct_value = runtime.execute_expression(&struct_expr).unwrap();

        // Test field access
        if let RuntimeValue::Struct(fields) = &struct_value {
            assert_eq!(fields.get("name"), Some(&RuntimeValue::String("Alice".to_string())));
            assert_eq!(fields.get("age"), Some(&RuntimeValue::Integer(30)));
        } else {
            panic!("Expected struct value");
        }
    }

    #[test]
    fn test_pattern_matching() {
        let mut runtime = create_test_runtime();
        
        // Test literal pattern matching
        let pattern = Pattern::Literal(Literal::Integer(42));
        let value = RuntimeValue::Integer(42);
        
        let matches = runtime.pattern_matches(&pattern, &value).unwrap();
        assert!(matches);
        
        // Test identifier pattern (should always match and bind)
        let pattern = Pattern::Identifier("x".to_string());
        let value = RuntimeValue::String("hello".to_string());
        
        let matches = runtime.pattern_matches(&pattern, &value).unwrap();
        assert!(matches);
        
        // Check that the variable was bound
        let bound_value = runtime.get_variable("x").unwrap();
        assert_eq!(bound_value, &RuntimeValue::String("hello".to_string()));
    }

    #[test]
    fn test_list_operations() {
        let mut runtime = create_test_runtime();

        // Create a list
        let list_expr = Expression::List(ListLiteral {
            elements: vec![
                Expression::Literal(Literal::Integer(1)),
                Expression::Literal(Literal::Integer(2)),
                Expression::Literal(Literal::Integer(3)),
            ],
            location: SourceLocation { line: 1, column: 1 },
        });

        let list_value = runtime.execute_expression(&list_expr).unwrap();

        // Test list indexing
        let index_expr = Expression::IndexAccess(IndexAccess {
            object: Box::new(list_expr.clone()),
            index: Box::new(Expression::Literal(Literal::Integer(1))),
            location: SourceLocation { line: 1, column: 1 },
        });

        let indexed_value = runtime.execute_expression(&index_expr).unwrap();
        assert_eq!(indexed_value, RuntimeValue::Integer(2));
    }

    #[test]
    fn test_method_calls() {
        let mut runtime = create_test_runtime();

        // Test string method call
        let string_value = RuntimeValue::String("hello world".to_string());
        runtime.set_variable("s", string_value).unwrap();

        let method_call = Expression::MethodCall(MethodCall {
            object: Box::new(Expression::Identifier(Identifier {
                name: "s".to_string(),
                location: SourceLocation { line: 1, column: 1 },
            })),
            method_name: "len".to_string(),
            arguments: vec![],
            location: SourceLocation { line: 1, column: 1 },
        });

        let result = runtime.execute_expression(&method_call).unwrap();
        assert_eq!(result, RuntimeValue::Integer(11));
    }

    #[test]
    fn test_error_handling() {
        let mut runtime = create_test_runtime();

        // Test try expression with successful operation
        let try_expr = Expression::TryExpression(TryExpression {
            expression: Box::new(Expression::Literal(Literal::Integer(42))),
            location: SourceLocation { line: 1, column: 1 },
        });

        let result = runtime.execute_expression(&try_expr).unwrap();
        assert_eq!(result, RuntimeValue::Integer(42));
    }

    #[test]
    fn test_ownership_system() {
        let mut runtime = create_test_runtime();

        // Test basic ownership operations
        let value = RuntimeValue::String("owned_value".to_string());
        runtime.ownership_manager.store_value(
            "test_var".to_string(),
            value.clone(),
            crate::runtime::ownership::OwnershipMode::Owned
        ).unwrap();

        // Test that we can access the value
        assert!(runtime.ownership_manager.can_access("test_var"));

        // Test borrowing
        let borrowed = runtime.ownership_manager.borrow_immutable(
            "test_var",
            "borrower".to_string(),
            1
        ).unwrap();

        assert_eq!(borrowed, value);
    }

    #[test]
    fn test_macro_system() {
        let runtime = create_test_runtime();

        // Test built-in macro recognition
        assert!(runtime.macro_system.is_macro("println"));
        assert!(runtime.macro_system.is_macro("format"));
        assert!(!runtime.macro_system.is_macro("not_a_macro"));
    }

    #[test]
    fn test_simple_expression_evaluation() {
        let mut runtime = create_test_runtime();

        // Test simple literal evaluation
        let literal_expr = Expression::Literal(Literal::String("hello".to_string()));
        let result = runtime.execute_expression(&literal_expr).unwrap();
        assert_eq!(result, RuntimeValue::String("hello".to_string()));

        // Test boolean literal
        let bool_expr = Expression::Literal(Literal::Boolean(true));
        let result = runtime.execute_expression(&bool_expr).unwrap();
        assert_eq!(result, RuntimeValue::Boolean(true));
    }

    #[test]
    fn test_variable_scoping() {
        let mut runtime = create_test_runtime();

        // Set a variable in global scope
        runtime.set_variable("global_var", RuntimeValue::Integer(100)).unwrap();

        // Test that we can access it
        let result = runtime.get_variable("global_var").unwrap();
        assert_eq!(result, &RuntimeValue::Integer(100));

        // Test variable shadowing would require function calls with local scopes
        // This is a simplified test for the basic variable storage
        runtime.set_variable("global_var", RuntimeValue::String("updated".to_string())).unwrap();
        let result = runtime.get_variable("global_var").unwrap();
        assert_eq!(result, &RuntimeValue::String("updated".to_string()));
    }
}
