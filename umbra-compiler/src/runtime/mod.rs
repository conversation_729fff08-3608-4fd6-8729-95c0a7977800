/// Runtime system for Umbra
/// 
/// This module provides the runtime infrastructure for executing Umbra programs,
/// including memory management, exception handling, and built-in function execution.

pub mod exceptions;
pub mod memory;
pub mod builtins;
pub mod ai_ml_runtime;
pub mod async_runtime;
pub mod ownership;
pub mod macro_system;

#[cfg(test)]
mod tests;

use crate::error::{UmbraError, UmbraResult};
use crate::parser::ast::*;
use exceptions::ExceptionHandler;
use std::collections::HashMap;

/// Runtime value representation
#[derive(Debug, Clone, PartialEq)]
pub enum RuntimeValue {
    Integer(i64),
    Float(f64),
    String(String),
    Boolean(bool),
    List(Vec<RuntimeValue>),
    Map(HashMap<String, RuntimeValue>),
    Set(std::collections::HashSet<String>),
    Struct(HashMap<String, RuntimeValue>),
    Function(String), // Function name
    Object(String, Box<RuntimeValue>), // Object type and data
    Null,
}

impl RuntimeValue {
    pub fn type_name(&self) -> &'static str {
        match self {
            RuntimeValue::Integer(_) => "Integer",
            RuntimeValue::Float(_) => "Float",
            RuntimeValue::String(_) => "String",
            RuntimeValue::Boolean(_) => "Boolean",
            RuntimeValue::List(_) => "List",
            RuntimeValue::Map(_) => "Map",
            RuntimeValue::Set(_) => "Set",
            RuntimeValue::Struct(_) => "Struct",
            RuntimeValue::Function(_) => "Function",
            RuntimeValue::Object(_, _) => "Object",
            RuntimeValue::Null => "Null",
        }
    }

    pub fn to_string(&self) -> String {
        match self {
            RuntimeValue::Integer(i) => i.to_string(),
            RuntimeValue::Float(f) => f.to_string(),
            RuntimeValue::String(s) => s.clone(),
            RuntimeValue::Boolean(b) => b.to_string(),
            RuntimeValue::List(list) => {
                let items: Vec<String> = list.iter().map(|v| v.to_string()).collect();
                format!("[{}]", items.join(", "))
            }
            RuntimeValue::Map(map) => {
                let items: Vec<String> = map.iter()
                    .map(|(k, v)| format!("{}: {}", k, v.to_string()))
                    .collect();
                format!("{{{}}}", items.join(", "))
            }
            RuntimeValue::Set(set) => {
                let items: Vec<String> = set.iter().cloned().collect();
                format!("{{{}}}", items.join(", "))
            }
            RuntimeValue::Struct(fields) => {
                let items: Vec<String> = fields.iter()
                    .map(|(k, v)| format!("{}: {}", k, v.to_string()))
                    .collect();
                format!("struct {{{}}}", items.join(", "))
            }
            RuntimeValue::Function(name) => format!("function {}", name),
            RuntimeValue::Object(type_name, data) => format!("{}({})", type_name, data.to_string()),
            RuntimeValue::Null => "null".to_string(),
        }
    }

    pub fn is_truthy(&self) -> bool {
        match self {
            RuntimeValue::Boolean(b) => *b,
            RuntimeValue::Integer(i) => *i != 0,
            RuntimeValue::Float(f) => *f != 0.0,
            RuntimeValue::String(s) => !s.is_empty(),
            RuntimeValue::List(list) => !list.is_empty(),
            RuntimeValue::Map(map) => !map.is_empty(),
            RuntimeValue::Set(set) => !set.is_empty(),
            RuntimeValue::Struct(fields) => !fields.is_empty(),
            RuntimeValue::Null => false,
            RuntimeValue::Function(_) => true,
            RuntimeValue::Object(_, _) => true,
        }
    }
}

/// Runtime environment for executing Umbra programs
pub struct Runtime {
    /// Exception handler
    pub exception_handler: ExceptionHandler,
    /// Global variables
    pub globals: HashMap<String, RuntimeValue>,
    /// Function definitions
    pub functions: HashMap<String, FunctionDef>,
    /// Built-in functions
    pub builtins: HashMap<String, Box<dyn Fn(Vec<RuntimeValue>) -> UmbraResult<RuntimeValue>>>,
    /// Current call stack
    pub call_stack: Vec<CallFrame>,
    /// Memory manager
    pub memory: memory::MemoryManager,
    /// Return flag for early returns
    pub return_flag: bool,
    /// Return value for early returns
    pub return_value: RuntimeValue,
    /// Structure definitions
    pub structures: HashMap<String, StructureDef>,
    /// Trait definitions
    pub traits: HashMap<String, TraitDef>,
    /// Implementation definitions
    pub implementations: HashMap<String, Vec<ImplDef>>,
    /// Module imports
    pub imports: HashMap<String, ModulePath>,
    /// Exported symbols
    pub exports: Vec<String>,
    /// Async runtime for async/await operations
    pub async_runtime: async_runtime::AsyncRuntime,
    /// Memory ownership manager
    pub ownership_manager: ownership::OwnershipManager,
    /// Macro expansion system
    pub macro_system: macro_system::MacroSystem,
}

/// Call frame for function calls
#[derive(Debug, Clone)]
pub struct CallFrame {
    pub function_name: String,
    pub locals: HashMap<String, RuntimeValue>,
    pub line: usize,
    pub column: usize,
}

impl Runtime {
    pub fn new() -> Self {
        Self {
            exception_handler: ExceptionHandler::new(),
            globals: HashMap::new(),
            functions: HashMap::new(),
            builtins: HashMap::new(),
            call_stack: Vec::new(),
            memory: memory::MemoryManager::new(),
            return_flag: false,
            return_value: RuntimeValue::Null,
            structures: HashMap::new(),
            traits: HashMap::new(),
            implementations: HashMap::new(),
            imports: HashMap::new(),
            exports: Vec::new(),
            async_runtime: async_runtime::AsyncRuntime::new(),
            ownership_manager: ownership::OwnershipManager::new(),
            macro_system: macro_system::MacroSystem::new(),
        }
    }

    /// Execute a program
    pub fn execute_program(&mut self, program: &Program) -> UmbraResult<()> {
        // First pass: collect type and function definitions
        for statement in &program.statements {
            match statement {
                Statement::Function(func) => {
                    self.functions.insert(func.name.clone(), func.clone());
                }
                Statement::Structure(structure) => {
                    self.structures.insert(structure.name.clone(), structure.clone());
                }
                Statement::Trait(trait_def) => {
                    self.traits.insert(trait_def.name.clone(), trait_def.clone());
                }
                Statement::Implementation(impl_def) => {
                    let type_name = impl_def.implementing_type.to_string();
                    self.implementations.entry(type_name).or_insert_with(Vec::new).push(impl_def.clone());
                }
                _ => {}
            }
        }

        // Second pass: execute statements
        for statement in &program.statements {
            match statement {
                Statement::Function(_) | Statement::Structure(_) |
                Statement::Trait(_) | Statement::Implementation(_) => {
                    // Already processed in first pass
                }
                _ => {
                    self.execute_statement(statement)?;
                }
            }
        }

        // Execute main function if it exists
        if self.functions.contains_key("main") {
            self.call_function("main", Vec::new())?;
        }

        Ok(())
    }

    /// Execute a statement
    pub fn execute_statement(&mut self, statement: &Statement) -> UmbraResult<RuntimeValue> {
        match statement {
            Statement::Variable(var) => self.execute_variable_declaration(var),
            Statement::Assignment(assign) => self.execute_assignment(assign),
            Statement::Expression(expr) => self.execute_expression(&expr.expression),
            Statement::When(when) => {
                self.execute_when_statement(when)
            },
            Statement::Repeat(repeat) => {
                self.execute_repeat_statement(repeat)
            },
            Statement::Return(ret) => {
                self.execute_return_statement(ret)
            },
            Statement::Try(try_stmt) => self.execute_try_statement(try_stmt),
            Statement::Throw(throw) => self.execute_throw_statement(throw),
            Statement::Panic(panic) => self.execute_panic_statement(panic),
            Statement::Train(train) => self.execute_train_statement(train),
            Statement::Evaluate(evaluate) => self.execute_evaluate_statement(evaluate),
            Statement::Predict(predict) => self.execute_predict_statement(predict),
            Statement::Visualize(visualize) => self.execute_visualize_statement(visualize),
            Statement::Module(module) => self.execute_module_declaration(module),
            Statement::Import(import) => self.execute_import_statement(import),
            Statement::Export(export) => self.execute_export_statement(export),
            Statement::Function(_) => Ok(RuntimeValue::Null), // Already handled in first pass
            Statement::Structure(structure) => self.execute_structure_definition(structure),
            Statement::Trait(trait_def) => self.execute_trait_definition(trait_def),
            Statement::Implementation(impl_def) => self.execute_implementation(impl_def),
            Statement::Query(query) => self.execute_query_statement(query),
            Statement::Transaction(transaction) => self.execute_transaction_statement(transaction),
            Statement::Migration(migration) => self.execute_migration_statement(migration),
            Statement::Error(error_def) => self.execute_error_definition(error_def),
        }
    }

    /// Execute a variable declaration
    fn execute_variable_declaration(&mut self, var: &VariableDecl) -> UmbraResult<RuntimeValue> {
        let value = self.execute_expression(&var.value)?;
        
        if self.call_stack.is_empty() {
            self.globals.insert(var.name.clone(), value);
        } else {
            let frame = self.call_stack.last_mut().unwrap();
            frame.locals.insert(var.name.clone(), value);
        }
        
        Ok(RuntimeValue::Null)
    }

    /// Execute an assignment
    fn execute_assignment(&mut self, assign: &Assignment) -> UmbraResult<RuntimeValue> {
        let value = self.execute_expression(&assign.value)?;
        
        // Try to find variable in local scope first, then global
        if let Some(frame) = self.call_stack.last_mut() {
            if frame.locals.contains_key(&assign.name) {
                frame.locals.insert(assign.name.clone(), value);
                return Ok(RuntimeValue::Null);
            }
        }
        
        self.globals.insert(assign.name.clone(), value);
        Ok(RuntimeValue::Null)
    }

    /// Execute an expression
    pub fn execute_expression(&mut self, expr: &Expression) -> UmbraResult<RuntimeValue> {
        match expr {
            Expression::Literal(lit) => self.execute_literal(lit),
            Expression::Identifier(id) => self.execute_identifier(id),
            Expression::Binary(bin) => {
                self.execute_binary_expression(bin)
            },
            Expression::Unary(un) => {
                self.execute_unary_expression(un)
            },
            Expression::Call(call) => {
                self.execute_function_call(call)
            },
            Expression::List(list) => {
                self.execute_list_expression(list)
            },
            Expression::IndexAccess(index) => {
                self.execute_index_access(index)
            },
            Expression::TryExpression(try_expr) => {
                self.execute_try_expression(try_expr)
            },
            Expression::MethodCall(method_call) => {
                self.execute_method_call(method_call)
            },
            Expression::FieldAccess(field_access) => {
                self.execute_field_access(field_access)
            },
            Expression::Struct(struct_literal) => {
                self.execute_struct_literal(struct_literal)
            },
            Expression::Match(match_expr) => {
                self.execute_match_expression(match_expr)
            },
            Expression::QualifiedIdentifier(qualified_id) => {
                self.execute_qualified_identifier(qualified_id)
            },
            Expression::Some(some_expr) => {
                let value = self.execute_expression(&some_expr.value)?;
                Ok(RuntimeValue::Object("Some".to_string(), Box::new(value)))
            },
            Expression::None(_) => {
                Ok(RuntimeValue::Object("None".to_string(), Box::new(RuntimeValue::Null)))
            },
            Expression::Ok(ok_expr) => {
                let value = self.execute_expression(&ok_expr.value)?;
                Ok(RuntimeValue::Object("Ok".to_string(), Box::new(value)))
            },
            Expression::Err(err_expr) => {
                let value = self.execute_expression(&err_expr.value)?;
                Ok(RuntimeValue::Object("Err".to_string(), Box::new(value)))
            },
            Expression::Lambda(lambda) => {
                // Store lambda as a function-like object
                Ok(RuntimeValue::Function(format!("lambda_{}", lambda.parameters.len())))
            },
            Expression::ErrorPropagation(error_prop) => {
                self.execute_error_propagation(error_prop)
            },
        }
    }

    /// Execute a literal expression
    fn execute_literal(&self, literal: &Literal) -> UmbraResult<RuntimeValue> {
        match literal {
            Literal::Integer(i) => Ok(RuntimeValue::Integer(*i)),
            Literal::Float(f) => Ok(RuntimeValue::Float(*f)),
            Literal::String(s) => Ok(RuntimeValue::String(s.clone())),
            Literal::Boolean(b) => Ok(RuntimeValue::Boolean(*b)),
        }
    }

    /// Execute an identifier expression
    fn execute_identifier(&self, identifier: &Identifier) -> UmbraResult<RuntimeValue> {
        let name = &identifier.name;

        // Try local scope first
        if let Some(frame) = self.call_stack.last() {
            if let Some(value) = frame.locals.get(name) {
                return Ok(value.clone());
            }
        }

        // Try global scope
        if let Some(value) = self.globals.get(name) {
            return Ok(value.clone());
        }

        // Variable not found
        Err(UmbraError::Runtime(format!("Undefined variable: {}", name)))
    }

    /// Execute a function call
    fn execute_function_call(&mut self, call: &crate::parser::ast::FunctionCall) -> UmbraResult<RuntimeValue> {
        // Evaluate arguments
        let mut args = Vec::new();
        for arg in &call.arguments {
            args.push(self.execute_expression(arg)?);
        }

        // Handle AI/ML builtin functions
        match call.name.as_str() {
            "load_dataset" => {
                if args.len() != 1 {
                    return Err(UmbraError::Runtime("load_dataset expects 1 argument".to_string()));
                }
                match &args[0] {
                    RuntimeValue::String(file_path) => {
                        use crate::runtime::ai_ml_runtime::umbra_load_dataset;
                        umbra_load_dataset(file_path)
                    },
                    _ => Err(UmbraError::Runtime("load_dataset expects string argument".to_string()))
                }
            },
            "create_model" => {
                if args.len() != 1 {
                    return Err(UmbraError::Runtime("create_model expects 1 argument".to_string()));
                }
                match &args[0] {
                    RuntimeValue::String(model_type) => {
                        use crate::runtime::ai_ml_runtime::umbra_create_model;
                        umbra_create_model(model_type)
                    },
                    _ => Err(UmbraError::Runtime("create_model expects string argument".to_string()))
                }
            },
            "show" => {
                // Handle show function
                for arg in &args {
                    print!("{}", arg.to_string());
                }
                println!();
                Ok(RuntimeValue::Null)
            },
            _ => {
                // Try to call user-defined function
                self.call_function(&call.name, args)
            }
        }
    }

    /// Execute a train statement
    fn execute_train_statement(&mut self, train: &crate::parser::ast::TrainStatement) -> UmbraResult<RuntimeValue> {
        // Get model and dataset from variables by name
        let model_name = &train.model;
        let dataset_name = &train.dataset;

        let model = if let Some(frame) = self.call_stack.last() {
            frame.locals.get(model_name).cloned()
        } else {
            self.globals.get(model_name).cloned()
        }.ok_or_else(|| UmbraError::Runtime(format!("Undefined variable: {}", model_name)))?;

        let dataset = if let Some(frame) = self.call_stack.last() {
            frame.locals.get(dataset_name).cloned()
        } else {
            self.globals.get(dataset_name).cloned()
        }.ok_or_else(|| UmbraError::Runtime(format!("Undefined variable: {}", dataset_name)))?;

        // Convert config to HashMap
        let mut config = std::collections::HashMap::new();
        for param in &train.config {
            config.insert(param.name.clone(), self.execute_expression(&param.value)?);
        }

        use crate::runtime::ai_ml_runtime::umbra_train_model;
        umbra_train_model(&model, &dataset, &config)
    }

    /// Execute an evaluate statement
    fn execute_evaluate_statement(&mut self, evaluate: &crate::parser::ast::EvaluateStatement) -> UmbraResult<RuntimeValue> {
        let model_name = &evaluate.model;
        let dataset_name = &evaluate.dataset;

        let model = if let Some(frame) = self.call_stack.last() {
            frame.locals.get(model_name).cloned()
        } else {
            self.globals.get(model_name).cloned()
        }.ok_or_else(|| UmbraError::Runtime(format!("Undefined variable: {}", model_name)))?;

        let dataset = if let Some(frame) = self.call_stack.last() {
            frame.locals.get(dataset_name).cloned()
        } else {
            self.globals.get(dataset_name).cloned()
        }.ok_or_else(|| UmbraError::Runtime(format!("Undefined variable: {}", dataset_name)))?;

        use crate::runtime::ai_ml_runtime::umbra_evaluate_model;
        umbra_evaluate_model(&model, &dataset)
    }

    /// Execute a predict statement
    fn execute_predict_statement(&mut self, predict: &crate::parser::ast::PredictStatement) -> UmbraResult<RuntimeValue> {
        let sample_str = &predict.sample;  // sample is already a String
        let model_name = &predict.model;

        let model = if let Some(frame) = self.call_stack.last() {
            frame.locals.get(model_name).cloned()
        } else {
            self.globals.get(model_name).cloned()
        }.ok_or_else(|| UmbraError::Runtime(format!("Undefined variable: {}", model_name)))?;

        use crate::runtime::ai_ml_runtime::umbra_predict_sample;
        umbra_predict_sample(sample_str, &model)
    }

    /// Execute a visualize statement
    fn execute_visualize_statement(&mut self, visualize: &crate::parser::ast::VisualizeStatement) -> UmbraResult<RuntimeValue> {
        let metric_str = &visualize.metric;      // metric is already a String
        let dimension_str = &visualize.dimension; // dimension is already a String

        use crate::runtime::ai_ml_runtime::umbra_visualize_metric;
        umbra_visualize_metric(metric_str, dimension_str)
    }

    /// Execute a try statement
    fn execute_try_statement(&mut self, try_stmt: &TryStatement) -> UmbraResult<RuntimeValue> {
        self.exception_handler.enter_try_block();
        
        let mut result = RuntimeValue::Null;
        let mut exception_occurred = false;
        
        // Execute try block
        for statement in &try_stmt.try_block {
            match self.execute_statement(statement) {
                Ok(value) => result = value,
                Err(error) => {
                    exception_occurred = true;
                    // Try to handle with catch blocks
                    let handled = self.handle_exception_in_catch_blocks(&try_stmt.catch_clauses, error)?;
                    if !handled {
                        self.exception_handler.exit_try_block()?;
                        return Err(UmbraError::Runtime("Unhandled exception".to_string()));
                    }
                    break;
                }
            }
        }
        
        // Execute finally block if present
        if let Some(finally_block) = &try_stmt.finally_block {
            for statement in finally_block {
                self.execute_statement(statement)?;
            }
        }
        
        self.exception_handler.exit_try_block()?;
        Ok(result)
    }

    /// Handle exception in catch blocks
    fn handle_exception_in_catch_blocks(
        &mut self,
        catch_clauses: &[CatchClause],
        error: UmbraError,
    ) -> UmbraResult<bool> {
        for catch_clause in catch_clauses {
            // Check if this catch clause handles the error type
            if self.error_matches_catch_clause(&error, catch_clause) {
                // Bind error variable if specified
                if let Some(var_name) = &catch_clause.error_variable {
                    let error_value = RuntimeValue::String(error.to_string());
                    if let Some(frame) = self.call_stack.last_mut() {
                        frame.locals.insert(var_name.clone(), error_value);
                    } else {
                        self.globals.insert(var_name.clone(), error_value);
                    }
                }
                
                // Execute catch block
                for statement in &catch_clause.catch_block {
                    self.execute_statement(statement)?;
                }
                
                return Ok(true); // Exception handled
            }
        }
        
        Ok(false) // Exception not handled
    }

    /// Check if an error matches a catch clause
    fn error_matches_catch_clause(&self, error: &UmbraError, catch_clause: &CatchClause) -> bool {
        match &catch_clause.error_type {
            None => true, // Catch-all
            Some(error_type) => {
                // Match error type with catch clause type
                match error {
                    UmbraError::Runtime(_) => match error_type {
                        crate::parser::ast::Type::Basic(crate::parser::ast::BasicType::String) => true,
                        _ => false,
                    },
                    UmbraError::Type { .. } => match error_type {
                        crate::parser::ast::Type::Basic(crate::parser::ast::BasicType::String) => true,
                        _ => false,
                    },
                    UmbraError::Io(_) => match error_type {
                        crate::parser::ast::Type::Basic(crate::parser::ast::BasicType::String) => true,
                        _ => false,
                    },
                    UmbraError::Memory(_) => match error_type {
                        crate::parser::ast::Type::Basic(crate::parser::ast::BasicType::String) => true,
                        _ => false,
                    },
                    _ => true, // Generic exception
                }
            }
        }
    }

    /// Execute a throw statement
    fn execute_throw_statement(&mut self, throw_stmt: &ThrowStatement) -> UmbraResult<RuntimeValue> {
        let error_value = self.execute_expression(&throw_stmt.error_expression)?;
        let error_message = error_value.to_string();
        
        let exception = self.exception_handler.create_builtin_exception(
            "RuntimeError",
            error_message,
            throw_stmt.location.line,
            throw_stmt.location.column,
        );
        
        self.exception_handler.throw_exception(exception)?;
        Ok(RuntimeValue::Null)
    }

    /// Execute a panic statement
    fn execute_panic_statement(&mut self, panic_stmt: &PanicStatement) -> UmbraResult<RuntimeValue> {
        let message = if let Some(msg_expr) = &panic_stmt.message {
            self.execute_expression(msg_expr)?.to_string()
        } else {
            "Panic occurred".to_string()
        };
        
        Err(UmbraError::Runtime(format!("PANIC: {}", message)))
    }

    /// Call a function
    pub fn call_function(&mut self, name: &str, args: Vec<RuntimeValue>) -> UmbraResult<RuntimeValue> {
        // Check for built-in functions first
        if let Some(builtin) = self.builtins.get(name) {
            // Call built-in function
            return builtin(args);
        }

        if let Some(func) = self.functions.get(name).cloned() {
            // Validate argument count
            if args.len() != func.parameters.len() {
                return Err(UmbraError::Runtime(format!(
                    "Function '{}' expects {} arguments, got {}",
                    name, func.parameters.len(), args.len()
                )));
            }

            // Create new call frame
            let mut frame = CallFrame {
                function_name: name.to_string(),
                locals: HashMap::new(),
                line: func.location.line,
                column: func.location.column,
            };

            // Bind parameters
            for (param, arg) in func.parameters.iter().zip(args.iter()) {
                frame.locals.insert(param.name.clone(), arg.clone());
            }

            self.call_stack.push(frame);

            // Clear return flag before executing function
            self.return_flag = false;
            self.return_value = RuntimeValue::Null;

            // Execute function body
            let result = self.execute_block(&func.body);

            // Check if function returned early
            let final_result = if self.return_flag {
                self.return_value.clone()
            } else {
                result.unwrap_or(RuntimeValue::Null)
            };

            // Clean up call stack
            self.call_stack.pop();

            // Reset return flag
            self.return_flag = false;
            self.return_value = RuntimeValue::Null;

            Ok(final_result)
        } else {
            Err(UmbraError::Runtime(format!("Undefined function: {}", name)))
        }
    }

    // Add missing methods for REPL
    pub fn set_variable(&mut self, name: &str, value: RuntimeValue) -> UmbraResult<()> {
        if let Some(frame) = self.call_stack.last_mut() {
            frame.locals.insert(name.to_string(), value);
        } else {
            self.globals.insert(name.to_string(), value);
        }
        Ok(())
    }

    pub fn add_builtin_function<F>(&mut self, name: &str, func: F) -> UmbraResult<()>
    where
        F: Fn(Vec<RuntimeValue>) -> UmbraResult<RuntimeValue> + 'static,
    {
        // Store the function in the builtins map
        self.builtins.insert(name.to_string(), Box::new(func));
        Ok(())
    }

    pub fn define_function(&mut self, func_stmt: &crate::parser::ast::FunctionDef) -> UmbraResult<()> {
        // TODO: Implement function definition
        self.functions.insert(func_stmt.name.clone(), func_stmt.clone());
        Ok(())
    }

    pub fn import_module(&mut self, _import_stmt: &crate::parser::ast::ImportStatement) -> UmbraResult<()> {
        // TODO: Implement module import
        Ok(())
    }

    /// Execute a binary expression
    fn execute_binary_expression(&mut self, bin: &crate::parser::ast::BinaryOp) -> UmbraResult<RuntimeValue> {
        let left = self.execute_expression(&bin.left)?;
        let right = self.execute_expression(&bin.right)?;

        use crate::parser::ast::BinaryOperator;
        match bin.operator {
            BinaryOperator::Add => self.add_values(left, right),
            BinaryOperator::Subtract => self.subtract_values(left, right),
            BinaryOperator::Multiply => self.multiply_values(left, right),
            BinaryOperator::Divide => self.divide_values(left, right),
            BinaryOperator::Modulo => self.modulo_values(left, right),
            BinaryOperator::Equal => Ok(RuntimeValue::Boolean(self.values_equal(&left, &right))),
            BinaryOperator::NotEqual => Ok(RuntimeValue::Boolean(!self.values_equal(&left, &right))),
            BinaryOperator::Less => self.compare_values(left, right, |a, b| a < b),
            BinaryOperator::LessEqual => self.compare_values(left, right, |a, b| a <= b),
            BinaryOperator::Greater => self.compare_values(left, right, |a, b| a > b),
            BinaryOperator::GreaterEqual => self.compare_values(left, right, |a, b| a >= b),
            BinaryOperator::And => self.logical_and(left, right),
            BinaryOperator::Or => self.logical_or(left, right),
        }
    }

    /// Execute a unary expression
    fn execute_unary_expression(&mut self, un: &crate::parser::ast::UnaryOp) -> UmbraResult<RuntimeValue> {
        let operand = self.execute_expression(&un.operand)?;

        use crate::parser::ast::UnaryOperator;
        match un.operator {
            UnaryOperator::Minus => match operand {
                RuntimeValue::Integer(i) => Ok(RuntimeValue::Integer(-i)),
                RuntimeValue::Float(f) => Ok(RuntimeValue::Float(-f)),
                _ => Err(UmbraError::Runtime("Unary minus can only be applied to numbers".to_string())),
            },
            UnaryOperator::Not => match operand {
                RuntimeValue::Boolean(b) => Ok(RuntimeValue::Boolean(!b)),
                RuntimeValue::Null => Ok(RuntimeValue::Boolean(true)),
                _ => Ok(RuntimeValue::Boolean(false)),
            },
        }
    }

    /// Add two runtime values
    fn add_values(&self, left: RuntimeValue, right: RuntimeValue) -> UmbraResult<RuntimeValue> {
        match (left, right) {
            (RuntimeValue::Integer(a), RuntimeValue::Integer(b)) => Ok(RuntimeValue::Integer(a + b)),
            (RuntimeValue::Float(a), RuntimeValue::Float(b)) => Ok(RuntimeValue::Float(a + b)),
            (RuntimeValue::Integer(a), RuntimeValue::Float(b)) => Ok(RuntimeValue::Float(a as f64 + b)),
            (RuntimeValue::Float(a), RuntimeValue::Integer(b)) => Ok(RuntimeValue::Float(a + b as f64)),
            (RuntimeValue::String(a), RuntimeValue::String(b)) => Ok(RuntimeValue::String(a + &b)),
            (RuntimeValue::List(mut a), RuntimeValue::List(b)) => {
                a.extend(b);
                Ok(RuntimeValue::List(a))
            },
            _ => Err(UmbraError::Runtime("Cannot add these types".to_string())),
        }
    }

    /// Subtract two runtime values
    fn subtract_values(&self, left: RuntimeValue, right: RuntimeValue) -> UmbraResult<RuntimeValue> {
        match (left, right) {
            (RuntimeValue::Integer(a), RuntimeValue::Integer(b)) => Ok(RuntimeValue::Integer(a - b)),
            (RuntimeValue::Float(a), RuntimeValue::Float(b)) => Ok(RuntimeValue::Float(a - b)),
            (RuntimeValue::Integer(a), RuntimeValue::Float(b)) => Ok(RuntimeValue::Float(a as f64 - b)),
            (RuntimeValue::Float(a), RuntimeValue::Integer(b)) => Ok(RuntimeValue::Float(a - b as f64)),
            _ => Err(UmbraError::Runtime("Cannot subtract these types".to_string())),
        }
    }

    /// Multiply two runtime values
    fn multiply_values(&self, left: RuntimeValue, right: RuntimeValue) -> UmbraResult<RuntimeValue> {
        match (left, right) {
            (RuntimeValue::Integer(a), RuntimeValue::Integer(b)) => Ok(RuntimeValue::Integer(a * b)),
            (RuntimeValue::Float(a), RuntimeValue::Float(b)) => Ok(RuntimeValue::Float(a * b)),
            (RuntimeValue::Integer(a), RuntimeValue::Float(b)) => Ok(RuntimeValue::Float(a as f64 * b)),
            (RuntimeValue::Float(a), RuntimeValue::Integer(b)) => Ok(RuntimeValue::Float(a * b as f64)),
            (RuntimeValue::String(s), RuntimeValue::Integer(n)) => {
                if n < 0 {
                    return Err(UmbraError::Runtime("Cannot multiply string by negative number".to_string()));
                }
                Ok(RuntimeValue::String(s.repeat(n as usize)))
            },
            _ => Err(UmbraError::Runtime("Cannot multiply these types".to_string())),
        }
    }

    /// Divide two runtime values
    fn divide_values(&self, left: RuntimeValue, right: RuntimeValue) -> UmbraResult<RuntimeValue> {
        match (left, right) {
            (RuntimeValue::Integer(a), RuntimeValue::Integer(b)) => {
                if b == 0 {
                    return Err(UmbraError::Runtime("Division by zero".to_string()));
                }
                Ok(RuntimeValue::Float(a as f64 / b as f64))
            },
            (RuntimeValue::Float(a), RuntimeValue::Float(b)) => {
                if b == 0.0 {
                    return Err(UmbraError::Runtime("Division by zero".to_string()));
                }
                Ok(RuntimeValue::Float(a / b))
            },
            (RuntimeValue::Integer(a), RuntimeValue::Float(b)) => {
                if b == 0.0 {
                    return Err(UmbraError::Runtime("Division by zero".to_string()));
                }
                Ok(RuntimeValue::Float(a as f64 / b))
            },
            (RuntimeValue::Float(a), RuntimeValue::Integer(b)) => {
                if b == 0 {
                    return Err(UmbraError::Runtime("Division by zero".to_string()));
                }
                Ok(RuntimeValue::Float(a / b as f64))
            },
            _ => Err(UmbraError::Runtime("Cannot divide these types".to_string())),
        }
    }

    /// Modulo operation on two runtime values
    fn modulo_values(&self, left: RuntimeValue, right: RuntimeValue) -> UmbraResult<RuntimeValue> {
        match (left, right) {
            (RuntimeValue::Integer(a), RuntimeValue::Integer(b)) => {
                if b == 0 {
                    return Err(UmbraError::Runtime("Modulo by zero".to_string()));
                }
                Ok(RuntimeValue::Integer(a % b))
            },
            (RuntimeValue::Float(a), RuntimeValue::Float(b)) => {
                if b == 0.0 {
                    return Err(UmbraError::Runtime("Modulo by zero".to_string()));
                }
                Ok(RuntimeValue::Float(a % b))
            },
            _ => Err(UmbraError::Runtime("Cannot perform modulo on these types".to_string())),
        }
    }

    /// Check if two values are equal
    fn values_equal(&self, left: &RuntimeValue, right: &RuntimeValue) -> bool {
        match (left, right) {
            (RuntimeValue::Integer(a), RuntimeValue::Integer(b)) => a == b,
            (RuntimeValue::Float(a), RuntimeValue::Float(b)) => (a - b).abs() < f64::EPSILON,
            (RuntimeValue::Integer(a), RuntimeValue::Float(b)) => (*a as f64 - b).abs() < f64::EPSILON,
            (RuntimeValue::Float(a), RuntimeValue::Integer(b)) => (a - *b as f64).abs() < f64::EPSILON,
            (RuntimeValue::String(a), RuntimeValue::String(b)) => a == b,
            (RuntimeValue::Boolean(a), RuntimeValue::Boolean(b)) => a == b,
            (RuntimeValue::Null, RuntimeValue::Null) => true,
            _ => false,
        }
    }

    /// Compare two values using a comparison function
    fn compare_values<F>(&self, left: RuntimeValue, right: RuntimeValue, cmp: F) -> UmbraResult<RuntimeValue>
    where
        F: Fn(f64, f64) -> bool,
    {
        let (left_num, right_num) = match (left, right) {
            (RuntimeValue::Integer(a), RuntimeValue::Integer(b)) => (a as f64, b as f64),
            (RuntimeValue::Float(a), RuntimeValue::Float(b)) => (a, b),
            (RuntimeValue::Integer(a), RuntimeValue::Float(b)) => (a as f64, b),
            (RuntimeValue::Float(a), RuntimeValue::Integer(b)) => (a, b as f64),
            _ => return Err(UmbraError::Runtime("Cannot compare these types".to_string())),
        };

        Ok(RuntimeValue::Boolean(cmp(left_num, right_num)))
    }

    /// Logical AND operation
    fn logical_and(&self, left: RuntimeValue, right: RuntimeValue) -> UmbraResult<RuntimeValue> {
        let left_bool = self.is_truthy(&left);
        if !left_bool {
            Ok(RuntimeValue::Boolean(false))
        } else {
            Ok(RuntimeValue::Boolean(self.is_truthy(&right)))
        }
    }

    /// Logical OR operation
    fn logical_or(&self, left: RuntimeValue, right: RuntimeValue) -> UmbraResult<RuntimeValue> {
        let left_bool = self.is_truthy(&left);
        if left_bool {
            Ok(RuntimeValue::Boolean(true))
        } else {
            Ok(RuntimeValue::Boolean(self.is_truthy(&right)))
        }
    }



    /// Check if a value is truthy
    fn is_truthy(&self, value: &RuntimeValue) -> bool {
        match value {
            RuntimeValue::Boolean(b) => *b,
            RuntimeValue::Null => false,
            RuntimeValue::Integer(i) => *i != 0,
            RuntimeValue::Float(f) => *f != 0.0,
            RuntimeValue::String(s) => !s.is_empty(),
            RuntimeValue::List(l) => !l.is_empty(),
            RuntimeValue::Map(m) => !m.is_empty(),
            RuntimeValue::Set(s) => !s.is_empty(),
            _ => true,
        }
    }

    /// Execute a list expression
    fn execute_list_expression(&mut self, list: &crate::parser::ast::ListLiteral) -> UmbraResult<RuntimeValue> {
        let mut values = Vec::new();
        for element in &list.elements {
            values.push(self.execute_expression(element)?);
        }
        Ok(RuntimeValue::List(values))
    }

    /// Execute index access
    fn execute_index_access(&mut self, index: &crate::parser::ast::IndexAccess) -> UmbraResult<RuntimeValue> {
        let object = self.execute_expression(&index.object)?;
        let index_value = self.execute_expression(&index.index)?;

        match (object, index_value) {
            (RuntimeValue::List(list), RuntimeValue::Integer(i)) => {
                let idx = if i < 0 {
                    // Negative indexing from the end
                    list.len() as i64 + i
                } else {
                    i
                };

                if idx < 0 || idx >= list.len() as i64 {
                    return Err(UmbraError::Runtime(format!("List index {} out of bounds", i)));
                }

                Ok(list[idx as usize].clone())
            },
            (RuntimeValue::String(s), RuntimeValue::Integer(i)) => {
                let chars: Vec<char> = s.chars().collect();
                let idx = if i < 0 {
                    // Negative indexing from the end
                    chars.len() as i64 + i
                } else {
                    i
                };

                if idx < 0 || idx >= chars.len() as i64 {
                    return Err(UmbraError::Runtime(format!("String index {} out of bounds", i)));
                }

                Ok(RuntimeValue::String(chars[idx as usize].to_string()))
            },
            (RuntimeValue::Map(map), RuntimeValue::String(key)) => {
                Ok(map.get(&key).cloned().unwrap_or(RuntimeValue::Null))
            },
            _ => Err(UmbraError::Runtime("Invalid index access".to_string())),
        }
    }

    /// Execute a when statement (conditional)
    fn execute_when_statement(&mut self, when: &crate::parser::ast::WhenStatement) -> UmbraResult<RuntimeValue> {
        let condition = self.execute_expression(&when.condition)?;

        if self.is_truthy(&condition) {
            self.execute_block(&when.then_body)
        } else if let Some(else_body) = &when.else_body {
            self.execute_block(else_body)
        } else {
            Ok(RuntimeValue::Null)
        }
    }

    /// Execute a repeat statement (for-in loop)
    fn execute_repeat_statement(&mut self, repeat: &crate::parser::ast::RepeatStatement) -> UmbraResult<RuntimeValue> {
        let iterable = self.execute_expression(&repeat.iterable)?;
        let mut last_result = RuntimeValue::Null;

        match iterable {
            RuntimeValue::List(list) => {
                for item in list {
                    // Set the loop variable
                    if let Some(frame) = self.call_stack.last_mut() {
                        frame.locals.insert(repeat.variable.clone(), item);
                    } else {
                        self.globals.insert(repeat.variable.clone(), item);
                    }

                    // Execute the body
                    last_result = self.execute_block(&repeat.body)?;
                }
            },
            RuntimeValue::String(s) => {
                for ch in s.chars() {
                    let char_value = RuntimeValue::String(ch.to_string());

                    // Set the loop variable
                    if let Some(frame) = self.call_stack.last_mut() {
                        frame.locals.insert(repeat.variable.clone(), char_value);
                    } else {
                        self.globals.insert(repeat.variable.clone(), char_value);
                    }

                    // Execute the body
                    last_result = self.execute_block(&repeat.body)?;
                }
            },
            _ => return Err(UmbraError::Runtime("Cannot iterate over this type".to_string())),
        }

        Ok(last_result)
    }

    /// Execute a return statement
    fn execute_return_statement(&mut self, ret: &crate::parser::ast::ReturnStatement) -> UmbraResult<RuntimeValue> {
        let return_value = if let Some(value) = &ret.value {
            self.execute_expression(value)?
        } else {
            RuntimeValue::Null
        };

        // Set return flag and value for call stack unwinding
        self.return_flag = true;
        self.return_value = return_value.clone();

        Ok(return_value)
    }

    /// Execute a block of statements
    fn execute_block(&mut self, statements: &[crate::parser::ast::Statement]) -> UmbraResult<RuntimeValue> {
        let mut last_result = RuntimeValue::Null;

        for statement in statements {
            last_result = self.execute_statement(statement)?;

            // Check for early return
            if self.return_flag {
                break;
            }
        }

        Ok(last_result)
    }



    /// Execute a try expression
    fn execute_try_expression(&mut self, try_expr: &crate::parser::ast::TryExpression) -> UmbraResult<RuntimeValue> {
        match self.execute_expression(&try_expr.expression) {
            Ok(value) => Ok(value),
            Err(_) => {
                // Try expressions return null on error
                Ok(RuntimeValue::Null)
            }
        }
    }

    /// Execute a module declaration
    fn execute_module_declaration(&mut self, _module: &ModuleDeclaration) -> UmbraResult<RuntimeValue> {
        // Module declarations are handled at compile time
        Ok(RuntimeValue::Null)
    }

    /// Execute an import statement
    fn execute_import_statement(&mut self, import: &ImportStatement) -> UmbraResult<RuntimeValue> {
        match &import.import_type {
            ImportType::Module(path) => {
                self.imports.insert(path.segments.last().unwrap().clone(), path.clone());
            }
            ImportType::ModuleAs(path, alias) => {
                self.imports.insert(alias.clone(), path.clone());
            }
            ImportType::Symbol(path, symbol) => {
                // For now, just record the import
                self.imports.insert(symbol.clone(), path.clone());
            }
            ImportType::SymbolAs(path, _symbol, alias) => {
                self.imports.insert(alias.clone(), path.clone());
            }
            ImportType::Wildcard(path) => {
                self.imports.insert("*".to_string(), path.clone());
            }
            ImportType::Multiple(path, items) => {
                for item in items {
                    let name = item.alias.as_ref().unwrap_or(&item.name);
                    self.imports.insert(name.clone(), path.clone());
                }
            }
        }
        Ok(RuntimeValue::Null)
    }

    /// Execute an export statement
    fn execute_export_statement(&mut self, export: &ExportStatement) -> UmbraResult<RuntimeValue> {
        match &export.export_type {
            ExportType::Function(name) => {
                self.exports.push(name.clone());
            }
            ExportType::Struct(name) => {
                self.exports.push(name.clone());
            }
            ExportType::Variable(name) => {
                self.exports.push(name.clone());
            }
            ExportType::Multiple(names) => {
                self.exports.extend(names.clone());
            }
            ExportType::ReExport(_path) => {
                // Re-exports are handled at compile time
            }
            ExportType::ReExportSymbol(_path, symbol) => {
                self.exports.push(symbol.clone());
            }
            ExportType::ReExportWildcard(_path) => {
                // Wildcard re-exports are handled at compile time
            }
        }
        Ok(RuntimeValue::Null)
    }

    /// Execute a structure definition
    fn execute_structure_definition(&mut self, _structure: &StructureDef) -> UmbraResult<RuntimeValue> {
        // Structure definitions are already processed in first pass
        Ok(RuntimeValue::Null)
    }

    /// Execute a trait definition
    fn execute_trait_definition(&mut self, _trait_def: &TraitDef) -> UmbraResult<RuntimeValue> {
        // Trait definitions are already processed in first pass
        Ok(RuntimeValue::Null)
    }

    /// Execute an implementation
    fn execute_implementation(&mut self, _impl_def: &ImplDef) -> UmbraResult<RuntimeValue> {
        // Implementation definitions are already processed in first pass
        Ok(RuntimeValue::Null)
    }

    /// Execute a query statement
    fn execute_query_statement(&mut self, query: &QueryStatement) -> UmbraResult<RuntimeValue> {
        println!("🗄️ Executing database query: {}", query.query);

        // For now, return a mock result
        // In a real implementation, this would connect to a database
        let mock_result = RuntimeValue::Map(HashMap::from([
            ("status".to_string(), RuntimeValue::String("success".to_string())),
            ("rows_affected".to_string(), RuntimeValue::Integer(1)),
            ("data".to_string(), RuntimeValue::List(vec![
                RuntimeValue::Map(HashMap::from([
                    ("id".to_string(), RuntimeValue::Integer(1)),
                    ("name".to_string(), RuntimeValue::String("Sample".to_string())),
                ]))
            ])),
        ]));

        Ok(mock_result)
    }

    /// Execute a transaction statement
    fn execute_transaction_statement(&mut self, transaction: &TransactionStatement) -> UmbraResult<RuntimeValue> {
        println!("🔄 Starting database transaction");

        let mut result = RuntimeValue::Null;

        // Execute all statements in the transaction
        for statement in &transaction.statements {
            result = self.execute_statement(statement)?;
        }

        println!("✅ Transaction completed successfully");
        Ok(result)
    }

    /// Execute a migration statement
    fn execute_migration_statement(&mut self, migration: &MigrationStatement) -> UmbraResult<RuntimeValue> {
        println!("🔄 Running database migration: {}", migration.name);

        // Execute migration statements
        for statement in &migration.up_statements {
            self.execute_statement(statement)?;
        }

        println!("✅ Migration '{}' completed", migration.name);
        Ok(RuntimeValue::Null)
    }

    /// Execute an error definition
    fn execute_error_definition(&mut self, _error_def: &ErrorDefinition) -> UmbraResult<RuntimeValue> {
        // Error definitions are handled at compile time
        Ok(RuntimeValue::Null)
    }

    /// Execute a method call
    fn execute_method_call(&mut self, method_call: &MethodCall) -> UmbraResult<RuntimeValue> {
        let object = self.execute_expression(&method_call.object)?;
        let mut args = Vec::new();

        for arg in &method_call.arguments {
            args.push(self.execute_expression(arg)?);
        }

        // Handle built-in method calls
        match (&object, method_call.method_name.as_str()) {
            (RuntimeValue::List(list), "len") => {
                Ok(RuntimeValue::Integer(list.len() as i64))
            }
            (RuntimeValue::List(list), "get") => {
                if let Some(RuntimeValue::Integer(index)) = args.first() {
                    if let Some(value) = list.get(*index as usize) {
                        Ok(value.clone())
                    } else {
                        Ok(RuntimeValue::Null)
                    }
                } else {
                    Err(UmbraError::Runtime("List.get() requires integer index".to_string()))
                }
            }
            (RuntimeValue::String(s), "len") => {
                Ok(RuntimeValue::Integer(s.len() as i64))
            }
            (RuntimeValue::String(s), "to_upper") => {
                Ok(RuntimeValue::String(s.to_uppercase()))
            }
            (RuntimeValue::String(s), "to_lower") => {
                Ok(RuntimeValue::String(s.to_lowercase()))
            }
            (RuntimeValue::Map(map), "get") => {
                if let Some(RuntimeValue::String(key)) = args.first() {
                    Ok(map.get(key).cloned().unwrap_or(RuntimeValue::Null))
                } else {
                    Err(UmbraError::Runtime("Map.get() requires string key".to_string()))
                }
            }
            (RuntimeValue::Map(map), "keys") => {
                let keys: Vec<RuntimeValue> = map.keys()
                    .map(|k| RuntimeValue::String(k.clone()))
                    .collect();
                Ok(RuntimeValue::List(keys))
            }
            (RuntimeValue::Map(map), "values") => {
                let values: Vec<RuntimeValue> = map.values().cloned().collect();
                Ok(RuntimeValue::List(values))
            }
            _ => {
                // Try to find method in implementations
                if let RuntimeValue::Struct(fields) = &object {
                    if let Some(type_name) = fields.get("__type__") {
                        if let RuntimeValue::String(type_name) = type_name {
                            return self.call_method(type_name, &method_call.method_name, args);
                        }
                    }
                }
                Err(UmbraError::Runtime(format!(
                    "Method '{}' not found for type '{}'",
                    method_call.method_name,
                    object.type_name()
                )))
            }
        }
    }

    /// Execute a field access
    fn execute_field_access(&mut self, field_access: &FieldAccess) -> UmbraResult<RuntimeValue> {
        let object = self.execute_expression(&field_access.object)?;

        match object {
            RuntimeValue::Struct(fields) => {
                Ok(fields.get(&field_access.field).cloned().unwrap_or(RuntimeValue::Null))
            }
            RuntimeValue::Map(map) => {
                Ok(map.get(&field_access.field).cloned().unwrap_or(RuntimeValue::Null))
            }
            _ => Err(UmbraError::Runtime(format!(
                "Cannot access field '{}' on type '{}'",
                field_access.field,
                object.type_name()
            )))
        }
    }

    /// Execute a struct literal
    fn execute_struct_literal(&mut self, struct_literal: &StructLiteral) -> UmbraResult<RuntimeValue> {
        let mut fields = HashMap::new();

        // Add type information
        fields.insert("__type__".to_string(), RuntimeValue::String(struct_literal.struct_name.clone()));

        // Evaluate field values
        for field_init in &struct_literal.fields {
            let value = self.execute_expression(&field_init.value)?;
            fields.insert(field_init.name.clone(), value);
        }

        Ok(RuntimeValue::Struct(fields))
    }

    /// Execute a match expression
    fn execute_match_expression(&mut self, match_expr: &MatchExpression) -> UmbraResult<RuntimeValue> {
        let value = self.execute_expression(&match_expr.expression)?;

        for arm in &match_expr.arms {
            if self.pattern_matches(&arm.pattern, &value)? {
                return self.execute_expression(&arm.body);
            }
        }

        Err(UmbraError::Runtime("No matching pattern found in match expression".to_string()))
    }

    /// Execute a qualified identifier
    fn execute_qualified_identifier(&mut self, qualified_id: &QualifiedIdentifier) -> UmbraResult<RuntimeValue> {
        let path_str = qualified_id.path.to_string();

        // Try to resolve as imported symbol
        if let Some(_module_path) = self.imports.get(&path_str) {
            // For now, return a placeholder
            Ok(RuntimeValue::String(format!("imported::{}", path_str)))
        } else {
            Err(UmbraError::Runtime(format!("Unresolved qualified identifier: {}", path_str)))
        }
    }

    /// Execute error propagation (? operator)
    fn execute_error_propagation(&mut self, error_prop: &ErrorPropagationExpression) -> UmbraResult<RuntimeValue> {
        let result = self.execute_expression(&error_prop.expression)?;

        // Check if result is an error type
        if let RuntimeValue::Object(type_name, value) = &result {
            if type_name == "Err" {
                // Propagate the error
                return Err(UmbraError::Runtime(format!("Error propagated: {:?}", value)));
            }
        }

        Ok(result)
    }

    /// Call a method on a type
    fn call_method(&mut self, type_name: &str, method_name: &str, args: Vec<RuntimeValue>) -> UmbraResult<RuntimeValue> {
        if let Some(implementations) = self.implementations.get(type_name) {
            for impl_def in implementations {
                for method in &impl_def.methods {
                    if method.name == method_name {
                        // Create a new call frame for the method
                        let mut frame = CallFrame {
                            function_name: format!("{}::{}", type_name, method_name),
                            locals: HashMap::new(),
                            line: 0,
                            column: 0,
                        };

                        // Bind parameters
                        for (i, param) in method.parameters.iter().enumerate() {
                            if let Some(arg) = args.get(i) {
                                frame.locals.insert(param.name.clone(), arg.clone());
                            }
                        }

                        self.call_stack.push(frame);

                        // Execute method body
                        let result = self.execute_block(&method.body);

                        self.call_stack.pop();

                        return result;
                    }
                }
            }
        }

        Err(UmbraError::Runtime(format!("Method '{}' not found for type '{}'", method_name, type_name)))
    }

    /// Check if a pattern matches a value
    fn pattern_matches(&mut self, pattern: &Pattern, value: &RuntimeValue) -> UmbraResult<bool> {
        match pattern {
            Pattern::Literal(literal) => {
                let pattern_value = self.execute_literal(literal)?;
                Ok(pattern_value == *value)
            }
            Pattern::Identifier(name) => {
                // Identifiers always match and bind the value
                self.set_variable(name.clone(), value.clone())?;
                Ok(true)
            }
            Pattern::Wildcard => Ok(true),
            Pattern::Tuple(patterns) => {
                if let RuntimeValue::List(values) = value {
                    if patterns.len() != values.len() {
                        return Ok(false);
                    }
                    for (pattern, value) in patterns.iter().zip(values.iter()) {
                        if !self.pattern_matches(pattern, value)? {
                            return Ok(false);
                        }
                    }
                    Ok(true)
                } else {
                    Ok(false)
                }
            }
            Pattern::Struct(struct_name, fields) => {
                if let RuntimeValue::Struct(struct_fields) = value {
                    // Check if struct type matches
                    if let Some(RuntimeValue::String(type_name)) = struct_fields.get("__type__") {
                        if type_name != struct_name {
                            return Ok(false);
                        }
                    }

                    // Check field patterns
                    for field_pattern in fields {
                        if let Some(field_value) = struct_fields.get(&field_pattern.name) {
                            if !self.pattern_matches(&field_pattern.pattern, field_value)? {
                                return Ok(false);
                            }
                        } else {
                            return Ok(false);
                        }
                    }
                    Ok(true)
                } else {
                    Ok(false)
                }
            }
            Pattern::Some(inner_pattern) => {
                if let RuntimeValue::Object(type_name, inner_value) = value {
                    if type_name == "Some" {
                        self.pattern_matches(inner_pattern, inner_value)
                    } else {
                        Ok(false)
                    }
                } else {
                    Ok(false)
                }
            }
            Pattern::None => {
                matches!(value, RuntimeValue::Object(type_name, _) if type_name == "None")
                    .then(|| true)
                    .ok_or_else(|| UmbraError::Runtime("Pattern matching error".to_string()))
            }
            Pattern::Ok(inner_pattern) => {
                if let RuntimeValue::Object(type_name, inner_value) = value {
                    if type_name == "Ok" {
                        self.pattern_matches(inner_pattern, inner_value)
                    } else {
                        Ok(false)
                    }
                } else {
                    Ok(false)
                }
            }
            Pattern::Err(inner_pattern) => {
                if let RuntimeValue::Object(type_name, inner_value) = value {
                    if type_name == "Err" {
                        self.pattern_matches(inner_pattern, inner_value)
                    } else {
                        Ok(false)
                    }
                } else {
                    Ok(false)
                }
            }
            Pattern::Union(inner_pattern, variant_index) => {
                // For union patterns, check if the value matches the expected variant
                if let RuntimeValue::Object(type_name, inner_value) = value {
                    // In a real implementation, we'd check the variant index
                    // For now, just match the inner pattern
                    self.pattern_matches(inner_pattern, inner_value)
                } else {
                    Ok(false)
                }
            }
        }
    }
}

impl Default for Runtime {
    fn default() -> Self {
        Self::new()
    }
}

// Re-export AI/ML runtime functions
pub use ai_ml_runtime::{
    umbra_load_dataset,
    umbra_create_model,
    umbra_train_model,
    umbra_evaluate_model,
    umbra_predict_sample,
    umbra_visualize_metric,
};
