/// Runtime system for Umbra
/// 
/// This module provides the runtime infrastructure for executing Umbra programs,
/// including memory management, exception handling, and built-in function execution.

pub mod exceptions;
pub mod memory;
pub mod builtins;
pub mod ai_ml_runtime;

use crate::error::{UmbraError, UmbraResult};
use crate::parser::ast::*;
use exceptions::ExceptionHandler;
use std::collections::HashMap;

/// Runtime value representation
#[derive(Debug, <PERSON>lone, PartialEq)]
pub enum RuntimeValue {
    Integer(i64),
    Float(f64),
    String(String),
    Boolean(bool),
    List(Vec<RuntimeValue>),
    Map(HashMap<String, RuntimeValue>),
    Set(std::collections::HashSet<String>),
    Struct(HashMap<String, RuntimeValue>),
    Function(String), // Function name
    Object(String, Box<RuntimeValue>), // Object type and data
    Null,
}

impl RuntimeValue {
    pub fn type_name(&self) -> &'static str {
        match self {
            RuntimeValue::Integer(_) => "Integer",
            RuntimeValue::Float(_) => "Float",
            RuntimeValue::String(_) => "String",
            RuntimeValue::Boolean(_) => "Boolean",
            RuntimeValue::List(_) => "List",
            RuntimeValue::Map(_) => "Map",
            RuntimeValue::Set(_) => "Set",
            RuntimeValue::Struct(_) => "Struct",
            RuntimeValue::Function(_) => "Function",
            RuntimeValue::Object(_, _) => "Object",
            RuntimeValue::Null => "Null",
        }
    }

    pub fn to_string(&self) -> String {
        match self {
            RuntimeValue::Integer(i) => i.to_string(),
            RuntimeValue::Float(f) => f.to_string(),
            RuntimeValue::String(s) => s.clone(),
            RuntimeValue::Boolean(b) => b.to_string(),
            RuntimeValue::List(list) => {
                let items: Vec<String> = list.iter().map(|v| v.to_string()).collect();
                format!("[{}]", items.join(", "))
            }
            RuntimeValue::Map(map) => {
                let items: Vec<String> = map.iter()
                    .map(|(k, v)| format!("{}: {}", k, v.to_string()))
                    .collect();
                format!("{{{}}}", items.join(", "))
            }
            RuntimeValue::Set(set) => {
                let items: Vec<String> = set.iter().cloned().collect();
                format!("{{{}}}", items.join(", "))
            }
            RuntimeValue::Struct(fields) => {
                let items: Vec<String> = fields.iter()
                    .map(|(k, v)| format!("{}: {}", k, v.to_string()))
                    .collect();
                format!("struct {{{}}}", items.join(", "))
            }
            RuntimeValue::Function(name) => format!("function {}", name),
            RuntimeValue::Object(type_name, data) => format!("{}({})", type_name, data.to_string()),
            RuntimeValue::Null => "null".to_string(),
        }
    }

    pub fn is_truthy(&self) -> bool {
        match self {
            RuntimeValue::Boolean(b) => *b,
            RuntimeValue::Integer(i) => *i != 0,
            RuntimeValue::Float(f) => *f != 0.0,
            RuntimeValue::String(s) => !s.is_empty(),
            RuntimeValue::List(list) => !list.is_empty(),
            RuntimeValue::Map(map) => !map.is_empty(),
            RuntimeValue::Set(set) => !set.is_empty(),
            RuntimeValue::Struct(fields) => !fields.is_empty(),
            RuntimeValue::Null => false,
            RuntimeValue::Function(_) => true,
            RuntimeValue::Object(_, _) => true,
        }
    }
}

/// Runtime environment for executing Umbra programs
pub struct Runtime {
    /// Exception handler
    pub exception_handler: ExceptionHandler,
    /// Global variables
    pub globals: HashMap<String, RuntimeValue>,
    /// Function definitions
    pub functions: HashMap<String, FunctionDef>,
    /// Built-in functions
    pub builtins: HashMap<String, Box<dyn Fn(Vec<RuntimeValue>) -> UmbraResult<RuntimeValue>>>,
    /// Current call stack
    pub call_stack: Vec<CallFrame>,
    /// Memory manager
    pub memory: memory::MemoryManager,
    /// Return flag for early returns
    pub return_flag: bool,
    /// Return value for early returns
    pub return_value: RuntimeValue,
}

/// Call frame for function calls
#[derive(Debug, Clone)]
pub struct CallFrame {
    pub function_name: String,
    pub locals: HashMap<String, RuntimeValue>,
    pub line: usize,
    pub column: usize,
}

impl Runtime {
    pub fn new() -> Self {
        Self {
            exception_handler: ExceptionHandler::new(),
            globals: HashMap::new(),
            functions: HashMap::new(),
            builtins: HashMap::new(),
            call_stack: Vec::new(),
            memory: memory::MemoryManager::new(),
            return_flag: false,
            return_value: RuntimeValue::Null,
        }
    }

    /// Execute a program
    pub fn execute_program(&mut self, program: &Program) -> UmbraResult<()> {
        // First pass: collect function definitions
        for statement in &program.statements {
            if let Statement::Function(func) = statement {
                self.functions.insert(func.name.clone(), func.clone());
            }
        }

        // Second pass: execute statements
        for statement in &program.statements {
            match statement {
                Statement::Function(_) => {
                    // Already processed
                }
                _ => {
                    self.execute_statement(statement)?;
                }
            }
        }

        // Execute main function if it exists
        if self.functions.contains_key("main") {
            self.call_function("main", Vec::new())?;
        }

        Ok(())
    }

    /// Execute a statement
    pub fn execute_statement(&mut self, statement: &Statement) -> UmbraResult<RuntimeValue> {
        match statement {
            Statement::Variable(var) => self.execute_variable_declaration(var),
            Statement::Assignment(assign) => self.execute_assignment(assign),
            Statement::Expression(expr) => self.execute_expression(&expr.expression),
            Statement::When(when) => {
                self.execute_when_statement(when)
            },
            Statement::Repeat(repeat) => {
                self.execute_repeat_statement(repeat)
            },
            Statement::Return(ret) => {
                self.execute_return_statement(ret)
            },
            Statement::Try(try_stmt) => self.execute_try_statement(try_stmt),
            Statement::Throw(throw) => self.execute_throw_statement(throw),
            Statement::Panic(panic) => self.execute_panic_statement(panic),
            Statement::Train(train) => self.execute_train_statement(train),
            Statement::Evaluate(evaluate) => self.execute_evaluate_statement(evaluate),
            Statement::Predict(predict) => self.execute_predict_statement(predict),
            Statement::Visualize(visualize) => self.execute_visualize_statement(visualize),
            _ => Ok(RuntimeValue::Null),
        }
    }

    /// Execute a variable declaration
    fn execute_variable_declaration(&mut self, var: &VariableDecl) -> UmbraResult<RuntimeValue> {
        let value = self.execute_expression(&var.value)?;
        
        if self.call_stack.is_empty() {
            self.globals.insert(var.name.clone(), value);
        } else {
            let frame = self.call_stack.last_mut().unwrap();
            frame.locals.insert(var.name.clone(), value);
        }
        
        Ok(RuntimeValue::Null)
    }

    /// Execute an assignment
    fn execute_assignment(&mut self, assign: &Assignment) -> UmbraResult<RuntimeValue> {
        let value = self.execute_expression(&assign.value)?;
        
        // Try to find variable in local scope first, then global
        if let Some(frame) = self.call_stack.last_mut() {
            if frame.locals.contains_key(&assign.name) {
                frame.locals.insert(assign.name.clone(), value);
                return Ok(RuntimeValue::Null);
            }
        }
        
        self.globals.insert(assign.name.clone(), value);
        Ok(RuntimeValue::Null)
    }

    /// Execute an expression
    pub fn execute_expression(&mut self, expr: &Expression) -> UmbraResult<RuntimeValue> {
        match expr {
            Expression::Literal(lit) => self.execute_literal(lit),
            Expression::Identifier(id) => self.execute_identifier(id),
            Expression::Binary(bin) => {
                self.execute_binary_expression(bin)
            },
            Expression::Unary(un) => {
                self.execute_unary_expression(un)
            },
            Expression::Call(call) => {
                self.execute_function_call(call)
            },
            Expression::List(list) => {
                self.execute_list_expression(list)
            },
            Expression::IndexAccess(index) => {
                self.execute_index_access(index)
            },
            Expression::TryExpression(try_expr) => {
                self.execute_try_expression(try_expr)
            },
            _ => Ok(RuntimeValue::Null),
        }
    }

    /// Execute a literal expression
    fn execute_literal(&self, literal: &Literal) -> UmbraResult<RuntimeValue> {
        match literal {
            Literal::Integer(i) => Ok(RuntimeValue::Integer(*i)),
            Literal::Float(f) => Ok(RuntimeValue::Float(*f)),
            Literal::String(s) => Ok(RuntimeValue::String(s.clone())),
            Literal::Boolean(b) => Ok(RuntimeValue::Boolean(*b)),
        }
    }

    /// Execute an identifier expression
    fn execute_identifier(&self, identifier: &Identifier) -> UmbraResult<RuntimeValue> {
        let name = &identifier.name;

        // Try local scope first
        if let Some(frame) = self.call_stack.last() {
            if let Some(value) = frame.locals.get(name) {
                return Ok(value.clone());
            }
        }

        // Try global scope
        if let Some(value) = self.globals.get(name) {
            return Ok(value.clone());
        }

        // Variable not found
        Err(UmbraError::Runtime(format!("Undefined variable: {}", name)))
    }

    /// Execute a function call
    fn execute_function_call(&mut self, call: &crate::parser::ast::FunctionCall) -> UmbraResult<RuntimeValue> {
        // Evaluate arguments
        let mut args = Vec::new();
        for arg in &call.arguments {
            args.push(self.execute_expression(arg)?);
        }

        // Handle AI/ML builtin functions
        match call.name.as_str() {
            "load_dataset" => {
                if args.len() != 1 {
                    return Err(UmbraError::Runtime("load_dataset expects 1 argument".to_string()));
                }
                match &args[0] {
                    RuntimeValue::String(file_path) => {
                        use crate::runtime::ai_ml_runtime::umbra_load_dataset;
                        umbra_load_dataset(file_path)
                    },
                    _ => Err(UmbraError::Runtime("load_dataset expects string argument".to_string()))
                }
            },
            "create_model" => {
                if args.len() != 1 {
                    return Err(UmbraError::Runtime("create_model expects 1 argument".to_string()));
                }
                match &args[0] {
                    RuntimeValue::String(model_type) => {
                        use crate::runtime::ai_ml_runtime::umbra_create_model;
                        umbra_create_model(model_type)
                    },
                    _ => Err(UmbraError::Runtime("create_model expects string argument".to_string()))
                }
            },
            "show" => {
                // Handle show function
                for arg in &args {
                    print!("{}", arg.to_string());
                }
                println!();
                Ok(RuntimeValue::Null)
            },
            _ => {
                // Try to call user-defined function
                self.call_function(&call.name, args)
            }
        }
    }

    /// Execute a train statement
    fn execute_train_statement(&mut self, train: &crate::parser::ast::TrainStatement) -> UmbraResult<RuntimeValue> {
        // Get model and dataset from variables by name
        let model_name = &train.model;
        let dataset_name = &train.dataset;

        let model = if let Some(frame) = self.call_stack.last() {
            frame.locals.get(model_name).cloned()
        } else {
            self.globals.get(model_name).cloned()
        }.ok_or_else(|| UmbraError::Runtime(format!("Undefined variable: {}", model_name)))?;

        let dataset = if let Some(frame) = self.call_stack.last() {
            frame.locals.get(dataset_name).cloned()
        } else {
            self.globals.get(dataset_name).cloned()
        }.ok_or_else(|| UmbraError::Runtime(format!("Undefined variable: {}", dataset_name)))?;

        // Convert config to HashMap
        let mut config = std::collections::HashMap::new();
        for param in &train.config {
            config.insert(param.name.clone(), self.execute_expression(&param.value)?);
        }

        use crate::runtime::ai_ml_runtime::umbra_train_model;
        umbra_train_model(&model, &dataset, &config)
    }

    /// Execute an evaluate statement
    fn execute_evaluate_statement(&mut self, evaluate: &crate::parser::ast::EvaluateStatement) -> UmbraResult<RuntimeValue> {
        let model_name = &evaluate.model;
        let dataset_name = &evaluate.dataset;

        let model = if let Some(frame) = self.call_stack.last() {
            frame.locals.get(model_name).cloned()
        } else {
            self.globals.get(model_name).cloned()
        }.ok_or_else(|| UmbraError::Runtime(format!("Undefined variable: {}", model_name)))?;

        let dataset = if let Some(frame) = self.call_stack.last() {
            frame.locals.get(dataset_name).cloned()
        } else {
            self.globals.get(dataset_name).cloned()
        }.ok_or_else(|| UmbraError::Runtime(format!("Undefined variable: {}", dataset_name)))?;

        use crate::runtime::ai_ml_runtime::umbra_evaluate_model;
        umbra_evaluate_model(&model, &dataset)
    }

    /// Execute a predict statement
    fn execute_predict_statement(&mut self, predict: &crate::parser::ast::PredictStatement) -> UmbraResult<RuntimeValue> {
        let sample_str = &predict.sample;  // sample is already a String
        let model_name = &predict.model;

        let model = if let Some(frame) = self.call_stack.last() {
            frame.locals.get(model_name).cloned()
        } else {
            self.globals.get(model_name).cloned()
        }.ok_or_else(|| UmbraError::Runtime(format!("Undefined variable: {}", model_name)))?;

        use crate::runtime::ai_ml_runtime::umbra_predict_sample;
        umbra_predict_sample(sample_str, &model)
    }

    /// Execute a visualize statement
    fn execute_visualize_statement(&mut self, visualize: &crate::parser::ast::VisualizeStatement) -> UmbraResult<RuntimeValue> {
        let metric_str = &visualize.metric;      // metric is already a String
        let dimension_str = &visualize.dimension; // dimension is already a String

        use crate::runtime::ai_ml_runtime::umbra_visualize_metric;
        umbra_visualize_metric(metric_str, dimension_str)
    }

    /// Execute a try statement
    fn execute_try_statement(&mut self, try_stmt: &TryStatement) -> UmbraResult<RuntimeValue> {
        self.exception_handler.enter_try_block();
        
        let mut result = RuntimeValue::Null;
        let mut exception_occurred = false;
        
        // Execute try block
        for statement in &try_stmt.try_block {
            match self.execute_statement(statement) {
                Ok(value) => result = value,
                Err(error) => {
                    exception_occurred = true;
                    // Try to handle with catch blocks
                    let handled = self.handle_exception_in_catch_blocks(&try_stmt.catch_clauses, error)?;
                    if !handled {
                        self.exception_handler.exit_try_block()?;
                        return Err(UmbraError::Runtime("Unhandled exception".to_string()));
                    }
                    break;
                }
            }
        }
        
        // Execute finally block if present
        if let Some(finally_block) = &try_stmt.finally_block {
            for statement in finally_block {
                self.execute_statement(statement)?;
            }
        }
        
        self.exception_handler.exit_try_block()?;
        Ok(result)
    }

    /// Handle exception in catch blocks
    fn handle_exception_in_catch_blocks(
        &mut self,
        catch_clauses: &[CatchClause],
        error: UmbraError,
    ) -> UmbraResult<bool> {
        for catch_clause in catch_clauses {
            // Check if this catch clause handles the error type
            if self.error_matches_catch_clause(&error, catch_clause) {
                // Bind error variable if specified
                if let Some(var_name) = &catch_clause.error_variable {
                    let error_value = RuntimeValue::String(error.to_string());
                    if let Some(frame) = self.call_stack.last_mut() {
                        frame.locals.insert(var_name.clone(), error_value);
                    } else {
                        self.globals.insert(var_name.clone(), error_value);
                    }
                }
                
                // Execute catch block
                for statement in &catch_clause.catch_block {
                    self.execute_statement(statement)?;
                }
                
                return Ok(true); // Exception handled
            }
        }
        
        Ok(false) // Exception not handled
    }

    /// Check if an error matches a catch clause
    fn error_matches_catch_clause(&self, error: &UmbraError, catch_clause: &CatchClause) -> bool {
        match &catch_clause.error_type {
            None => true, // Catch-all
            Some(error_type) => {
                // Match error type with catch clause type
                match error {
                    UmbraError::Runtime(_) => match error_type {
                        crate::parser::ast::Type::Basic(crate::parser::ast::BasicType::String) => true,
                        _ => false,
                    },
                    UmbraError::Type { .. } => match error_type {
                        crate::parser::ast::Type::Basic(crate::parser::ast::BasicType::String) => true,
                        _ => false,
                    },
                    UmbraError::Io(_) => match error_type {
                        crate::parser::ast::Type::Basic(crate::parser::ast::BasicType::String) => true,
                        _ => false,
                    },
                    UmbraError::Memory(_) => match error_type {
                        crate::parser::ast::Type::Basic(crate::parser::ast::BasicType::String) => true,
                        _ => false,
                    },
                    _ => true, // Generic exception
                }
            }
        }
    }

    /// Execute a throw statement
    fn execute_throw_statement(&mut self, throw_stmt: &ThrowStatement) -> UmbraResult<RuntimeValue> {
        let error_value = self.execute_expression(&throw_stmt.error_expression)?;
        let error_message = error_value.to_string();
        
        let exception = self.exception_handler.create_builtin_exception(
            "RuntimeError",
            error_message,
            throw_stmt.location.line,
            throw_stmt.location.column,
        );
        
        self.exception_handler.throw_exception(exception)?;
        Ok(RuntimeValue::Null)
    }

    /// Execute a panic statement
    fn execute_panic_statement(&mut self, panic_stmt: &PanicStatement) -> UmbraResult<RuntimeValue> {
        let message = if let Some(msg_expr) = &panic_stmt.message {
            self.execute_expression(msg_expr)?.to_string()
        } else {
            "Panic occurred".to_string()
        };
        
        Err(UmbraError::Runtime(format!("PANIC: {}", message)))
    }

    /// Call a function
    pub fn call_function(&mut self, name: &str, args: Vec<RuntimeValue>) -> UmbraResult<RuntimeValue> {
        // Check for built-in functions first
        if let Some(builtin) = self.builtins.get(name) {
            // Call built-in function
            return builtin(args);
        }

        if let Some(func) = self.functions.get(name).cloned() {
            // Validate argument count
            if args.len() != func.parameters.len() {
                return Err(UmbraError::Runtime(format!(
                    "Function '{}' expects {} arguments, got {}",
                    name, func.parameters.len(), args.len()
                )));
            }

            // Create new call frame
            let mut frame = CallFrame {
                function_name: name.to_string(),
                locals: HashMap::new(),
                line: func.location.line,
                column: func.location.column,
            };

            // Bind parameters
            for (param, arg) in func.parameters.iter().zip(args.iter()) {
                frame.locals.insert(param.name.clone(), arg.clone());
            }

            self.call_stack.push(frame);

            // Clear return flag before executing function
            self.return_flag = false;
            self.return_value = RuntimeValue::Null;

            // Execute function body
            let result = self.execute_block(&func.body);

            // Check if function returned early
            let final_result = if self.return_flag {
                self.return_value.clone()
            } else {
                result.unwrap_or(RuntimeValue::Null)
            };

            // Clean up call stack
            self.call_stack.pop();

            // Reset return flag
            self.return_flag = false;
            self.return_value = RuntimeValue::Null;

            Ok(final_result)
        } else {
            Err(UmbraError::Runtime(format!("Undefined function: {}", name)))
        }
    }

    // Add missing methods for REPL
    pub fn set_variable(&mut self, name: &str, value: RuntimeValue) -> UmbraResult<()> {
        if let Some(frame) = self.call_stack.last_mut() {
            frame.locals.insert(name.to_string(), value);
        } else {
            self.globals.insert(name.to_string(), value);
        }
        Ok(())
    }

    pub fn add_builtin_function<F>(&mut self, name: &str, func: F) -> UmbraResult<()>
    where
        F: Fn(Vec<RuntimeValue>) -> UmbraResult<RuntimeValue> + 'static,
    {
        // Store the function in the builtins map
        self.builtins.insert(name.to_string(), Box::new(func));
        Ok(())
    }

    pub fn define_function(&mut self, func_stmt: &crate::parser::ast::FunctionDef) -> UmbraResult<()> {
        // TODO: Implement function definition
        self.functions.insert(func_stmt.name.clone(), func_stmt.clone());
        Ok(())
    }

    pub fn import_module(&mut self, _import_stmt: &crate::parser::ast::ImportStatement) -> UmbraResult<()> {
        // TODO: Implement module import
        Ok(())
    }

    /// Execute a binary expression
    fn execute_binary_expression(&mut self, bin: &crate::parser::ast::BinaryOp) -> UmbraResult<RuntimeValue> {
        let left = self.execute_expression(&bin.left)?;
        let right = self.execute_expression(&bin.right)?;

        use crate::parser::ast::BinaryOperator;
        match bin.operator {
            BinaryOperator::Add => self.add_values(left, right),
            BinaryOperator::Subtract => self.subtract_values(left, right),
            BinaryOperator::Multiply => self.multiply_values(left, right),
            BinaryOperator::Divide => self.divide_values(left, right),
            BinaryOperator::Modulo => self.modulo_values(left, right),
            BinaryOperator::Equal => Ok(RuntimeValue::Boolean(self.values_equal(&left, &right))),
            BinaryOperator::NotEqual => Ok(RuntimeValue::Boolean(!self.values_equal(&left, &right))),
            BinaryOperator::Less => self.compare_values(left, right, |a, b| a < b),
            BinaryOperator::LessEqual => self.compare_values(left, right, |a, b| a <= b),
            BinaryOperator::Greater => self.compare_values(left, right, |a, b| a > b),
            BinaryOperator::GreaterEqual => self.compare_values(left, right, |a, b| a >= b),
            BinaryOperator::And => self.logical_and(left, right),
            BinaryOperator::Or => self.logical_or(left, right),
        }
    }

    /// Execute a unary expression
    fn execute_unary_expression(&mut self, un: &crate::parser::ast::UnaryOp) -> UmbraResult<RuntimeValue> {
        let operand = self.execute_expression(&un.operand)?;

        use crate::parser::ast::UnaryOperator;
        match un.operator {
            UnaryOperator::Minus => match operand {
                RuntimeValue::Integer(i) => Ok(RuntimeValue::Integer(-i)),
                RuntimeValue::Float(f) => Ok(RuntimeValue::Float(-f)),
                _ => Err(UmbraError::Runtime("Unary minus can only be applied to numbers".to_string())),
            },
            UnaryOperator::Not => match operand {
                RuntimeValue::Boolean(b) => Ok(RuntimeValue::Boolean(!b)),
                RuntimeValue::Null => Ok(RuntimeValue::Boolean(true)),
                _ => Ok(RuntimeValue::Boolean(false)),
            },
        }
    }

    /// Add two runtime values
    fn add_values(&self, left: RuntimeValue, right: RuntimeValue) -> UmbraResult<RuntimeValue> {
        match (left, right) {
            (RuntimeValue::Integer(a), RuntimeValue::Integer(b)) => Ok(RuntimeValue::Integer(a + b)),
            (RuntimeValue::Float(a), RuntimeValue::Float(b)) => Ok(RuntimeValue::Float(a + b)),
            (RuntimeValue::Integer(a), RuntimeValue::Float(b)) => Ok(RuntimeValue::Float(a as f64 + b)),
            (RuntimeValue::Float(a), RuntimeValue::Integer(b)) => Ok(RuntimeValue::Float(a + b as f64)),
            (RuntimeValue::String(a), RuntimeValue::String(b)) => Ok(RuntimeValue::String(a + &b)),
            (RuntimeValue::List(mut a), RuntimeValue::List(b)) => {
                a.extend(b);
                Ok(RuntimeValue::List(a))
            },
            _ => Err(UmbraError::Runtime("Cannot add these types".to_string())),
        }
    }

    /// Subtract two runtime values
    fn subtract_values(&self, left: RuntimeValue, right: RuntimeValue) -> UmbraResult<RuntimeValue> {
        match (left, right) {
            (RuntimeValue::Integer(a), RuntimeValue::Integer(b)) => Ok(RuntimeValue::Integer(a - b)),
            (RuntimeValue::Float(a), RuntimeValue::Float(b)) => Ok(RuntimeValue::Float(a - b)),
            (RuntimeValue::Integer(a), RuntimeValue::Float(b)) => Ok(RuntimeValue::Float(a as f64 - b)),
            (RuntimeValue::Float(a), RuntimeValue::Integer(b)) => Ok(RuntimeValue::Float(a - b as f64)),
            _ => Err(UmbraError::Runtime("Cannot subtract these types".to_string())),
        }
    }

    /// Multiply two runtime values
    fn multiply_values(&self, left: RuntimeValue, right: RuntimeValue) -> UmbraResult<RuntimeValue> {
        match (left, right) {
            (RuntimeValue::Integer(a), RuntimeValue::Integer(b)) => Ok(RuntimeValue::Integer(a * b)),
            (RuntimeValue::Float(a), RuntimeValue::Float(b)) => Ok(RuntimeValue::Float(a * b)),
            (RuntimeValue::Integer(a), RuntimeValue::Float(b)) => Ok(RuntimeValue::Float(a as f64 * b)),
            (RuntimeValue::Float(a), RuntimeValue::Integer(b)) => Ok(RuntimeValue::Float(a * b as f64)),
            (RuntimeValue::String(s), RuntimeValue::Integer(n)) => {
                if n < 0 {
                    return Err(UmbraError::Runtime("Cannot multiply string by negative number".to_string()));
                }
                Ok(RuntimeValue::String(s.repeat(n as usize)))
            },
            _ => Err(UmbraError::Runtime("Cannot multiply these types".to_string())),
        }
    }

    /// Divide two runtime values
    fn divide_values(&self, left: RuntimeValue, right: RuntimeValue) -> UmbraResult<RuntimeValue> {
        match (left, right) {
            (RuntimeValue::Integer(a), RuntimeValue::Integer(b)) => {
                if b == 0 {
                    return Err(UmbraError::Runtime("Division by zero".to_string()));
                }
                Ok(RuntimeValue::Float(a as f64 / b as f64))
            },
            (RuntimeValue::Float(a), RuntimeValue::Float(b)) => {
                if b == 0.0 {
                    return Err(UmbraError::Runtime("Division by zero".to_string()));
                }
                Ok(RuntimeValue::Float(a / b))
            },
            (RuntimeValue::Integer(a), RuntimeValue::Float(b)) => {
                if b == 0.0 {
                    return Err(UmbraError::Runtime("Division by zero".to_string()));
                }
                Ok(RuntimeValue::Float(a as f64 / b))
            },
            (RuntimeValue::Float(a), RuntimeValue::Integer(b)) => {
                if b == 0 {
                    return Err(UmbraError::Runtime("Division by zero".to_string()));
                }
                Ok(RuntimeValue::Float(a / b as f64))
            },
            _ => Err(UmbraError::Runtime("Cannot divide these types".to_string())),
        }
    }

    /// Modulo operation on two runtime values
    fn modulo_values(&self, left: RuntimeValue, right: RuntimeValue) -> UmbraResult<RuntimeValue> {
        match (left, right) {
            (RuntimeValue::Integer(a), RuntimeValue::Integer(b)) => {
                if b == 0 {
                    return Err(UmbraError::Runtime("Modulo by zero".to_string()));
                }
                Ok(RuntimeValue::Integer(a % b))
            },
            (RuntimeValue::Float(a), RuntimeValue::Float(b)) => {
                if b == 0.0 {
                    return Err(UmbraError::Runtime("Modulo by zero".to_string()));
                }
                Ok(RuntimeValue::Float(a % b))
            },
            _ => Err(UmbraError::Runtime("Cannot perform modulo on these types".to_string())),
        }
    }

    /// Check if two values are equal
    fn values_equal(&self, left: &RuntimeValue, right: &RuntimeValue) -> bool {
        match (left, right) {
            (RuntimeValue::Integer(a), RuntimeValue::Integer(b)) => a == b,
            (RuntimeValue::Float(a), RuntimeValue::Float(b)) => (a - b).abs() < f64::EPSILON,
            (RuntimeValue::Integer(a), RuntimeValue::Float(b)) => (*a as f64 - b).abs() < f64::EPSILON,
            (RuntimeValue::Float(a), RuntimeValue::Integer(b)) => (a - *b as f64).abs() < f64::EPSILON,
            (RuntimeValue::String(a), RuntimeValue::String(b)) => a == b,
            (RuntimeValue::Boolean(a), RuntimeValue::Boolean(b)) => a == b,
            (RuntimeValue::Null, RuntimeValue::Null) => true,
            _ => false,
        }
    }

    /// Compare two values using a comparison function
    fn compare_values<F>(&self, left: RuntimeValue, right: RuntimeValue, cmp: F) -> UmbraResult<RuntimeValue>
    where
        F: Fn(f64, f64) -> bool,
    {
        let (left_num, right_num) = match (left, right) {
            (RuntimeValue::Integer(a), RuntimeValue::Integer(b)) => (a as f64, b as f64),
            (RuntimeValue::Float(a), RuntimeValue::Float(b)) => (a, b),
            (RuntimeValue::Integer(a), RuntimeValue::Float(b)) => (a as f64, b),
            (RuntimeValue::Float(a), RuntimeValue::Integer(b)) => (a, b as f64),
            _ => return Err(UmbraError::Runtime("Cannot compare these types".to_string())),
        };

        Ok(RuntimeValue::Boolean(cmp(left_num, right_num)))
    }

    /// Logical AND operation
    fn logical_and(&self, left: RuntimeValue, right: RuntimeValue) -> UmbraResult<RuntimeValue> {
        let left_bool = self.is_truthy(&left);
        if !left_bool {
            Ok(RuntimeValue::Boolean(false))
        } else {
            Ok(RuntimeValue::Boolean(self.is_truthy(&right)))
        }
    }

    /// Logical OR operation
    fn logical_or(&self, left: RuntimeValue, right: RuntimeValue) -> UmbraResult<RuntimeValue> {
        let left_bool = self.is_truthy(&left);
        if left_bool {
            Ok(RuntimeValue::Boolean(true))
        } else {
            Ok(RuntimeValue::Boolean(self.is_truthy(&right)))
        }
    }



    /// Check if a value is truthy
    fn is_truthy(&self, value: &RuntimeValue) -> bool {
        match value {
            RuntimeValue::Boolean(b) => *b,
            RuntimeValue::Null => false,
            RuntimeValue::Integer(i) => *i != 0,
            RuntimeValue::Float(f) => *f != 0.0,
            RuntimeValue::String(s) => !s.is_empty(),
            RuntimeValue::List(l) => !l.is_empty(),
            RuntimeValue::Map(m) => !m.is_empty(),
            RuntimeValue::Set(s) => !s.is_empty(),
            _ => true,
        }
    }

    /// Execute a list expression
    fn execute_list_expression(&mut self, list: &crate::parser::ast::ListLiteral) -> UmbraResult<RuntimeValue> {
        let mut values = Vec::new();
        for element in &list.elements {
            values.push(self.execute_expression(element)?);
        }
        Ok(RuntimeValue::List(values))
    }

    /// Execute index access
    fn execute_index_access(&mut self, index: &crate::parser::ast::IndexAccess) -> UmbraResult<RuntimeValue> {
        let object = self.execute_expression(&index.object)?;
        let index_value = self.execute_expression(&index.index)?;

        match (object, index_value) {
            (RuntimeValue::List(list), RuntimeValue::Integer(i)) => {
                let idx = if i < 0 {
                    // Negative indexing from the end
                    list.len() as i64 + i
                } else {
                    i
                };

                if idx < 0 || idx >= list.len() as i64 {
                    return Err(UmbraError::Runtime(format!("List index {} out of bounds", i)));
                }

                Ok(list[idx as usize].clone())
            },
            (RuntimeValue::String(s), RuntimeValue::Integer(i)) => {
                let chars: Vec<char> = s.chars().collect();
                let idx = if i < 0 {
                    // Negative indexing from the end
                    chars.len() as i64 + i
                } else {
                    i
                };

                if idx < 0 || idx >= chars.len() as i64 {
                    return Err(UmbraError::Runtime(format!("String index {} out of bounds", i)));
                }

                Ok(RuntimeValue::String(chars[idx as usize].to_string()))
            },
            (RuntimeValue::Map(map), RuntimeValue::String(key)) => {
                Ok(map.get(&key).cloned().unwrap_or(RuntimeValue::Null))
            },
            _ => Err(UmbraError::Runtime("Invalid index access".to_string())),
        }
    }

    /// Execute a when statement (conditional)
    fn execute_when_statement(&mut self, when: &crate::parser::ast::WhenStatement) -> UmbraResult<RuntimeValue> {
        let condition = self.execute_expression(&when.condition)?;

        if self.is_truthy(&condition) {
            self.execute_block(&when.then_body)
        } else if let Some(else_body) = &when.else_body {
            self.execute_block(else_body)
        } else {
            Ok(RuntimeValue::Null)
        }
    }

    /// Execute a repeat statement (for-in loop)
    fn execute_repeat_statement(&mut self, repeat: &crate::parser::ast::RepeatStatement) -> UmbraResult<RuntimeValue> {
        let iterable = self.execute_expression(&repeat.iterable)?;
        let mut last_result = RuntimeValue::Null;

        match iterable {
            RuntimeValue::List(list) => {
                for item in list {
                    // Set the loop variable
                    if let Some(frame) = self.call_stack.last_mut() {
                        frame.locals.insert(repeat.variable.clone(), item);
                    } else {
                        self.globals.insert(repeat.variable.clone(), item);
                    }

                    // Execute the body
                    last_result = self.execute_block(&repeat.body)?;
                }
            },
            RuntimeValue::String(s) => {
                for ch in s.chars() {
                    let char_value = RuntimeValue::String(ch.to_string());

                    // Set the loop variable
                    if let Some(frame) = self.call_stack.last_mut() {
                        frame.locals.insert(repeat.variable.clone(), char_value);
                    } else {
                        self.globals.insert(repeat.variable.clone(), char_value);
                    }

                    // Execute the body
                    last_result = self.execute_block(&repeat.body)?;
                }
            },
            _ => return Err(UmbraError::Runtime("Cannot iterate over this type".to_string())),
        }

        Ok(last_result)
    }

    /// Execute a return statement
    fn execute_return_statement(&mut self, ret: &crate::parser::ast::ReturnStatement) -> UmbraResult<RuntimeValue> {
        let return_value = if let Some(value) = &ret.value {
            self.execute_expression(value)?
        } else {
            RuntimeValue::Null
        };

        // Set return flag and value for call stack unwinding
        self.return_flag = true;
        self.return_value = return_value.clone();

        Ok(return_value)
    }

    /// Execute a block of statements
    fn execute_block(&mut self, statements: &[crate::parser::ast::Statement]) -> UmbraResult<RuntimeValue> {
        let mut last_result = RuntimeValue::Null;

        for statement in statements {
            last_result = self.execute_statement(statement)?;

            // Check for early return
            if self.return_flag {
                break;
            }
        }

        Ok(last_result)
    }

    /// Check if a pattern matches a value
    fn pattern_matches(&self, pattern: &crate::parser::ast::Pattern, value: &RuntimeValue) -> UmbraResult<bool> {
        use crate::parser::ast::Pattern;

        match pattern {
            Pattern::Literal(lit) => {
                let pattern_value = match lit {
                    crate::parser::ast::Literal::Integer(i) => RuntimeValue::Integer(*i),
                    crate::parser::ast::Literal::Float(f) => RuntimeValue::Float(*f),
                    crate::parser::ast::Literal::String(s) => RuntimeValue::String(s.clone()),
                    crate::parser::ast::Literal::Boolean(b) => RuntimeValue::Boolean(*b),
                };
                Ok(self.values_equal(&pattern_value, value))
            },
            Pattern::Identifier(_) => {
                // Variable patterns always match and bind the value
                Ok(true)
            },
            Pattern::Wildcard => Ok(true),
            Pattern::None => {
                // Match null/none values
                Ok(matches!(value, RuntimeValue::Null))
            },
            Pattern::Some(inner_pattern) => {
                // Match non-null values with inner pattern
                if matches!(value, RuntimeValue::Null) {
                    Ok(false)
                } else {
                    self.pattern_matches(inner_pattern, value)
                }
            },
            Pattern::Ok(inner_pattern) => {
                // For now, treat as regular pattern matching
                self.pattern_matches(inner_pattern, value)
            },
            Pattern::Err(inner_pattern) => {
                // For now, treat as regular pattern matching
                self.pattern_matches(inner_pattern, value)
            },
            Pattern::Struct(struct_name, field_patterns) => {
                // Match struct patterns
                if let RuntimeValue::Struct(fields) = value {
                    // Check if all field patterns match
                    for field_pattern in field_patterns {
                        if let Some(field_value) = fields.get(&field_pattern.name) {
                            if !self.pattern_matches(&field_pattern.pattern, field_value)? {
                                return Ok(false);
                            }
                        } else {
                            return Ok(false); // Field not found
                        }
                    }
                    Ok(true)
                } else {
                    Ok(false)
                }
            },
            Pattern::Union(inner_pattern, _variant_index) => {
                // For now, just match the inner pattern
                self.pattern_matches(inner_pattern, value)
            },
        }
    }

    /// Execute a try expression
    fn execute_try_expression(&mut self, try_expr: &crate::parser::ast::TryExpression) -> UmbraResult<RuntimeValue> {
        match self.execute_expression(&try_expr.expression) {
            Ok(value) => Ok(value),
            Err(_) => {
                // Try expressions return null on error
                Ok(RuntimeValue::Null)
            }
        }
    }

    // Additional execution methods would be implemented here...
    // This is a foundation that can be extended with more functionality
}

impl Default for Runtime {
    fn default() -> Self {
        Self::new()
    }
}

// Re-export AI/ML runtime functions
pub use ai_ml_runtime::{
    umbra_load_dataset,
    umbra_create_model,
    umbra_train_model,
    umbra_evaluate_model,
    umbra_predict_sample,
    umbra_visualize_metric,
};
