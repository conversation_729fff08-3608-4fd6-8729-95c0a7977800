use std::collections::HashMap;
use std::fmt;

use crate::error::{Umbra<PERSON><PERSON><PERSON>, UmbraR<PERSON>ult};
use crate::parser::ast::*;
use crate::lexer::token::Token;

/// Macro definition
#[derive(Debug, <PERSON>lone)]
pub struct MacroDefinition {
    pub name: String,
    pub parameters: Vec<MacroParameter>,
    pub body: MacroBody,
    pub is_function_like: bool,
}

/// Macro parameter
#[derive(Debug, Clone)]
pub struct MacroParameter {
    pub name: String,
    pub parameter_type: MacroParameterType,
    pub is_optional: bool,
}

/// Types of macro parameters
#[derive(Debug, Clone)]
pub enum MacroParameterType {
    /// Single token
    Token,
    /// Expression
    Expression,
    /// Statement
    Statement,
    /// Block of statements
    Block,
    /// Type annotation
    Type,
    /// Pattern
    Pattern,
    /// Identifier
    Identifier,
    /// Literal value
    Literal,
    /// Variable number of arguments
    Variadic(Box<MacroParameterType>),
}

/// Macro body containing the expansion template
#[derive(Debug, <PERSON>lone)]
pub enum MacroBody {
    /// Token-based expansion
    Tokens(Vec<MacroToken>),
    /// AST-based expansion
    Ast(Vec<Statement>),
    /// Rust-like procedural macro (function)
    Procedural(String), // Function name for procedural macros
}

/// Token in macro expansion with possible parameter substitution
#[derive(Debug, Clone)]
pub enum MacroToken {
    /// Literal token
    Literal(Token),
    /// Parameter substitution
    Parameter(String),
    /// Conditional expansion
    Conditional {
        condition: String,
        then_tokens: Vec<MacroToken>,
        else_tokens: Option<Vec<MacroToken>>,
    },
    /// Repetition
    Repeat {
        pattern: Vec<MacroToken>,
        separator: Option<Token>,
        min_count: usize,
        max_count: Option<usize>,
    },
}

/// Macro expansion context
#[derive(Debug, Clone)]
pub struct MacroContext {
    pub arguments: HashMap<String, MacroArgument>,
    pub expansion_depth: usize,
    pub max_expansion_depth: usize,
}

/// Macro argument value
#[derive(Debug, Clone)]
pub enum MacroArgument {
    Token(Token),
    Tokens(Vec<Token>),
    Expression(Expression),
    Statement(Statement),
    Block(Vec<Statement>),
    Type(Type),
    Pattern(Pattern),
    Identifier(String),
    Literal(Literal),
}

/// Macro expansion system
pub struct MacroSystem {
    /// Registered macros
    macros: HashMap<String, MacroDefinition>,
    /// Built-in macros
    builtins: HashMap<String, fn(&MacroContext) -> UmbraResult<Vec<Token>>>,
    /// Maximum expansion depth to prevent infinite recursion
    max_expansion_depth: usize,
}

impl MacroSystem {
    pub fn new() -> Self {
        let mut system = Self {
            macros: HashMap::new(),
            builtins: HashMap::new(),
            max_expansion_depth: 100,
        };
        
        system.register_builtin_macros();
        system
    }

    /// Register built-in macros
    fn register_builtin_macros(&mut self) {
        self.builtins.insert("println".to_string(), builtin_println);
        self.builtins.insert("format".to_string(), builtin_format);
        self.builtins.insert("assert".to_string(), builtin_assert);
        self.builtins.insert("debug".to_string(), builtin_debug);
        self.builtins.insert("todo".to_string(), builtin_todo);
        self.builtins.insert("unreachable".to_string(), builtin_unreachable);
    }

    /// Register a macro
    pub fn register_macro(&mut self, macro_def: MacroDefinition) {
        self.macros.insert(macro_def.name.clone(), macro_def);
    }

    /// Check if a name is a macro
    pub fn is_macro(&self, name: &str) -> bool {
        self.macros.contains_key(name) || self.builtins.contains_key(name)
    }

    /// Expand a macro call
    pub fn expand_macro(&self, name: &str, args: Vec<MacroArgument>) -> UmbraResult<Vec<Token>> {
        // Check built-in macros first
        if let Some(builtin_fn) = self.builtins.get(name) {
            let context = MacroContext {
                arguments: args.into_iter().enumerate()
                    .map(|(i, arg)| (format!("arg{}", i), arg))
                    .collect(),
                expansion_depth: 0,
                max_expansion_depth: self.max_expansion_depth,
            };
            return builtin_fn(&context);
        }

        // Check user-defined macros
        if let Some(macro_def) = self.macros.get(name) {
            return self.expand_user_macro(macro_def, args);
        }

        Err(UmbraError::Runtime(format!("Macro '{}' not found", name)))
    }

    /// Expand a user-defined macro
    fn expand_user_macro(&self, macro_def: &MacroDefinition, args: Vec<MacroArgument>) -> UmbraResult<Vec<Token>> {
        // Validate argument count
        let required_args = macro_def.parameters.iter()
            .filter(|p| !p.is_optional)
            .count();
        
        if args.len() < required_args {
            return Err(UmbraError::Runtime(format!(
                "Macro '{}' requires at least {} arguments, got {}",
                macro_def.name, required_args, args.len()
            )));
        }

        // Create expansion context
        let mut context = MacroContext {
            arguments: HashMap::new(),
            expansion_depth: 0,
            max_expansion_depth: self.max_expansion_depth,
        };

        // Bind arguments to parameters
        for (i, (param, arg)) in macro_def.parameters.iter().zip(args.iter()).enumerate() {
            context.arguments.insert(param.name.clone(), arg.clone());
        }

        // Expand macro body
        match &macro_def.body {
            MacroBody::Tokens(tokens) => self.expand_token_body(tokens, &context),
            MacroBody::Ast(statements) => self.expand_ast_body(statements, &context),
            MacroBody::Procedural(func_name) => {
                // For procedural macros, we'd call the function
                // For now, return empty tokens
                Ok(Vec::new())
            }
        }
    }

    /// Expand token-based macro body
    fn expand_token_body(&self, tokens: &[MacroToken], context: &MacroContext) -> UmbraResult<Vec<Token>> {
        let mut result = Vec::new();
        
        for token in tokens {
            match token {
                MacroToken::Literal(tok) => {
                    result.push(tok.clone());
                }
                MacroToken::Parameter(param_name) => {
                    if let Some(arg) = context.arguments.get(param_name) {
                        match arg {
                            MacroArgument::Token(tok) => result.push(tok.clone()),
                            MacroArgument::Tokens(toks) => result.extend(toks.clone()),
                            MacroArgument::Identifier(name) => {
                                result.push(Token::Identifier(name.clone()));
                            }
                            _ => {
                                // Convert other argument types to tokens
                                result.push(Token::Identifier(format!("{:?}", arg)));
                            }
                        }
                    }
                }
                MacroToken::Conditional { condition, then_tokens, else_tokens } => {
                    // Evaluate condition (simplified)
                    let condition_result = context.arguments.contains_key(condition);
                    
                    if condition_result {
                        let expanded = self.expand_token_body(then_tokens, context)?;
                        result.extend(expanded);
                    } else if let Some(else_tokens) = else_tokens {
                        let expanded = self.expand_token_body(else_tokens, context)?;
                        result.extend(expanded);
                    }
                }
                MacroToken::Repeat { pattern, separator, min_count, max_count } => {
                    // Simplified repetition - repeat pattern once for now
                    let expanded = self.expand_token_body(pattern, context)?;
                    result.extend(expanded);
                    
                    if let Some(sep) = separator {
                        result.push(sep.clone());
                    }
                }
            }
        }
        
        Ok(result)
    }

    /// Expand AST-based macro body
    fn expand_ast_body(&self, _statements: &[Statement], _context: &MacroContext) -> UmbraResult<Vec<Token>> {
        // For AST-based macros, we'd need to convert the AST back to tokens
        // This is a complex operation that would require a token generator
        // For now, return empty tokens
        Ok(Vec::new())
    }

    /// Parse macro arguments from tokens
    pub fn parse_macro_args(&self, tokens: &[Token]) -> UmbraResult<Vec<MacroArgument>> {
        let mut args = Vec::new();
        let mut current_arg = Vec::new();
        let mut paren_depth = 0;
        
        for token in tokens {
            match token {
                Token::LeftParen => {
                    paren_depth += 1;
                    current_arg.push(token.clone());
                }
                Token::RightParen => {
                    paren_depth -= 1;
                    if paren_depth == 0 {
                        if !current_arg.is_empty() {
                            args.push(MacroArgument::Tokens(current_arg.clone()));
                            current_arg.clear();
                        }
                    } else {
                        current_arg.push(token.clone());
                    }
                }
                Token::Comma if paren_depth == 0 => {
                    if !current_arg.is_empty() {
                        args.push(MacroArgument::Tokens(current_arg.clone()));
                        current_arg.clear();
                    }
                }
                _ => {
                    current_arg.push(token.clone());
                }
            }
        }
        
        if !current_arg.is_empty() {
            args.push(MacroArgument::Tokens(current_arg));
        }
        
        Ok(args)
    }
}

// Built-in macro implementations

fn builtin_println(context: &MacroContext) -> UmbraResult<Vec<Token>> {
    // Generate tokens for println! expansion
    let mut tokens = vec![
        Token::Identifier("print".to_string()),
        Token::LeftParen,
    ];
    
    // Add arguments
    for (i, (_, arg)) in context.arguments.iter().enumerate() {
        if i > 0 {
            tokens.push(Token::Comma);
        }
        
        match arg {
            MacroArgument::Tokens(toks) => tokens.extend(toks.clone()),
            MacroArgument::Identifier(name) => tokens.push(Token::Identifier(name.clone())),
            _ => tokens.push(Token::Identifier(format!("{:?}", arg))),
        }
    }
    
    tokens.extend(vec![
        Token::RightParen,
        Token::Semicolon,
        Token::Identifier("print".to_string()),
        Token::LeftParen,
        Token::StringLiteral("\\n".to_string()),
        Token::RightParen,
    ]);
    
    Ok(tokens)
}

fn builtin_format(_context: &MacroContext) -> UmbraResult<Vec<Token>> {
    // Simplified format! macro
    Ok(vec![
        Token::Identifier("format_string".to_string()),
        Token::LeftParen,
        Token::RightParen,
    ])
}

fn builtin_assert(_context: &MacroContext) -> UmbraResult<Vec<Token>> {
    // Simplified assert! macro
    Ok(vec![
        Token::Identifier("assert_condition".to_string()),
        Token::LeftParen,
        Token::RightParen,
    ])
}

fn builtin_debug(_context: &MacroContext) -> UmbraResult<Vec<Token>> {
    // Simplified debug! macro
    Ok(vec![
        Token::Identifier("debug_print".to_string()),
        Token::LeftParen,
        Token::RightParen,
    ])
}

fn builtin_todo(_context: &MacroContext) -> UmbraResult<Vec<Token>> {
    Ok(vec![
        Token::Identifier("panic".to_string()),
        Token::LeftParen,
        Token::StringLiteral("not yet implemented".to_string()),
        Token::RightParen,
    ])
}

fn builtin_unreachable(_context: &MacroContext) -> UmbraResult<Vec<Token>> {
    Ok(vec![
        Token::Identifier("panic".to_string()),
        Token::LeftParen,
        Token::StringLiteral("unreachable code reached".to_string()),
        Token::RightParen,
    ])
}

impl Default for MacroSystem {
    fn default() -> Self {
        Self::new()
    }
}

impl fmt::Display for MacroArgument {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            MacroArgument::Token(token) => write!(f, "{:?}", token),
            MacroArgument::Tokens(tokens) => write!(f, "{:?}", tokens),
            MacroArgument::Expression(expr) => write!(f, "{:?}", expr),
            MacroArgument::Statement(stmt) => write!(f, "{:?}", stmt),
            MacroArgument::Block(block) => write!(f, "{:?}", block),
            MacroArgument::Type(ty) => write!(f, "{:?}", ty),
            MacroArgument::Pattern(pattern) => write!(f, "{:?}", pattern),
            MacroArgument::Identifier(name) => write!(f, "{}", name),
            MacroArgument::Literal(lit) => write!(f, "{:?}", lit),
        }
    }
}
