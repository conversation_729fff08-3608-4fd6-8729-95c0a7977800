/// Auto-completion implementation for Umbra LSP
/// 
/// Provides intelligent auto-completion for keywords, functions, variables,
/// and AI/ML operations based on context analysis.

use crate::parser::ast::*;
use crate::semantic::symbol_table::{SymbolTable, SymbolType};
use tower_lsp::lsp_types::*;

/// Completion provider for Umbra language
pub struct CompletionProvider {
    /// Built-in keywords
    keywords: Vec<CompletionItem>,
    
    /// AI/ML specific completions
    ai_ml_completions: Vec<CompletionItem>,
    
    /// Standard library completions
    stdlib_completions: Vec<CompletionItem>,
}

impl CompletionProvider {
    /// Create a new completion provider
    pub fn new() -> Self {
        Self {
            keywords: Self::create_keyword_completions(),
            ai_ml_completions: Self::create_ai_ml_completions(),
            stdlib_completions: Self::create_stdlib_completions(),
        }
    }
    
    /// Get completions for a specific context
    pub fn get_completions(
        &self,
        content: &str,
        position: Position,
        symbols: Option<&SymbolTable>,
        _ast: Option<&Program>,
    ) -> Vec<CompletionItem> {
        let mut completions = Vec::new();

        // Get the current line and character position
        let lines: Vec<&str> = content.lines().collect();
        let line_idx = position.line as usize;
        let char_idx = position.character as usize;

        if line_idx >= lines.len() {
            return completions;
        }

        let current_line = lines[line_idx];
        let _line_prefix = if char_idx <= current_line.len() {
            &current_line[..char_idx]
        } else {
            current_line
        };

        // Analyze context at position
        let context = self.analyze_context(content, position);

        match context {
            CompletionContext::Global => {
                // Global scope - show keywords, functions, etc.
                completions.extend(self.keywords.clone());
                completions.extend(self.ai_ml_completions.clone());
                completions.extend(self.stdlib_completions.clone());

                if let Some(symbols) = symbols {
                    completions.extend(self.get_symbol_completions(symbols));
                }
            }
            CompletionContext::AfterDot(object_name) => {
                // Method/field access - show methods for the object type
                completions.extend(self.get_method_completions(&object_name, symbols));
            }
            CompletionContext::FunctionCall(function_name) => {
                // Function parameter completion
                completions.extend(self.get_parameter_completions(&function_name, symbols));
            }
            CompletionContext::Import => {
                // Module import completion
                completions.extend(self.get_module_completions());
            }
            CompletionContext::Type => {
                // Type annotation completion
                completions.extend(self.get_type_completions());
            }
            CompletionContext::AIMLOperation => {
                // AI/ML specific operations
                completions.extend(self.ai_ml_completions.clone());
            }
            CompletionContext::StringLiteral => {
                // No completions inside string literals
                return completions;
            }
            CompletionContext::Comment => {
                // No completions inside comments
                return completions;
            }
            CompletionContext::StructField(struct_name) => {
                // Struct field completions
                completions.extend(self.get_struct_field_completions(&struct_name, symbols));
            }
            CompletionContext::VariableDeclaration => {
                // Type completions for variable declarations
                completions.extend(self.get_type_completions());
            }
        }

        // Sort completions by relevance
        self.sort_completions_by_relevance(&mut completions, content, position);

        completions
    }
    
    /// Analyze the context at a given position
    fn analyze_context(&self, content: &str, position: Position) -> CompletionContext {
        let lines: Vec<&str> = content.lines().collect();
        let line_index = position.line as usize;
        let char_index = position.character as usize;

        if line_index >= lines.len() {
            return CompletionContext::Global;
        }

        let line = lines[line_index];
        let prefix = if char_index <= line.len() {
            &line[..char_index]
        } else {
            line
        };

        // Check if we're inside a string literal
        if self.is_inside_string_literal(prefix) {
            return CompletionContext::StringLiteral;
        }

        // Check if we're inside a comment
        if self.is_inside_comment(prefix) {
            return CompletionContext::Comment;
        }

        // Check for dot notation (method/field access)
        if let Some(dot_pos) = prefix.rfind('.') {
            let object_part = &prefix[..dot_pos];
            if let Some(object_name) = self.extract_identifier_before_dot(object_part) {
                return CompletionContext::AfterDot(object_name);
            }
        }

        // Check for struct field access
        if let Some(struct_name) = self.detect_struct_context(prefix) {
            return CompletionContext::StructField(struct_name);
        }

        // Check for variable declaration
        if self.is_variable_declaration_context(prefix) {
            return CompletionContext::VariableDeclaration;
        }

        // Check for import statements
        if prefix.trim_start().starts_with("bring") || prefix.trim_start().starts_with("import") {
            return CompletionContext::Import;
        }

        // Check for type annotations
        if prefix.contains(':') && !prefix.contains('=') && !self.is_inside_string_literal(prefix) {
            return CompletionContext::Type;
        }

        // Check for AI/ML keywords
        if prefix.contains("train") || prefix.contains("evaluate") || prefix.contains("predict") ||
           prefix.contains("model") || prefix.contains("dataset") {
            return CompletionContext::AIMLOperation;
        }

        // Check for function calls
        if let Some(paren_pos) = prefix.rfind('(') {
            let function_part = &prefix[..paren_pos];
            if let Some(function_name) = self.extract_identifier_before_paren(function_part) {
                return CompletionContext::FunctionCall(function_name);
            }
        }

        CompletionContext::Global
    }
    
    /// Extract identifier before dot
    fn extract_identifier_before_dot(&self, text: &str) -> Option<String> {
        let trimmed = text.trim_end();
        if let Some(start) = trimmed.rfind(|c: char| !c.is_alphanumeric() && c != '_') {
            Some(trimmed[start + 1..].to_string())
        } else {
            Some(trimmed.to_string())
        }
    }
    
    /// Extract identifier before parenthesis
    fn extract_identifier_before_paren(&self, text: &str) -> Option<String> {
        let trimmed = text.trim_end();
        if let Some(start) = trimmed.rfind(|c: char| !c.is_alphanumeric() && c != '_') {
            Some(trimmed[start + 1..].to_string())
        } else {
            Some(trimmed.to_string())
        }
    }

    /// Check if we're inside a string literal
    fn is_inside_string_literal(&self, text: &str) -> bool {
        let mut in_string = false;
        let mut escape_next = false;

        for ch in text.chars() {
            if escape_next {
                escape_next = false;
                continue;
            }

            match ch {
                '"' => in_string = !in_string,
                '\\' if in_string => escape_next = true,
                _ => {}
            }
        }

        in_string
    }

    /// Check if we're inside a comment
    fn is_inside_comment(&self, text: &str) -> bool {
        text.trim_start().starts_with("//") || text.contains("/*")
    }

    /// Detect struct context for field completion
    fn detect_struct_context(&self, text: &str) -> Option<String> {
        // Look for patterns like "StructName {" or "let x = StructName {"
        if let Some(brace_pos) = text.rfind('{') {
            let before_brace = &text[..brace_pos].trim_end();
            if let Some(struct_name) = self.extract_identifier_before_brace(before_brace) {
                return Some(struct_name);
            }
        }
        None
    }

    /// Extract identifier before brace
    fn extract_identifier_before_brace(&self, text: &str) -> Option<String> {
        let trimmed = text.trim_end();
        if let Some(start) = trimmed.rfind(|c: char| !c.is_alphanumeric() && c != '_') {
            let identifier = trimmed[start + 1..].trim();
            if !identifier.is_empty() && identifier.chars().next().unwrap().is_alphabetic() {
                Some(identifier.to_string())
            } else {
                None
            }
        } else if !trimmed.is_empty() && trimmed.chars().next().unwrap().is_alphabetic() {
            Some(trimmed.to_string())
        } else {
            None
        }
    }

    /// Check if we're in a variable declaration context
    fn is_variable_declaration_context(&self, text: &str) -> bool {
        let trimmed = text.trim_start();
        trimmed.starts_with("let ") && trimmed.contains(':') && !trimmed.contains('=')
    }
    
    /// Create keyword completions
    fn create_keyword_completions() -> Vec<CompletionItem> {
        let keywords = vec![
            ("fn", "Function declaration", "fn name(params) -> return_type {\n    $0\n}"),
            ("let", "Variable declaration", "let ${1:variable_name}: ${2:type} := ${3:value}"),
            ("when", "Conditional statement", "when ${1:condition} {\n    $0\n}"),
            ("otherwise", "Else clause", "otherwise {\n    $0\n}"),
            ("repeat", "Loop statement", "repeat ${1:item} in ${2:iterable} {\n    $0\n}"),
            ("while", "While loop", "while ${1:condition} {\n    $0\n}"),
            ("for", "For loop", "for ${1:i} in ${2:range(n)} {\n    $0\n}"),
            ("in", "In operator", "in"),
            ("return", "Return statement", "return ${1:value}"),
            ("bring", "Import statement", "bring ${1:module_name}"),
            ("export", "Export statement", "export ${1:symbol_name}"),
            ("module", "Module declaration", "module ${1:module_name}"),
            ("struct", "Structure definition", "struct ${1:Name} {\n    $0\n}"),
            ("enum", "Enumeration definition", "enum ${1:Name} {\n    $0\n}"),
            ("trait", "Trait definition", "trait ${1:Name} {\n    $0\n}"),
            ("impl", "Implementation block", "impl ${1:Type} {\n    $0\n}"),
            ("using", "Using statement", "using ${1:trait_name}"),
            ("true", "Boolean true", "true"),
            ("false", "Boolean false", "false"),
            ("void", "Void type", "void"),
            ("Integer", "Integer type", "Integer"),
            ("Float", "Float type", "Float"),
            ("String", "String type", "String"),
            ("Boolean", "Boolean type", "Boolean"),
            ("List", "List type", "List<${1:T}>"),
            ("Map", "Map type", "Map<${1:K}, ${2:V}>"),
            ("Set", "Set type", "Set<${1:T}>"),
        ];
        
        keywords
            .into_iter()
            .map(|(keyword, detail, snippet)| CompletionItem {
                label: keyword.to_string(),
                kind: Some(CompletionItemKind::KEYWORD),
                detail: Some(detail.to_string()),
                documentation: Some(Documentation::String(format!(
                    "Umbra keyword: {keyword}\n\nUsage: {snippet}"
                ))),
                insert_text: Some(snippet.to_string()),
                insert_text_format: Some(InsertTextFormat::SNIPPET),
                ..CompletionItem::default()
            })
            .collect()
    }
    
    /// Create AI/ML specific completions
    fn create_ai_ml_completions() -> Vec<CompletionItem> {
        let ai_ml_items = vec![
            ("train", "Train a model", "train ${1:model} using ${2:dataset}"),
            ("evaluate", "Evaluate model performance", "evaluate ${1:model} on ${2:test_data}"),
            ("predict", "Make predictions", "predict ${1:model} with ${2:input_data}"),
            ("model", "Model type", "model"),
            ("dataset", "Dataset type", "dataset"),
            ("tensor", "Tensor type", "tensor"),
            ("load_model", "Load a pre-trained model", "load_model(\"${1:model_path}\")"),
            ("save_model", "Save a trained model", "save_model(${1:model}, \"${2:path}\")"),
            ("load_dataset", "Load a dataset", "load_dataset(\"${1:dataset_path}\")"),
            ("split_dataset", "Split dataset", "split_dataset(${1:dataset}, ${2:ratio})"),
            ("normalize", "Normalize data", "normalize(${1:data})"),
            ("accuracy", "Calculate accuracy", "accuracy(${1:predictions}, ${2:labels})"),
            ("loss", "Calculate loss", "loss(${1:predictions}, ${2:labels})"),
        ];
        
        ai_ml_items
            .into_iter()
            .map(|(name, detail, snippet)| CompletionItem {
                label: name.to_string(),
                kind: Some(CompletionItemKind::FUNCTION),
                detail: Some(detail.to_string()),
                documentation: Some(Documentation::String(format!(
                    "AI/ML operation: {name}\n\nUsage: {snippet}"
                ))),
                insert_text: Some(snippet.to_string()),
                insert_text_format: Some(InsertTextFormat::SNIPPET),
                ..CompletionItem::default()
            })
            .collect()
    }
    
    /// Create standard library completions
    fn create_stdlib_completions() -> Vec<CompletionItem> {
        let stdlib_items = vec![
            // I/O Functions
            ("show", "Display a value", "show(${1:value})"),
            ("read_line", "Read line from input", "read_line()"),
            ("read_file", "Read file contents", "read_file(${1:path})"),
            ("write_file", "Write to file", "write_file(${1:path}, ${2:content})"),

            // Math Functions
            ("abs", "Absolute value", "abs(${1:number})"),
            ("max", "Maximum of two values", "max(${1:a}, ${2:b})"),
            ("min", "Minimum of two values", "min(${1:a}, ${2:b})"),
            ("sqrt", "Square root", "sqrt(${1:number})"),
            ("pow", "Power", "pow(${1:base}, ${2:exponent})"),
            ("sin", "Sine", "sin(${1:angle})"),
            ("cos", "Cosine", "cos(${1:angle})"),
            ("tan", "Tangent", "tan(${1:angle})"),
            ("floor", "Floor", "floor(${1:number})"),
            ("ceil", "Ceiling", "ceil(${1:number})"),
            ("round", "Round", "round(${1:number})"),

            // String Functions
            ("str_len", "String length", "str_len(${1:string})"),
            ("to_upper", "Convert to uppercase", "to_upper(${1:string})"),
            ("to_lower", "Convert to lowercase", "to_lower(${1:string})"),
            ("str_contains", "Check if string contains substring", "str_contains(${1:haystack}, ${2:needle})"),
            ("str_split", "Split string", "str_split(${1:string}, ${2:delimiter})"),
            ("str_replace", "Replace substring", "str_replace(${1:string}, ${2:old}, ${3:new})"),
            ("str_trim", "Trim whitespace", "str_trim(${1:string})"),
            ("str_starts_with", "Check if string starts with", "str_starts_with(${1:string}, ${2:prefix})"),
            ("str_ends_with", "Check if string ends with", "str_ends_with(${1:string}, ${2:suffix})"),

            // Array Functions
            ("array_length", "Array length", "array_length(${1:array})"),
            ("array_push", "Add element to array", "array_push(${1:array}, ${2:element})"),
            ("array_pop", "Remove last element", "array_pop(${1:array})"),
            ("array_get", "Get element at index", "array_get(${1:array}, ${2:index})"),
            ("array_set", "Set element at index", "array_set(${1:array}, ${2:index}, ${3:value})"),
            ("array_sum", "Sum array elements", "array_sum(${1:array})"),
            ("array_sort", "Sort array", "array_sort(${1:array})"),
            ("array_reverse", "Reverse array", "array_reverse(${1:array})"),

            // Type Conversion
            ("to_string", "Convert to string", "to_string(${1:value})"),
            ("to_integer", "Convert to integer", "to_integer(${1:value})"),
            ("to_float", "Convert to float", "to_float(${1:value})"),
            ("to_boolean", "Convert to boolean", "to_boolean(${1:value})"),

            // Utility Functions
            ("typeof", "Get type of value", "typeof(${1:value})"),
            ("is_null", "Check if null", "is_null(${1:value})"),
            ("is_empty", "Check if empty", "is_empty(${1:collection})"),
            ("clone", "Clone value", "clone(${1:value})"),
        ];
        
        stdlib_items
            .into_iter()
            .map(|(name, detail, snippet)| CompletionItem {
                label: name.to_string(),
                kind: Some(CompletionItemKind::FUNCTION),
                detail: Some(detail.to_string()),
                documentation: Some(Documentation::String(format!(
                    "Standard library function: {name}\n\nUsage: {snippet}"
                ))),
                insert_text: Some(snippet.to_string()),
                insert_text_format: Some(InsertTextFormat::SNIPPET),
                ..CompletionItem::default()
            })
            .collect()
    }
    
    /// Get completions from symbol table
    fn get_symbol_completions(&self, symbols: &SymbolTable) -> Vec<CompletionItem> {
        let mut completions = Vec::new();
        
        for (name, symbol) in symbols.symbols() {
            let (kind, detail) = match symbol.symbol_type() {
                SymbolType::Function => (CompletionItemKind::FUNCTION, "User-defined function"),
                SymbolType::Variable => (CompletionItemKind::VARIABLE, "Variable"),
                SymbolType::Structure => (CompletionItemKind::STRUCT, "Structure"),
                SymbolType::Parameter => (CompletionItemKind::VARIABLE, "Parameter"),
                SymbolType::Trait => (CompletionItemKind::INTERFACE, "Trait"),
                SymbolType::Implementation => (CompletionItemKind::CLASS, "Implementation"),
            };
            
            completions.push(CompletionItem {
                label: name.clone(),
                kind: Some(kind),
                detail: Some(detail.to_string()),
                documentation: Some(Documentation::String(format!(
                    "{detail}: {name}"
                ))),
                ..CompletionItem::default()
            });
        }
        
        completions
    }
    
    /// Get method completions for an object
    fn get_method_completions(&self, object_name: &str, symbols: Option<&SymbolTable>) -> Vec<CompletionItem> {
        let mut completions = Vec::new();
        
        // Add common methods based on inferred type
        match object_name {
            name if name.contains("dataset") => {
                completions.extend(self.get_dataset_methods());
            }
            name if name.contains("model") => {
                completions.extend(self.get_model_methods());
            }
            name if name.contains("tensor") => {
                completions.extend(self.get_tensor_methods());
            }
            _ => {
                // Generic object methods
                completions.extend(self.get_generic_methods());
            }
        }
        
        completions
    }
    
    /// Get dataset-specific methods
    fn get_dataset_methods(&self) -> Vec<CompletionItem> {
        vec![
            CompletionItem {
                label: "split".to_string(),
                kind: Some(CompletionItemKind::METHOD),
                detail: Some("Split dataset".to_string()),
                insert_text: Some("split(${1:ratio})".to_string()),
                insert_text_format: Some(InsertTextFormat::SNIPPET),
                ..CompletionItem::default()
            },
            CompletionItem {
                label: "shuffle".to_string(),
                kind: Some(CompletionItemKind::METHOD),
                detail: Some("Shuffle dataset".to_string()),
                insert_text: Some("shuffle()".to_string()),
                ..CompletionItem::default()
            },
            CompletionItem {
                label: "size".to_string(),
                kind: Some(CompletionItemKind::METHOD),
                detail: Some("Get dataset size".to_string()),
                insert_text: Some("size()".to_string()),
                ..CompletionItem::default()
            },
        ]
    }
    
    /// Get model-specific methods
    fn get_model_methods(&self) -> Vec<CompletionItem> {
        vec![
            CompletionItem {
                label: "train".to_string(),
                kind: Some(CompletionItemKind::METHOD),
                detail: Some("Train model".to_string()),
                insert_text: Some("train(${1:dataset})".to_string()),
                insert_text_format: Some(InsertTextFormat::SNIPPET),
                ..CompletionItem::default()
            },
            CompletionItem {
                label: "predict".to_string(),
                kind: Some(CompletionItemKind::METHOD),
                detail: Some("Make prediction".to_string()),
                insert_text: Some("predict(${1:input})".to_string()),
                insert_text_format: Some(InsertTextFormat::SNIPPET),
                ..CompletionItem::default()
            },
            CompletionItem {
                label: "save".to_string(),
                kind: Some(CompletionItemKind::METHOD),
                detail: Some("Save model".to_string()),
                insert_text: Some("save(\"${1:path}\")".to_string()),
                insert_text_format: Some(InsertTextFormat::SNIPPET),
                ..CompletionItem::default()
            },
        ]
    }
    
    /// Get tensor-specific methods
    fn get_tensor_methods(&self) -> Vec<CompletionItem> {
        vec![
            CompletionItem {
                label: "shape".to_string(),
                kind: Some(CompletionItemKind::METHOD),
                detail: Some("Get tensor shape".to_string()),
                insert_text: Some("shape()".to_string()),
                ..CompletionItem::default()
            },
            CompletionItem {
                label: "reshape".to_string(),
                kind: Some(CompletionItemKind::METHOD),
                detail: Some("Reshape tensor".to_string()),
                insert_text: Some("reshape(${1:new_shape})".to_string()),
                insert_text_format: Some(InsertTextFormat::SNIPPET),
                ..CompletionItem::default()
            },
        ]
    }
    
    /// Get generic object methods
    fn get_generic_methods(&self) -> Vec<CompletionItem> {
        vec![
            CompletionItem {
                label: "to_string".to_string(),
                kind: Some(CompletionItemKind::METHOD),
                detail: Some("Convert to string".to_string()),
                insert_text: Some("to_string()".to_string()),
                ..CompletionItem::default()
            },
        ]
    }
    
    /// Get parameter completions for function calls
    fn get_parameter_completions(&self, function_name: &str, symbols: Option<&SymbolTable>) -> Vec<CompletionItem> {
        // In a real implementation, we'd look up the function signature
        // and provide parameter name completions
        Vec::new()
    }
    
    /// Get module completions for import statements
    fn get_module_completions(&self) -> Vec<CompletionItem> {
        vec![
            CompletionItem {
                label: "std::io".to_string(),
                kind: Some(CompletionItemKind::MODULE),
                detail: Some("Standard I/O module".to_string()),
                ..CompletionItem::default()
            },
            CompletionItem {
                label: "std::math".to_string(),
                kind: Some(CompletionItemKind::MODULE),
                detail: Some("Math utilities module".to_string()),
                ..CompletionItem::default()
            },
            CompletionItem {
                label: "std::collections".to_string(),
                kind: Some(CompletionItemKind::MODULE),
                detail: Some("Collections module".to_string()),
                ..CompletionItem::default()
            },
        ]
    }
    
    /// Get type completions
    fn get_type_completions(&self) -> Vec<CompletionItem> {
        let types = vec![
            "Integer", "Float", "Boolean", "String",
            "Dataset", "Model", "Tensor",
            "Optional", "Result", "List",
        ];
        
        types
            .into_iter()
            .map(|type_name| CompletionItem {
                label: type_name.to_string(),
                kind: Some(CompletionItemKind::TYPE_PARAMETER),
                detail: Some(format!("Umbra type: {type_name}")),
                ..CompletionItem::default()
            })
            .collect()
    }
}

/// Completion context types
#[derive(Debug, Clone)]
enum CompletionContext {
    /// Global scope
    Global,
    /// After dot notation (method/field access)
    AfterDot(String),
    /// Inside function call
    FunctionCall(String),
    /// Import statement
    Import,
    /// Type annotation
    Type,
    /// AI/ML operation context
    AIMLOperation,
    /// Inside string literal
    StringLiteral,
    /// Inside comment
    Comment,
    /// Struct field access
    StructField(String),
    /// Variable declaration
    VariableDeclaration,
}

impl CompletionProvider {
    /// Sort completions by relevance
    fn sort_completions_by_relevance(&self, completions: &mut Vec<CompletionItem>, _content: &str, _position: Position) {
        // Sort by kind first (keywords, then functions, then variables)
        completions.sort_by(|a, b| {
            let a_priority = self.get_completion_priority(a);
            let b_priority = self.get_completion_priority(b);
            a_priority.cmp(&b_priority).then_with(|| a.label.cmp(&b.label))
        });
    }

    /// Get completion priority for sorting
    fn get_completion_priority(&self, item: &CompletionItem) -> u8 {
        match item.kind {
            Some(CompletionItemKind::KEYWORD) => 1,
            Some(CompletionItemKind::FUNCTION) => 2,
            Some(CompletionItemKind::METHOD) => 3,
            Some(CompletionItemKind::VARIABLE) => 4,
            Some(CompletionItemKind::FIELD) => 5,
            Some(CompletionItemKind::CLASS) => 6,
            Some(CompletionItemKind::MODULE) => 7,
            _ => 8,
        }
    }

    /// Get struct field completions
    fn get_struct_field_completions(&self, _struct_name: &str, _symbols: Option<&SymbolTable>) -> Vec<CompletionItem> {
        // Simplified implementation - provide generic field completions
        vec![
            CompletionItem {
                label: "field".to_string(),
                kind: Some(CompletionItemKind::FIELD),
                detail: Some("Struct field".to_string()),
                documentation: None,
                deprecated: None,
                preselect: None,
                sort_text: None,
                filter_text: None,
                insert_text: Some("field".to_string()),
                insert_text_format: None,
                insert_text_mode: None,
                text_edit: None,
                additional_text_edits: None,
                command: None,
                commit_characters: None,
                data: None,
                tags: None,
            }
        ]
    }
}

impl Default for CompletionProvider {
    fn default() -> Self {
        Self::new()
    }
}
