[package]
name = "umbra-compiler"
version = "1.0.1"
edition = "2021"
authors = ["Eclipse Softworks <<EMAIL>>"]
description = "Umbra programming language compiler - AI/ML focused compiled language"
license = "MIT"
homepage = "https://eclipse-softworks.com"
repository = "https://github.com/eclipse-softworks/umbra"
documentation = "https://eclipse-softworks.com/docs/umbra"
keywords = ["umbra", "ai", "ml", "machine-learning", "artificial-intelligence", "programming-language", "compiler", "llvm"]
categories = ["programming-languages", "artificial-intelligence", "machine-learning"]
[features]
default = ["llvm-backend", "python-interop"]
llvm-backend = ["inkwell"]
python-interop = ["pyo3"]
windows-simple = []
windows-full = ["llvm-backend", "windows-python"]  # Full Windows with LLVM and Python
windows-python = ["pyo3"]

[target.'cfg(windows)'.dependencies]
# Windows-specific dependencies
winapi = { version = "0.3", features = ["winuser", "wincon", "processenv"] }

[lib]
name = "umbra_compiler"
path = "src/lib.rs"

[[bin]]
name = "umbra"
path = "src/main.rs"

[dependencies]
# LLVM bindings for code generation (optional for Windows)
inkwell = { version = "0.4", features = ["llvm14-0"], optional = true }

# CLI and argument parsing
clap = { version = "4.4", features = ["derive"] }

# Error handling and diagnostics
thiserror = "1.0"
anyhow = "1.0"

# Parsing and lexing utilities
logos = "0.14"

# Serialization for AST and metadata
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
toml = "0.8"

# System calls and platform-specific functionality
libc = "0.2"

# Date and time handling
chrono = { version = "0.4", features = ["serde"] }

# HTTP client for package registry
reqwest = { version = "0.11", features = ["json", "multipart"] }
tokio = { version = "1.0", features = ["full"] }

# UUID generation for debug sessions
uuid = { version = "1.0", features = ["v4"] }

# Hashing for debug information and package integrity
md5 = "0.7"
sha2 = "0.10"

# File system and path utilities
walkdir = "2.4"

# String and text processing
unicode-segmentation = "1.10"
base64 = "0.21"
urlencoding = "2.1"

# Colored output for diagnostics
colored = "2.0"

# XML processing
quick-xml = "0.31"

# Compression
flate2 = "1.0"

# Terminal handling
crossterm = "0.27"

# Temporary files
tempfile = "3.8"

# Parallel processing
num_cpus = "1.16"

# Testing framework dependencies
glob = "0.3"
regex = "1.0"
rand = "0.8"

# Static initialization
lazy_static = "1.4"

# Database integration dependencies
url = "2.4"
fastrand = "2.0"
log = "0.4"

# Language Server Protocol
tower-lsp = "0.20"
lsp-types = "0.95"



# Python interoperability
pyo3 = { version = "0.20", features = ["auto-initialize", "abi3-py38", "generate-import-lib"], optional = true }
which = "4.4"



# Archive handling
tar = "0.4"

[build-dependencies]
chrono = "0.4"

[dev-dependencies]
# Testing utilities
criterion = "0.5"
tempfile = "3.8"

[profile.release]
# Optimize for size and performance
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"

[profile.dev]
# Fast compilation for development
opt-level = 0
debug = true
