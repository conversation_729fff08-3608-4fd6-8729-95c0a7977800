#!/usr/bin/env python3
"""
Test script to verify Python AI/ML library integration for Umbra compiler
"""

import sys
import json
from datetime import datetime

def test_library_imports():
    """Test importing all required AI/ML libraries"""
    results = {}
    
    # Core libraries
    libraries = [
        ('numpy', 'np'),
        ('pandas', 'pd'),
        ('sklearn', None),
        ('matplotlib.pyplot', 'plt'),
        ('joblib', None),
        ('seaborn', 'sns'),
        ('tensorflow', 'tf'),
        ('torch', None),
    ]
    
    for lib_name, alias in libraries:
        try:
            if alias:
                exec(f"import {lib_name} as {alias}")
                module = eval(alias)
            else:
                exec(f"import {lib_name}")
                module = eval(lib_name.split('.')[0])
            
            version = getattr(module, '__version__', 'unknown')
            results[lib_name] = {
                'status': 'available',
                'version': version
            }
            print(f"✅ {lib_name}: {version}")
        except ImportError as e:
            results[lib_name] = {
                'status': 'missing',
                'error': str(e)
            }
            print(f"❌ {lib_name}: Not available - {e}")
    
    return results

def test_basic_ml_operations():
    """Test basic ML operations that Umbra will use"""
    print("\n=== Testing Basic ML Operations ===")
    
    try:
        import numpy as np
        import pandas as pd
        from sklearn.datasets import make_classification
        from sklearn.ensemble import RandomForestClassifier
        from sklearn.model_selection import train_test_split
        from sklearn.metrics import accuracy_score
        
        # Create sample dataset
        X, y = make_classification(n_samples=100, n_features=4, n_classes=2, random_state=42)
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
        
        # Train model
        model = RandomForestClassifier(n_estimators=10, random_state=42)
        model.fit(X_train, y_train)
        
        # Make predictions
        predictions = model.predict(X_test)
        accuracy = accuracy_score(y_test, predictions)
        
        print(f"✅ ML Pipeline Test: Accuracy = {accuracy:.4f}")
        return True
        
    except Exception as e:
        print(f"❌ ML Pipeline Test Failed: {e}")
        return False

def test_data_operations():
    """Test data manipulation operations"""
    print("\n=== Testing Data Operations ===")
    
    try:
        import pandas as pd
        import numpy as np
        
        # Create sample DataFrame
        data = {
            'feature1': np.random.randn(50),
            'feature2': np.random.randn(50),
            'target': np.random.choice([0, 1], 50)
        }
        df = pd.DataFrame(data)
        
        # Basic operations
        print(f"✅ DataFrame created: {df.shape}")
        print(f"✅ Data types: {df.dtypes.to_dict()}")
        print(f"✅ Summary stats available")
        
        return True
        
    except Exception as e:
        print(f"❌ Data Operations Test Failed: {e}")
        return False

def test_visualization():
    """Test visualization capabilities"""
    print("\n=== Testing Visualization ===")
    
    try:
        import matplotlib
        matplotlib.use('Agg')  # Use non-interactive backend
        import matplotlib.pyplot as plt
        import numpy as np
        
        # Create simple plot
        x = np.linspace(0, 10, 100)
        y = np.sin(x)
        
        plt.figure(figsize=(8, 6))
        plt.plot(x, y)
        plt.title("Test Plot")
        plt.savefig('/tmp/test_plot.png')
        plt.close()
        
        print("✅ Matplotlib plotting works")
        return True
        
    except Exception as e:
        print(f"❌ Visualization Test Failed: {e}")
        return False

def test_json_serialization():
    """Test JSON serialization for data exchange"""
    print("\n=== Testing JSON Serialization ===")
    
    try:
        import json
        import numpy as np
        
        # Test data structures
        test_data = {
            'integers': [1, 2, 3, 4, 5],
            'floats': [1.1, 2.2, 3.3],
            'strings': ['a', 'b', 'c'],
            'nested': {
                'array': np.array([1, 2, 3]).tolist(),
                'boolean': True
            }
        }
        
        # Serialize and deserialize
        json_str = json.dumps(test_data)
        parsed_data = json.loads(json_str)
        
        print("✅ JSON serialization works")
        return True
        
    except Exception as e:
        print(f"❌ JSON Serialization Test Failed: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Umbra AI/ML Python Integration Test")
    print("=" * 50)
    
    # Test library imports
    library_results = test_library_imports()
    
    # Test basic operations
    ml_test = test_basic_ml_operations()
    data_test = test_data_operations()
    viz_test = test_visualization()
    json_test = test_json_serialization()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Summary")
    print("=" * 50)
    
    available_libs = sum(1 for result in library_results.values() if result['status'] == 'available')
    total_libs = len(library_results)
    
    print(f"Libraries Available: {available_libs}/{total_libs}")
    
    tests_passed = sum([ml_test, data_test, viz_test, json_test])
    total_tests = 4
    
    print(f"Functional Tests Passed: {tests_passed}/{total_tests}")
    
    # Critical libraries check
    critical_libs = ['numpy', 'pandas', 'sklearn', 'matplotlib.pyplot', 'joblib']
    missing_critical = [lib for lib in critical_libs if library_results.get(lib, {}).get('status') != 'available']
    
    if missing_critical:
        print(f"\n⚠️  Missing Critical Libraries: {missing_critical}")
        print("These libraries are required for Umbra AI/ML features to work properly.")
        
        print("\n📦 Installation Commands:")
        if 'seaborn' in missing_critical:
            print("pip3 install seaborn")
        if 'tensorflow' in missing_critical:
            print("pip3 install tensorflow")
        if 'torch' in missing_critical:
            print("pip3 install torch")
    
    # Overall status
    if tests_passed == total_tests and not missing_critical:
        print("\n🎉 All tests passed! Umbra AI/ML integration is ready.")
        return 0
    elif tests_passed >= 3 and len(missing_critical) <= 2:
        print("\n⚠️  Most functionality available, some optional libraries missing.")
        return 1
    else:
        print("\n❌ Critical issues found. AI/ML features may not work properly.")
        return 2

if __name__ == "__main__":
    sys.exit(main())
