#!/bin/bash
# Comprehensive Windows Installer Builder for Umbra Programming Language
# Creates a professional Windows installer with full functionality

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
VERSION="1.0.1"
DIST_DIR="$SCRIPT_DIR/distribution/windows-installer-new"
NSIS_DIR="$DIST_DIR/nsis-build"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Check dependencies
check_dependencies() {
    log_info "Checking dependencies..."
    
    if ! command -v makensis &> /dev/null; then
        log_error "NSIS not found. Installing..."
        sudo apt-get update && sudo apt-get install -y nsis
    fi
    
    log_success "Dependencies checked"
}

# Prepare distribution files
prepare_distribution() {
    log_info "Preparing Windows distribution files..."
    
    mkdir -p "$DIST_DIR"/{bin,docs,examples,dependencies}
    mkdir -p "$NSIS_DIR"
    
    # Copy Windows binary
    if [[ -f "$SCRIPT_DIR/umbra-compiler/target/x86_64-pc-windows-gnu/release/umbra.exe" ]]; then
        cp "$SCRIPT_DIR/umbra-compiler/target/x86_64-pc-windows-gnu/release/umbra.exe" "$DIST_DIR/bin/"
        log_success "Windows binary copied ($(du -h "$DIST_DIR/bin/umbra.exe" | cut -f1))"
    else
        log_error "Windows binary not found!"
        exit 1
    fi
    
    # Copy documentation
    cp "$SCRIPT_DIR/LICENSE" "$DIST_DIR/" 2>/dev/null || echo "MIT License" > "$DIST_DIR/LICENSE"
    cp "$SCRIPT_DIR/README.md" "$DIST_DIR/" 2>/dev/null || echo "# Umbra Programming Language" > "$DIST_DIR/README.md"
    
    # Copy showcase programs as examples
    if [[ -d "$SCRIPT_DIR/umbra_showcase_programs" ]]; then
        cp -r "$SCRIPT_DIR/umbra_showcase_programs"/* "$DIST_DIR/examples/"
        log_success "Showcase programs copied as examples"
    fi
    
    # Copy documentation
    if [[ -d "$SCRIPT_DIR/docs" ]]; then
        cp -r "$SCRIPT_DIR/docs"/* "$DIST_DIR/docs/"
        log_success "Documentation copied"
    fi
    
    # Create Windows-specific files
    create_windows_files
    
    log_success "Distribution files prepared"
}

# Create Windows-specific files
create_windows_files() {
    log_info "Creating Windows-specific files..."
    
    # Create batch file for easy command line access
    cat > "$DIST_DIR/bin/umbra.bat" << 'EOF'
@echo off
REM Umbra Programming Language Launcher
REM Ensures proper environment setup

set UMBRA_HOME=%~dp0
set PATH=%UMBRA_HOME%;%PATH%

REM Launch Umbra with all arguments
"%UMBRA_HOME%\umbra.exe" %*
EOF
    
    # Create PowerShell script for advanced users
    cat > "$DIST_DIR/bin/umbra.ps1" << 'EOF'
# Umbra Programming Language PowerShell Launcher
# Provides enhanced functionality for Windows PowerShell users

param(
    [Parameter(ValueFromRemainingArguments=$true)]
    [string[]]$Arguments
)

$UmbraHome = Split-Path -Parent $MyInvocation.MyCommand.Path
$UmbraExe = Join-Path $UmbraHome "umbra.exe"

if (Test-Path $UmbraExe) {
    & $UmbraExe @Arguments
} else {
    Write-Error "Umbra executable not found at: $UmbraExe"
    exit 1
}
EOF
    
    # Create configuration file
    cat > "$DIST_DIR/umbra.conf" << 'EOF'
# Umbra Programming Language Configuration
# Windows-specific settings

[compiler]
optimization_level = 2
debug_symbols = false
target_cpu = native

[runtime]
memory_limit = 2GB
gc_enabled = true
thread_pool_size = auto

[ai_ml]
python_integration = true
gpu_acceleration = auto
model_cache_dir = %APPDATA%\Umbra\models

[lsp]
enabled = true
port = 9257
log_level = info

[windows]
console_colors = true
file_associations = true
path_integration = true
EOF
    
    log_success "Windows-specific files created"
}

# Create comprehensive NSIS installer script
create_nsis_installer() {
    log_info "Creating NSIS installer script..."
    
    cat > "$NSIS_DIR/umbra-installer.nsi" << 'EOF'
; Umbra Programming Language - Professional Windows Installer
; Complete installation with PATH integration, file associations, and shortcuts

!define PRODUCT_NAME "Umbra Programming Language"
!define PRODUCT_VERSION "1.0.1"
!define PRODUCT_PUBLISHER "Eclipse Softworks"
!define PRODUCT_WEB_SITE "https://umbra-lang.org"
!define PRODUCT_DIR_REGKEY "Software\Microsoft\Windows\CurrentVersion\App Paths\umbra.exe"
!define PRODUCT_UNINST_KEY "Software\Microsoft\Windows\CurrentVersion\Uninstall\${PRODUCT_NAME}"
!define PRODUCT_UNINST_ROOT_KEY "HKLM"

; Modern UI
!include "MUI2.nsh"
!include "x64.nsh"

; Installer settings
Name "${PRODUCT_NAME} ${PRODUCT_VERSION}"
OutFile "umbra-${PRODUCT_VERSION}-windows-x64-installer.exe"
InstallDir "$PROGRAMFILES64\Umbra"
InstallDirRegKey HKLM "${PRODUCT_DIR_REGKEY}" ""
ShowInstDetails show
ShowUnInstDetails show
RequestExecutionLevel admin

; Modern UI Configuration
!define MUI_ABORTWARNING
!define MUI_ICON "${NSISDIR}\Contrib\Graphics\Icons\modern-install.ico"
!define MUI_UNICON "${NSISDIR}\Contrib\Graphics\Icons\modern-uninstall.ico"
!define MUI_HEADERIMAGE
!define MUI_HEADERIMAGE_BITMAP "${NSISDIR}\Contrib\Graphics\Header\nsis3-metro.bmp"
!define MUI_WELCOMEFINISHPAGE_BITMAP "${NSISDIR}\Contrib\Graphics\Wizard\nsis3-metro.bmp"

; Pages
!insertmacro MUI_PAGE_WELCOME
!insertmacro MUI_PAGE_LICENSE "LICENSE"
!insertmacro MUI_PAGE_DIRECTORY
!insertmacro MUI_PAGE_COMPONENTS
!insertmacro MUI_PAGE_INSTFILES
!define MUI_FINISHPAGE_RUN "$INSTDIR\bin\umbra.exe"
!define MUI_FINISHPAGE_RUN_PARAMETERS "--version"
!insertmacro MUI_PAGE_FINISH

; Uninstaller pages
!insertmacro MUI_UNPAGE_INSTFILES

; Language
!insertmacro MUI_LANGUAGE "English"

; Version information
VIProductVersion "*******"
VIAddVersionKey "ProductName" "${PRODUCT_NAME}"
VIAddVersionKey "Comments" "Modern programming language with AI/ML capabilities"
VIAddVersionKey "CompanyName" "${PRODUCT_PUBLISHER}"
VIAddVersionKey "LegalCopyright" "© ${PRODUCT_PUBLISHER}"
VIAddVersionKey "FileDescription" "${PRODUCT_NAME} Installer"
VIAddVersionKey "FileVersion" "${PRODUCT_VERSION}"

; Main installer section
Section "Umbra Compiler (Required)" SEC01
  SectionIn RO
  SetOutPath "$INSTDIR"
  
  ; Install main files
  File "LICENSE"
  File "README.md"
  File "umbra.conf"
  
  ; Install binary
  SetOutPath "$INSTDIR\bin"
  File "bin\umbra.exe"
  File "bin\umbra.bat"
  File "bin\umbra.ps1"
  
  ; Create directories
  CreateDirectory "$APPDATA\Umbra"
  CreateDirectory "$APPDATA\Umbra\models"
  CreateDirectory "$APPDATA\Umbra\cache"
  
  ; Register application
  WriteRegStr HKLM "${PRODUCT_DIR_REGKEY}" "" "$INSTDIR\bin\umbra.exe"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "DisplayName" "$(^Name)"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "UninstallString" "$INSTDIR\uninst.exe"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "DisplayIcon" "$INSTDIR\bin\umbra.exe"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "DisplayVersion" "${PRODUCT_VERSION}"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "URLInfoAbout" "${PRODUCT_WEB_SITE}"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "Publisher" "${PRODUCT_PUBLISHER}"
  
  ; Add to PATH
  DetailPrint "Adding Umbra to system PATH..."
  Call AddToPath
  
  ; Create uninstaller
  WriteUninstaller "$INSTDIR\uninst.exe"
SectionEnd

Section "File Associations" SEC02
  ; Register .umbra file extension
  WriteRegStr HKCR ".umbra" "" "UmbraSourceFile"
  WriteRegStr HKCR "UmbraSourceFile" "" "Umbra Source File"
  WriteRegStr HKCR "UmbraSourceFile\DefaultIcon" "" "$INSTDIR\bin\umbra.exe,0"
  WriteRegStr HKCR "UmbraSourceFile\shell\open\command" "" '"$INSTDIR\bin\umbra.exe" "run" "%1"'
  WriteRegStr HKCR "UmbraSourceFile\shell\compile\command" "" '"$INSTDIR\bin\umbra.exe" "compile" "%1"'
SectionEnd

Section "Desktop Shortcut" SEC03
  CreateShortCut "$DESKTOP\Umbra.lnk" "$INSTDIR\bin\umbra.exe" "--version"
SectionEnd

Section "Start Menu Shortcuts" SEC04
  CreateDirectory "$SMPROGRAMS\Umbra Programming Language"
  CreateShortCut "$SMPROGRAMS\Umbra Programming Language\Umbra.lnk" "$INSTDIR\bin\umbra.exe"
  CreateShortCut "$SMPROGRAMS\Umbra Programming Language\Umbra REPL.lnk" "$INSTDIR\bin\umbra.exe" "repl"
  CreateShortCut "$SMPROGRAMS\Umbra Programming Language\Command Prompt.lnk" "$SYSDIR\cmd.exe" "/k cd /d $\"$INSTDIR\bin$\""
  CreateShortCut "$SMPROGRAMS\Umbra Programming Language\Uninstall.lnk" "$INSTDIR\uninst.exe"
SectionEnd

Section "Examples and Documentation" SEC05
  SetOutPath "$INSTDIR\examples"
  File /r "examples\*.*"
  SetOutPath "$INSTDIR\docs"
  File /r "docs\*.*"
SectionEnd

; Section descriptions
!insertmacro MUI_FUNCTION_DESCRIPTION_BEGIN
  !insertmacro MUI_DESCRIPTION_TEXT ${SEC01} "Core Umbra compiler and runtime (required)"
  !insertmacro MUI_DESCRIPTION_TEXT ${SEC02} "Associate .umbra files with Umbra compiler"
  !insertmacro MUI_DESCRIPTION_TEXT ${SEC03} "Create a desktop shortcut for easy access"
  !insertmacro MUI_DESCRIPTION_TEXT ${SEC04} "Add Umbra to the Start Menu"
  !insertmacro MUI_DESCRIPTION_TEXT ${SEC05} "Install example programs and documentation"
!insertmacro MUI_FUNCTION_DESCRIPTION_END
EOF
    
    # Add PATH management functions to NSIS script
    cat >> "$NSIS_DIR/umbra-installer.nsi" << 'EOF'

; Add to PATH function
Function AddToPath
  Push $R0
  Push $R1
  Push $R2
  Push $R3

  ReadRegStr $R0 HKLM "SYSTEM\CurrentControlSet\Control\Session Manager\Environment" "PATH"
  StrCpy $R1 "$INSTDIR\bin"
  StrLen $R2 "$R1"
  StrCpy $R3 $R0 $R2
  StrCmp $R3 $R1 +3
    StrCmp $R0 "" AddToPath_NTPath
      StrCpy $R0 "$R0;$R1"
  Goto AddToPath_NTPath
  AddToPath_NTPath:
    WriteRegExpandStr HKLM "SYSTEM\CurrentControlSet\Control\Session Manager\Environment" "PATH" $R0
    SendMessage ${HWND_BROADCAST} ${WM_WININICHANGE} 0 "STR:Environment" /TIMEOUT=5000

  Pop $R3
  Pop $R2
  Pop $R1
  Pop $R0
FunctionEnd

; Remove from PATH function
Function un.RemoveFromPath
  Push $R0
  Push $R1
  Push $R2
  Push $R3
  Push $R4
  Push $R5
  Push $R6

  ReadRegStr $R0 HKLM "SYSTEM\CurrentControlSet\Control\Session Manager\Environment" "PATH"
  StrCpy $R1 "$INSTDIR\bin"
  StrLen $R2 "$R1"
  StrLen $R3 $R0
  StrCpy $R4 0

  loop:
    StrCpy $R5 $R0 $R2 $R4
    StrCmp $R5 $R1 found
    StrCmp $R4 $R3 done
    IntOp $R4 $R4 + 1
    Goto loop

  found:
    StrCpy $R5 $R0 $R4
    IntOp $R4 $R4 + $R2
    StrCpy $R6 $R0 "" $R4
    StrCpy $R0 "$R5$R6"
    WriteRegExpandStr HKLM "SYSTEM\CurrentControlSet\Control\Session Manager\Environment" "PATH" $R0
    SendMessage ${HWND_BROADCAST} ${WM_WININICHANGE} 0 "STR:Environment" /TIMEOUT=5000

  done:
  Pop $R6
  Pop $R5
  Pop $R4
  Pop $R3
  Pop $R2
  Pop $R1
  Pop $R0
FunctionEnd

; Uninstaller
Section Uninstall
  ; Remove from PATH
  Call un.RemoveFromPath

  ; Remove file associations
  DeleteRegKey HKCR ".umbra"
  DeleteRegKey HKCR "UmbraSourceFile"

  ; Remove files
  Delete "$INSTDIR\bin\umbra.exe"
  Delete "$INSTDIR\bin\umbra.bat"
  Delete "$INSTDIR\bin\umbra.ps1"
  Delete "$INSTDIR\LICENSE"
  Delete "$INSTDIR\README.md"
  Delete "$INSTDIR\umbra.conf"
  Delete "$INSTDIR\uninst.exe"

  ; Remove directories
  RMDir /r "$INSTDIR\examples"
  RMDir /r "$INSTDIR\docs"
  RMDir "$INSTDIR\bin"
  RMDir "$INSTDIR"

  ; Remove shortcuts
  Delete "$DESKTOP\Umbra.lnk"
  RMDir /r "$SMPROGRAMS\Umbra Programming Language"

  ; Remove registry entries
  DeleteRegKey ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}"
  DeleteRegKey HKLM "${PRODUCT_DIR_REGKEY}"

  SetAutoClose true
SectionEnd

; Constants for PATH manipulation
!ifndef HWND_BROADCAST
!define HWND_BROADCAST 0xffff
!endif
!ifndef WM_WININICHANGE
!define WM_WININICHANGE 0x001A
!endif
EOF

    log_success "NSIS installer script created"
}

# Build the installer
build_installer() {
    log_info "Building Windows installer..."

    # Copy all files to NSIS build directory (excluding nsis-build itself)
    find "$DIST_DIR" -maxdepth 1 -type f -exec cp {} "$NSIS_DIR/" \;
    find "$DIST_DIR" -maxdepth 1 -type d ! -name "nsis-build" ! -path "$DIST_DIR" -exec cp -r {} "$NSIS_DIR/" \;

    # Build the installer
    cd "$NSIS_DIR"

    if makensis umbra-installer.nsi; then
        # Move installer to packages directory
        mkdir -p "$SCRIPT_DIR/distribution/packages/exe"
        mv umbra-*.exe "$SCRIPT_DIR/distribution/packages/exe/"
        log_success "Windows installer built successfully!"
        return 0
    else
        log_error "Failed to build Windows installer"
        return 1
    fi
}

# Main execution
main() {
    log_info "🚀 Building Umbra Windows Installer"
    log_info "==================================="

    check_dependencies
    prepare_distribution
    create_nsis_installer
    build_installer

    # Show results
    if [[ -f "$SCRIPT_DIR/distribution/packages/exe/umbra-"*".exe" ]]; then
        log_success "🎉 Windows installer created successfully!"
        echo
        echo "📦 Installer details:"
        ls -lh "$SCRIPT_DIR/distribution/packages/exe/"umbra-*.exe
        echo
        log_info "✅ Features included:"
        echo "  • Full Umbra compiler (6.6MB Windows binary)"
        echo "  • System PATH integration"
        echo "  • File associations for .umbra files"
        echo "  • Desktop and Start Menu shortcuts"
        echo "  • Complete showcase programs and examples"
        echo "  • Documentation and configuration files"
        echo "  • PowerShell and Batch script launchers"
        echo "  • Clean uninstallation support"
        echo
        log_info "🎯 Ready for distribution!"
    else
        log_error "Failed to create Windows installer"
        exit 1
    fi
}

# Run main function
main "$@"
