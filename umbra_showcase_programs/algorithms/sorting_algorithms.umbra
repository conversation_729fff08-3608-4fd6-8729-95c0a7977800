// Sorting Algorithms Demonstration
// Showcases algorithmic thinking and control structures in Umbra

show("Sorting Algorithms Demonstration")
show("================================")

// Bubble Sort Implementation Simulation
show("Bubble Sort Algorithm Simulation:")

let array_size: Integer := 8
show("Sorting array of size: ", array_size)

// Simulate bubble sort steps with sample data
let step1_a: Integer := 64
let step1_b: Integer := 34
let step1_c: Integer := 25
let step1_d: Integer := 12
let step1_e: Integer := 22
let step1_f: Integer := 11
let step1_g: Integer := 90
let step1_h: Integer := 5

show("Initial array: ", step1_a, ", ", step1_b, ", ", step1_c, ", ", step1_d, ", ", step1_e, ", ", step1_f, ", ", step1_g, ", ", step1_h)

// First pass simulation
show("First pass comparisons:")

// Compare and swap simulation
when step1_a > step1_b:
    show("Swap ", step1_a, " and ", step1_b)
    let temp1: Integer := step1_a
    step1_a := step1_b
    step1_b := temp1

when step1_b > step1_c:
    show("Swap ", step1_b, " and ", step1_c)
    let temp2: Integer := step1_b
    step1_b := step1_c
    step1_c := temp2

when step1_c > step1_d:
    show("Swap ", step1_c, " and ", step1_d)
    let temp3: Integer := step1_c
    step1_c := step1_d
    step1_d := temp3

show("After first pass: ", step1_a, ", ", step1_b, ", ", step1_c, ", ", step1_d, ", ", step1_e, ", ", step1_f, ", ", step1_g, ", ", step1_h)

// Selection Sort Simulation
show("Selection Sort Algorithm Simulation:")

let sel_a: Integer := 29
let sel_b: Integer := 10
let sel_c: Integer := 14
let sel_d: Integer := 37
let sel_e: Integer := 13

show("Initial array: ", sel_a, ", ", sel_b, ", ", sel_c, ", ", sel_d, ", ", sel_e)

// Find minimum and place at beginning
let min_val: Integer := sel_a
let min_pos: Integer := 1

when sel_b < min_val:
    min_val := sel_b
    min_pos := 2

when sel_c < min_val:
    min_val := sel_c
    min_pos := 3

when sel_d < min_val:
    min_val := sel_d
    min_pos := 4

when sel_e < min_val:
    min_val := sel_e
    min_pos := 5

show("Minimum value found: ", min_val, " at position ", min_pos)

// Insertion Sort Simulation
show("Insertion Sort Algorithm Simulation:")

let ins_a: Integer := 5
let ins_b: Integer := 2
let ins_c: Integer := 4
let ins_d: Integer := 6
let ins_e: Integer := 1
let ins_f: Integer := 3

show("Initial array: ", ins_a, ", ", ins_b, ", ", ins_c, ", ", ins_d, ", ", ins_e, ", ", ins_f)

// Simulate insertion of second element
show("Inserting element ", ins_b, " into sorted portion")
when ins_b < ins_a:
    show("Move ", ins_b, " before ", ins_a)

// Binary Search Simulation
show("Binary Search Algorithm Simulation:")

let search_array_size: Integer := 7
let search_target: Integer := 22

show("Searching for ", search_target, " in sorted array")
show("Sorted array: 5, 11, 22, 34, 45, 67, 89")

let left: Integer := 1
let right: Integer := search_array_size
let found: Integer := 0

// First iteration
let mid: Integer := (left + right) / 2
show("First iteration: left=", left, ", right=", right, ", mid=", mid)
show("Middle element is 34")

when search_target < 34:
    show("Target is less than middle, search left half")
    right := mid - 1
otherwise:
    when search_target > 34:
        show("Target is greater than middle, search right half")
        left := mid + 1
    otherwise:
        show("Target found at position ", mid)
        found := 1

// Algorithm complexity analysis
show("Algorithm Complexity Analysis:")

let n: Integer := 1000
show("For array size n = ", n)

let bubble_sort_ops: Integer := n * n
let selection_sort_ops: Integer := n * n
let insertion_sort_ops: Integer := n * n
let binary_search_ops: Integer := 10  // log2(1000) approximately

show("Bubble Sort operations: O(n²) ≈ ", bubble_sort_ops)
show("Selection Sort operations: O(n²) ≈ ", selection_sort_ops)
show("Insertion Sort operations: O(n²) ≈ ", insertion_sort_ops)
show("Binary Search operations: O(log n) ≈ ", binary_search_ops)

show("Sorting algorithms demonstration complete.")
show("Various sorting techniques and their complexities analyzed.")
