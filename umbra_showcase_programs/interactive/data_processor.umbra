// Interactive Data Processing Tool
// Demonstrates data manipulation and analysis capabilities

show("Interactive Data Processing Tool")
show("===============================")
show("Processing sample datasets and performing analysis...")

// Sample data processing
show("Loading and processing customer data...")

let customer_count: Integer := 150
let active_customers: Integer := 120
let inactive_customers: Integer := customer_count - active_customers

show("Customer Statistics:")
show("Total customers: ", customer_count)
show("Active customers: ", active_customers)
show("Inactive customers: ", inactive_customers)

let activity_rate: Float := (active_customers * 100) / customer_count
show("Activity rate: ", activity_rate, "%")

// Sales data processing
show("Processing sales data...")

let q1_sales: Integer := 25000
let q2_sales: Integer := 32000
let q3_sales: Integer := 28000
let q4_sales: Integer := 35000

show("Quarterly Sales:")
show("Q1: ", q1_sales)
show("Q2: ", q2_sales)
show("Q3: ", q3_sales)
show("Q4: ", q4_sales)

let total_sales: Integer := q1_sales + q2_sales + q3_sales + q4_sales
let average_quarterly_sales: Integer := total_sales / 4

show("Total annual sales: ", total_sales)
show("Average quarterly sales: ", average_quarterly_sales)

// Growth analysis
let q1_to_q2_growth: Integer := q2_sales - q1_sales
let q2_to_q3_growth: Integer := q3_sales - q2_sales
let q3_to_q4_growth: Integer := q4_sales - q3_sales

show("Growth Analysis:")
show("Q1 to Q2 growth: ", q1_to_q2_growth)
show("Q2 to Q3 growth: ", q2_to_q3_growth)
show("Q3 to Q4 growth: ", q3_to_q4_growth)

// Product performance analysis
show("Analyzing product performance...")

let product_a_sales: Integer := 15000
let product_b_sales: Integer := 22000
let product_c_sales: Integer := 18000

show("Product Sales:")
show("Product A: ", product_a_sales)
show("Product B: ", product_b_sales)
show("Product C: ", product_c_sales)

let total_product_sales: Integer := product_a_sales + product_b_sales + product_c_sales

let product_a_share: Float := (product_a_sales * 100) / total_product_sales
let product_b_share: Float := (product_b_sales * 100) / total_product_sales
let product_c_share: Float := (product_c_sales * 100) / total_product_sales

show("Market Share:")
show("Product A: ", product_a_share, "%")
show("Product B: ", product_b_share, "%")
show("Product C: ", product_c_share, "%")

// Employee data processing
show("Processing employee data...")

let total_employees: Integer := 85
let full_time: Integer := 65
let part_time: Integer := 20

show("Employee Distribution:")
show("Total employees: ", total_employees)
show("Full-time: ", full_time)
show("Part-time: ", part_time)

let full_time_percentage: Float := (full_time * 100) / total_employees
let part_time_percentage: Float := (part_time * 100) / total_employees

show("Employment Type Distribution:")
show("Full-time: ", full_time_percentage, "%")
show("Part-time: ", part_time_percentage, "%")

// Budget analysis
show("Performing budget analysis...")

let revenue: Integer := 500000
let expenses: Integer := 380000
let profit: Integer := revenue - expenses
let profit_margin: Float := (profit * 100) / revenue

show("Financial Summary:")
show("Revenue: ", revenue)
show("Expenses: ", expenses)
show("Profit: ", profit)
show("Profit margin: ", profit_margin, "%")

show("Data processing analysis complete.")
show("All metrics calculated and analyzed successfully.")
