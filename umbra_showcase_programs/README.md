# Umbra Showcase Programs

This collection demonstrates the full capabilities of the Umbra programming language, including its native AI/ML features, standard library functions, and integration capabilities.

## Directory Structure

```
umbra_showcase_programs/
├── ai_ml/                    # AI/ML specific programs
├── standard_library/         # Standard library demonstrations
├── interactive/              # Interactive user programs
├── integration/              # AI/ML + standard library integration
├── algorithms/               # Algorithm implementations
├── data_processing/          # Data analysis and processing
└── README.md                # This documentation file
```

## Program Categories

### 1. AI/ML Features (`ai_ml/`)

**customer_churn_prediction.umbra**

- Demonstrates real machine learning operations using native Umbra AI/ML syntax
- Uses actual customer data to predict churn probability
- Shows multiple model types: Neural Network, Random Forest, SVM
- Includes model training, evaluation, and prediction capabilities

**data_classification_system.umbra**

- Comprehensive data analysis and classification system
- Demonstrates multiple classifier types and model comparison
- Includes cross-validation and performance evaluation
- Shows real-world data science workflow

### 2. Standard Library Features (`standard_library/`)

**mathematical_operations.umbra**

- Showcases Umbra's mathematical capabilities
- Demonstrates arithmetic operations, calculations, and formulas
- Includes geometric calculations, percentages, and sequences
- Shows working with different numeric types

**string_processing.umbra**

- Comprehensive string manipulation and text processing
- Demonstrates string concatenation, formatting, and analysis
- Shows practical text processing scenarios
- Includes structured text creation and manipulation

### 3. Interactive Programs (`interactive/`)

**calculator.umbra**

- Interactive calculator with multiple operation types
- Demonstrates user input handling and mathematical operations
- Includes complex calculations, percentages, and financial computations
- Shows practical real-world calculation scenarios

**data_processor.umbra**

- Interactive data processing and analysis tool
- Demonstrates data manipulation and statistical analysis
- Includes business metrics calculation and trend analysis
- Shows practical data processing workflows

### 4. Integration Examples (`integration/`)

**business_analytics.umbra**

- Combines AI/ML capabilities with traditional business logic
- Demonstrates integration of machine learning with business metrics
- Includes ROI analysis and market opportunity assessment
- Shows practical business intelligence applications

**recommendation_system.umbra**

- Smart recommendation system using AI and business rules
- Integrates machine learning models with rule-based logic
- Demonstrates personalized recommendation generation
- Includes customer analysis and marketing campaign optimization

### 5. Algorithms (`algorithms/`)

**sorting_algorithms.umbra**

- Demonstrates various sorting algorithm implementations
- Shows algorithmic thinking and control structures
- Includes complexity analysis and performance comparison
- Demonstrates search algorithms and optimization techniques

### 6. Data Processing (`data_processing/`)

**file_analysis.umbra**

- Comprehensive file analysis and data processing system
- Demonstrates dataset loading and statistical analysis
- Includes data quality assessment and insight generation
- Shows practical data analysis workflows

## Running the Programs

To run any of these programs, use the Umbra compiler from the main directory:

```bash
# From the umbra-compiler directory
cargo run run ../umbra_showcase_programs/ai_ml/customer_churn_prediction.umbra
cargo run run ../umbra_showcase_programs/standard_library/mathematical_operations.umbra
cargo run run ../umbra_showcase_programs/interactive/calculator.umbra
```

## Tested and Verified Programs

All programs have been tested and verified to work correctly:

✅ **customer_churn_prediction.umbra** - Successfully loads datasets, trains models, and makes predictions
✅ **mathematical_operations.umbra** - Performs all mathematical calculations correctly
✅ **calculator.umbra** - Executes all calculation examples successfully
✅ **data_classification_system.umbra** - Demonstrates comprehensive ML workflow
✅ **string_processing.umbra** - Shows string manipulation capabilities
✅ **data_processor.umbra** - Performs data analysis and processing
✅ **business_analytics.umbra** - Integrates AI/ML with business logic
✅ **recommendation_system.umbra** - Combines ML models with rule-based logic
✅ **sorting_algorithms.umbra** - Demonstrates algorithmic implementations
✅ **file_analysis.umbra** - Analyzes datasets and generates insights

## Prerequisites

1. **Data Files**: Some AI/ML programs require dataset files in the `data/` directory:
   - `data/customer_churn.csv`
   - `data/training_data.csv`
   - `data/validation_data.csv`
   - `data/test_data.csv`

2. **Python Dependencies**: AI/ML programs require Python with scikit-learn, pandas, and numpy installed.

## Key Features Demonstrated

### Native AI/ML Syntax

- `load_dataset()` - Load CSV datasets with automatic dimension detection
- `create_model()` - Create various ML model types
- `train` statements - Train models with comprehensive parameter configuration
- `evaluate` statements - Evaluate model performance with real metrics
- `predict` statements - Make predictions on new data

### Standard Library Features

- Mathematical operations and calculations
- String manipulation and text processing
- Control structures (when/otherwise statements)
- Variable declarations and type system
- Input/output operations

### Integration Capabilities

- Combining AI/ML with traditional programming
- Business logic integration with machine learning
- Data processing with predictive analytics
- Real-world application development

## Program Characteristics

- **Real Functionality**: All programs perform actual operations, no simulated results
- **Practical Applications**: Programs solve real-world problems and use cases
- **Comprehensive Coverage**: Demonstrates the full range of Umbra's capabilities
- **Production Ready**: Programs are structured for real-world deployment
- **Educational Value**: Each program teaches specific Umbra language features

## Notes

- Programs are designed to be self-contained and demonstrate specific features
- Error handling and edge cases are included where appropriate
- Comments explain the purpose and functionality of each section
- Programs can be used as templates for developing similar applications

This showcase collection provides a comprehensive overview of Umbra's capabilities as both an AI/ML-focused language and a general-purpose programming language.
