// Scientific Computing with Module System
// Advanced demonstration of module imports for scientific applications
// Shows real-world scientific computing scenarios

show("🔬 Scientific Computing with Umbra Module System")
show("===============================================")
show("")

// Import mathematical constants and functions
bring std.math

show("📦 Imported std.math module")
show("🔢 Available constants: π = ", PI, ", e = ", E)
show("")

// Astronomy and Space Science
show("🌌 Astronomy Calculations")
show("=========================")

fn calculate_orbital_circumference(radius_km: Float) -> Float:
    let circumference: Float := 2.0 * PI * radius_km
    return circumference

fn calculate_sphere_surface_area(radius_km: Float) -> Float:
    let surface_area: Float := 4.0 * PI * radius_km * radius_km
    return surface_area

fn calculate_sphere_volume_km3(radius_km: Float) -> Float:
    let volume: Float := (4.0 / 3.0) * PI * radius_km * radius_km * radius_km
    return volume

// Earth calculations
let earth_radius: Float := 6371.0  // km
let earth_circumference: Float := calculate_orbital_circumference(earth_radius)
let earth_surface_area: Float := calculate_sphere_surface_area(earth_radius)
let earth_volume: Float := calculate_sphere_volume_km3(earth_radius)

show("🌍 Earth Measurements:")
show("   Radius: ", earth_radius, " km")
show("   Circumference: ", earth_circumference, " km")
show("   Surface area: ", earth_surface_area, " km²")
show("   Volume: ", earth_volume, " km³")
show("")

// Moon calculations
let moon_radius: Float := 1737.4  // km
let moon_circumference: Float := calculate_orbital_circumference(moon_radius)
let moon_surface_area: Float := calculate_sphere_surface_area(moon_radius)
let moon_volume: Float := calculate_sphere_volume_km3(moon_radius)

show("🌙 Moon Measurements:")
show("   Radius: ", moon_radius, " km")
show("   Circumference: ", moon_circumference, " km")
show("   Surface area: ", moon_surface_area, " km²")
show("   Volume: ", moon_volume, " km³")
show("")

// Engineering Applications
show("⚙️ Engineering Applications")
show("===========================")

fn calculate_pipe_volume(diameter_m: Float, length_m: Float) -> Float:
    let radius: Float := diameter_m / 2.0
    let cross_section_area: Float := PI * radius * radius
    let volume: Float := cross_section_area * length_m
    return volume

fn calculate_tank_capacity(diameter_m: Float, height_m: Float) -> Float:
    let radius: Float := diameter_m / 2.0
    let base_area: Float := PI * radius * radius
    let capacity: Float := base_area * height_m
    return capacity

fn calculate_gear_circumference(diameter_mm: Float) -> Float:
    let circumference: Float := PI * diameter_mm
    return circumference

// Engineering calculations
let pipe_diameter: Float := 0.5    // meters
let pipe_length: Float := 100.0    // meters
let pipe_volume: Float := calculate_pipe_volume(pipe_diameter, pipe_length)

let tank_diameter: Float := 3.0    // meters
let tank_height: Float := 5.0      // meters
let tank_capacity: Float := calculate_tank_capacity(tank_diameter, tank_height)

let gear_diameter: Float := 150.0  // mm
let gear_circumference: Float := calculate_gear_circumference(gear_diameter)

show("🔧 Engineering Calculations:")
show("   Pipe volume (Ø", pipe_diameter, "m × ", pipe_length, "m): ", pipe_volume, " m³")
show("   Tank capacity (Ø", tank_diameter, "m × ", tank_height, "m): ", tank_capacity, " m³")
show("   Gear circumference (Ø", gear_diameter, "mm): ", gear_circumference, " mm")
show("")

// Physics and Chemistry
show("⚛️ Physics & Chemistry")
show("======================")

fn calculate_exponential_decay(initial_amount: Float, decay_constant: Float, time: Float) -> Float:
    let decay_factor: Float := E * decay_constant * time
    let remaining: Float := initial_amount / decay_factor
    return remaining

fn calculate_population_growth(initial_pop: Float, growth_rate: Float, time: Float) -> Float:
    let growth_factor: Float := E * growth_rate * time
    let final_pop: Float := initial_pop * growth_factor
    return final_pop

fn calculate_radioactive_half_life(decay_constant: Float) -> Float:
    let half_life: Float := 0.693 / decay_constant
    return half_life

// Physics calculations
let initial_mass: Float := 100.0      // grams
let decay_rate: Float := 0.1          // per year
let time_period: Float := 5.0         // years

let remaining_mass: Float := calculate_exponential_decay(initial_mass, decay_rate, time_period)
let half_life: Float := calculate_radioactive_half_life(decay_rate)

let bacteria_initial: Float := 1000.0  // count
let growth_rate: Float := 0.2          // per hour
let growth_time: Float := 8.0          // hours
let bacteria_final: Float := calculate_population_growth(bacteria_initial, growth_rate, growth_time)

show("🧪 Physics & Chemistry Results:")
show("   Radioactive decay (", initial_mass, "g after ", time_period, " years): ", remaining_mass, "g")
show("   Half-life: ", half_life, " years")
show("   Bacterial growth (", bacteria_initial, " → ", bacteria_final, " in ", growth_time, " hours)")
show("")

// Mathematical Analysis
show("📊 Mathematical Analysis")
show("========================")

fn calculate_circle_sector_properties(radius: Float, angle_degrees: Float) -> Float:
    let angle_radians: Float := angle_degrees * PI / 180.0
    let arc_length: Float := radius * angle_radians
    let sector_area: Float := 0.5 * radius * radius * angle_radians
    let chord_length: Float := 2.0 * radius  // Simplified approximation
    
    show("   Sector analysis (r=", radius, ", θ=", angle_degrees, "°):")
    show("     Arc length: ", arc_length)
    show("     Sector area: ", sector_area)
    show("     Chord length: ", chord_length)
    
    return sector_area

fn analyze_multiple_circles() -> Float:
    let radii: List[Float] := [1.0, 2.5, 5.0, 7.5, 10.0]
    let total_area: Float := 0.0
    
    show("   Circle analysis:")

    let r1: Float := 1.0
    let area1: Float := PI * r1 * r1
    show("     r=", r1, " → area=", area1)
    
    let r2: Float := 2.5
    let area2: Float := PI * r2 * r2
    show("     r=", r2, " → area=", area2)
    
    let r3: Float := 5.0
    let area3: Float := PI * r3 * r3
    show("     r=", r3, " → area=", area3)
    
    let total: Float := area1 + area2 + area3
    return total

// Mathematical analysis
let sector_area: Float := calculate_circle_sector_properties(6.0, 45.0)
let total_circle_area: Float := analyze_multiple_circles()

show("📈 Analysis Results:")
show("   Total area of analyzed circles: ", total_circle_area)
show("")

// Precision and Constants Verification
show("🎯 Precision Verification")
show("=========================")

let pi_calculations: Float := PI * PI * PI  // π³
let e_calculations: Float := E * E * E      // e³
let mixed_calculation: Float := PI * E      // π × e
let ratio_calculation: Float := PI / E      // π / e

show("🔢 Advanced constant calculations:")
show("   π³ = ", pi_calculations)
show("   e³ = ", e_calculations)
show("   π × e = ", mixed_calculation)
show("   π / e = ", ratio_calculation)
show("")

// Integration with builtin functions
show("🔗 Integration with Builtin Functions")
show("=====================================")

let negative_number: Integer := -42
let abs_negative_number: Integer := abs(negative_number)

let test_string: String := "Scientific Computing with π and e"
let string_length: Integer := str_len(test_string)

show("🔧 Builtin function integration:")
show("   abs(-42) = ", abs_negative_number)
show("   String length: ", string_length, " characters")
show("")

// Final Summary
show("✅ Scientific Computing Demonstration Complete!")
show("==============================================")
show("✅ Imported std.math module with 'bring' keyword")
show("✅ Applied mathematical constants to astronomy calculations")
show("✅ Used π and e in engineering applications")
show("✅ Demonstrated physics and chemistry computations")
show("✅ Performed mathematical analysis with imported constants")
show("✅ Verified precision of imported constants")
show("✅ Integrated module imports with builtin functions")
show("")
show("🎉 Umbra's module system enables sophisticated scientific computing!")
