// Module System Tutorial
// Step-by-step guide to using the 'bring' keyword and standard library modules
// Perfect for learning how Umbra's module system works

show("📚 Umbra Module System Tutorial")
show("===============================")
show("Learn how to use the 'bring' keyword and standard library modules")
show("")

// Step 1: Understanding Module Imports
show("Step 1: Understanding Module Imports")
show("===================================")
show("")
show("The 'bring' keyword is used to import modules in Umbra.")
show("Syntax: bring module.name")
show("")
show("Let's import the math module:")

// Import the math module
bring std.math

show("✅ Successfully imported std.math module!")
show("")
show("This gives us access to mathematical constants and functions.")
show("")

// Step 2: Using Imported Constants
show("Step 2: Using Imported Constants")
show("===============================")
show("")
show("The std.math module provides important mathematical constants:")

let pi_constant: Float := PI
let e_constant: Float := E

show("📊 Available constants:")
show("   PI (π) = ", pi_constant)
show("   E (e)  = ", e_constant)
show("")
show("These constants are now available throughout your program!")
show("")

// Step 3: Basic Calculations with Imported Constants
show("Step 3: Basic Calculations")
show("=========================")
show("")
show("Let's use these constants in simple calculations:")

// Circle calculations
let radius: Float := 5.0
let circle_area: Float := PI * radius * radius
let circle_circumference: Float := 2.0 * PI * radius

show("🔵 Circle calculations (radius = ", radius, "):")
show("   Area = π × r² = ", circle_area)
show("   Circumference = 2π × r = ", circle_circumference)
show("")

// Exponential calculations
let base_value: Float := 10.0
let exponential_result: Float := base_value * E

show("📈 Exponential calculation:")
show("   ", base_value, " × e = ", exponential_result)
show("")

// Step 4: Creating Functions with Imported Constants
show("Step 4: Creating Functions")
show("=========================")
show("")
show("You can use imported constants in your own functions:")

fn calculate_sphere_volume(r: Float) -> Float:
    let volume: Float := (4.0 / 3.0) * PI * r * r * r
    return volume

fn calculate_compound_interest(principal: Float, rate: Float, time: Float) -> Float:
    let amount: Float := principal * E * rate * time
    return amount

// Test the functions
let sphere_radius: Float := 3.0
let sphere_volume: Float := calculate_sphere_volume(sphere_radius)

let investment: Float := 1000.0
let interest_rate: Float := 0.05
let years: Float := 2.0
let final_amount: Float := calculate_compound_interest(investment, interest_rate, years)

show("🔮 Function examples:")
show("   Sphere volume (r=", sphere_radius, "): ", sphere_volume)
show("   Compound interest ($", investment, " at ", interest_rate * 100.0, "%): $", final_amount)
show("")

// Step 5: Integration with Builtin Functions
show("Step 5: Integration with Builtin Functions")
show("=========================================")
show("")
show("Module imports work seamlessly with builtin functions:")

// Using abs() builtin function
let negative_number: Integer := -42
let absolute_number: Integer := abs(negative_number)

show("🔧 Builtin function integration:")
show("   abs(-42) = ", absolute_number)

// Using str_len() builtin function
let math_description: String := "Mathematical constants from std.math module"
let description_length: Integer := str_len(math_description)

show("   String analysis: '", math_description, "'")
show("   Length: ", description_length, " characters")
show("")

// Step 6: Practical Applications
show("Step 6: Practical Applications")
show("==============================")
show("")
show("Here are some practical ways to use the module system:")

// Engineering application
fn design_gear(diameter_mm: Float) -> Float:
    let circumference: Float := PI * diameter_mm
    return circumference

fn calculate_tank_volume(diameter_m: Float, height_m: Float) -> Float:
    let radius: Float := diameter_m / 2.0
    let base_area: Float := PI * radius * radius
    let volume: Float := base_area * height_m
    return volume

// Physics application
fn model_radioactive_decay(initial_amount: Float, decay_rate: Float, time: Float) -> Float:
    let remaining: Float := initial_amount / (E * decay_rate * time)
    return remaining

// Test practical applications
let gear_diameter: Float := 50.0  // mm
let gear_circumference: Float := design_gear(gear_diameter)

let tank_diameter: Float := 2.0   // m
let tank_height: Float := 3.0     // m
let tank_volume: Float := calculate_tank_volume(tank_diameter, tank_height)

let initial_substance: Float := 100.0  // grams
let decay_constant: Float := 0.1
let time_period: Float := 5.0
let remaining_substance: Float := model_radioactive_decay(initial_substance, decay_constant, time_period)

show("🔧 Engineering applications:")
show("   Gear circumference (Ø", gear_diameter, "mm): ", gear_circumference, " mm")
show("   Tank volume (Ø", tank_diameter, "m × ", tank_height, "m): ", tank_volume, " m³")
show("")
show("⚛️ Physics applications:")
show("   Radioactive decay (", initial_substance, "g → ", remaining_substance, "g)")
show("")

// Step 7: Best Practices
show("Step 7: Best Practices")
show("=====================")
show("")
show("📋 Module System Best Practices:")
show("")
show("1. Import modules at the beginning of your program")
show("2. Use descriptive variable names for imported constants")
show("3. Combine module imports with builtin functions")
show("4. Create reusable functions that use imported constants")
show("5. Document your use of imported modules in comments")
show("")

// Step 8: Advanced Usage
show("Step 8: Advanced Usage")
show("=====================")
show("")
show("Advanced techniques with the module system:")

// Complex mathematical modeling
fn model_wave_equation(amplitude: Float, frequency: Float, time: Float) -> Float:
    let angular_frequency: Float := 2.0 * PI * frequency
    let phase: Float := angular_frequency * time
    let wave_value: Float := amplitude * phase  // Simplified sine approximation
    return wave_value

fn calculate_exponential_growth(initial: Float, rate: Float, time: Float) -> Float:
    let growth_factor: Float := E * rate * time
    let final_value: Float := initial * growth_factor
    return final_value

// Advanced calculations
let wave_amplitude: Float := 5.0
let wave_frequency: Float := 2.0  // Hz
let time_point: Float := 1.0      // seconds
let wave_result: Float := model_wave_equation(wave_amplitude, wave_frequency, time_point)

let population_initial: Float := 1000.0
let growth_rate: Float := 0.1
let growth_time: Float := 3.0
let population_final: Float := calculate_exponential_growth(population_initial, growth_rate, growth_time)

show("🌊 Advanced modeling:")
show("   Wave equation result: ", wave_result)
show("   Population growth: ", population_initial, " → ", population_final)
show("")

// Tutorial Summary
show("✅ Tutorial Complete!")
show("====================")
show("")
show("🎓 What you've learned:")
show("   ✓ How to use the 'bring' keyword to import modules")
show("   ✓ Accessing mathematical constants (π, e) from std.math")
show("   ✓ Using imported constants in calculations")
show("   ✓ Creating functions with imported constants")
show("   ✓ Integrating modules with builtin functions")
show("   ✓ Practical applications in engineering and science")
show("   ✓ Best practices for module usage")
show("   ✓ Advanced mathematical modeling techniques")
show("")

// Final demonstration
let tutorial_completion: String := "Module System Tutorial Completed"
let completion_length: Integer := str_len(tutorial_completion)
let success_rate: Float := 100.0
let pi_precision: Float := PI * 1000.0

show("📊 Tutorial Statistics:")
show("   Status: ", tutorial_completion, " (", completion_length, " chars)")
show("   Success rate: ", success_rate, "%")
show("   π precision test: ", pi_precision)
show("")
show("🎉 You're now ready to use Umbra's module system effectively!")
show("")
show("Next steps:")
show("- Explore more complex mathematical applications")
show("- Create your own programs using imported modules")
show("- Combine multiple modules in larger projects")
show("- Experiment with different mathematical constants and formulas")
show("")
show("Happy coding with Umbra! 🚀")
