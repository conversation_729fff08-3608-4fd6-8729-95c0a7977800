// String Processing Demonstration
// Showcases Umbra's string manipulation and text processing capabilities

show("String Processing Demonstration")
show("===============================")

// Basic string operations
let greeting: String := "Hello"
let target: String := "World"
let punctuation: String := "!"

show("Basic String Operations:")
show("greeting = ", greeting)
show("target = ", target)
show("punctuation = ", punctuation)

// String concatenation
let message: String := greeting + " " + target + punctuation
show("Concatenated message: ", message)

// Working with longer strings
let sentence: String := "The quick brown fox jumps over the lazy dog"
show("Sample sentence: ", sentence)

// String analysis
let word1: String := "programming"
let word2: String := "language"
let word3: String := "Umbra"

show("String Analysis:")
show("Word 1: ", word1)
show("Word 2: ", word2)
show("Word 3: ", word3)

// Creating formatted output
let name: String := "Alice"
let age: Integer := 25
let city: String := "New York"

show("Formatted Information:")
show("Name: ", name)
show("Age: ", age, " years old")
show("City: ", city)

let profile: String := name + " is " + city + " resident"
show("Profile: ", profile)

// Working with multiple strings
let fruits: String := "apple"
let vegetables: String := "carrot"
let grains: String := "rice"

show("Food Categories:")
show("Fruit: ", fruits)
show("Vegetable: ", vegetables)
show("Grain: ", grains)

let shopping_list: String := fruits + ", " + vegetables + ", " + grains
show("Shopping list: ", shopping_list)

// Text formatting examples
let title: String := "Data Analysis Report"
let author: String := "John Smith"
let date: String := "2024-01-15"

show("Document Information:")
show("Title: ", title)
show("Author: ", author)
show("Date: ", date)

let header: String := title + " by " + author + " (" + date + ")"
show("Document header: ", header)

// Working with numbers and strings
let product: String := "Laptop"
let price: Integer := 999
let currency: String := "USD"

show("Product Information:")
show("Product: ", product)
show("Price: ", price, " ", currency)

let product_description: String := product + " costs " + currency
show("Description: ", product_description)

// Creating structured text
let first_name: String := "Emma"
let last_name: String := "Johnson"
let department: String := "Engineering"
let position: String := "Software Developer"

show("Employee Information:")
show("First Name: ", first_name)
show("Last Name: ", last_name)
show("Department: ", department)
show("Position: ", position)

let full_name: String := first_name + " " + last_name
let job_title: String := position + " in " + department
show("Full Name: ", full_name)
show("Job Title: ", job_title)

show("String processing demonstration complete.")
