use std::env;
use std::process::Command;

fn main() {
    // Get target triple first
    let target = env::var("TARGET").unwrap_or_else(|_| "unknown".to_string());
    let is_windows_target = target.contains("windows");

    // Get build date - use simple approach for cross-compilation
    let build_date = if is_windows_target {
        "2025-01-18 12:00:00 UTC".to_string() // Static date for Windows builds
    } else {
        chrono::Utc::now().format("%Y-%m-%d %H:%M:%S UTC").to_string()
    };
    println!("cargo:rustc-env=BUILD_DATE={}", build_date);

    // Get git commit hash - skip for Windows cross-compilation
    let git_commit = if is_windows_target {
        "cross-compiled".to_string()
    } else {
        Command::new("git")
            .args(&["rev-parse", "--short", "HEAD"])
            .output()
            .map(|output| String::from_utf8_lossy(&output.stdout).trim().to_string())
            .unwrap_or_else(|_| "unknown".to_string())
    };
    println!("cargo:rustc-env=BUILD_COMMIT={}", git_commit);

    println!("cargo:rustc-env=BUILD_TARGET={}", target);

    // Tell cargo to rerun if git HEAD changes (skip for Windows)
    if !is_windows_target {
        println!("cargo:rerun-if-changed=.git/HEAD");
        println!("cargo:rerun-if-changed=.git/refs/heads/");
    }
}
