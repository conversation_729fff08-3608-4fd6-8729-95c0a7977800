// Basic Mathematics Example for Umbra
// Demonstrates variables, functions, and mathematical operations

fn add(a: Integer, b: Integer) -> Integer: {
    return a + b
}

fn multiply(a: Integer, b: Integer) -> Integer: {
    return a * b
}

fn main() -> Void: {
    let x: Integer := 10
    let y: Integer := 5
    
    let sum: Integer := add(x, y)
    let product: Integer := multiply(x, y)
    
    show("Basic Math Operations:")
    show("10 + 5 = 15")
    show("10 * 5 = 50")
    
    when sum > product: {
        show("Sum is greater than product")
    } otherwise: {
        show("Product is greater than sum")
    }
}
