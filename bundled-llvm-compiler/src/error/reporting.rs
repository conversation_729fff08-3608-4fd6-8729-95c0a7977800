/// Error reporting and analysis utilities for Umbra
/// 
/// This module provides comprehensive error reporting capabilities including
/// console output, file logging, JSON export, HTML reports, and error analysis.

use crate::error::{EnhancedError, ErrorSeverity};
use std::collections::HashMap;
use std::fs::File;
use std::io::Write;
use std::time::{SystemTime, UNIX_EPOCH};

/// Error report format options
#[derive(Debug, Clone, PartialEq)]
pub enum ErrorReportFormat {
    Console,
    Json,
    Html,
    Xml,
    Markdown,
    Plain,
}

/// Comprehensive error report
#[derive(Debug, Clone)]
pub struct ErrorReport {
    pub timestamp: u64,
    pub errors: Vec<EnhancedError>,
    pub summary: ErrorSummary,
    pub environment: EnvironmentInfo,
    pub metadata: HashMap<String, String>,
}

/// Error summary statistics
#[derive(Debug, Clone)]
pub struct ErrorSummary {
    pub total_errors: usize,
    pub critical_errors: usize,
    pub high_errors: usize,
    pub medium_errors: usize,
    pub low_errors: usize,
    pub error_types: HashMap<String, usize>,
    pub most_common_error: Option<String>,
}

/// Environment information for error reports
#[derive(Debug, Clone)]
pub struct EnvironmentInfo {
    pub os: String,
    pub arch: String,
    pub umbra_version: String,
    pub rust_version: String,
    pub working_directory: String,
    pub user: Option<String>,
}

impl EnvironmentInfo {
    pub fn collect() -> Self {
        Self {
            os: std::env::consts::OS.to_string(),
            arch: std::env::consts::ARCH.to_string(),
            umbra_version: env!("CARGO_PKG_VERSION").to_string(),
            rust_version: "1.70.0".to_string(), // This would be dynamically determined
            working_directory: std::env::current_dir()
                .map(|p| p.to_string_lossy().to_string())
                .unwrap_or_else(|_| "unknown".to_string()),
            user: std::env::var("USER").or_else(|_| std::env::var("USERNAME")).ok(),
        }
    }
}

/// Error reporter trait
pub trait ErrorReporter {
    fn report(&self, report: &ErrorReport) -> Result<(), Box<dyn std::error::Error>>;
    fn format(&self) -> ErrorReportFormat;
}

/// Console error reporter
pub struct ConsoleReporter {
    pub use_colors: bool,
    pub verbose: bool,
}

impl ConsoleReporter {
    pub fn new() -> Self {
        Self {
            use_colors: true,
            verbose: false,
        }
    }

    pub fn with_colors(mut self, use_colors: bool) -> Self {
        self.use_colors = use_colors;
        self
    }

    pub fn with_verbose(mut self, verbose: bool) -> Self {
        self.verbose = verbose;
        self
    }

    fn colorize(&self, text: &str, color: &str) -> String {
        if self.use_colors {
            match color {
                "red" => format!("\x1b[31m{}\x1b[0m", text),
                "yellow" => format!("\x1b[33m{}\x1b[0m", text),
                "green" => format!("\x1b[32m{}\x1b[0m", text),
                "blue" => format!("\x1b[34m{}\x1b[0m", text),
                "bold" => format!("\x1b[1m{}\x1b[0m", text),
                _ => text.to_string(),
            }
        } else {
            text.to_string()
        }
    }
}

impl Default for ConsoleReporter {
    fn default() -> Self {
        Self::new()
    }
}

impl ErrorReporter for ConsoleReporter {
    fn report(&self, report: &ErrorReport) -> Result<(), Box<dyn std::error::Error>> {
        println!("{}", self.colorize("Umbra Error Report", "bold"));
        println!("{}", "=".repeat(50));
        
        // Print summary
        println!("Total Errors: {}", report.summary.total_errors);
        println!("Critical: {}", self.colorize(&report.summary.critical_errors.to_string(), "red"));
        println!("High: {}", self.colorize(&report.summary.high_errors.to_string(), "yellow"));
        println!("Medium: {}", report.summary.medium_errors);
        println!("Low: {}", report.summary.low_errors);
        println!();

        // Print errors
        for (i, error) in report.errors.iter().enumerate() {
            let severity_color = match error.severity {
                ErrorSeverity::Critical => "red",
                ErrorSeverity::High => "yellow",
                ErrorSeverity::Medium => "blue",
                ErrorSeverity::Low => "green",
                ErrorSeverity::Info => "green",
            };

            println!("{}. {}", i + 1, self.colorize(&format!("{:?}", error.severity), severity_color));
            println!("   {}", error.error);
            
            if let Some(ref code) = error.error_code {
                println!("   Code: {}", code);
            }

            if self.verbose {
                if let Some(ref context) = error.context {
                    println!("   Context: {}", context.operation);
                }
                
                if !error.suggestions.is_empty() {
                    println!("   Suggestions:");
                    for suggestion in &error.suggestions {
                        println!("     - {}", suggestion);
                    }
                }
            }
            println!();
        }

        if self.verbose {
            println!("Environment:");
            println!("  OS: {}", report.environment.os);
            println!("  Architecture: {}", report.environment.arch);
            println!("  Umbra Version: {}", report.environment.umbra_version);
            println!("  Working Directory: {}", report.environment.working_directory);
        }

        Ok(())
    }

    fn format(&self) -> ErrorReportFormat {
        ErrorReportFormat::Console
    }
}

/// File error reporter
pub struct FileReporter {
    pub file_path: String,
    pub format: ErrorReportFormat,
}

impl FileReporter {
    pub fn new(file_path: &str, format: ErrorReportFormat) -> Self {
        Self {
            file_path: file_path.to_string(),
            format,
        }
    }
}

impl ErrorReporter for FileReporter {
    fn report(&self, report: &ErrorReport) -> Result<(), Box<dyn std::error::Error>> {
        let mut file = File::create(&self.file_path)?;
        
        match self.format {
            ErrorReportFormat::Plain => {
                writeln!(file, "Umbra Error Report")?;
                writeln!(file, "==================")?;
                writeln!(file, "Timestamp: {}", report.timestamp)?;
                writeln!(file, "Total Errors: {}", report.summary.total_errors)?;
                writeln!(file)?;

                for (i, error) in report.errors.iter().enumerate() {
                    writeln!(file, "{}. {:?}: {}", i + 1, error.severity, error.error)?;
                    if let Some(ref code) = error.error_code {
                        writeln!(file, "   Code: {}", code)?;
                    }
                    writeln!(file)?;
                }
            }
            ErrorReportFormat::Json => {
                let json_reporter = JsonReporter::new();
                let json_content = json_reporter.generate_json(report)?;
                write!(file, "{}", json_content)?;
            }
            ErrorReportFormat::Html => {
                let html_reporter = HtmlReporter::new();
                let html_content = html_reporter.generate_html(report)?;
                write!(file, "{}", html_content)?;
            }
            _ => {
                return Err("Unsupported format for file reporter".into());
            }
        }

        Ok(())
    }

    fn format(&self) -> ErrorReportFormat {
        self.format.clone()
    }
}

/// JSON error reporter
pub struct JsonReporter;

impl JsonReporter {
    pub fn new() -> Self {
        Self
    }

    pub fn generate_json(&self, report: &ErrorReport) -> Result<String, Box<dyn std::error::Error>> {
        // Simple JSON generation (in a real implementation, you'd use serde)
        let mut json = String::new();
        json.push_str("{\n");
        json.push_str(&format!("  \"timestamp\": {},\n", report.timestamp));
        json.push_str(&format!("  \"total_errors\": {},\n", report.summary.total_errors));
        json.push_str("  \"errors\": [\n");

        for (i, error) in report.errors.iter().enumerate() {
            json.push_str("    {\n");
            json.push_str(&format!("      \"severity\": \"{:?}\",\n", error.severity));
            json.push_str(&format!("      \"message\": \"{}\",\n", error.error.to_string().replace('"', "\\\"")));
            if let Some(ref code) = error.error_code {
                json.push_str(&format!("      \"code\": \"{}\",\n", code));
            }
            json.push_str("      \"suggestions\": [\n");
            for (j, suggestion) in error.suggestions.iter().enumerate() {
                json.push_str(&format!("        \"{}\"", suggestion.replace('"', "\\\"")));
                if j < error.suggestions.len() - 1 {
                    json.push_str(",");
                }
                json.push_str("\n");
            }
            json.push_str("      ]\n");
            json.push_str("    }");
            if i < report.errors.len() - 1 {
                json.push_str(",");
            }
            json.push_str("\n");
        }

        json.push_str("  ],\n");
        json.push_str("  \"environment\": {\n");
        json.push_str(&format!("    \"os\": \"{}\",\n", report.environment.os));
        json.push_str(&format!("    \"arch\": \"{}\",\n", report.environment.arch));
        json.push_str(&format!("    \"umbra_version\": \"{}\"\n", report.environment.umbra_version));
        json.push_str("  }\n");
        json.push_str("}\n");

        Ok(json)
    }
}

impl Default for JsonReporter {
    fn default() -> Self {
        Self::new()
    }
}

impl ErrorReporter for JsonReporter {
    fn report(&self, report: &ErrorReport) -> Result<(), Box<dyn std::error::Error>> {
        let json = self.generate_json(report)?;
        println!("{}", json);
        Ok(())
    }

    fn format(&self) -> ErrorReportFormat {
        ErrorReportFormat::Json
    }
}

/// HTML error reporter
pub struct HtmlReporter;

impl HtmlReporter {
    pub fn new() -> Self {
        Self
    }

    pub fn generate_html(&self, report: &ErrorReport) -> Result<String, Box<dyn std::error::Error>> {
        let mut html = String::new();
        
        html.push_str("<!DOCTYPE html>\n");
        html.push_str("<html>\n<head>\n");
        html.push_str("<title>Umbra Error Report</title>\n");
        html.push_str("<style>\n");
        html.push_str("body { font-family: Arial, sans-serif; margin: 20px; }\n");
        html.push_str(".error { margin: 10px 0; padding: 10px; border-left: 4px solid; }\n");
        html.push_str(".critical { border-color: #d32f2f; background-color: #ffebee; }\n");
        html.push_str(".high { border-color: #f57c00; background-color: #fff3e0; }\n");
        html.push_str(".medium { border-color: #1976d2; background-color: #e3f2fd; }\n");
        html.push_str(".low { border-color: #388e3c; background-color: #e8f5e8; }\n");
        html.push_str("</style>\n");
        html.push_str("</head>\n<body>\n");
        
        html.push_str("<h1>Umbra Error Report</h1>\n");
        html.push_str(&format!("<p>Generated: {}</p>\n", report.timestamp));
        html.push_str(&format!("<p>Total Errors: {}</p>\n", report.summary.total_errors));
        
        html.push_str("<h2>Errors</h2>\n");
        for (i, error) in report.errors.iter().enumerate() {
            let severity_class = match error.severity {
                ErrorSeverity::Critical => "critical",
                ErrorSeverity::High => "high",
                ErrorSeverity::Medium => "medium",
                ErrorSeverity::Low => "low",
                ErrorSeverity::Info => "low",
            };
            
            html.push_str(&format!("<div class=\"error {}\">\n", severity_class));
            html.push_str(&format!("<h3>{}. {:?}</h3>\n", i + 1, error.severity));
            html.push_str(&format!("<p>{}</p>\n", error.error.to_string().replace('<', "&lt;").replace('>', "&gt;")));
            
            if let Some(ref code) = error.error_code {
                html.push_str(&format!("<p><strong>Code:</strong> {}</p>\n", code));
            }
            
            if !error.suggestions.is_empty() {
                html.push_str("<h4>Suggestions:</h4>\n<ul>\n");
                for suggestion in &error.suggestions {
                    html.push_str(&format!("<li>{}</li>\n", suggestion.replace('<', "&lt;").replace('>', "&gt;")));
                }
                html.push_str("</ul>\n");
            }
            
            html.push_str("</div>\n");
        }
        
        html.push_str("</body>\n</html>\n");
        Ok(html)
    }
}

impl Default for HtmlReporter {
    fn default() -> Self {
        Self::new()
    }
}

impl ErrorReporter for HtmlReporter {
    fn report(&self, report: &ErrorReport) -> Result<(), Box<dyn std::error::Error>> {
        let html = self.generate_html(report)?;
        println!("{}", html);
        Ok(())
    }

    fn format(&self) -> ErrorReportFormat {
        ErrorReportFormat::Html
    }
}

/// Error analyzer for pattern detection and trends
pub struct ErrorAnalyzer;

/// Error pattern for analysis
#[derive(Debug, Clone)]
pub struct ErrorPattern {
    pub pattern: String,
    pub frequency: usize,
    pub severity: ErrorSeverity,
    pub first_seen: u64,
    pub last_seen: u64,
}

/// Error trend information
#[derive(Debug, Clone)]
pub struct ErrorTrend {
    pub error_type: String,
    pub trend_direction: TrendDirection,
    pub change_percentage: f64,
    pub time_window: u64,
}

#[derive(Debug, Clone, PartialEq)]
pub enum TrendDirection {
    Increasing,
    Decreasing,
    Stable,
}

impl ErrorAnalyzer {
    pub fn new() -> Self {
        Self
    }

    pub fn analyze_patterns(&self, errors: &[EnhancedError]) -> Vec<ErrorPattern> {
        let mut patterns = HashMap::new();
        let now = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_secs();

        for error in errors {
            let pattern_key = error.error.to_string();
            let entry = patterns.entry(pattern_key.clone()).or_insert(ErrorPattern {
                pattern: pattern_key,
                frequency: 0,
                severity: error.severity.clone(),
                first_seen: now,
                last_seen: now,
            });

            entry.frequency += 1;
            entry.last_seen = now;
        }

        patterns.into_values().collect()
    }

    pub fn detect_trends(&self, _current_errors: &[EnhancedError], _historical_errors: &[EnhancedError]) -> Vec<ErrorTrend> {
        // This would implement trend analysis comparing current vs historical errors
        Vec::new()
    }
}

impl Default for ErrorAnalyzer {
    fn default() -> Self {
        Self::new()
    }
}
