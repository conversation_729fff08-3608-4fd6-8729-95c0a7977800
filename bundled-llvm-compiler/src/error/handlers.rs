/// Advanced error handling utilities for Umbra
/// 
/// This module provides sophisticated error handling mechanisms including
/// error recovery, retry logic, circuit breakers, and error reporting.

use crate::error::{UmbraError, UmbraResult, RecoveryStrategy};
use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use std::time::{Duration, Instant};

/// Error handler trait for custom error processing
pub trait ErrorHandler: Send + Sync {
    fn handle_error(&self, error: &UmbraError) -> Option<RecoveryStrategy>;
    fn can_handle(&self, error: &UmbraError) -> bool;
    fn priority(&self) -> u32;
}

/// Error handler registry
pub struct ErrorHandlerRegistry {
    handlers: Vec<Box<dyn ErrorHandler>>,
}

impl ErrorHandlerRegistry {
    pub fn new() -> Self {
        Self {
            handlers: Vec::new(),
        }
    }

    pub fn register(&mut self, handler: Box<dyn ErrorHandler>) {
        self.handlers.push(handler);
        // Sort by priority (higher priority first)
        self.handlers.sort_by(|a, b| b.priority().cmp(&a.priority()));
    }

    pub fn handle_error(&self, error: &UmbraError) -> Option<RecoveryStrategy> {
        for handler in &self.handlers {
            if handler.can_handle(error) {
                if let Some(strategy) = handler.handle_error(error) {
                    return Some(strategy);
                }
            }
        }
        None
    }
}

impl Default for ErrorHandlerRegistry {
    fn default() -> Self {
        Self::new()
    }
}

/// Retry configuration
#[derive(Debug, Clone)]
pub struct RetryConfig {
    pub max_attempts: usize,
    pub base_delay: Duration,
    pub max_delay: Duration,
    pub backoff_multiplier: f64,
    pub jitter: bool,
}

impl Default for RetryConfig {
    fn default() -> Self {
        Self {
            max_attempts: 3,
            base_delay: Duration::from_millis(100),
            max_delay: Duration::from_secs(30),
            backoff_multiplier: 2.0,
            jitter: true,
        }
    }
}

/// Retry executor with exponential backoff
pub struct RetryExecutor {
    config: RetryConfig,
}

impl RetryExecutor {
    pub fn new(config: RetryConfig) -> Self {
        Self { config }
    }

    pub fn execute<F, T>(&self, mut operation: F) -> UmbraResult<T>
    where
        F: FnMut() -> UmbraResult<T>,
    {
        let mut last_error = None;
        let mut delay = self.config.base_delay;

        for attempt in 1..=self.config.max_attempts {
            match operation() {
                Ok(result) => return Ok(result),
                Err(error) => {
                    last_error = Some(error);
                    
                    if attempt < self.config.max_attempts {
                        // Apply jitter if enabled
                        let actual_delay = if self.config.jitter {
                            let jitter_factor = 0.1;
                            let jitter = (rand::random::<f64>() - 0.5) * 2.0 * jitter_factor;
                            let jittered_delay = delay.as_millis() as f64 * (1.0 + jitter);
                            Duration::from_millis(jittered_delay.max(0.0) as u64)
                        } else {
                            delay
                        };

                        std::thread::sleep(actual_delay);
                        
                        // Exponential backoff
                        delay = std::cmp::min(
                            Duration::from_millis(
                                (delay.as_millis() as f64 * self.config.backoff_multiplier) as u64
                            ),
                            self.config.max_delay,
                        );
                    }
                }
            }
        }

        Err(last_error.unwrap_or_else(|| {
            UmbraError::Runtime("Retry operation failed without error".to_string())
        }))
    }

    pub async fn execute_async<F, Fut, T>(&self, mut operation: F) -> UmbraResult<T>
    where
        F: FnMut() -> Fut,
        Fut: std::future::Future<Output = UmbraResult<T>>,
    {
        let mut last_error = None;
        let mut delay = self.config.base_delay;

        for attempt in 1..=self.config.max_attempts {
            match operation().await {
                Ok(result) => return Ok(result),
                Err(error) => {
                    last_error = Some(error);
                    
                    if attempt < self.config.max_attempts {
                        // Apply jitter if enabled
                        let actual_delay = if self.config.jitter {
                            let jitter_factor = 0.1;
                            let jitter = (rand::random::<f64>() - 0.5) * 2.0 * jitter_factor;
                            let jittered_delay = delay.as_millis() as f64 * (1.0 + jitter);
                            Duration::from_millis(jittered_delay.max(0.0) as u64)
                        } else {
                            delay
                        };

                        tokio::time::sleep(actual_delay).await;
                        
                        // Exponential backoff
                        delay = std::cmp::min(
                            Duration::from_millis(
                                (delay.as_millis() as f64 * self.config.backoff_multiplier) as u64
                            ),
                            self.config.max_delay,
                        );
                    }
                }
            }
        }

        Err(last_error.unwrap_or_else(|| {
            UmbraError::Runtime("Async retry operation failed without error".to_string())
        }))
    }
}

/// Circuit breaker states
#[derive(Debug, Clone, PartialEq)]
pub enum CircuitBreakerState {
    Closed,
    Open,
    HalfOpen,
}

/// Circuit breaker configuration
#[derive(Debug, Clone)]
pub struct CircuitBreakerConfig {
    pub failure_threshold: usize,
    pub success_threshold: usize,
    pub timeout: Duration,
    pub reset_timeout: Duration,
}

impl Default for CircuitBreakerConfig {
    fn default() -> Self {
        Self {
            failure_threshold: 5,
            success_threshold: 3,
            timeout: Duration::from_secs(60),
            reset_timeout: Duration::from_secs(30),
        }
    }
}

/// Circuit breaker for preventing cascading failures
pub struct CircuitBreaker {
    config: CircuitBreakerConfig,
    state: Arc<Mutex<CircuitBreakerState>>,
    failure_count: Arc<Mutex<usize>>,
    success_count: Arc<Mutex<usize>>,
    last_failure_time: Arc<Mutex<Option<Instant>>>,
}

impl CircuitBreaker {
    pub fn new(config: CircuitBreakerConfig) -> Self {
        Self {
            config,
            state: Arc::new(Mutex::new(CircuitBreakerState::Closed)),
            failure_count: Arc::new(Mutex::new(0)),
            success_count: Arc::new(Mutex::new(0)),
            last_failure_time: Arc::new(Mutex::new(None)),
        }
    }

    pub fn execute<F, T>(&self, operation: F) -> UmbraResult<T>
    where
        F: FnOnce() -> UmbraResult<T>,
    {
        // Check if circuit breaker allows execution
        if !self.can_execute() {
            return Err(UmbraError::Runtime("Circuit breaker is open".to_string()));
        }

        match operation() {
            Ok(result) => {
                self.on_success();
                Ok(result)
            }
            Err(error) => {
                self.on_failure();
                Err(error)
            }
        }
    }

    fn can_execute(&self) -> bool {
        let state = self.state.lock().unwrap();
        match *state {
            CircuitBreakerState::Closed => true,
            CircuitBreakerState::Open => {
                // Check if reset timeout has passed
                if let Some(last_failure) = *self.last_failure_time.lock().unwrap() {
                    if last_failure.elapsed() >= self.config.reset_timeout {
                        // Transition to half-open
                        drop(state);
                        *self.state.lock().unwrap() = CircuitBreakerState::HalfOpen;
                        *self.success_count.lock().unwrap() = 0;
                        return true;
                    }
                }
                false
            }
            CircuitBreakerState::HalfOpen => true,
        }
    }

    fn on_success(&self) {
        let mut state = self.state.lock().unwrap();
        match *state {
            CircuitBreakerState::Closed => {
                *self.failure_count.lock().unwrap() = 0;
            }
            CircuitBreakerState::HalfOpen => {
                let mut success_count = self.success_count.lock().unwrap();
                *success_count += 1;
                if *success_count >= self.config.success_threshold {
                    *state = CircuitBreakerState::Closed;
                    *self.failure_count.lock().unwrap() = 0;
                }
            }
            CircuitBreakerState::Open => {
                // Should not happen, but reset if it does
                *state = CircuitBreakerState::Closed;
                *self.failure_count.lock().unwrap() = 0;
            }
        }
    }

    fn on_failure(&self) {
        let mut state = self.state.lock().unwrap();
        let mut failure_count = self.failure_count.lock().unwrap();
        
        *failure_count += 1;
        *self.last_failure_time.lock().unwrap() = Some(Instant::now());

        match *state {
            CircuitBreakerState::Closed => {
                if *failure_count >= self.config.failure_threshold {
                    *state = CircuitBreakerState::Open;
                }
            }
            CircuitBreakerState::HalfOpen => {
                *state = CircuitBreakerState::Open;
            }
            CircuitBreakerState::Open => {
                // Already open, just update failure time
            }
        }
    }

    pub fn get_state(&self) -> CircuitBreakerState {
        self.state.lock().unwrap().clone()
    }

    pub fn reset(&self) {
        *self.state.lock().unwrap() = CircuitBreakerState::Closed;
        *self.failure_count.lock().unwrap() = 0;
        *self.success_count.lock().unwrap() = 0;
        *self.last_failure_time.lock().unwrap() = None;
    }
}

/// Error metrics collector
#[derive(Debug, Default)]
pub struct ErrorMetrics {
    pub error_counts: HashMap<String, usize>,
    pub error_rates: HashMap<String, f64>,
    pub last_errors: Vec<(Instant, UmbraError)>,
    pub max_history: usize,
}

impl ErrorMetrics {
    pub fn new() -> Self {
        Self {
            max_history: 1000,
            ..Default::default()
        }
    }

    pub fn record_error(&mut self, error: &UmbraError) {
        // Count error by type
        let error_type = format!("{:?}", std::mem::discriminant(error));
        *self.error_counts.entry(error_type).or_insert(0) += 1;

        // Add to history
        self.last_errors.push((Instant::now(), error.clone()));
        
        // Trim history if needed
        if self.last_errors.len() > self.max_history {
            self.last_errors.remove(0);
        }

        // Update error rates
        self.update_error_rates();
    }

    fn update_error_rates(&mut self) {
        let now = Instant::now();
        let window = Duration::from_secs(60); // 1-minute window

        // Count errors in the last minute
        let recent_errors: HashMap<String, usize> = self.last_errors
            .iter()
            .filter(|(timestamp, _)| now.duration_since(*timestamp) <= window)
            .fold(HashMap::new(), |mut acc, (_, error)| {
                let error_type = format!("{:?}", std::mem::discriminant(error));
                *acc.entry(error_type).or_insert(0) += 1;
                acc
            });

        // Calculate rates (errors per minute)
        for (error_type, count) in recent_errors {
            self.error_rates.insert(error_type, count as f64);
        }
    }

    pub fn get_error_rate(&self, error_type: &str) -> f64 {
        self.error_rates.get(error_type).copied().unwrap_or(0.0)
    }

    pub fn get_total_errors(&self) -> usize {
        self.error_counts.values().sum()
    }

    pub fn clear(&mut self) {
        self.error_counts.clear();
        self.error_rates.clear();
        self.last_errors.clear();
    }
}
