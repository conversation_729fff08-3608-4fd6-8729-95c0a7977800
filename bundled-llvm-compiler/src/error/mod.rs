/// Advanced error handling system for Umbra
///
/// This module provides comprehensive error handling capabilities including
/// error types, diagnostics, recovery strategies, and reporting utilities.

use std::fmt;
use std::error::Error;
use std::collections::HashMap;

/// Stack frame for exception tracing
#[derive(Debug, <PERSON><PERSON>, PartialEq)]
pub struct StackFrame {
    pub function_name: String,
    pub file_name: String,
    pub line: usize,
    pub column: usize,
}

pub mod handlers;
pub mod reporting;

/// Main error type for Umbra compiler
#[derive(Debug, <PERSON><PERSON>, thiserror::Error)]
pub enum UmbraError {
    #[error("Lexical error at line {line}, column {column}: {message}")]
    Lexical {
        message: String,
        line: usize,
        column: usize,
    },

    #[error("Parse error at line {line}, column {column}: {message}")]
    Parse {
        message: String,
        line: usize,
        column: usize,
    },

    #[error("Semantic error at line {line}, column {column}: {message}")]
    Semantic {
        message: String,
        line: usize,
        column: usize,
    },

    #[error("Type error at line {line}, column {column}: {message}")]
    Type {
        message: String,
        line: usize,
        column: usize,
        expected_type: Option<String>,
        actual_type: Option<String>,
    },

    #[error("Type inference error: {0}")]
    TypeInference(String),

    #[error("Runtime error: {0}")]
    Runtime(String),

    #[error("IO error: {0}")]
    Io(String),

    #[error("Code generation error: {0}")]
    CodeGen(String),

    #[error("Module error: {0}")]
    Module(String),

    #[error("Internal compiler error: {0}")]
    #[allow(dead_code)]
    Internal(String),

    #[error("Test error: {0}")]
    TestError(String),

    #[error("Memory error: {0}")]
    Memory(String),

    #[error("Concurrency error: {0}")]
    Concurrency(String),

    #[error("Network error: {0}")]
    Network(String),

    #[error("Database error: {0}")]
    Database(String),

    #[error("Database connection error: {0}")]
    DatabaseConnection(String),

    #[error("Database query error: {0}")]
    DatabaseQuery(String),

    #[error("Database transaction error: {0}")]
    DatabaseTransaction(String),

    #[error("Database migration error: {0}")]
    DatabaseMigration(String),

    #[error("Configuration error: {0}")]
    Config(String),

    #[error("Validation error: {0}")]
    Validation(String),

    #[error("Security error: {0}")]
    Security(String),

    #[error("Performance error: {0}")]
    Performance(String),

    #[error("AI/ML error: {0}")]
    AiMl(String),

    #[error("Timeout error: {0}")]
    Timeout(String),

    #[error("Resource exhausted: {0}")]
    ResourceExhausted(String),

    #[error("Permission denied: {0}")]
    PermissionDenied(String),

    #[error("Exception '{exception_type}' at line {line}, column {column}: {message}")]
    Exception {
        exception_type: String,
        message: String,
        stack_trace: Vec<StackFrame>,
        line: usize,
        column: usize,
    },

    #[error("User exception '{name}' at line {line}, column {column}: {message}")]
    UserException {
        name: String,
        message: String,
        data: Option<String>, // JSON serialized data
        stack_trace: Vec<StackFrame>,
        line: usize,
        column: usize,
    },

    #[error("Not found: {0}")]
    NotFound(String),

    #[error("Already exists: {0}")]
    AlreadyExists(String),

    #[error("Interrupted: {0}")]
    Interrupted(String),

    #[error("Invalid argument: {0}")]
    InvalidArgument(String),

    #[error("Out of range: {0}")]
    OutOfRange(String),

    #[error("Uninitialized: {0}")]
    Uninitialized(String),

    #[error("Deprecated: {0}")]
    Deprecated(String),

    #[error("Not implemented: {0}")]
    NotImplemented(String),

    #[cfg(feature = "python-interop")]
    #[error("Python error: {0}")]
    Python(String),
}

/// Result type for Umbra operations
pub type UmbraResult<T> = Result<T, UmbraError>;

// Standard error conversions - placed immediately after UmbraError definition
impl From<std::io::Error> for UmbraError {
    fn from(error: std::io::Error) -> Self {
        UmbraError::Io(error.to_string())
    }
}

impl From<std::path::StripPrefixError> for UmbraError {
    fn from(error: std::path::StripPrefixError) -> Self {
        UmbraError::CodeGen(format!("Path error: {error}"))
    }
}

impl From<serde_json::Error> for UmbraError {
    fn from(error: serde_json::Error) -> Self {
        UmbraError::CodeGen(format!("JSON error: {error}"))
    }
}

impl From<toml::de::Error> for UmbraError {
    fn from(error: toml::de::Error) -> Self {
        UmbraError::CodeGen(format!("TOML error: {error}"))
    }
}

impl From<walkdir::Error> for UmbraError {
    fn from(error: walkdir::Error) -> Self {
        UmbraError::CodeGen(format!("Directory traversal error: {error}"))
    }
}

#[cfg(feature = "python-interop")]
impl From<pyo3::PyErr> for UmbraError {
    fn from(error: pyo3::PyErr) -> Self {
        UmbraError::Runtime(format!("Python error: {}", error))
    }
}

#[cfg(feature = "python-interop")]
impl From<UmbraError> for pyo3::PyErr {
    fn from(error: UmbraError) -> Self {
        pyo3::exceptions::PyRuntimeError::new_err(error.to_string())
    }
}

/// Source location information
#[derive(Debug, Clone, Copy, PartialEq, Eq, serde::Serialize, serde::Deserialize)]
pub struct SourceLocation {
    pub line: usize,
    pub column: usize,
}

impl SourceLocation {
    pub fn new(line: usize, column: usize) -> Self {
        Self { line, column }
    }
}

impl fmt::Display for SourceLocation {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}:{}", self.line, self.column)
    }
}

/// Diagnostic level
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum DiagnosticLevel {
    Error,
    Warning,
    Info,
    Hint,
}

/// Diagnostic information
#[derive(Debug, Clone)]
pub struct Diagnostic {
    pub level: DiagnosticLevel,
    pub message: String,
    pub location: Option<SourceLocation>,
    pub code: Option<String>,
    pub suggestions: Vec<String>,
}

/// Collection of diagnostics
#[derive(Debug, Default)]
pub struct DiagnosticBag {
    pub diagnostics: Vec<Diagnostic>,
}

impl DiagnosticBag {
    pub fn new() -> Self {
        Self::default()
    }

    pub fn add_error(&mut self, message: String, location: Option<SourceLocation>) {
        self.diagnostics.push(Diagnostic {
            level: DiagnosticLevel::Error,
            message,
            location,
            code: None,
            suggestions: Vec::new(),
        });
    }

    pub fn add_warning(&mut self, message: String, location: Option<SourceLocation>) {
        self.diagnostics.push(Diagnostic {
            level: DiagnosticLevel::Warning,
            message,
            location,
            code: None,
            suggestions: Vec::new(),
        });
    }

    pub fn has_errors(&self) -> bool {
        self.diagnostics.iter().any(|d| d.level == DiagnosticLevel::Error)
    }

    pub fn error_count(&self) -> usize {
        self.diagnostics.iter().filter(|d| d.level == DiagnosticLevel::Error).count()
    }

    pub fn warning_count(&self) -> usize {
        self.diagnostics.iter().filter(|d| d.level == DiagnosticLevel::Warning).count()
    }

    pub fn clear(&mut self) {
        self.diagnostics.clear();
    }
}

/// Error context for providing additional information about errors
#[derive(Debug, Clone)]
pub struct ErrorContext {
    pub operation: String,
    pub file_path: Option<String>,
    pub function_name: Option<String>,
    pub additional_info: HashMap<String, String>,
}

impl ErrorContext {
    pub fn new(operation: &str) -> Self {
        Self {
            operation: operation.to_string(),
            file_path: None,
            function_name: None,
            additional_info: HashMap::new(),
        }
    }

    pub fn with_file(mut self, file_path: &str) -> Self {
        self.file_path = Some(file_path.to_string());
        self
    }

    pub fn with_function(mut self, function_name: &str) -> Self {
        self.function_name = Some(function_name.to_string());
        self
    }

    pub fn with_info(mut self, key: &str, value: &str) -> Self {
        self.additional_info.insert(key.to_string(), value.to_string());
        self
    }
}

/// Enhanced error with context and suggestions
#[derive(Debug, Clone)]
pub struct EnhancedError {
    pub error: UmbraError,
    pub context: Option<ErrorContext>,
    pub suggestions: Vec<String>,
    pub related_errors: Vec<UmbraError>,
    pub severity: ErrorSeverity,
    pub error_code: Option<String>,
    pub help_url: Option<String>,
}

#[derive(Debug, Clone, PartialEq, Eq)]
pub enum ErrorSeverity {
    Critical,
    High,
    Medium,
    Low,
    Info,
}

impl EnhancedError {
    pub fn new(error: UmbraError) -> Self {
        Self {
            error,
            context: None,
            suggestions: Vec::new(),
            related_errors: Vec::new(),
            severity: ErrorSeverity::High,
            error_code: None,
            help_url: None,
        }
    }

    pub fn with_context(mut self, context: ErrorContext) -> Self {
        self.context = Some(context);
        self
    }

    pub fn with_suggestion(mut self, suggestion: &str) -> Self {
        self.suggestions.push(suggestion.to_string());
        self
    }

    pub fn with_suggestions(mut self, suggestions: Vec<String>) -> Self {
        self.suggestions.extend(suggestions);
        self
    }

    pub fn with_related_error(mut self, error: UmbraError) -> Self {
        self.related_errors.push(error);
        self
    }

    pub fn with_severity(mut self, severity: ErrorSeverity) -> Self {
        self.severity = severity;
        self
    }

    pub fn with_error_code(mut self, code: &str) -> Self {
        self.error_code = Some(code.to_string());
        self
    }

    pub fn with_help_url(mut self, url: &str) -> Self {
        self.help_url = Some(url.to_string());
        self
    }
}

impl fmt::Display for EnhancedError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        // Display main error
        writeln!(f, "{}", self.error)?;

        // Display error code if available
        if let Some(ref code) = self.error_code {
            writeln!(f, "Error Code: {}", code)?;
        }

        // Display context if available
        if let Some(ref context) = self.context {
            writeln!(f, "Context: {}", context.operation)?;
            if let Some(ref file) = context.file_path {
                writeln!(f, "  File: {}", file)?;
            }
            if let Some(ref func) = context.function_name {
                writeln!(f, "  Function: {}", func)?;
            }
            for (key, value) in &context.additional_info {
                writeln!(f, "  {}: {}", key, value)?;
            }
        }

        // Display suggestions
        if !self.suggestions.is_empty() {
            writeln!(f, "Suggestions:")?;
            for suggestion in &self.suggestions {
                writeln!(f, "  - {}", suggestion)?;
            }
        }

        // Display help URL if available
        if let Some(ref url) = self.help_url {
            writeln!(f, "Help: {}", url)?;
        }

        // Display related errors
        if !self.related_errors.is_empty() {
            writeln!(f, "Related errors:")?;
            for related in &self.related_errors {
                writeln!(f, "  - {}", related)?;
            }
        }

        Ok(())
    }
}

/// Error recovery strategies
#[derive(Debug, Clone)]
pub enum RecoveryStrategy {
    /// Continue with default value
    UseDefault(String),
    /// Skip the problematic item
    Skip,
    /// Retry the operation
    Retry { max_attempts: usize, delay_ms: u64 },
    /// Ask user for input
    RequestUserInput(String),
    /// Fallback to alternative approach
    Fallback(String),
    /// Abort operation
    Abort,
}

/// Error recovery context
#[derive(Debug, Clone)]
pub struct RecoveryContext {
    pub strategy: RecoveryStrategy,
    pub attempted_recoveries: usize,
    pub max_recoveries: usize,
    pub recovery_history: Vec<String>,
}

impl RecoveryContext {
    pub fn new(strategy: RecoveryStrategy) -> Self {
        Self {
            strategy,
            attempted_recoveries: 0,
            max_recoveries: 3,
            recovery_history: Vec::new(),
        }
    }

    pub fn with_max_recoveries(mut self, max: usize) -> Self {
        self.max_recoveries = max;
        self
    }

    pub fn can_recover(&self) -> bool {
        self.attempted_recoveries < self.max_recoveries
    }

    pub fn record_recovery(&mut self, description: &str) {
        self.attempted_recoveries += 1;
        self.recovery_history.push(description.to_string());
    }
}

/// Result type with recovery capabilities
pub type RecoverableResult<T> = Result<T, (UmbraError, Option<RecoveryContext>)>;

/// Error aggregator for collecting multiple errors
#[derive(Debug, Default)]
pub struct ErrorAggregator {
    pub errors: Vec<EnhancedError>,
    pub warnings: Vec<EnhancedError>,
    pub max_errors: Option<usize>,
    pub fail_fast: bool,
}

impl ErrorAggregator {
    pub fn new() -> Self {
        Self::default()
    }

    pub fn with_max_errors(mut self, max: usize) -> Self {
        self.max_errors = Some(max);
        self
    }

    pub fn with_fail_fast(mut self, fail_fast: bool) -> Self {
        self.fail_fast = fail_fast;
        self
    }

    pub fn add_error(&mut self, error: EnhancedError) -> Result<(), UmbraError> {
        self.errors.push(error);

        if self.fail_fast || self.max_errors.map_or(false, |max| self.errors.len() >= max) {
            return Err(UmbraError::Runtime("Too many errors".to_string()));
        }

        Ok(())
    }

    pub fn add_warning(&mut self, warning: EnhancedError) {
        self.warnings.push(warning);
    }

    pub fn has_errors(&self) -> bool {
        !self.errors.is_empty()
    }

    pub fn error_count(&self) -> usize {
        self.errors.len()
    }

    pub fn warning_count(&self) -> usize {
        self.warnings.len()
    }

    pub fn clear(&mut self) {
        self.errors.clear();
        self.warnings.clear();
    }

    pub fn into_result<T>(self, value: T) -> Result<T, Vec<EnhancedError>> {
        if self.has_errors() {
            Err(self.errors)
        } else {
            Ok(value)
        }
    }
}



// Re-export handler utilities
pub use handlers::{
    ErrorHandler, ErrorHandlerRegistry,
};

// Re-export reporting utilities

/// Convenience macros for error handling
#[macro_export]
macro_rules! umbra_error {
    ($kind:ident, $msg:expr) => {
        $crate::error::UmbraError::$kind($msg.to_string())
    };
    ($kind:ident, $fmt:expr, $($arg:tt)*) => {
        $crate::error::UmbraError::$kind(format!($fmt, $($arg)*))
    };
}

#[macro_export]
macro_rules! umbra_bail {
    ($kind:ident, $msg:expr) => {
        return Err($crate::umbra_error!($kind, $msg))
    };
    ($kind:ident, $fmt:expr, $($arg:tt)*) => {
        return Err($crate::umbra_error!($kind, $fmt, $($arg)*))
    };
}

#[macro_export]
macro_rules! umbra_ensure {
    ($cond:expr, $kind:ident, $msg:expr) => {
        if !($cond) {
            $crate::umbra_bail!($kind, $msg);
        }
    };
    ($cond:expr, $kind:ident, $fmt:expr, $($arg:tt)*) => {
        if !($cond) {
            $crate::umbra_bail!($kind, $fmt, $($arg)*);
        }
    };
}

/// Error handling utilities
pub struct ErrorUtils;

impl ErrorUtils {
    /// Convert a standard error to an enhanced error with suggestions
    pub fn enhance_error(error: UmbraError) -> EnhancedError {
        let mut enhanced = EnhancedError::new(error.clone());
        
        // Add context-specific suggestions based on error type
        match &error {
            UmbraError::Parse {  .. } => {
                enhanced = enhanced
                    .with_suggestion("Check for missing semicolons or brackets")
                    .with_suggestion("Verify correct syntax according to Umbra language specification")
                    .with_error_code("E001");
            }
            UmbraError::Semantic {  .. } => {
                enhanced = enhanced
                    .with_suggestion("Check variable declarations and types")
                    .with_suggestion("Ensure all referenced symbols are defined")
                    .with_error_code("E002");
            }
            UmbraError::Type { expected_type, actual_type, .. } => {
                if let (Some(expected), Some(actual)) = (expected_type, actual_type) {
                    enhanced = enhanced
                        .with_suggestion(&format!("Expected type '{}', but got '{}'", expected, actual))
                        .with_suggestion("Consider adding explicit type conversion");
                }
                enhanced = enhanced.with_error_code("E003");
            }
            UmbraError::Runtime(_msg) => {
                enhanced = enhanced
                    .with_suggestion("Check runtime conditions and input validation")
                    .with_error_code("E004");
            }
            UmbraError::Io(_) => {
                enhanced = enhanced
                    .with_suggestion("Check file permissions and paths")
                    .with_suggestion("Ensure the file or directory exists")
                    .with_error_code("E005");
            }
            UmbraError::Memory(_msg) => {
                enhanced = enhanced
                    .with_suggestion("Check for memory leaks or excessive allocations")
                    .with_suggestion("Consider using memory profiling tools")
                    .with_severity(ErrorSeverity::Critical)
                    .with_error_code("E006");
            }
            UmbraError::Concurrency(_msg) => {
                enhanced = enhanced
                    .with_suggestion("Check for race conditions or deadlocks")
                    .with_suggestion("Review synchronization mechanisms")
                    .with_error_code("E007");
            }
            UmbraError::Network(_msg) => {
                enhanced = enhanced
                    .with_suggestion("Check network connectivity")
                    .with_suggestion("Verify server endpoints and ports")
                    .with_error_code("E008");
            }
            UmbraError::Security(_msg) => {
                enhanced = enhanced
                    .with_suggestion("Review security policies and permissions")
                    .with_suggestion("Check for potential security vulnerabilities")
                    .with_severity(ErrorSeverity::Critical)
                    .with_error_code("E009");
            }
            UmbraError::Performance(_msg) => {
                enhanced = enhanced
                    .with_suggestion("Profile the application to identify bottlenecks")
                    .with_suggestion("Consider optimization strategies")
                    .with_error_code("E010");
            }
            UmbraError::AiMl(_msg) => {
                enhanced = enhanced
                    .with_suggestion("Check model configuration and data quality")
                    .with_suggestion("Verify training parameters and datasets")
                    .with_error_code("E011");
            }
            _ => {
                enhanced = enhanced.with_error_code("E999");
            }
        }
        
        enhanced
    }

    /// Create a context for an operation
    pub fn create_context(operation: &str) -> ErrorContext {
        ErrorContext::new(operation)
    }

    /// Check if an error is recoverable
    pub fn is_recoverable(error: &UmbraError) -> bool {
        match error {
            UmbraError::Network(_) => true,
            UmbraError::Timeout(_) => true,
            UmbraError::ResourceExhausted(_) => true,
            UmbraError::Io(_) => true,
            UmbraError::Runtime(_) => true,
            _ => false,
        }
    }

    /// Get suggested recovery strategy for an error
    pub fn suggest_recovery(error: &UmbraError) -> Option<RecoveryStrategy> {
        match error {
            UmbraError::Network(_) => Some(RecoveryStrategy::Retry {
                max_attempts: 3,
                delay_ms: 1000,
            }),
            UmbraError::Timeout(_) => Some(RecoveryStrategy::Retry {
                max_attempts: 2,
                delay_ms: 2000,
            }),
            UmbraError::ResourceExhausted(_) => Some(RecoveryStrategy::Fallback(
                "Use alternative resource or reduce load".to_string()
            )),
            UmbraError::NotFound(_) => Some(RecoveryStrategy::UseDefault(
                "Use default value or create missing resource".to_string()
            )),
            UmbraError::PermissionDenied(_) => Some(RecoveryStrategy::RequestUserInput(
                "Request elevated permissions or alternative path".to_string()
            )),
            _ => None,
        }
    }

    /// Format error for display
    pub fn format_error(error: &UmbraError, verbose: bool) -> String {
        if verbose {
            let enhanced = Self::enhance_error(error.clone());
            format!("{}", enhanced)
        } else {
            format!("{}", error)
        }
    }

    /// Extract error chain from nested errors
    pub fn error_chain(error: &UmbraError) -> Vec<String> {
        let mut chain = vec![error.to_string()];
        
        // Add source chain if available
        let mut source = error.source();
        while let Some(err) = source {
            chain.push(err.to_string());
            source = err.source();
        }
        
        chain
    }

    /// Check if error matches a pattern
    pub fn matches_pattern(error: &UmbraError, pattern: &str) -> bool {
        let error_str = error.to_string().to_lowercase();
        let pattern_lower = pattern.to_lowercase();
        
        error_str.contains(&pattern_lower)
    }

    /// Group errors by type
    pub fn group_errors(errors: &[UmbraError]) -> std::collections::HashMap<String, Vec<&UmbraError>> {
        let mut groups = std::collections::HashMap::new();
        
        for error in errors {
            let error_type = format!("{:?}", std::mem::discriminant(error));
            groups.entry(error_type).or_insert_with(Vec::new).push(error);
        }
        
        groups
    }

    /// Calculate error severity score
    pub fn severity_score(error: &UmbraError) -> u32 {
        match error {
            UmbraError::Security(_) => 100,
            UmbraError::Memory(_) => 90,
            UmbraError::Concurrency(_) => 80,
            UmbraError::Parse { .. } => 70,
            UmbraError::Semantic { .. } => 70,
            UmbraError::Type { .. } => 60,
            UmbraError::Runtime(_) => 50,
            UmbraError::Network(_) => 40,
            UmbraError::Io(_) => 30,
            UmbraError::Performance(_) => 20,
            UmbraError::Config(_) => 10,
            _ => 5,
        }
    }
}

// Global error handler instance
lazy_static::lazy_static! {
    pub static ref GLOBAL_ERROR_HANDLER: std::sync::Mutex<ErrorHandlerRegistry> = 
        std::sync::Mutex::new(ErrorHandlerRegistry::new());
}

/// Initialize default error handlers
pub fn init_default_handlers() {
    // This would be called during application startup
    // to register default error handlers
}

/// Convenience function to handle errors with global handler
pub fn handle_error(error: &UmbraError) -> Option<RecoveryStrategy> {
    GLOBAL_ERROR_HANDLER
        .lock()
        .unwrap()
        .handle_error(error)
}
