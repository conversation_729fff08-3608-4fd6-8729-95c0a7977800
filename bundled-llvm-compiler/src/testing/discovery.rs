// Test discovery system for Umbra testing framework
// Automatically discovers and parses test files and functions

use crate::error::{UmbraError, UmbraResult};
use crate::parser::{Parser, ast::*};
use crate::lexer::Lex<PERSON>;
use crate::testing::{TestSuite, TestCase, TestType, TestConfig};
use std::collections::HashMap;
use std::time::Duration;
use std::fs;
use std::path::{Path, PathBuf};
use glob::glob;

/// Test discovery engine
pub struct TestDiscoverer {
    config: TestConfig,
    patterns: Vec<String>,
}

/// Discovered test metadata
#[derive(Debug, Clone)]
pub struct TestMetadata {
    pub file_path: PathBuf,
    pub line_number: usize,
    pub attributes: HashMap<String, String>,
}

impl TestDiscoverer {
    pub fn new(config: &TestConfig) -> Self {
        Self {
            config: config.clone(),
            patterns: config.patterns.clone(),
        }
    }

    /// Discover all test suites in a directory
    pub fn discover(&self, directory: &Path) -> UmbraResult<Vec<TestSuite>> {
        let mut suites = Vec::new();
        
        // Find all test files matching patterns
        let test_files = self.find_test_files(directory)?;
        
        for file_path in test_files {
            if let Ok(suite) = self.parse_test_file(&file_path) {
                suites.push(suite);
            }
        }
        
        Ok(suites)
    }

    /// Find test files matching configured patterns
    fn find_test_files(&self, directory: &Path) -> UmbraResult<Vec<PathBuf>> {
        let mut files = Vec::new();
        
        for pattern in &self.patterns {
            let full_pattern = directory.join(pattern);
            let pattern_str = full_pattern.to_string_lossy();
            
            for entry in glob(&pattern_str).map_err(|e| {
                UmbraError::Runtime(format!("Invalid glob pattern: {e}"))
            })? {
                match entry {
                    Ok(path) => {
                        if path.is_file() {
                            files.push(path);
                        }
                    }
                    Err(e) => {
                        eprintln!("Warning: Error reading file: {e}");
                    }
                }
            }
        }
        
        Ok(files)
    }

    /// Parse a test file and extract test cases
    fn parse_test_file(&self, file_path: &Path) -> UmbraResult<TestSuite> {
        let content = fs::read_to_string(file_path)?;
        let mut lexer = Lexer::new(content);
        let tokens = lexer.tokenize()?;
        let mut parser = Parser::new(tokens);
        let program = parser.parse()?;

        let suite_name = file_path.file_stem()
            .and_then(|s| s.to_str())
            .unwrap_or("unknown")
            .to_string();

        let mut test_cases = Vec::new();
        let suite_attributes = HashMap::new();

        // Extract test functions and metadata
        for statement in &program.statements {
            match statement {
                Statement::Function(func) => {
                    if self.is_test_function(func) {
                        let test_case = self.create_test_case(func, file_path)?;
                        test_cases.push(test_case);
                    }
                }
                // Note: Comment parsing would be handled differently in a real implementation
                _ => {}
            }
        }

        Ok(TestSuite {
            name: suite_name,
            description: suite_attributes.get("description").cloned(),
            tests: test_cases,
            setup: None, // TODO: Parse setup functions
            teardown: None, // TODO: Parse teardown functions
            tags: self.parse_tags(&suite_attributes),
        })
    }

    /// Check if a function is a test function
    fn is_test_function(&self, func: &FunctionDef) -> bool {
        // Check for test naming conventions
        if func.name.starts_with("test_") || func.name.ends_with("_test") {
            return true;
        }

        // Check for test attributes (when we implement them)
        // TODO: Parse function attributes/annotations
        
        false
    }

    /// Create a test case from a function definition
    fn create_test_case(&self, func: &FunctionDef, file_path: &Path) -> UmbraResult<TestCase> {
        let test_type = self.determine_test_type(func);
        let attributes = HashMap::new();
        
        // TODO: Parse function attributes/comments for metadata
        
        Ok(TestCase {
            name: func.name.clone(),
            description: attributes.get("description").cloned(),
            test_type,
            function: func.name.clone(),
            timeout: self.parse_timeout(&attributes),
            tags: self.parse_tags(&attributes),
            dependencies: self.parse_dependencies(&attributes),
            estimated_duration: Some(Duration::from_secs(5)),
            code: format!("fn {}() {{ /* test code */ }}", func.name),
        })
    }

    /// Determine the type of test based on function name and attributes
    fn determine_test_type(&self, func: &FunctionDef) -> TestType {
        let name = &func.name;
        
        if name.contains("integration") || name.contains("e2e") {
            TestType::Integration
        } else if name.contains("property") || name.contains("prop") {
            TestType::Property
        } else if name.contains("benchmark") || name.contains("perf") {
            TestType::Performance
        } else if name.contains("ai") || name.contains("ml") || name.contains("model") {
            TestType::AiMl
        } else {
            TestType::Unit
        }
    }

    /// Parse attribute comment (e.g., // @description: This is a test)
    fn parse_attribute_comment(&self, comment: &str) -> Option<(String, String)> {
        let trimmed = comment.trim_start_matches("//").trim();
        if trimmed.starts_with('@') {
            if let Some(colon_pos) = trimmed.find(':') {
                let key = trimmed[1..colon_pos].trim().to_string();
                let value = trimmed[colon_pos + 1..].trim().to_string();
                return Some((key, value));
            }
        }
        None
    }

    /// Parse timeout from attributes
    fn parse_timeout(&self, attributes: &HashMap<String, String>) -> Option<std::time::Duration> {
        attributes.get("timeout")
            .and_then(|s| s.parse::<u64>().ok())
            .map(std::time::Duration::from_secs)
    }

    /// Parse tags from attributes
    fn parse_tags(&self, attributes: &HashMap<String, String>) -> Vec<String> {
        attributes.get("tags")
            .map(|s| s.split(',').map(|tag| tag.trim().to_string()).collect())
            .unwrap_or_default()
    }

    /// Parse dependencies from attributes
    fn parse_dependencies(&self, attributes: &HashMap<String, String>) -> Vec<String> {
        attributes.get("depends")
            .map(|s| s.split(',').map(|dep| dep.trim().to_string()).collect())
            .unwrap_or_default()
    }

    /// Discover test suites with filtering
    pub fn discover_with_filter<F>(&self, directory: &Path, filter: F) -> UmbraResult<Vec<TestSuite>>
    where
        F: Fn(&TestSuite) -> bool,
    {
        let suites = self.discover(directory)?;
        Ok(suites.into_iter().filter(filter).collect())
    }

    /// Discover tests by tag
    pub fn discover_by_tag(&self, directory: &Path, tag: &str) -> UmbraResult<Vec<TestSuite>> {
        self.discover_with_filter(directory, |suite| {
            suite.tags.contains(&tag.to_string()) ||
            suite.tests.iter().any(|test| test.tags.contains(&tag.to_string()))
        })
    }

    /// Discover tests by type
    pub fn discover_by_type(&self, directory: &Path, test_type: TestType) -> UmbraResult<Vec<TestSuite>> {
        let mut suites = self.discover(directory)?;
        
        // Filter tests within each suite
        for suite in &mut suites {
            suite.tests.retain(|test| test.test_type == test_type);
        }
        
        // Remove empty suites
        suites.retain(|suite| !suite.tests.is_empty());
        
        Ok(suites)
    }

    /// Get test statistics for a directory
    pub fn get_test_statistics(&self, directory: &Path) -> UmbraResult<TestStatistics> {
        let suites = self.discover(directory)?;
        
        let mut stats = TestStatistics {
            total_suites: suites.len(),
            total_tests: 0,
            tests_by_type: HashMap::new(),
            tests_by_tag: HashMap::new(),
            files_scanned: 0,
        };

        for suite in &suites {
            stats.total_tests += suite.tests.len();
            
            for test in &suite.tests {
                *stats.tests_by_type.entry(test.test_type.clone()).or_insert(0) += 1;
                
                for tag in &test.tags {
                    *stats.tests_by_tag.entry(tag.clone()).or_insert(0) += 1;
                }
            }
        }

        // Count scanned files
        stats.files_scanned = self.find_test_files(directory)?.len();

        Ok(stats)
    }
}

/// Test discovery statistics
#[derive(Debug, Clone)]
pub struct TestStatistics {
    pub total_suites: usize,
    pub total_tests: usize,
    pub tests_by_type: HashMap<TestType, usize>,
    pub tests_by_tag: HashMap<String, usize>,
    pub files_scanned: usize,
}

/// Test file watcher for continuous testing
pub struct TestWatcher {
    discoverer: TestDiscoverer,
    watched_directories: Vec<PathBuf>,
}

impl TestWatcher {
    pub fn new(config: &TestConfig) -> Self {
        Self {
            discoverer: TestDiscoverer::new(config),
            watched_directories: Vec::new(),
        }
    }

    pub fn add_directory(&mut self, directory: PathBuf) {
        self.watched_directories.push(directory);
    }

    /// Check for changes and re-discover tests
    pub fn check_for_changes(&self) -> UmbraResult<Vec<TestSuite>> {
        let mut all_suites = Vec::new();
        
        for directory in &self.watched_directories {
            let suites = self.discoverer.discover(directory)?;
            all_suites.extend(suites);
        }
        
        Ok(all_suites)
    }
}

/// Test pattern matcher for advanced filtering
pub struct TestPatternMatcher {
    patterns: Vec<regex::Regex>,
}

impl TestPatternMatcher {
    pub fn new(patterns: Vec<String>) -> UmbraResult<Self> {
        let mut regexes = Vec::new();
        
        for pattern in patterns {
            let regex = regex::Regex::new(&pattern)
                .map_err(|e| UmbraError::Runtime(format!("Invalid regex pattern: {e}")))?;
            regexes.push(regex);
        }
        
        Ok(Self { patterns: regexes })
    }

    pub fn matches(&self, test_name: &str) -> bool {
        if self.patterns.is_empty() {
            return true;
        }
        
        self.patterns.iter().any(|pattern| pattern.is_match(test_name))
    }

    pub fn filter_suites(&self, suites: Vec<TestSuite>) -> Vec<TestSuite> {
        let mut filtered_suites = Vec::new();
        
        for mut suite in suites {
            suite.tests.retain(|test| self.matches(&test.name));
            if !suite.tests.is_empty() {
                filtered_suites.push(suite);
            }
        }
        
        filtered_suites
    }
}

/// Test dependency resolver
pub struct TestDependencyResolver {
    dependency_graph: HashMap<String, Vec<String>>,
}

impl TestDependencyResolver {
    pub fn new() -> Self {
        Self {
            dependency_graph: HashMap::new(),
        }
    }

    pub fn add_dependency(&mut self, test: String, dependency: String) {
        self.dependency_graph.entry(test).or_default().push(dependency);
    }

    /// Resolve test execution order based on dependencies
    pub fn resolve_execution_order(&self, tests: &[TestCase]) -> UmbraResult<Vec<String>> {
        let mut order = Vec::new();
        let mut visited = std::collections::HashSet::new();
        let mut visiting = std::collections::HashSet::new();

        for test in tests {
            if !visited.contains(&test.name) {
                self.visit(&test.name, &mut order, &mut visited, &mut visiting)?;
            }
        }

        Ok(order)
    }

    fn visit(
        &self,
        test: &str,
        order: &mut Vec<String>,
        visited: &mut std::collections::HashSet<String>,
        visiting: &mut std::collections::HashSet<String>,
    ) -> UmbraResult<()> {
        if visiting.contains(test) {
            return Err(UmbraError::Runtime(format!("Circular dependency detected: {test}")));
        }

        if visited.contains(test) {
            return Ok(());
        }

        visiting.insert(test.to_string());

        if let Some(dependencies) = self.dependency_graph.get(test) {
            for dep in dependencies {
                self.visit(dep, order, visited, visiting)?;
            }
        }

        visiting.remove(test);
        visited.insert(test.to_string());
        order.push(test.to_string());

        Ok(())
    }
}
