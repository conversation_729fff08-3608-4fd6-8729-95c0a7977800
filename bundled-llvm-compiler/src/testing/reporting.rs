// Test reporting system for Umbra testing framework
// Provides various output formats and detailed test result analysis

use crate::testing::{TestReport<PERSON>, TestResult, TestStatus, CoverageData};
use std::fs::File;
use std::io::{self, Write};
use std::time::Duration;
use colored::*;

/// Console test reporter with colored output
pub struct ConsoleReporter {
    verbose: bool,
    show_passed: bool,
    use_colors: bool,
}

impl ConsoleReporter {
    pub fn new(verbose: bool) -> Self {
        Self {
            verbose,
            show_passed: true,
            use_colors: true,
        }
    }

    pub fn with_colors(mut self, use_colors: bool) -> Self {
        self.use_colors = use_colors;
        self
    }

    pub fn show_passed_tests(mut self, show: bool) -> Self {
        self.show_passed = show;
        self
    }

    fn format_duration(&self, duration: Duration) -> String {
        let millis = duration.as_millis();
        if millis < 1000 {
            format!("{millis}ms")
        } else {
            format!("{:.2}s", duration.as_secs_f64())
        }
    }

    fn status_symbol(&self, status: &TestStatus) -> String {
        if !self.use_colors {
            match status {
                TestStatus::Passed => "✓".to_string(),
                TestStatus::Failed => "✗".to_string(),
                TestStatus::Skipped => "-".to_string(),
                TestStatus::Timeout => "T".to_string(),
                TestStatus::Error => "E".to_string(),
            }
        } else {
            match status {
                TestStatus::Passed => "✓".green().to_string(),
                TestStatus::Failed => "✗".red().to_string(),
                TestStatus::Skipped => "-".yellow().to_string(),
                TestStatus::Timeout => "T".red().to_string(),
                TestStatus::Error => "E".red().bold().to_string(),
            }
        }
    }

    fn format_test_name(&self, name: &str, status: &TestStatus) -> String {
        if !self.use_colors {
            name.to_string()
        } else {
            match status {
                TestStatus::Passed => name.green().to_string(),
                TestStatus::Failed => name.red().to_string(),
                TestStatus::Skipped => name.yellow().to_string(),
                TestStatus::Timeout => name.red().to_string(),
                TestStatus::Error => name.red().bold().to_string(),
            }
        }
    }
}

impl TestReporter for ConsoleReporter {
    fn report_start(&self, total_tests: usize) {
        if self.use_colors {
            println!("{} Running {} tests", "🧪".blue(), total_tests.to_string().bold());
        } else {
            println!("Running {total_tests} tests");
        }
        println!();
    }

    fn report_test(&self, result: &TestResult) {
        match result.status {
            TestStatus::Passed => {
                if self.show_passed || self.verbose {
                    println!(
                        "{} {} ({})",
                        self.status_symbol(&result.status),
                        self.format_test_name(&result.test_name, &result.status),
                        self.format_duration(result.duration)
                    );
                }
            }
            _ => {
                println!(
                    "{} {} ({})",
                    self.status_symbol(&result.status),
                    self.format_test_name(&result.test_name, &result.status),
                    self.format_duration(result.duration)
                );

                if let Some(message) = &result.message {
                    println!("    {message}");
                }

                if self.verbose {
                    for assertion in &result.assertions {
                        if !assertion.passed {
                            println!("    Assertion failed: {}", assertion.assertion_type);
                            if let (Some(expected), Some(actual)) = (&assertion.expected, &assertion.actual) {
                                println!("      Expected: {expected}");
                                println!("      Actual:   {actual}");
                            }
                        }
                    }
                }
            }
        }
    }

    fn report_summary(&self, results: &[TestResult]) {
        println!();
        
        let passed = results.iter().filter(|r| r.status == TestStatus::Passed).count();
        let failed = results.iter().filter(|r| r.status == TestStatus::Failed).count();
        let skipped = results.iter().filter(|r| r.status == TestStatus::Skipped).count();
        let timeout = results.iter().filter(|r| r.status == TestStatus::Timeout).count();
        let error = results.iter().filter(|r| r.status == TestStatus::Error).count();
        
        let total_time: Duration = results.iter().map(|r| r.duration).sum();
        
        if self.use_colors {
            println!("{}", "Test Results:".bold().underline());
            println!("  {} passed", passed.to_string().green());
            if failed > 0 {
                println!("  {} failed", failed.to_string().red());
            }
            if skipped > 0 {
                println!("  {} skipped", skipped.to_string().yellow());
            }
            if timeout > 0 {
                println!("  {} timeout", timeout.to_string().red());
            }
            if error > 0 {
                println!("  {} error", error.to_string().red().bold());
            }
            println!("  Total time: {}", self.format_duration(total_time).cyan());
        } else {
            println!("Test Results:");
            println!("  {passed} passed");
            if failed > 0 {
                println!("  {failed} failed");
            }
            if skipped > 0 {
                println!("  {skipped} skipped");
            }
            if timeout > 0 {
                println!("  {timeout} timeout");
            }
            if error > 0 {
                println!("  {error} error");
            }
            println!("  Total time: {}", self.format_duration(total_time));
        }

        println!();
        
        if failed + timeout + error == 0 {
            if self.use_colors {
                println!("{}", "All tests passed! 🎉".green().bold());
            } else {
                println!("All tests passed!");
            }
        } else if self.use_colors {
            println!("{}", "Some tests failed! ❌".red().bold());
        } else {
            println!("Some tests failed!");
        }
    }

    fn report_coverage(&self, coverage: &CoverageData) {
        println!();
        if self.use_colors {
            println!("{}", "Coverage Report:".bold().underline());
        } else {
            println!("Coverage Report:");
        }
        
        let line_pct = coverage.line_coverage();
        let branch_pct = coverage.branch_coverage();
        let function_pct = coverage.function_coverage();
        
        println!("  Lines:     {}/{} ({:.1}%)", 
                 coverage.lines_covered, coverage.lines_total, line_pct);
        println!("  Branches:  {}/{} ({:.1}%)", 
                 coverage.branches_covered, coverage.branches_total, branch_pct);
        println!("  Functions: {}/{} ({:.1}%)", 
                 coverage.functions_covered, coverage.functions_total, function_pct);
    }
}

/// JUnit XML reporter for CI/CD integration
pub struct JUnitReporter {
    output_file: String,
}

impl JUnitReporter {
    pub fn new(output_file: String) -> Self {
        Self { output_file }
    }

    fn escape_xml(&self, text: &str) -> String {
        text.replace('&', "&amp;")
            .replace('<', "&lt;")
            .replace('>', "&gt;")
            .replace('"', "&quot;")
            .replace('\'', "&apos;")
    }

    fn write_xml(&self, results: &[TestResult]) -> io::Result<()> {
        let mut file = File::create(&self.output_file)?;
        
        writeln!(file, r#"<?xml version="1.0" encoding="UTF-8"?>"#)?;
        
        let total_tests = results.len();
        let failures = results.iter().filter(|r| r.status == TestStatus::Failed).count();
        let errors = results.iter().filter(|r| r.status == TestStatus::Error).count();
        let skipped = results.iter().filter(|r| r.status == TestStatus::Skipped).count();
        let total_time: Duration = results.iter().map(|r| r.duration).sum();
        
        writeln!(
            file,
            r#"<testsuite name="umbra-tests" tests="{}" failures="{}" errors="{}" skipped="{}" time="{:.3}">"#,
            total_tests, failures, errors, skipped, total_time.as_secs_f64()
        )?;
        
        for result in results {
            let time = result.duration.as_secs_f64();
            writeln!(
                file,
                r#"  <testcase name="{}" classname="{}" time="{:.3}">"#,
                self.escape_xml(&result.test_name),
                self.escape_xml(&result.suite_name),
                time
            )?;
            
            match result.status {
                TestStatus::Failed => {
                    if let Some(message) = &result.message {
                        writeln!(
                            file,
                            r#"    <failure message="{}">{}</failure>"#,
                            self.escape_xml(message),
                            self.escape_xml(message)
                        )?;
                    }
                }
                TestStatus::Error => {
                    if let Some(message) = &result.message {
                        writeln!(
                            file,
                            r#"    <error message="{}">{}</error>"#,
                            self.escape_xml(message),
                            self.escape_xml(message)
                        )?;
                    }
                }
                TestStatus::Skipped => {
                    writeln!(file, r#"    <skipped/>"#)?;
                }
                _ => {}
            }
            
            writeln!(file, r#"  </testcase>"#)?;
        }
        
        writeln!(file, r#"</testsuite>"#)?;
        Ok(())
    }
}

impl TestReporter for JUnitReporter {
    fn report_start(&self, _total_tests: usize) {
        // JUnit reporter doesn't need to report start
    }

    fn report_test(&self, _result: &TestResult) {
        // JUnit reporter collects all results and writes at the end
    }

    fn report_summary(&self, results: &[TestResult]) {
        if let Err(e) = self.write_xml(results) {
            eprintln!("Failed to write JUnit XML report: {e}");
        } else {
            println!("JUnit XML report written to: {}", self.output_file);
        }
    }

    fn report_coverage(&self, _coverage: &CoverageData) {
        // JUnit format doesn't include coverage data
    }
}

/// JSON reporter for programmatic consumption
pub struct JsonReporter {
    output_file: String,
}

impl JsonReporter {
    pub fn new(output_file: String) -> Self {
        Self { output_file }
    }

    fn write_json(&self, results: &[TestResult]) -> io::Result<()> {
        use serde_json::json;
        
        let json_results: Vec<_> = results.iter().map(|result| {
            json!({
                "name": result.test_name,
                "suite": result.suite_name,
                "status": format!("{:?}", result.status),
                "duration_ms": result.duration.as_millis(),
                "message": result.message,
                "assertions": result.assertions.len(),
                "failed_assertions": result.assertions.iter().filter(|a| !a.passed).count()
            })
        }).collect();
        
        let summary = json!({
            "total_tests": results.len(),
            "passed": results.iter().filter(|r| r.status == TestStatus::Passed).count(),
            "failed": results.iter().filter(|r| r.status == TestStatus::Failed).count(),
            "skipped": results.iter().filter(|r| r.status == TestStatus::Skipped).count(),
            "timeout": results.iter().filter(|r| r.status == TestStatus::Timeout).count(),
            "error": results.iter().filter(|r| r.status == TestStatus::Error).count(),
            "total_time_ms": results.iter().map(|r| r.duration).sum::<Duration>().as_millis()
        });
        
        let report = json!({
            "summary": summary,
            "tests": json_results
        });
        
        let mut file = File::create(&self.output_file)?;
        writeln!(file, "{}", serde_json::to_string_pretty(&report)?)?;
        Ok(())
    }
}

impl TestReporter for JsonReporter {
    fn report_start(&self, _total_tests: usize) {}
    fn report_test(&self, _result: &TestResult) {}

    fn report_summary(&self, results: &[TestResult]) {
        if let Err(e) = self.write_json(results) {
            eprintln!("Failed to write JSON report: {e}");
        } else {
            println!("JSON report written to: {}", self.output_file);
        }
    }

    fn report_coverage(&self, _coverage: &CoverageData) {}
}

/// HTML reporter for web-based test results
pub struct HtmlReporter {
    output_file: String,
}

impl HtmlReporter {
    pub fn new(output_file: String) -> Self {
        Self { output_file }
    }

    fn write_html(&self, results: &[TestResult]) -> io::Result<()> {
        let mut file = File::create(&self.output_file)?;
        
        writeln!(file, r#"<!DOCTYPE html>"#)?;
        writeln!(file, r#"<html><head><title>Umbra Test Results</title>"#)?;
        writeln!(file, r#"<style>"#)?;
        writeln!(file, r#"body {{ font-family: Arial, sans-serif; margin: 20px; }}"#)?;
        writeln!(file, r#".passed {{ color: green; }}"#)?;
        writeln!(file, r#".failed {{ color: red; }}"#)?;
        writeln!(file, r#".skipped {{ color: orange; }}"#)?;
        writeln!(file, r#".error {{ color: red; font-weight: bold; }}"#)?;
        writeln!(file, r#"table {{ border-collapse: collapse; width: 100%; }}"#)?;
        writeln!(file, r#"th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}"#)?;
        writeln!(file, r#"th {{ background-color: #f2f2f2; }}"#)?;
        writeln!(file, r#"</style></head><body>"#)?;
        
        writeln!(file, r#"<h1>Umbra Test Results</h1>"#)?;
        
        // Summary
        let passed = results.iter().filter(|r| r.status == TestStatus::Passed).count();
        let failed = results.iter().filter(|r| r.status == TestStatus::Failed).count();
        let skipped = results.iter().filter(|r| r.status == TestStatus::Skipped).count();
        let error = results.iter().filter(|r| r.status == TestStatus::Error).count();
        
        writeln!(file, r#"<h2>Summary</h2>"#)?;
        writeln!(file, r#"<p>Total: {} | Passed: {} | Failed: {} | Skipped: {} | Error: {}</p>"#,
                 results.len(), passed, failed, skipped, error)?;
        
        // Test results table
        writeln!(file, r#"<h2>Test Results</h2>"#)?;
        writeln!(file, r#"<table>"#)?;
        writeln!(file, r#"<tr><th>Test Name</th><th>Suite</th><th>Status</th><th>Duration</th><th>Message</th></tr>"#)?;
        
        for result in results {
            let status_class = match result.status {
                TestStatus::Passed => "passed",
                TestStatus::Failed => "failed",
                TestStatus::Skipped => "skipped",
                TestStatus::Error => "error",
                TestStatus::Timeout => "failed",
            };
            
            writeln!(
                file,
                r#"<tr><td>{}</td><td>{}</td><td class="{}">{:?}</td><td>{}ms</td><td>{}</td></tr>"#,
                result.test_name,
                result.suite_name,
                status_class,
                result.status,
                result.duration.as_millis(),
                result.message.as_deref().unwrap_or("")
            )?;
        }
        
        writeln!(file, r#"</table>"#)?;
        writeln!(file, r#"</body></html>"#)?;
        Ok(())
    }
}

impl TestReporter for HtmlReporter {
    fn report_start(&self, _total_tests: usize) {}
    fn report_test(&self, _result: &TestResult) {}

    fn report_summary(&self, results: &[TestResult]) {
        if let Err(e) = self.write_html(results) {
            eprintln!("Failed to write HTML report: {e}");
        } else {
            println!("HTML report written to: {}", self.output_file);
        }
    }

    fn report_coverage(&self, _coverage: &CoverageData) {}
}

/// Multi-reporter that delegates to multiple reporters
pub struct MultiReporter {
    reporters: Vec<Box<dyn TestReporter>>,
}

impl MultiReporter {
    pub fn new() -> Self {
        Self {
            reporters: Vec::new(),
        }
    }

    pub fn add_reporter(&mut self, reporter: Box<dyn TestReporter>) {
        self.reporters.push(reporter);
    }
}

impl TestReporter for MultiReporter {
    fn report_start(&self, total_tests: usize) {
        for reporter in &self.reporters {
            reporter.report_start(total_tests);
        }
    }

    fn report_test(&self, result: &TestResult) {
        for reporter in &self.reporters {
            reporter.report_test(result);
        }
    }

    fn report_summary(&self, results: &[TestResult]) {
        for reporter in &self.reporters {
            reporter.report_summary(results);
        }
    }

    fn report_coverage(&self, coverage: &CoverageData) {
        for reporter in &self.reporters {
            reporter.report_coverage(coverage);
        }
    }
}
