/// Parallel test execution system for Umbra
/// 
/// This module provides comprehensive parallel test execution with
/// thread management, load balancing, and result aggregation.

use crate::error::UmbraResult;
use crate::testing::{TestCase, TestResult, TestStatus, DefaultTestRunner};
use std::collections::HashMap;
use std::sync::{Arc, Mutex, mpsc};
use std::thread;
use std::time::{Duration, Instant};

/// Parallel test execution configuration
#[derive(Debug, Clone)]
pub struct ParallelConfig {
    /// Number of worker threads
    pub thread_count: usize,
    /// Maximum test execution time per test
    pub timeout: Duration,
    /// Enable load balancing
    pub load_balancing: bool,
    /// Test isolation level
    pub isolation_level: IsolationLevel,
    /// Resource sharing mode
    pub resource_sharing: ResourceSharing,
}

/// Test isolation levels
#[derive(Debug, Clone, PartialEq)]
pub enum IsolationLevel {
    /// No isolation - tests can interfere with each other
    None,
    /// Process isolation - each test runs in separate process
    Process,
    /// Thread isolation - tests run in separate threads with isolated state
    Thread,
    /// Full isolation - complete environment isolation
    Full,
}

/// Resource sharing modes
#[derive(Debu<PERSON>, <PERSON><PERSON>, PartialEq)]
pub enum ResourceSharing {
    /// No resource sharing
    None,
    /// Share read-only resources
    ReadOnly,
    /// Share all resources with synchronization
    Synchronized,
}

impl Default for ParallelConfig {
    fn default() -> Self {
        Self {
            thread_count: num_cpus::get(),
            timeout: Duration::from_secs(30),
            load_balancing: true,
            isolation_level: IsolationLevel::Thread,
            resource_sharing: ResourceSharing::ReadOnly,
        }
    }
}

/// Parallel test executor
pub struct ParallelTestExecutor {
    config: ParallelConfig,
    test_runner: DefaultTestRunner,
    thread_pool: Option<ThreadPool>,
}

/// Thread pool for test execution
struct ThreadPool {
    workers: Vec<Worker>,
    sender: mpsc::Sender<Job>,
}

/// Worker thread
struct Worker {
    id: usize,
    thread: Option<thread::JoinHandle<()>>,
}

/// Job to be executed by worker
type Job = Box<dyn FnOnce() + Send + 'static>;

/// Test execution context
#[derive(Debug)]
pub struct TestExecutionContext {
    pub test_id: String,
    pub thread_id: usize,
    pub start_time: Instant,
    pub timeout: Duration,
    pub isolation_level: IsolationLevel,
}

/// Parallel test results
#[derive(Debug)]
pub struct ParallelTestResults {
    pub results: HashMap<String, TestResult>,
    pub execution_stats: ExecutionStats,
    pub thread_stats: HashMap<usize, ThreadStats>,
}

/// Execution statistics
#[derive(Debug, Default)]
pub struct ExecutionStats {
    pub total_tests: usize,
    pub passed_tests: usize,
    pub failed_tests: usize,
    pub skipped_tests: usize,
    pub total_execution_time: Duration,
    pub average_execution_time: Duration,
    pub max_execution_time: Duration,
    pub min_execution_time: Duration,
    pub thread_utilization: f64,
}

/// Thread-specific statistics
#[derive(Debug, Default, Clone)]
pub struct ThreadStats {
    pub thread_id: usize,
    pub tests_executed: usize,
    pub total_execution_time: Duration,
    pub idle_time: Duration,
    pub errors: usize,
}

impl ParallelTestExecutor {
    /// Create a new parallel test executor
    pub fn new(config: ParallelConfig) -> Self {
        Self {
            config,
            test_runner: DefaultTestRunner::new(),
            thread_pool: None,
        }
    }

    /// Initialize the thread pool
    pub fn initialize(&mut self) -> UmbraResult<()> {
        self.thread_pool = Some(ThreadPool::new(self.config.thread_count)?);
        println!("🚀 Initialized parallel test executor with {} threads", self.config.thread_count);
        Ok(())
    }

    /// Execute tests in parallel
    pub fn execute_tests(&mut self, tests: Vec<TestCase>) -> UmbraResult<ParallelTestResults> {
        if self.thread_pool.is_none() {
            self.initialize()?;
        }

        let start_time = Instant::now();
        let total_tests = tests.len();

        println!("🏃 Running {} tests in parallel...", total_tests);

        // Prepare shared state
        let results = Arc::new(Mutex::new(HashMap::new()));
        let thread_stats = Arc::new(Mutex::new(HashMap::new()));
        let (tx, rx) = mpsc::channel();

        // Initialize thread stats
        for i in 0..self.config.thread_count {
            thread_stats.lock().unwrap().insert(i, ThreadStats {
                thread_id: i,
                ..Default::default()
            });
        }

        // Distribute tests to workers
        let test_batches = self.distribute_tests(tests)?;
        let mut active_jobs = 0;

        for (thread_id, batch) in test_batches.into_iter().enumerate() {
            if batch.is_empty() {
                continue;
            }

            let results_clone = Arc::clone(&results);
            let thread_stats_clone = Arc::clone(&thread_stats);
            let tx_clone = tx.clone();
            let config = self.config.clone();

            if let Some(ref thread_pool) = self.thread_pool {
                thread_pool.execute(move || {
                    let batch_result = Self::execute_test_batch(
                        batch,
                        thread_id,
                        config,
                        results_clone,
                        thread_stats_clone,
                    );
                    tx_clone.send((thread_id, batch_result)).unwrap();
                });
                active_jobs += 1;
            }
        }

        // Wait for all jobs to complete
        for _ in 0..active_jobs {
            match rx.recv_timeout(self.config.timeout * 2) {
                Ok((thread_id, result)) => {
                    if let Err(e) = result {
                        println!("❌ Thread {} failed: {}", thread_id, e);
                    }
                }
                Err(_) => {
                    println!("⚠️ Timeout waiting for test completion");
                    break;
                }
            }
        }

        // Collect results
        let final_results = results.lock().unwrap().clone();
        let final_thread_stats = thread_stats.lock().unwrap().clone();

        let execution_stats = self.calculate_execution_stats(&final_results, start_time.elapsed());

        Ok(ParallelTestResults {
            results: final_results,
            execution_stats,
            thread_stats: final_thread_stats,
        })
    }

    /// Distribute tests across threads with load balancing
    fn distribute_tests(&self, tests: Vec<TestCase>) -> UmbraResult<Vec<Vec<TestCase>>> {
        let mut batches: Vec<Vec<TestCase>> = vec![Vec::new(); self.config.thread_count];

        if self.config.load_balancing {
            // Sort tests by estimated execution time (if available)
            let mut sorted_tests = tests;
            sorted_tests.sort_by(|a, b| {
                b.estimated_duration.unwrap_or(Duration::from_millis(100))
                    .cmp(&a.estimated_duration.unwrap_or(Duration::from_millis(100)))
            });

            // Distribute using longest processing time first
            let mut thread_loads = vec![Duration::ZERO; self.config.thread_count];
            
            for test in sorted_tests {
                let min_load_thread = thread_loads
                    .iter()
                    .enumerate()
                    .min_by_key(|(_, &load)| load)
                    .map(|(idx, _)| idx)
                    .unwrap_or(0);

                batches[min_load_thread].push(test.clone());
                thread_loads[min_load_thread] += test.estimated_duration.unwrap_or(Duration::from_millis(100));
            }
        } else {
            // Simple round-robin distribution
            for (i, test) in tests.into_iter().enumerate() {
                batches[i % self.config.thread_count].push(test);
            }
        }

        Ok(batches)
    }

    /// Execute a batch of tests on a single thread
    fn execute_test_batch(
        tests: Vec<TestCase>,
        thread_id: usize,
        config: ParallelConfig,
        results: Arc<Mutex<HashMap<String, TestResult>>>,
        thread_stats: Arc<Mutex<HashMap<usize, ThreadStats>>>,
    ) -> UmbraResult<()> {
        let thread_start = Instant::now();
        let mut local_stats = ThreadStats {
            thread_id,
            ..Default::default()
        };

        for test in tests {
            let test_start = Instant::now();
            
            // Create execution context
            let context = TestExecutionContext {
                test_id: test.name.clone(),
                thread_id,
                start_time: test_start,
                timeout: config.timeout,
                isolation_level: config.isolation_level.clone(),
            };

            // Execute test with isolation
            let result = Self::execute_test_with_isolation(&test, &context);
            
            let execution_time = test_start.elapsed();
            local_stats.tests_executed += 1;
            local_stats.total_execution_time += execution_time;

            if result.status == TestStatus::Failed {
                local_stats.errors += 1;
            }

            // Store result
            results.lock().unwrap().insert(test.name.clone(), result);
        }

        // Update thread stats
        local_stats.idle_time = thread_start.elapsed() - local_stats.total_execution_time;
        thread_stats.lock().unwrap().insert(thread_id, local_stats);

        Ok(())
    }

    /// Execute a single test with proper isolation
    fn execute_test_with_isolation(
        test: &TestCase,
        context: &TestExecutionContext,
    ) -> TestResult {
        match context.isolation_level {
            IsolationLevel::None => {
                // Direct execution
                Self::execute_test_direct(test)
            }
            IsolationLevel::Thread => {
                // Thread-local execution
                Self::execute_test_thread_isolated(test, context)
            }
            IsolationLevel::Process => {
                // Process isolation
                Self::execute_test_process_isolated(test, context)
            }
            IsolationLevel::Full => {
                // Full environment isolation
                Self::execute_test_fully_isolated(test, context)
            }
        }
    }

    /// Execute test directly without isolation
    fn execute_test_direct(test: &TestCase) -> TestResult {
        // This would use the regular test runner
        TestResult {
            test_name: test.name.clone(),
            suite_name: "parallel".to_string(),
            status: TestStatus::Passed,
            duration: Duration::from_millis(10),
            message: None,
            error: None,
            assertions: Vec::new(),
            coverage: None,
            performance_data: None,
            name: test.name.clone(),
            stdout: None,
            stderr: None,
            memory_usage: Some(0),
            allocations: Some(0),
            assertion_count: 1,
        }
    }

    /// Execute test with thread isolation
    fn execute_test_thread_isolated(test: &TestCase, _context: &TestExecutionContext) -> TestResult {
        // Create isolated thread-local state
        thread_local! {
            static TEST_STATE: std::cell::RefCell<HashMap<String, String>> = std::cell::RefCell::new(HashMap::new());
        }

        TEST_STATE.with(|state| {
            state.borrow_mut().clear();
            // Execute test with isolated state
            Self::execute_test_direct(test)
        })
    }

    /// Execute test with process isolation
    fn execute_test_process_isolated(test: &TestCase, _context: &TestExecutionContext) -> TestResult {
        // This would spawn a separate process for the test
        // For now, fall back to thread isolation
        Self::execute_test_thread_isolated(test, _context)
    }

    /// Execute test with full isolation
    fn execute_test_fully_isolated(test: &TestCase, context: &TestExecutionContext) -> TestResult {
        // This would provide complete environment isolation
        // For now, fall back to process isolation
        Self::execute_test_process_isolated(test, context)
    }

    /// Calculate execution statistics
    fn calculate_execution_stats(
        &self,
        results: &HashMap<String, TestResult>,
        total_time: Duration,
    ) -> ExecutionStats {
        let total_tests = results.len();
        let passed_tests = results.values().filter(|r| r.status == TestStatus::Passed).count();
        let failed_tests = results.values().filter(|r| r.status == TestStatus::Failed).count();
        let skipped_tests = results.values().filter(|r| r.status == TestStatus::Skipped).count();

        let durations: Vec<Duration> = results.values().map(|r| r.duration).collect();
        let total_test_time: Duration = durations.iter().sum();
        let average_execution_time = if total_tests > 0 {
            total_test_time / total_tests as u32
        } else {
            Duration::ZERO
        };

        let max_execution_time = durations.iter().max().copied().unwrap_or(Duration::ZERO);
        let min_execution_time = durations.iter().min().copied().unwrap_or(Duration::ZERO);

        let thread_utilization = if total_time.as_millis() > 0 {
            (total_test_time.as_millis() as f64) / (total_time.as_millis() as f64 * self.config.thread_count as f64)
        } else {
            0.0
        };

        ExecutionStats {
            total_tests,
            passed_tests,
            failed_tests,
            skipped_tests,
            total_execution_time: total_time,
            average_execution_time,
            max_execution_time,
            min_execution_time,
            thread_utilization,
        }
    }

    /// Print execution summary
    pub fn print_summary(&self, results: &ParallelTestResults) {
        println!("\n📊 Parallel Test Execution Summary");
        println!("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
        
        let stats = &results.execution_stats;
        println!("📈 Overall Statistics:");
        println!("  Total tests: {}", stats.total_tests);
        println!("  ✅ Passed: {}", stats.passed_tests);
        println!("  ❌ Failed: {}", stats.failed_tests);
        println!("  ⏭️  Skipped: {}", stats.skipped_tests);
        println!("  ⏱️  Total time: {:?}", stats.total_execution_time);
        println!("  📊 Average time: {:?}", stats.average_execution_time);
        println!("  🚀 Thread utilization: {:.1}%", stats.thread_utilization * 100.0);

        println!("\n🧵 Thread Statistics:");
        for (thread_id, thread_stats) in &results.thread_stats {
            println!("  Thread {}: {} tests, {:?} execution time, {} errors",
                thread_id, thread_stats.tests_executed, thread_stats.total_execution_time, thread_stats.errors);
        }
    }
}

impl ThreadPool {
    fn new(size: usize) -> UmbraResult<ThreadPool> {
        assert!(size > 0);

        let (sender, receiver) = mpsc::channel();
        let receiver = Arc::new(Mutex::new(receiver));
        let mut workers = Vec::with_capacity(size);

        for id in 0..size {
            workers.push(Worker::new(id, Arc::clone(&receiver))?);
        }

        Ok(ThreadPool { workers, sender })
    }

    fn execute<F>(&self, f: F)
    where
        F: FnOnce() + Send + 'static,
    {
        let job = Box::new(f);
        self.sender.send(job).unwrap();
    }
}

impl Worker {
    fn new(id: usize, receiver: Arc<Mutex<mpsc::Receiver<Job>>>) -> UmbraResult<Worker> {
        let thread = thread::spawn(move || loop {
            let job = receiver.lock().unwrap().recv();

            match job {
                Ok(job) => {
                    job();
                }
                Err(_) => {
                    break;
                }
            }
        });

        Ok(Worker {
            id,
            thread: Some(thread),
        })
    }
}

impl Drop for ThreadPool {
    fn drop(&mut self) {
        for worker in &mut self.workers {
            if let Some(thread) = worker.thread.take() {
                thread.join().unwrap();
            }
        }
    }
}
