/// Language Server Protocol implementation for Umbra
/// 
/// Provides advanced IDE features including IntelliSense, debugging, 
/// code completion, and real-time error checking.

use crate::error::UmbraResult;
use crate::lexer::Lexer;
use crate::parser::Parser;
use crate::semantic::SemanticAnalyzer;
use std::collections::HashMap;
use serde::{Deserialize, Serialize};

/// Language server for Umbra
pub struct UmbraLanguageServer {
    /// Open documents cache
    pub documents: HashMap<String, Document>,
    /// Symbol table cache
    pub symbols: HashMap<String, Vec<Symbol>>,
    /// Diagnostics cache
    pub diagnostics: HashMap<String, Vec<Diagnostic>>,
    /// Configuration
    pub config: LanguageServerConfig,
}

#[derive(Debug, Clone)]
pub struct Document {
    pub uri: String,
    pub content: String,
    pub version: i32,
    pub language_id: String,
    pub last_modified: std::time::SystemTime,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Symbol {
    pub name: String,
    pub kind: SymbolKind,
    pub location: Location,
    pub detail: Option<String>,
    pub documentation: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SymbolKind {
    File,
    Module,
    Namespace,
    Package,
    Class,
    Method,
    Property,
    Field,
    Constructor,
    Enum,
    Interface,
    Function,
    Variable,
    Constant,
    String,
    Number,
    Boolean,
    Array,
    Object,
    Key,
    Null,
    EnumMember,
    Struct,
    Event,
    Operator,
    TypeParameter,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Location {
    pub uri: String,
    pub range: Range,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Range {
    pub start: Position,
    pub end: Position,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Position {
    pub line: u32,
    pub character: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Diagnostic {
    pub range: Range,
    pub severity: DiagnosticSeverity,
    pub code: Option<String>,
    pub source: String,
    pub message: String,
    pub related_information: Vec<DiagnosticRelatedInformation>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DiagnosticSeverity {
    Error = 1,
    Warning = 2,
    Information = 3,
    Hint = 4,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DiagnosticRelatedInformation {
    pub location: Location,
    pub message: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CompletionItem {
    pub label: String,
    pub kind: CompletionItemKind,
    pub detail: Option<String>,
    pub documentation: Option<String>,
    pub insert_text: Option<String>,
    pub filter_text: Option<String>,
    pub sort_text: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum CompletionItemKind {
    Text = 1,
    Method = 2,
    Function = 3,
    Constructor = 4,
    Field = 5,
    Variable = 6,
    Class = 7,
    Interface = 8,
    Module = 9,
    Property = 10,
    Unit = 11,
    Value = 12,
    Enum = 13,
    Keyword = 14,
    Snippet = 15,
    Color = 16,
    File = 17,
    Reference = 18,
    Folder = 19,
    EnumMember = 20,
    Constant = 21,
    Struct = 22,
    Event = 23,
    Operator = 24,
    TypeParameter = 25,
}

#[derive(Debug, Clone)]
pub struct LanguageServerConfig {
    pub enable_diagnostics: bool,
    pub enable_completion: bool,
    pub enable_hover: bool,
    pub enable_signature_help: bool,
    pub enable_goto_definition: bool,
    pub enable_find_references: bool,
    pub max_diagnostics: usize,
    pub completion_trigger_characters: Vec<String>,
}

impl UmbraLanguageServer {
    /// Create new language server
    pub fn new(config: LanguageServerConfig) -> Self {
        Self {
            documents: HashMap::new(),
            symbols: HashMap::new(),
            diagnostics: HashMap::new(),
            config,
        }
    }

    /// Open document
    pub fn did_open(&mut self, uri: String, content: String, version: i32, language_id: String) -> UmbraResult<()> {
        let document = Document {
            uri: uri.clone(),
            content: content.clone(),
            version,
            language_id,
            last_modified: std::time::SystemTime::now(),
        };

        self.documents.insert(uri.clone(), document);
        
        // Analyze document and update diagnostics
        self.analyze_document(&uri)?;
        
        Ok(())
    }

    /// Change document
    pub fn did_change(&mut self, uri: String, content: String, version: i32) -> UmbraResult<()> {
        if let Some(document) = self.documents.get_mut(&uri) {
            document.content = content;
            document.version = version;
            document.last_modified = std::time::SystemTime::now();
            
            // Re-analyze document
            self.analyze_document(&uri)?;
        }
        
        Ok(())
    }

    /// Close document
    pub fn did_close(&mut self, uri: String) -> UmbraResult<()> {
        self.documents.remove(&uri);
        self.symbols.remove(&uri);
        self.diagnostics.remove(&uri);
        Ok(())
    }

    /// Analyze document and update symbols/diagnostics
    fn analyze_document(&mut self, uri: &str) -> UmbraResult<()> {
        if let Some(document) = self.documents.get(uri) {
            // Tokenize
            let mut lexer = Lexer::new(document.content.clone());
            let tokens = match lexer.tokenize() {
                Ok(tokens) => tokens,
                Err(e) => {
                    // Add lexer error as diagnostic
                    let diagnostic = Diagnostic {
                        range: Range {
                            start: Position { line: 0, character: 0 },
                            end: Position { line: 0, character: 0 },
                        },
                        severity: DiagnosticSeverity::Error,
                        code: Some("lexer_error".to_string()),
                        source: "umbra-lsp".to_string(),
                        message: format!("Lexer error: {}", e),
                        related_information: Vec::new(),
                    };
                    self.diagnostics.insert(uri.to_string(), vec![diagnostic]);
                    return Ok(());
                }
            };

            // Parse
            let mut parser = Parser::new(tokens);
            let ast = match parser.parse() {
                Ok(ast) => ast,
                Err(e) => {
                    // Add parser error as diagnostic
                    let diagnostic = Diagnostic {
                        range: Range {
                            start: Position { line: 0, character: 0 },
                            end: Position { line: 0, character: 0 },
                        },
                        severity: DiagnosticSeverity::Error,
                        code: Some("parser_error".to_string()),
                        source: "umbra-lsp".to_string(),
                        message: format!("Parser error: {}", e),
                        related_information: Vec::new(),
                    };
                    self.diagnostics.insert(uri.to_string(), vec![diagnostic]);
                    return Ok(());
                }
            };

            // Semantic analysis
            let mut analyzer = SemanticAnalyzer::new();
            match analyzer.analyze(&ast) {
                Ok(_) => {
                    // Extract symbols from AST
                    let symbols = self.extract_symbols(&ast, uri);
                    self.symbols.insert(uri.to_string(), symbols);
                    
                    // Clear diagnostics if analysis succeeded
                    self.diagnostics.insert(uri.to_string(), Vec::new());
                }
                Err(e) => {
                    // Add semantic error as diagnostic
                    let diagnostic = Diagnostic {
                        range: Range {
                            start: Position { line: 0, character: 0 },
                            end: Position { line: 0, character: 0 },
                        },
                        severity: DiagnosticSeverity::Error,
                        code: Some("semantic_error".to_string()),
                        source: "umbra-lsp".to_string(),
                        message: format!("Semantic error: {}", e),
                        related_information: Vec::new(),
                    };
                    self.diagnostics.insert(uri.to_string(), vec![diagnostic]);
                }
            }
        }

        Ok(())
    }

    /// Extract symbols from AST
    fn extract_symbols(&self, ast: &crate::parser::ast::Program, uri: &str) -> Vec<Symbol> {
        let mut symbols = Vec::new();
        
        // Extract function symbols
        for statement in &ast.statements {
            if let crate::parser::ast::Statement::Function(func) = statement {
                symbols.push(Symbol {
                    name: func.name.clone(),
                    kind: SymbolKind::Function,
                    location: Location {
                        uri: uri.to_string(),
                        range: Range {
                            start: Position { line: 0, character: 0 }, // TODO: Get actual position
                            end: Position { line: 0, character: 0 },
                        },
                    },
                    detail: Some(format!("function {}", func.name)),
                    documentation: None,
                });
            }
        }
        
        symbols
    }

    /// Get completion items at position
    pub fn completion(&self, uri: &str, position: Position) -> UmbraResult<Vec<CompletionItem>> {
        let mut items = Vec::new();
        
        // Add built-in functions
        items.extend(self.get_builtin_completions());
        
        // Add symbols from current document
        if let Some(symbols) = self.symbols.get(uri) {
            for symbol in symbols {
                items.push(CompletionItem {
                    label: symbol.name.clone(),
                    kind: self.symbol_kind_to_completion_kind(&symbol.kind),
                    detail: symbol.detail.clone(),
                    documentation: symbol.documentation.clone(),
                    insert_text: Some(symbol.name.clone()),
                    filter_text: Some(symbol.name.clone()),
                    sort_text: Some(symbol.name.clone()),
                });
            }
        }
        
        Ok(items)
    }

    fn get_builtin_completions(&self) -> Vec<CompletionItem> {
        vec![
            CompletionItem {
                label: "show".to_string(),
                kind: CompletionItemKind::Function,
                detail: Some("show(value) -> void".to_string()),
                documentation: Some("Print value to console".to_string()),
                insert_text: Some("show($1)".to_string()),
                filter_text: Some("show".to_string()),
                sort_text: Some("show".to_string()),
            },
            CompletionItem {
                label: "len".to_string(),
                kind: CompletionItemKind::Function,
                detail: Some("len(collection) -> Integer".to_string()),
                documentation: Some("Get length of collection".to_string()),
                insert_text: Some("len($1)".to_string()),
                filter_text: Some("len".to_string()),
                sort_text: Some("len".to_string()),
            },
            CompletionItem {
                label: "if".to_string(),
                kind: CompletionItemKind::Keyword,
                detail: Some("if statement".to_string()),
                documentation: Some("Conditional statement".to_string()),
                insert_text: Some("if $1 {\n\t$2\n}".to_string()),
                filter_text: Some("if".to_string()),
                sort_text: Some("if".to_string()),
            },
        ]
    }

    fn symbol_kind_to_completion_kind(&self, kind: &SymbolKind) -> CompletionItemKind {
        match kind {
            SymbolKind::Function => CompletionItemKind::Function,
            SymbolKind::Variable => CompletionItemKind::Variable,
            SymbolKind::Class => CompletionItemKind::Class,
            SymbolKind::Module => CompletionItemKind::Module,
            SymbolKind::Constant => CompletionItemKind::Constant,
            _ => CompletionItemKind::Text,
        }
    }

    /// Get diagnostics for document
    pub fn get_diagnostics(&self, uri: &str) -> Vec<Diagnostic> {
        self.diagnostics.get(uri).cloned().unwrap_or_default()
    }

    /// Get hover information at position
    pub fn hover(&self, uri: &str, position: Position) -> UmbraResult<Option<String>> {
        // In a real implementation, this would find the symbol at the position
        // and return its documentation
        Ok(Some("Hover information would appear here".to_string()))
    }

    /// Go to definition
    pub fn goto_definition(&self, uri: &str, position: Position) -> UmbraResult<Option<Location>> {
        // In a real implementation, this would find the definition location
        // of the symbol at the given position
        Ok(None)
    }

    /// Find references
    pub fn find_references(&self, uri: &str, position: Position) -> UmbraResult<Vec<Location>> {
        // In a real implementation, this would find all references to the symbol
        Ok(Vec::new())
    }
}

impl Default for LanguageServerConfig {
    fn default() -> Self {
        Self {
            enable_diagnostics: true,
            enable_completion: true,
            enable_hover: true,
            enable_signature_help: true,
            enable_goto_definition: true,
            enable_find_references: true,
            max_diagnostics: 100,
            completion_trigger_characters: vec![".".to_string(), "::".to_string()],
        }
    }
}
