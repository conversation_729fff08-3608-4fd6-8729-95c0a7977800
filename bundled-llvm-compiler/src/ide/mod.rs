/// IDE Integration Tools for Umbra
/// 
/// Provides comprehensive IDE integration beyond LSP including project templates,
/// code snippets, extensions, and enhanced development workflows.

pub mod templates;
pub mod snippets;
pub mod extensions;
pub mod workflows;
pub mod project_wizard;
pub mod code_actions;
pub mod language_server;

use crate::error::UmbraResult;
use std::collections::HashMap;
use std::path::Path;
use serde::{Deserialize, Serialize};

/// IDE integration manager
pub struct IDEIntegration {
    /// Template manager
    pub templates: templates::TemplateManager,
    
    /// Snippet manager
    pub snippets: snippets::SnippetManager,
    
    /// Extension manager
    pub extensions: extensions::ExtensionManager,
    
    /// Workflow manager
    pub workflows: workflows::WorkflowManager,
    
    /// Project wizard
    pub project_wizard: project_wizard::ProjectWizard,
    
    /// Code actions provider
    pub code_actions: code_actions::CodeActionsProvider,
    
    /// IDE configuration
    pub config: IDEConfig,
}

/// IDE configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IDEConfig {
    /// Enable auto-completion
    pub auto_completion: bool,
    
    /// Enable syntax highlighting
    pub syntax_highlighting: bool,
    
    /// Enable error checking
    pub error_checking: bool,
    
    /// Enable code formatting
    pub code_formatting: bool,
    
    /// Enable refactoring tools
    pub refactoring: bool,
    
    /// Enable AI/ML specific features
    pub ai_ml_features: bool,
    
    /// Theme configuration
    pub theme: ThemeConfig,
    
    /// Editor settings
    pub editor: EditorConfig,
    
    /// Build integration
    pub build_integration: BuildIntegrationConfig,
    
    /// Debug integration
    pub debug_integration: DebugIntegrationConfig,
}

/// Theme configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ThemeConfig {
    /// Color scheme
    pub color_scheme: String,
    
    /// Font family
    pub font_family: String,
    
    /// Font size
    pub font_size: u32,
    
    /// Line height
    pub line_height: f32,
    
    /// Custom colors for Umbra syntax
    pub umbra_colors: HashMap<String, String>,
}

/// Editor configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EditorConfig {
    /// Tab size
    pub tab_size: u32,
    
    /// Use spaces instead of tabs
    pub use_spaces: bool,
    
    /// Auto-indent
    pub auto_indent: bool,
    
    /// Show line numbers
    pub line_numbers: bool,
    
    /// Word wrap
    pub word_wrap: bool,
    
    /// Show whitespace
    pub show_whitespace: bool,
    
    /// Auto-save interval (seconds)
    pub auto_save_interval: Option<u32>,
}

/// Build integration configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BuildIntegrationConfig {
    /// Auto-build on save
    pub auto_build: bool,
    
    /// Show build output
    pub show_output: bool,
    
    /// Build on file change
    pub watch_mode: bool,
    
    /// Parallel builds
    pub parallel: bool,
    
    /// Build notifications
    pub notifications: bool,
}

/// Debug integration configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DebugIntegrationConfig {
    /// Auto-attach debugger
    pub auto_attach: bool,
    
    /// Show debug console
    pub show_console: bool,
    
    /// Break on exceptions
    pub break_on_exceptions: bool,
    
    /// Show variable tooltips
    pub variable_tooltips: bool,
    
    /// Inline variable values
    pub inline_values: bool,
}

/// IDE feature capability
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IDECapability {
    /// Feature name
    pub name: String,
    
    /// Feature description
    pub description: String,
    
    /// Whether feature is enabled
    pub enabled: bool,
    
    /// Feature version
    pub version: String,
    
    /// Required dependencies
    pub dependencies: Vec<String>,
    
    /// Configuration options
    pub config_options: HashMap<String, serde_json::Value>,
}

/// IDE command for automation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IDECommand {
    /// Command ID
    pub id: String,
    
    /// Command title
    pub title: String,
    
    /// Command category
    pub category: String,
    
    /// Keyboard shortcut
    pub shortcut: Option<String>,
    
    /// Command arguments
    pub arguments: Vec<String>,
    
    /// Command description
    pub description: String,
}

impl IDEIntegration {
    /// Create new IDE integration
    pub fn new() -> UmbraResult<Self> {
        Ok(Self {
            templates: templates::TemplateManager::new()?,
            snippets: snippets::SnippetManager::new()?,
            extensions: extensions::ExtensionManager::new()?,
            workflows: workflows::WorkflowManager::new()?,
            project_wizard: project_wizard::ProjectWizard::new()?,
            code_actions: code_actions::CodeActionsProvider::new()?,
            config: IDEConfig::default(),
        })
    }
    
    /// Initialize IDE integration with configuration
    pub fn initialize(&mut self, config_path: Option<&Path>) -> UmbraResult<()> {
        // Load configuration
        if let Some(path) = config_path {
            self.load_config(path)?;
        }
        
        // Initialize components
        self.templates.initialize()?;
        self.snippets.initialize()?;
        self.extensions.initialize()?;
        self.workflows.initialize()?;
        self.project_wizard.initialize()?;
        self.code_actions.initialize()?;
        
        println!("🔧 IDE integration initialized");
        Ok(())
    }
    
    /// Load IDE configuration
    pub fn load_config(&mut self, path: &Path) -> UmbraResult<()> {
        if path.exists() {
            let content = std::fs::read_to_string(path)?;
            self.config = serde_json::from_str(&content)
                .map_err(|e| crate::error::UmbraError::CodeGen(format!("Failed to parse IDE config: {e}")))?;
            println!("📋 IDE configuration loaded from {}", path.display());
        }
        Ok(())
    }
    
    /// Save IDE configuration
    pub fn save_config(&self, path: &Path) -> UmbraResult<()> {
        let content = serde_json::to_string_pretty(&self.config)
            .map_err(|e| crate::error::UmbraError::CodeGen(format!("Failed to serialize IDE config: {e}")))?;
        
        std::fs::write(path, content)?;
        println!("💾 IDE configuration saved to {}", path.display());
        Ok(())
    }
    
    /// Get available IDE capabilities
    pub fn get_capabilities(&self) -> Vec<IDECapability> {
        vec![
            IDECapability {
                name: "syntax_highlighting".to_string(),
                description: "Umbra syntax highlighting with AI/ML keywords".to_string(),
                enabled: self.config.syntax_highlighting,
                version: "1.0.0".to_string(),
                dependencies: vec![],
                config_options: HashMap::new(),
            },
            IDECapability {
                name: "auto_completion".to_string(),
                description: "Intelligent code completion for Umbra".to_string(),
                enabled: self.config.auto_completion,
                version: "1.0.0".to_string(),
                dependencies: vec!["lsp".to_string()],
                config_options: HashMap::new(),
            },
            IDECapability {
                name: "debugging".to_string(),
                description: "Integrated debugging support".to_string(),
                enabled: self.config.debug_integration.auto_attach,
                version: "1.0.0".to_string(),
                dependencies: vec!["debugger".to_string()],
                config_options: HashMap::new(),
            },
            IDECapability {
                name: "ai_ml_features".to_string(),
                description: "AI/ML specific development features".to_string(),
                enabled: self.config.ai_ml_features,
                version: "1.0.0".to_string(),
                dependencies: vec!["ai_stdlib".to_string()],
                config_options: HashMap::new(),
            },
        ]
    }
    
    /// Get available IDE commands
    pub fn get_commands(&self) -> Vec<IDECommand> {
        vec![
            IDECommand {
                id: "umbra.build".to_string(),
                title: "Build Project".to_string(),
                category: "Build".to_string(),
                shortcut: Some("Ctrl+Shift+B".to_string()),
                arguments: vec![],
                description: "Build the current Umbra project".to_string(),
            },
            IDECommand {
                id: "umbra.test".to_string(),
                title: "Run Tests".to_string(),
                category: "Test".to_string(),
                shortcut: Some("Ctrl+Shift+T".to_string()),
                arguments: vec![],
                description: "Run all tests in the project".to_string(),
            },
            IDECommand {
                id: "umbra.debug".to_string(),
                title: "Start Debugging".to_string(),
                category: "Debug".to_string(),
                shortcut: Some("F5".to_string()),
                arguments: vec![],
                description: "Start debugging the current program".to_string(),
            },
            IDECommand {
                id: "umbra.format".to_string(),
                title: "Format Document".to_string(),
                category: "Edit".to_string(),
                shortcut: Some("Shift+Alt+F".to_string()),
                arguments: vec![],
                description: "Format the current Umbra document".to_string(),
            },
            IDECommand {
                id: "umbra.new_project".to_string(),
                title: "New Umbra Project".to_string(),
                category: "File".to_string(),
                shortcut: Some("Ctrl+Shift+N".to_string()),
                arguments: vec![],
                description: "Create a new Umbra project using the wizard".to_string(),
            },
        ]
    }
    
    /// Update configuration
    pub fn update_config(&mut self, config: IDEConfig) {
        self.config = config;
        println!("🔄 IDE configuration updated");
    }
    
    /// Get configuration
    pub fn config(&self) -> &IDEConfig {
        &self.config
    }
    
    /// Enable feature
    pub fn enable_feature(&mut self, feature: &str) -> UmbraResult<()> {
        match feature {
            "auto_completion" => self.config.auto_completion = true,
            "syntax_highlighting" => self.config.syntax_highlighting = true,
            "error_checking" => self.config.error_checking = true,
            "code_formatting" => self.config.code_formatting = true,
            "refactoring" => self.config.refactoring = true,
            "ai_ml_features" => self.config.ai_ml_features = true,
            _ => return Err(crate::error::UmbraError::CodeGen(format!("Unknown feature: {feature}"))),
        }
        println!("✅ Feature '{feature}' enabled");
        Ok(())
    }
    
    /// Disable feature
    pub fn disable_feature(&mut self, feature: &str) -> UmbraResult<()> {
        match feature {
            "auto_completion" => self.config.auto_completion = false,
            "syntax_highlighting" => self.config.syntax_highlighting = false,
            "error_checking" => self.config.error_checking = false,
            "code_formatting" => self.config.code_formatting = false,
            "refactoring" => self.config.refactoring = false,
            "ai_ml_features" => self.config.ai_ml_features = false,
            _ => return Err(crate::error::UmbraError::CodeGen(format!("Unknown feature: {feature}"))),
        }
        println!("❌ Feature '{feature}' disabled");
        Ok(())
    }
}

impl Default for IDEConfig {
    fn default() -> Self {
        Self {
            auto_completion: true,
            syntax_highlighting: true,
            error_checking: true,
            code_formatting: true,
            refactoring: true,
            ai_ml_features: true,
            theme: ThemeConfig::default(),
            editor: EditorConfig::default(),
            build_integration: BuildIntegrationConfig::default(),
            debug_integration: DebugIntegrationConfig::default(),
        }
    }
}

impl Default for ThemeConfig {
    fn default() -> Self {
        let mut umbra_colors = HashMap::new();
        umbra_colors.insert("keyword".to_string(), "#569CD6".to_string());
        umbra_colors.insert("ai_keyword".to_string(), "#FF6B6B".to_string());
        umbra_colors.insert("string".to_string(), "#CE9178".to_string());
        umbra_colors.insert("number".to_string(), "#B5CEA8".to_string());
        umbra_colors.insert("comment".to_string(), "#6A9955".to_string());
        umbra_colors.insert("function".to_string(), "#DCDCAA".to_string());
        umbra_colors.insert("variable".to_string(), "#9CDCFE".to_string());
        umbra_colors.insert("type".to_string(), "#4EC9B0".to_string());
        
        Self {
            color_scheme: "dark".to_string(),
            font_family: "Fira Code".to_string(),
            font_size: 14,
            line_height: 1.5,
            umbra_colors,
        }
    }
}

impl Default for EditorConfig {
    fn default() -> Self {
        Self {
            tab_size: 4,
            use_spaces: true,
            auto_indent: true,
            line_numbers: true,
            word_wrap: false,
            show_whitespace: false,
            auto_save_interval: Some(30),
        }
    }
}

impl Default for BuildIntegrationConfig {
    fn default() -> Self {
        Self {
            auto_build: false,
            show_output: true,
            watch_mode: false,
            parallel: true,
            notifications: true,
        }
    }
}

impl Default for DebugIntegrationConfig {
    fn default() -> Self {
        Self {
            auto_attach: false,
            show_console: true,
            break_on_exceptions: true,
            variable_tooltips: true,
            inline_values: true,
        }
    }
}

impl Default for IDEIntegration {
    fn default() -> Self {
        Self::new().unwrap()
    }
}
