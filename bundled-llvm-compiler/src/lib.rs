// Umbra Compiler Library
// This exposes the compiler components as a library for testing and external use

#![allow(unused_variables)]
#![allow(unused_imports)]
#![allow(dead_code)]
#![allow(unused_assignments)]
#![allow(unused_mut)]
#![allow(unreachable_patterns)]
#![allow(deprecated)]
#![allow(non_camel_case_types)]
#![allow(non_snake_case)]
#![allow(unused_doc_comments)]
#![allow(ambiguous_glob_reexports)]
#![allow(private_interfaces)]
#![allow(invalid_value)]

pub mod error;
pub mod lexer;
pub mod parser;
pub mod semantic;
pub mod codegen;
pub mod module;
pub mod stdlib;
pub mod runtime;
pub mod repl;
pub mod lsp;
pub mod debug;
pub mod testing;
pub mod optimization;
pub mod build;
pub mod ide;
pub mod interop;
pub mod publishing;
pub mod ai_ml;
pub mod database;
pub mod platform;

// Re-export commonly used types
pub use error::{UmbraError, UmbraResult};
pub use lexer::{<PERSON><PERSON>, <PERSON><PERSON>, TokenType};
pub use parser::{Parser, ast::Program};
pub use semantic::SemanticAnalyzer;

// Bundled LLVM support
pub mod llvm;
