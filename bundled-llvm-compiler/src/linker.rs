use std::path::{Path, PathBuf};
use std::process::Command;
use tempfile::TempDir;

use crate::codegen::RuntimeLibrary;
use crate::error::{UmbraError, UmbraResult};

/// Linker for creating native executables from object files
pub struct Linker {
    temp_dir: TempDir,
}

impl Linker {
    pub fn new() -> UmbraResult<Self> {
        let temp_dir = TempDir::new().map_err(|e| UmbraError::from(e))?;

        Ok(Self { temp_dir })
    }

    /// Link object file with runtime library to create executable
    pub fn link_executable(
        &self,
        object_file: &Path,
        output_path: &Path,
        debug: bool,
        verbose: bool,
    ) -> UmbraResult<()> {
        // Generate runtime library C file
        let runtime_c_path = self.temp_dir.path().join("umbra_runtime.c");
        RuntimeLibrary::write_runtime_to_file(&runtime_c_path).map_err(|e| UmbraError::from(e))?;

        // Compile runtime library to object file
        let runtime_o_path = self.temp_dir.path().join("umbra_runtime.o");
        self.compile_c_to_object(&runtime_c_path, &runtime_o_path, debug, verbose)?;

        // Link object files together
        self.link_objects(&[object_file, &runtime_o_path], output_path, debug, verbose)?;

        Ok(())
    }

    fn compile_c_to_object(
        &self,
        c_file: &Path,
        object_file: &Path,
        debug: bool,
        verbose: bool,
    ) -> UmbraResult<()> {
        let mut cmd = Command::new("gcc");
        cmd.arg("-c").arg(c_file).arg("-o").arg(object_file);

        if debug {
            cmd.arg("-g");
        } else {
            cmd.arg("-O2");
        }

        // Add standard flags including position-independent code
        cmd.arg("-std=c99")
            .arg("-Wall")
            .arg("-Wextra")
            .arg("-fPIC"); // Position Independent Code for shared libraries

        if verbose {
            println!("Compiling runtime: {cmd:?}");
        }

        let output = cmd
            .output()
            .map_err(|e| UmbraError::Runtime(format!("Failed to run gcc: {e}")))?;

        if !output.status.success() {
            let stderr = String::from_utf8_lossy(&output.stderr);
            return Err(UmbraError::Runtime(format!(
                "gcc compilation failed: {stderr}"
            )));
        }

        Ok(())
    }

    fn link_objects(
        &self,
        object_files: &[&Path],
        output_path: &Path,
        debug: bool,
        verbose: bool,
    ) -> UmbraResult<()> {
        let mut cmd = Command::new("gcc");

        // Add object files
        for obj in object_files {
            cmd.arg(obj);
        }

        cmd.arg("-o").arg(output_path);

        if debug {
            cmd.arg("-g");
        }

        // Link with standard libraries
        cmd.arg("-lm"); // Math library

        if verbose {
            println!("Linking: {cmd:?}");
        }

        let output = cmd
            .output()
            .map_err(|e| UmbraError::Runtime(format!("Failed to run linker: {e}")))?;

        if !output.status.success() {
            let stderr = String::from_utf8_lossy(&output.stderr);
            return Err(UmbraError::Runtime(format!("Linking failed: {stderr}")));
        }

        Ok(())
    }

    /// Check if required tools are available
    pub fn check_tools() -> UmbraResult<()> {
        // Check for GCC
        let gcc_check = Command::new("gcc").arg("--version").output();

        if gcc_check.is_err() {
            return Err(UmbraError::Runtime(
                "GCC not found. Please install GCC to compile Umbra programs.".to_string(),
            ));
        }

        // Check for LLVM tools (optional but recommended)
        let llc_check = Command::new("llc").arg("--version").output();

        if llc_check.is_err() {
            eprintln!("Warning: LLVM tools not found. Some optimizations may not be available.");
        }

        Ok(())
    }

    /// Get the temporary directory path
    pub fn temp_dir(&self) -> &Path {
        self.temp_dir.path()
    }
}

/// Builder for creating executables with different configurations
pub struct ExecutableBuilder {
    linker: Linker,
    debug: bool,
    optimization_level: u8,
    verbose: bool,
}

impl ExecutableBuilder {
    pub fn new() -> UmbraResult<Self> {
        Ok(Self {
            linker: Linker::new()?,
            debug: false,
            optimization_level: 2,
            verbose: false,
        })
    }

    pub fn debug(mut self, debug: bool) -> Self {
        self.debug = debug;
        self
    }

    pub fn optimization_level(mut self, level: u8) -> Self {
        self.optimization_level = level.min(3);
        self
    }

    pub fn verbose(mut self, verbose: bool) -> Self {
        self.verbose = verbose;
        self
    }

    pub fn build(&self, object_file: &Path, output_path: &Path) -> UmbraResult<()> {
        self.linker
            .link_executable(object_file, output_path, self.debug, self.verbose)
    }

    pub fn temp_dir(&self) -> &Path {
        self.linker.temp_dir()
    }
}

/// Utility functions for executable creation
pub mod utils {
    use super::*;

    /// Get the default executable extension for the current platform
    #[allow(dead_code)]
    pub fn get_executable_extension() -> &'static str {
        if cfg!(windows) {
            ".exe"
        } else {
            ""
        }
    }

    /// Create an output path from an input path
    #[allow(dead_code)]
    pub fn create_output_path(input_path: &Path, output_dir: Option<&Path>) -> PathBuf {
        let mut output_path = if let Some(dir) = output_dir {
            dir.to_path_buf()
        } else {
            input_path.parent().unwrap_or(Path::new(".")).to_path_buf()
        };

        let stem = input_path.file_stem().unwrap_or_default();
        output_path.push(format!(
            "{}{}",
            stem.to_string_lossy(),
            get_executable_extension()
        ));
        output_path
    }

    /// Check if a path is an Umbra source file
    pub fn is_umbra_file(path: &Path) -> bool {
        path.extension()
            .and_then(|ext| ext.to_str())
            .map(|ext| ext.eq_ignore_ascii_case("umbra"))
            .unwrap_or(false)
    }

    /// Validate that required system tools are available
    pub fn validate_system() -> UmbraResult<()> {
        Linker::check_tools()
    }
}
