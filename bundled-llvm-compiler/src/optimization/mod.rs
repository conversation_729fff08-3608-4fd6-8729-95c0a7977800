/// Compilation optimization module
/// 
/// This module provides various optimization strategies for improving
/// compilation speed and efficiency.

pub mod cache;
pub mod parallel;
pub mod profiler;
pub mod large_program;

use crate::error::UmbraResult;
use crate::parser::ast::Program;
use std::path::Path;

/// Optimization configuration
#[derive(Debug, <PERSON>lone)]
pub struct OptimizationConfig {
    /// Enable parallel compilation
    pub parallel_compilation: bool,
    
    /// Enable compilation caching
    pub enable_cache: bool,
    
    /// Cache directory path
    pub cache_dir: Option<String>,
    
    /// Number of parallel threads (0 = auto-detect)
    pub thread_count: usize,
    
    /// Enable incremental compilation
    pub incremental: bool,
    
    /// Enable fast compilation mode (reduced optimizations)
    pub fast_mode: bool,
}

impl Default for OptimizationConfig {
    fn default() -> Self {
        Self {
            parallel_compilation: true,
            enable_cache: true,
            cache_dir: None,
            thread_count: 0, // Auto-detect
            incremental: true,
            fast_mode: false,
        }
    }
}

/// Compilation optimizer
pub struct CompilationOptimizer {
    config: OptimizationConfig,
    cache: Option<cache::CompilationCache>,
    parallel_executor: Option<parallel::ParallelExecutor>,
}

impl CompilationOptimizer {
    /// Create a new compilation optimizer
    pub fn new(config: OptimizationConfig) -> UmbraResult<Self> {
        let cache = if config.enable_cache {
            Some(cache::CompilationCache::new(config.cache_dir.clone())?)
        } else {
            None
        };
        
        let parallel_executor = if config.parallel_compilation {
            Some(parallel::ParallelExecutor::new(config.thread_count)?)
        } else {
            None
        };
        
        Ok(Self {
            config,
            cache,
            parallel_executor,
        })
    }
    
    /// Optimize compilation of a single file
    pub fn optimize_file_compilation(&mut self, file_path: &Path, program: &Program) -> UmbraResult<bool> {
        // Check cache first
        if let Some(cache) = &mut self.cache {
            if cache.is_cached(file_path)?
                && !cache.is_stale(file_path)? {
                    // Use cached result
                    return Ok(true);
                }
        }
        
        // File needs compilation
        Ok(false)
    }
    
    /// Optimize compilation of multiple files
    pub fn optimize_batch_compilation(&mut self, files: &[(std::path::PathBuf, Program)]) -> UmbraResult<Vec<bool>> {
        let mut results = Vec::new();
        
        if let Some(executor) = &mut self.parallel_executor {
            // Use parallel compilation
            results = executor.compile_parallel(files)?;
        } else {
            // Sequential compilation
            for (file_path, program) in files {
                let cached = self.optimize_file_compilation(file_path, program)?;
                results.push(cached);
            }
        }
        
        Ok(results)
    }
    
    /// Update cache with compilation result
    pub fn update_cache(&mut self, file_path: &Path, success: bool) -> UmbraResult<()> {
        if let Some(cache) = &mut self.cache {
            cache.update(file_path, success)?;
        }
        Ok(())
    }
    
    /// Get optimization statistics
    pub fn get_stats(&self) -> OptimizationStats {
        OptimizationStats {
            cache_hits: self.cache.as_ref().map(|c| c.get_hit_count()).unwrap_or(0),
            cache_misses: self.cache.as_ref().map(|c| c.get_miss_count()).unwrap_or(0),
            parallel_jobs: self.parallel_executor.as_ref().map(|e| e.get_job_count()).unwrap_or(0),
            threads_used: self.parallel_executor.as_ref().map(|e| e.get_thread_count()).unwrap_or(1),
        }
    }
    
    /// Clear compilation cache
    pub fn clear_cache(&mut self) -> UmbraResult<()> {
        if let Some(cache) = &mut self.cache {
            cache.clear()?;
        }
        Ok(())
    }
    
    /// Enable or disable fast compilation mode
    pub fn set_fast_mode(&mut self, enabled: bool) {
        self.config.fast_mode = enabled;
    }
    
    /// Check if fast mode is enabled
    pub fn is_fast_mode(&self) -> bool {
        self.config.fast_mode
    }
}

/// Optimization statistics
#[derive(Debug, Clone)]
pub struct OptimizationStats {
    pub cache_hits: usize,
    pub cache_misses: usize,
    pub parallel_jobs: usize,
    pub threads_used: usize,
}

impl OptimizationStats {
    /// Calculate cache hit ratio
    pub fn cache_hit_ratio(&self) -> f64 {
        let total = self.cache_hits + self.cache_misses;
        if total == 0 {
            0.0
        } else {
            self.cache_hits as f64 / total as f64
        }
    }
    
    /// Get total cache accesses
    pub fn total_cache_accesses(&self) -> usize {
        self.cache_hits + self.cache_misses
    }
}

/// Compilation timing utilities
pub mod timing {
    use std::time::{Duration, Instant};
    
    /// Timer for measuring compilation phases
    pub struct CompilationTimer {
        start_time: Instant,
        phase_times: Vec<(String, Duration)>,
        current_phase: Option<(String, Instant)>,
    }
    
    impl CompilationTimer {
        pub fn new() -> Self {
            Self {
                start_time: Instant::now(),
                phase_times: Vec::new(),
                current_phase: None,
            }
        }
        
        pub fn start_phase(&mut self, name: String) {
            if let Some((phase_name, start_time)) = self.current_phase.take() {
                let duration = start_time.elapsed();
                self.phase_times.push((phase_name, duration));
            }
            self.current_phase = Some((name, Instant::now()));
        }
        
        pub fn end_phase(&mut self) {
            if let Some((phase_name, start_time)) = self.current_phase.take() {
                let duration = start_time.elapsed();
                self.phase_times.push((phase_name, duration));
            }
        }
        
        pub fn total_time(&self) -> Duration {
            self.start_time.elapsed()
        }
        
        pub fn get_phase_times(&self) -> &[(String, Duration)] {
            &self.phase_times
        }
        
        pub fn print_summary(&self) {
            println!("Compilation timing summary:");
            println!("Total time: {:?}", self.total_time());
            for (phase, duration) in &self.phase_times {
                println!("  {phase}: {duration:?}");
            }
        }
    }
    
    impl Default for CompilationTimer {
        fn default() -> Self {
            Self::new()
        }
    }
}

/// Incremental compilation utilities
pub mod incremental {
    use crate::error::UmbraResult;
    use std::collections::HashMap;
    use std::path::{Path, PathBuf};
    use std::time::SystemTime;
    
    /// Dependency tracker for incremental compilation
    pub struct DependencyTracker {
        dependencies: HashMap<PathBuf, Vec<PathBuf>>,
        timestamps: HashMap<PathBuf, SystemTime>,
    }
    
    impl DependencyTracker {
        pub fn new() -> Self {
            Self {
                dependencies: HashMap::new(),
                timestamps: HashMap::new(),
            }
        }
        
        pub fn add_dependency(&mut self, file: PathBuf, dependency: PathBuf) {
            self.dependencies.entry(file).or_default().push(dependency);
        }
        
        pub fn update_timestamp(&mut self, file: &Path) -> UmbraResult<()> {
            let metadata = std::fs::metadata(file)?;
            let modified = metadata.modified()?;
            self.timestamps.insert(file.to_path_buf(), modified);
            Ok(())
        }
        
        pub fn needs_recompilation(&self, file: &Path) -> UmbraResult<bool> {
            let file_time = match self.timestamps.get(file) {
                Some(time) => *time,
                None => return Ok(true), // File not tracked, needs compilation
            };
            
            // Check if any dependencies are newer
            if let Some(deps) = self.dependencies.get(file) {
                for dep in deps {
                    if let Some(dep_time) = self.timestamps.get(dep) {
                        if *dep_time > file_time {
                            return Ok(true);
                        }
                    }
                }
            }
            
            Ok(false)
        }
        
        pub fn get_dependencies(&self, file: &Path) -> Option<&[PathBuf]> {
            self.dependencies.get(file).map(|v| v.as_slice())
        }
    }
    
    impl Default for DependencyTracker {
        fn default() -> Self {
            Self::new()
        }
    }
}
