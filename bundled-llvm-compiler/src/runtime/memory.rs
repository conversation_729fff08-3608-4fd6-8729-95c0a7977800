/// Memory management system for Umbra runtime
/// 
/// This module provides automatic memory management with garbage collection,
/// reference counting, and manual memory allocation functions.

use crate::error::{UmbraError, UmbraResult};
use std::collections::{HashMap, HashSet};

/// Memory manager for Umbra runtime
pub struct MemoryManager {
    /// Allocated objects tracked by the GC
    allocated_objects: HashMap<ObjectId, GcObject>,
    /// Root set for garbage collection
    roots: HashSet<ObjectId>,
    /// Next object ID
    next_id: ObjectId,
    /// Memory statistics
    stats: MemoryStats,
    /// GC configuration
    config: GcConfig,
}

/// Unique identifier for allocated objects
pub type ObjectId = u64;

/// Garbage collected object
#[derive(Debug)]
pub struct GcObject {
    pub id: ObjectId,
    pub data: Box<dyn GcData>,
    pub references: Vec<ObjectId>,
    pub ref_count: usize,
    pub marked: bool,
    pub size: usize,
}

/// Trait for garbage collected data
pub trait GcData: std::fmt::Debug + Send + Sync {
    fn get_references(&self) -> Vec<ObjectId>;
    fn size(&self) -> usize;
    fn type_name(&self) -> &'static str;
}

/// Memory statistics
#[derive(Debug, Default)]
pub struct MemoryStats {
    pub total_allocated: usize,
    pub total_freed: usize,
    pub current_usage: usize,
    pub gc_runs: usize,
    pub objects_collected: usize,
    pub peak_usage: usize,
}

/// Garbage collection configuration
#[derive(Debug)]
pub struct GcConfig {
    /// Threshold for triggering GC (bytes)
    pub gc_threshold: usize,
    /// Enable automatic GC
    pub auto_gc: bool,
    /// Enable reference counting
    pub ref_counting: bool,
    /// Enable generational GC
    pub generational: bool,
}

impl Default for GcConfig {
    fn default() -> Self {
        Self {
            gc_threshold: 1024 * 1024, // 1MB
            auto_gc: true,
            ref_counting: true,
            generational: false,
        }
    }
}

impl MemoryManager {
    pub fn new() -> Self {
        Self {
            allocated_objects: HashMap::new(),
            roots: HashSet::new(),
            next_id: 1,
            stats: MemoryStats::default(),
            config: GcConfig::default(),
        }
    }

    /// Allocate a new object
    pub fn allocate<T: GcData + 'static>(&mut self, data: T) -> UmbraResult<ObjectId> {
        let id = self.next_id;
        self.next_id += 1;

        let size = data.size();
        let references = data.get_references();

        let obj = GcObject {
            id,
            data: Box::new(data),
            references,
            ref_count: 0,
            marked: false,
            size,
        };

        self.allocated_objects.insert(id, obj);
        self.stats.total_allocated += size;
        self.stats.current_usage += size;

        if self.stats.current_usage > self.stats.peak_usage {
            self.stats.peak_usage = self.stats.current_usage;
        }

        // Trigger GC if threshold exceeded
        if self.config.auto_gc && self.stats.current_usage > self.config.gc_threshold {
            self.collect_garbage()?;
        }

        Ok(id)
    }

    /// Add a root reference
    pub fn add_root(&mut self, id: ObjectId) {
        self.roots.insert(id);
        if let Some(obj) = self.allocated_objects.get_mut(&id) {
            obj.ref_count += 1;
        }
    }

    /// Remove a root reference
    pub fn remove_root(&mut self, id: ObjectId) {
        self.roots.remove(&id);
        if let Some(obj) = self.allocated_objects.get_mut(&id) {
            if obj.ref_count > 0 {
                obj.ref_count -= 1;
            }
        }
    }

    /// Increment reference count
    pub fn retain(&mut self, id: ObjectId) -> UmbraResult<()> {
        if let Some(obj) = self.allocated_objects.get_mut(&id) {
            obj.ref_count += 1;
            Ok(())
        } else {
            Err(UmbraError::Memory(format!("Object {} not found", id)))
        }
    }

    /// Decrement reference count
    pub fn release(&mut self, id: ObjectId) -> UmbraResult<()> {
        if let Some(obj) = self.allocated_objects.get_mut(&id) {
            if obj.ref_count > 0 {
                obj.ref_count -= 1;
                
                // If ref count reaches 0 and ref counting is enabled, free immediately
                if self.config.ref_counting && obj.ref_count == 0 && !self.roots.contains(&id) {
                    self.free_object(id)?;
                }
            }
            Ok(())
        } else {
            Err(UmbraError::Memory(format!("Object {} not found", id)))
        }
    }

    /// Free an object immediately
    pub fn free_object(&mut self, id: ObjectId) -> UmbraResult<()> {
        if let Some(obj) = self.allocated_objects.remove(&id) {
            self.stats.total_freed += obj.size;
            self.stats.current_usage -= obj.size;
            self.roots.remove(&id);
            Ok(())
        } else {
            Err(UmbraError::Memory(format!("Object {} not found", id)))
        }
    }

    /// Run garbage collection
    pub fn collect_garbage(&mut self) -> UmbraResult<usize> {
        self.stats.gc_runs += 1;
        let initial_count = self.allocated_objects.len();

        // Mark phase: mark all reachable objects
        self.mark_reachable_objects();

        // Sweep phase: free unmarked objects
        let freed_count = self.sweep_unmarked_objects();

        self.stats.objects_collected += freed_count;

        Ok(freed_count)
    }

    /// Mark all reachable objects starting from roots
    fn mark_reachable_objects(&mut self) {
        // Clear all marks
        for obj in self.allocated_objects.values_mut() {
            obj.marked = false;
        }

        // Mark from roots
        for &root_id in &self.roots.clone() {
            self.mark_object(root_id);
        }
    }

    /// Mark an object and all its references
    fn mark_object(&mut self, id: ObjectId) {
        if let Some(obj) = self.allocated_objects.get_mut(&id) {
            if obj.marked {
                return; // Already marked
            }
            
            obj.marked = true;
            let references = obj.references.clone();
            
            // Mark all referenced objects
            for &ref_id in &references {
                self.mark_object(ref_id);
            }
        }
    }

    /// Sweep unmarked objects
    fn sweep_unmarked_objects(&mut self) -> usize {
        let mut to_remove = Vec::new();
        
        for (&id, obj) in &self.allocated_objects {
            if !obj.marked {
                to_remove.push(id);
            }
        }
        
        let freed_count = to_remove.len();
        
        for id in to_remove {
            if let Some(obj) = self.allocated_objects.remove(&id) {
                self.stats.total_freed += obj.size;
                self.stats.current_usage -= obj.size;
            }
        }
        
        freed_count
    }

    /// Get object by ID
    pub fn get_object(&self, id: ObjectId) -> Option<&GcObject> {
        self.allocated_objects.get(&id)
    }

    /// Get mutable object by ID
    pub fn get_object_mut(&mut self, id: ObjectId) -> Option<&mut GcObject> {
        self.allocated_objects.get_mut(&id)
    }

    /// Get memory statistics
    pub fn get_stats(&self) -> &MemoryStats {
        &self.stats
    }

    /// Configure garbage collector
    pub fn configure(&mut self, config: GcConfig) {
        self.config = config;
    }

    /// Manual memory allocation (for malloc-like functionality)
    pub fn malloc(&mut self, size: usize) -> UmbraResult<*mut u8> {
        let layout = std::alloc::Layout::from_size_align(size, 8)
            .map_err(|e| UmbraError::Memory(format!("Invalid layout: {}", e)))?;
        
        let ptr = unsafe { std::alloc::alloc(layout) };
        
        if ptr.is_null() {
            Err(UmbraError::Memory("Failed to allocate memory".to_string()))
        } else {
            self.stats.total_allocated += size;
            self.stats.current_usage += size;
            Ok(ptr)
        }
    }

    /// Manual memory deallocation (for free-like functionality)
    pub fn free(&mut self, ptr: *mut u8, size: usize) -> UmbraResult<()> {
        if ptr.is_null() {
            return Err(UmbraError::Memory("Cannot free null pointer".to_string()));
        }

        let layout = std::alloc::Layout::from_size_align(size, 8)
            .map_err(|e| UmbraError::Memory(format!("Invalid layout: {}", e)))?;
        
        unsafe {
            std::alloc::dealloc(ptr, layout);
        }
        
        self.stats.total_freed += size;
        self.stats.current_usage -= size;
        
        Ok(())
    }

    /// Force garbage collection
    pub fn force_gc(&mut self) -> UmbraResult<usize> {
        self.collect_garbage()
    }

    /// Get current memory usage
    pub fn current_usage(&self) -> usize {
        self.stats.current_usage
    }

    /// Get peak memory usage
    pub fn peak_usage(&self) -> usize {
        self.stats.peak_usage
    }

    /// Check if an object exists
    pub fn object_exists(&self, id: ObjectId) -> bool {
        self.allocated_objects.contains_key(&id)
    }

    /// Get all object IDs
    pub fn get_all_objects(&self) -> Vec<ObjectId> {
        self.allocated_objects.keys().cloned().collect()
    }

    /// Get objects by type
    pub fn get_objects_by_type(&self, type_name: &str) -> Vec<ObjectId> {
        self.allocated_objects
            .iter()
            .filter(|(_, obj)| obj.data.type_name() == type_name)
            .map(|(&id, _)| id)
            .collect()
    }
}

/// Example GC data implementation for strings
#[derive(Debug)]
pub struct GcString {
    pub value: String,
}

impl GcData for GcString {
    fn get_references(&self) -> Vec<ObjectId> {
        Vec::new() // Strings don't reference other objects
    }

    fn size(&self) -> usize {
        std::mem::size_of::<Self>() + self.value.capacity()
    }

    fn type_name(&self) -> &'static str {
        "String"
    }
}

/// Example GC data implementation for lists
#[derive(Debug)]
pub struct GcList {
    pub elements: Vec<ObjectId>,
}

impl GcData for GcList {
    fn get_references(&self) -> Vec<ObjectId> {
        self.elements.clone()
    }

    fn size(&self) -> usize {
        std::mem::size_of::<Self>() + self.elements.capacity() * std::mem::size_of::<ObjectId>()
    }

    fn type_name(&self) -> &'static str {
        "List"
    }
}

/// Example GC data implementation for maps
#[derive(Debug)]
pub struct GcMap {
    pub entries: HashMap<String, ObjectId>,
}

impl GcData for GcMap {
    fn get_references(&self) -> Vec<ObjectId> {
        self.entries.values().cloned().collect()
    }

    fn size(&self) -> usize {
        std::mem::size_of::<Self>() + 
        self.entries.capacity() * (std::mem::size_of::<String>() + std::mem::size_of::<ObjectId>()) +
        self.entries.keys().map(|k| k.capacity()).sum::<usize>()
    }

    fn type_name(&self) -> &'static str {
        "Map"
    }
}

impl Drop for MemoryManager {
    fn drop(&mut self) {
        // Clean up all allocated objects
        self.allocated_objects.clear();
    }
}
