/// Runtime exception handling system for Umbra
/// 
/// This module provides the runtime infrastructure for exception handling,
/// including exception propagation, stack unwinding, and exception objects.

use crate::error::{UmbraError, UmbraResult, StackFrame};
use std::collections::HashMap;
use std::any::Any;
use std::fmt;

/// Runtime exception that can be thrown and caught
#[derive(Debug, Clone)]
pub struct RuntimeException {
    pub exception_type: String,
    pub message: String,
    pub data: Option<String>, // Changed from Box<dyn Any> to String for Clone compatibility
    pub stack_trace: Vec<StackFrame>,
    pub line: usize,
    pub column: usize,
}

impl RuntimeException {
    pub fn new(
        exception_type: String,
        message: String,
        line: usize,
        column: usize,
    ) -> Self {
        Self {
            exception_type,
            message,
            data: None,
            stack_trace: Vec::new(),
            line,
            column,
        }
    }

    pub fn with_data<T: Any + Send + Sync + std::fmt::Debug>(mut self, data: T) -> Self {
        self.data = Some(format!("{:?}", data));
        self
    }

    pub fn add_stack_frame(&mut self, frame: StackFrame) {
        self.stack_trace.push(frame);
    }

    pub fn to_umbra_error(self) -> UmbraError {
        UmbraError::Exception {
            exception_type: self.exception_type,
            message: self.message,
            stack_trace: self.stack_trace,
            line: self.line,
            column: self.column,
        }
    }
}

impl fmt::Display for RuntimeException {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}: {}", self.exception_type, self.message)
    }
}

/// Exception handler that manages try/catch blocks
#[derive(Debug)]
pub struct ExceptionHandler {
    /// Stack of active try blocks
    try_stack: Vec<TryBlock>,
    /// Current exception being handled
    current_exception: Option<RuntimeException>,
    /// Exception type registry
    exception_types: HashMap<String, ExceptionTypeInfo>,
}

#[derive(Debug)]
struct TryBlock {
    /// Catch handlers for this try block
    catch_handlers: Vec<CatchHandler>,
    /// Finally block if present
    finally_block: Option<FinallyBlock>,
    /// Stack depth when try block was entered
    stack_depth: usize,
}

#[derive(Debug)]
struct CatchHandler {
    /// Exception type this handler catches (None for catch-all)
    exception_type: Option<String>,
    /// Variable name to bind the exception to
    variable_name: Option<String>,
    /// Handler function
    handler: fn(&RuntimeException) -> UmbraResult<()>,
}

#[derive(Debug)]
struct FinallyBlock {
    /// Finally handler function
    handler: fn() -> UmbraResult<()>,
}

#[derive(Debug, Clone)]
pub struct ExceptionTypeInfo {
    pub name: String,
    pub parent: Option<String>,
    pub fields: HashMap<String, String>, // field_name -> field_type
}

impl ExceptionHandler {
    pub fn new() -> Self {
        let mut handler = Self {
            try_stack: Vec::new(),
            current_exception: None,
            exception_types: HashMap::new(),
        };

        // Register built-in exception types
        handler.register_builtin_exceptions();
        handler
    }

    fn register_builtin_exceptions(&mut self) {
        // Base exception type
        self.exception_types.insert("Exception".to_string(), ExceptionTypeInfo {
            name: "Exception".to_string(),
            parent: None,
            fields: HashMap::from([
                ("message".to_string(), "String".to_string()),
                ("stack_trace".to_string(), "List[StackFrame]".to_string()),
            ]),
        });

        // Runtime exceptions
        self.exception_types.insert("RuntimeError".to_string(), ExceptionTypeInfo {
            name: "RuntimeError".to_string(),
            parent: Some("Exception".to_string()),
            fields: HashMap::new(),
        });

        // Type exceptions
        self.exception_types.insert("TypeError".to_string(), ExceptionTypeInfo {
            name: "TypeError".to_string(),
            parent: Some("Exception".to_string()),
            fields: HashMap::from([
                ("expected_type".to_string(), "String".to_string()),
                ("actual_type".to_string(), "String".to_string()),
            ]),
        });

        // Index exceptions
        self.exception_types.insert("IndexError".to_string(), ExceptionTypeInfo {
            name: "IndexError".to_string(),
            parent: Some("Exception".to_string()),
            fields: HashMap::from([
                ("index".to_string(), "Integer".to_string()),
                ("length".to_string(), "Integer".to_string()),
            ]),
        });

        // Key exceptions
        self.exception_types.insert("KeyError".to_string(), ExceptionTypeInfo {
            name: "KeyError".to_string(),
            parent: Some("Exception".to_string()),
            fields: HashMap::from([
                ("key".to_string(), "String".to_string()),
            ]),
        });

        // I/O exceptions
        self.exception_types.insert("IOError".to_string(), ExceptionTypeInfo {
            name: "IOError".to_string(),
            parent: Some("Exception".to_string()),
            fields: HashMap::from([
                ("filename".to_string(), "String".to_string()),
                ("operation".to_string(), "String".to_string()),
            ]),
        });

        // Division by zero
        self.exception_types.insert("DivisionByZeroError".to_string(), ExceptionTypeInfo {
            name: "DivisionByZeroError".to_string(),
            parent: Some("RuntimeError".to_string()),
            fields: HashMap::new(),
        });

        // Null pointer exceptions
        self.exception_types.insert("NullPointerError".to_string(), ExceptionTypeInfo {
            name: "NullPointerError".to_string(),
            parent: Some("RuntimeError".to_string()),
            fields: HashMap::new(),
        });
    }

    /// Register a custom exception type
    pub fn register_exception_type(&mut self, info: ExceptionTypeInfo) {
        self.exception_types.insert(info.name.clone(), info);
    }

    /// Enter a try block
    pub fn enter_try_block(&mut self) {
        self.try_stack.push(TryBlock {
            catch_handlers: Vec::new(),
            finally_block: None,
            stack_depth: self.try_stack.len(),
        });
    }

    /// Add a catch handler to the current try block
    pub fn add_catch_handler(
        &mut self,
        exception_type: Option<String>,
        variable_name: Option<String>,
        handler: fn(&RuntimeException) -> UmbraResult<()>,
    ) -> UmbraResult<()> {
        if let Some(try_block) = self.try_stack.last_mut() {
            try_block.catch_handlers.push(CatchHandler {
                exception_type,
                variable_name,
                handler,
            });
            Ok(())
        } else {
            Err(UmbraError::Runtime("No active try block for catch handler".to_string()))
        }
    }

    /// Add a finally block to the current try block
    pub fn add_finally_block(&mut self, handler: fn() -> UmbraResult<()>) -> UmbraResult<()> {
        if let Some(try_block) = self.try_stack.last_mut() {
            try_block.finally_block = Some(FinallyBlock { handler });
            Ok(())
        } else {
            Err(UmbraError::Runtime("No active try block for finally handler".to_string()))
        }
    }

    /// Exit the current try block
    pub fn exit_try_block(&mut self) -> UmbraResult<()> {
        if let Some(try_block) = self.try_stack.pop() {
            // Execute finally block if present
            if let Some(finally_block) = try_block.finally_block {
                (finally_block.handler)()?;
            }
            Ok(())
        } else {
            Err(UmbraError::Runtime("No active try block to exit".to_string()))
        }
    }

    /// Throw an exception
    pub fn throw_exception(&mut self, mut exception: RuntimeException) -> UmbraResult<()> {
        // Add current stack frame
        exception.add_stack_frame(StackFrame {
            function_name: "current_function".to_string(), // TODO: Get actual function name
            file_name: "current_file.umbra".to_string(),   // TODO: Get actual file name
            line: exception.line,
            column: exception.column,
        });

        // Try to handle the exception
        self.handle_exception(exception)
    }

    /// Handle an exception by finding appropriate catch handler
    fn handle_exception(&mut self, exception: RuntimeException) -> UmbraResult<()> {
        // Walk up the try stack to find a matching handler
        while let Some(try_block) = self.try_stack.last() {
            for catch_handler in &try_block.catch_handlers {
                if self.exception_matches(&exception, &catch_handler.exception_type) {
                    // Found a matching handler
                    self.current_exception = Some(exception.clone());
                    let result = (catch_handler.handler)(&exception);
                    self.current_exception = None;
                    return result;
                }
            }

            // No handler found in this try block, continue unwinding
            let try_block = self.try_stack.pop().unwrap();
            if let Some(finally_block) = try_block.finally_block {
                (finally_block.handler)()?;
            }
        }

        // No handler found, convert to UmbraError
        Err(exception.to_umbra_error())
    }

    /// Check if an exception matches a catch handler type
    fn exception_matches(&self, exception: &RuntimeException, handler_type: &Option<String>) -> bool {
        match handler_type {
            None => true, // Catch-all handler
            Some(handler_type) => {
                // Check if exception type matches or is a subtype
                self.is_exception_subtype(&exception.exception_type, handler_type)
            }
        }
    }

    /// Check if one exception type is a subtype of another
    fn is_exception_subtype(&self, exception_type: &str, target_type: &str) -> bool {
        if exception_type == target_type {
            return true;
        }

        if let Some(type_info) = self.exception_types.get(exception_type) {
            if let Some(parent) = &type_info.parent {
                return self.is_exception_subtype(parent, target_type);
            }
        }

        false
    }

    /// Get the current exception being handled
    pub fn current_exception(&self) -> Option<&RuntimeException> {
        self.current_exception.as_ref()
    }

    /// Create a built-in exception
    pub fn create_builtin_exception(
        &self,
        exception_type: &str,
        message: String,
        line: usize,
        column: usize,
    ) -> RuntimeException {
        RuntimeException::new(exception_type.to_string(), message, line, column)
    }
}

impl Default for ExceptionHandler {
    fn default() -> Self {
        Self::new()
    }
}
