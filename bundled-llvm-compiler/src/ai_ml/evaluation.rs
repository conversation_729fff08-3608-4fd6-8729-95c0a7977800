use crate::error::{<PERSON><PERSON><PERSON><PERSON><PERSON>, UmbraResult};
use crate::ai_ml::{Dataset, Model};
use std::collections::HashMap;
use std::fs::File;
use std::io::Write;
use std::process::Command;
use serde::{Deserialize, Serialize};
use std::time::{Duration, Instant};

/// Advanced model evaluation manager with comprehensive metrics
pub struct EvaluationManager {
    pub working_dir: String,
    pub python_executable: String,
    pub evaluation_cache: HashMap<String, EvaluationResults>,
}

/// Comprehensive evaluation results
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EvaluationResults {
    pub model_name: String,
    pub dataset_name: String,
    pub task_type: TaskType,
    pub metrics: HashMap<String, f64>,
    pub confusion_matrix: Option<Vec<Vec<usize>>>,
    pub classification_report: Option<ClassificationReport>,
    pub regression_metrics: Option<RegressionMetrics>,
    pub cross_validation_scores: Option<CrossValidationResults>,
    pub feature_importance: Option<Vec<FeatureImportance>>,
    pub prediction_distribution: Option<PredictionDistribution>,
    pub evaluation_time: Duration,
    pub model_size_mb: f64,
    pub inference_time_ms: f64,
}

/// Task type for evaluation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TaskType {
    BinaryClassification,
    MultiClassClassification,
    Regression,
    MultiLabelClassification,
}

/// Classification report with per-class metrics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ClassificationReport {
    pub classes: Vec<String>,
    pub precision: Vec<f64>,
    pub recall: Vec<f64>,
    pub f1_score: Vec<f64>,
    pub support: Vec<usize>,
    pub macro_avg: ClassMetrics,
    pub weighted_avg: ClassMetrics,
}

/// Individual class metrics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ClassMetrics {
    pub precision: f64,
    pub recall: f64,
    pub f1_score: f64,
    pub support: usize,
}

/// Regression-specific metrics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RegressionMetrics {
    pub mse: f64,
    pub rmse: f64,
    pub mae: f64,
    pub r2: f64,
    pub adjusted_r2: f64,
    pub mape: f64, // Mean Absolute Percentage Error
    pub residual_stats: ResidualStats,
}

/// Residual analysis statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResidualStats {
    pub mean: f64,
    pub std_dev: f64,
    pub skewness: f64,
    pub kurtosis: f64,
    pub durbin_watson: f64, // Test for autocorrelation
}

/// Cross-validation results
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CrossValidationResults {
    pub scores: Vec<f64>,
    pub mean_score: f64,
    pub std_score: f64,
    pub confidence_interval: (f64, f64),
    pub fold_count: usize,
}

/// Feature importance information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FeatureImportance {
    pub feature_name: String,
    pub importance: f64,
    pub rank: usize,
}

/// Prediction distribution analysis
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PredictionDistribution {
    pub bins: Vec<f64>,
    pub counts: Vec<usize>,
    pub mean: f64,
    pub std_dev: f64,
    pub percentiles: HashMap<String, f64>, // "25", "50", "75", "90", "95", "99"
}

impl EvaluationManager {
    pub fn new(working_dir: String) -> Self {
        Self {
            working_dir,
            python_executable: "python3".to_string(),
            evaluation_cache: HashMap::new(),
        }
    }

    /// Comprehensive model evaluation with advanced metrics
    pub fn evaluate_model_comprehensive(
        &mut self,
        model: &Model,
        dataset: &Dataset,
        model_path: &str,
        cross_validate: bool,
        cv_folds: usize,
    ) -> UmbraResult<EvaluationResults> {
        let start_time = Instant::now();

        if !model.trained {
            return Err(UmbraError::Runtime("Model must be trained before evaluation".to_string()));
        }

        // Check cache first
        let cache_key = format!("{}_{}", model.name, dataset.name);
        if let Some(cached_result) = self.evaluation_cache.get(&cache_key) {
            return Ok(cached_result.clone());
        }

        // Save dataset to temporary file
        let dataset_path = format!("{}/evaluation_data.csv", self.working_dir);
        dataset.to_csv(&dataset_path)?;

        // Determine task type
        let task_type = self.determine_task_type(dataset)?;

        // Generate comprehensive evaluation script
        let script_content = self.generate_comprehensive_evaluation_script(
            model, &dataset_path, model_path, &task_type, cross_validate, cv_folds
        )?;

        let script_path = format!("{}/comprehensive_evaluate.py", self.working_dir);
        let mut script_file = File::create(&script_path)
            .map_err(|e| UmbraError::Runtime(format!("Failed to create evaluation script: {}", e)))?;

        script_file.write_all(script_content.as_bytes())
            .map_err(|e| UmbraError::Runtime(format!("Failed to write evaluation script: {}", e)))?;

        // Execute evaluation script
        println!("📊 Running comprehensive model evaluation...");
        let output = Command::new(&self.python_executable)
            .arg(&script_path)
            .current_dir(&self.working_dir)
            .output()
            .map_err(|e| UmbraError::Runtime(format!("Failed to execute evaluation script: {}", e)))?;

        if !output.status.success() {
            let error_msg = String::from_utf8_lossy(&output.stderr);
            return Err(UmbraError::Runtime(format!("Evaluation script failed: {}", error_msg)));
        }

        // Parse results from JSON output
        let results_path = format!("{}/evaluation_results.json", self.working_dir);
        let results_file = File::open(&results_path)
            .map_err(|e| UmbraError::Runtime(format!("Failed to open results file: {}", e)))?;

        let mut results: EvaluationResults = serde_json::from_reader(results_file)
            .map_err(|e| UmbraError::Runtime(format!("Failed to parse evaluation results: {}", e)))?;

        results.evaluation_time = start_time.elapsed();
        results.model_name = model.name.clone();
        results.dataset_name = dataset.name.clone();

        // Cache results
        self.evaluation_cache.insert(cache_key, results.clone());

        Ok(results)
    }

    /// Determine task type from dataset
    fn determine_task_type(&self, dataset: &Dataset) -> UmbraResult<TaskType> {
        if let Some(ref labels) = dataset.labels {
            // Convert to strings for proper hashing and comparison
            let unique_labels: std::collections::HashSet<String> = labels.iter()
                .map(|&x| x.to_string())
                .collect();
            let num_unique = unique_labels.len();

            if num_unique == 2 {
                Ok(TaskType::BinaryClassification)
            } else if num_unique < 100 && num_unique > 2 {
                Ok(TaskType::MultiClassClassification)
            } else {
                Ok(TaskType::Regression)
            }
        } else {
            Ok(TaskType::Regression)
        }
    }

    /// Evaluate a trained model on a dataset
    pub fn evaluate_model(
        &self,
        model: &Model,
        dataset: &Dataset,
        model_path: &str,
        metrics: &[String],
    ) -> UmbraResult<EvaluationResults> {
        if !model.trained {
            return Err(UmbraError::Runtime("Model must be trained before evaluation".to_string()));
        }

        // Save dataset to temporary file
        let dataset_path = format!("{}/evaluation_data.csv", self.working_dir);
        dataset.to_csv(&dataset_path)?;

        // Generate evaluation script
        let script_content = self.generate_evaluation_script(model, &dataset_path, model_path, metrics)?;
        let script_path = format!("{}/evaluate_model.py", self.working_dir);
        
        let mut script_file = File::create(&script_path)
            .map_err(|e| UmbraError::Runtime(format!("Failed to create evaluation script: {}", e)))?;
        
        script_file.write_all(script_content.as_bytes())
            .map_err(|e| UmbraError::Runtime(format!("Failed to write evaluation script: {}", e)))?;

        // Execute evaluation script
        println!("📊 Evaluating model...");
        let output = Command::new(&self.python_executable)
            .arg(&script_path)
            .current_dir(&self.working_dir)
            .output()
            .map_err(|e| UmbraError::Runtime(format!("Failed to execute evaluation script: {}", e)))?;

        if !output.status.success() {
            let stderr = String::from_utf8_lossy(&output.stderr);
            return Err(UmbraError::Runtime(format!("Evaluation failed: {}", stderr)));
        }

        // Parse evaluation results
        let stdout = String::from_utf8_lossy(&output.stdout);
        self.parse_evaluation_results(&stdout, metrics)
    }

    fn generate_evaluation_script(
        &self,
        model: &Model,
        dataset_path: &str,
        model_path: &str,
        metrics: &[String],
    ) -> UmbraResult<String> {
        match model.model_type {
            crate::ai_ml::ModelType::NeuralNetwork => {
                self.generate_neural_network_evaluation_script(dataset_path, model_path, metrics)
            }
            crate::ai_ml::ModelType::LinearRegression => {
                self.generate_linear_regression_evaluation_script(dataset_path, model_path, metrics)
            }
            _ => Err(UmbraError::Runtime(format!("Evaluation not implemented for {:?}", model.model_type))),
        }
    }

    fn generate_neural_network_evaluation_script(
        &self,
        dataset_path: &str,
        model_path: &str,
        metrics: &[String],
    ) -> UmbraResult<String> {
        let metrics_imports = if metrics.contains(&"precision".to_string()) || 
                                metrics.contains(&"recall".to_string()) || 
                                metrics.contains(&"f1".to_string()) {
            "from sklearn.metrics import precision_score, recall_score, f1_score, classification_report"
        } else {
            ""
        };

        let script = format!(r#"
import numpy as np
import pandas as pd
import tensorflow as tf
from tensorflow import keras
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import accuracy_score, mean_squared_error, mean_absolute_error
{}
import json

# Load dataset
data = pd.read_csv('{}')
X = data.iloc[:, :-1].values
y = data.iloc[:, -1].values

# Preprocess data (same as training)
scaler = StandardScaler()
X = scaler.fit_transform(X)

# Load model
model = keras.models.load_model('{}')

# Make predictions
predictions = model.predict(X)
if predictions.shape[1] == 1:
    y_pred = (predictions > 0.5).astype(int).flatten()
else:
    y_pred = np.argmax(predictions, axis=1)

# Calculate metrics
results = {{}}
"#, metrics_imports, dataset_path, model_path);

        let mut metric_calculations = String::new();
        for metric in metrics {
            match metric.as_str() {
                "accuracy" => {
                    metric_calculations.push_str("results['accuracy'] = accuracy_score(y, y_pred)\n");
                }
                "precision" => {
                    metric_calculations.push_str("results['precision'] = precision_score(y, y_pred, average='weighted')\n");
                }
                "recall" => {
                    metric_calculations.push_str("results['recall'] = recall_score(y, y_pred, average='weighted')\n");
                }
                "f1" => {
                    metric_calculations.push_str("results['f1'] = f1_score(y, y_pred, average='weighted')\n");
                }
                "loss" => {
                    metric_calculations.push_str("loss = model.evaluate(X, y, verbose=0)\nresults['loss'] = loss[0] if isinstance(loss, list) else loss\n");
                }
                _ => {}
            }
        }

        let final_script = format!("{}{}
# Print results
for metric, value in results.items():
    print(f'{{metric}}: {{value:.4f}}')

# Save results
with open('evaluation_results.json', 'w') as f:
    json.dump(results, f, indent=2)
", script, metric_calculations);

        Ok(final_script)
    }

    fn generate_linear_regression_evaluation_script(
        &self,
        dataset_path: &str,
        model_path: &str,
        metrics: &[String],
    ) -> UmbraResult<String> {
        let script = format!(r#"
import numpy as np
import pandas as pd
import joblib
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import json

# Load dataset
data = pd.read_csv('{}')
X = data.iloc[:, :-1].values
y = data.iloc[:, -1].values

# Load model and scaler
model = joblib.load('{}')
scaler = joblib.load('{}_scaler.pkl')

# Preprocess data
X = scaler.transform(X)

# Make predictions
y_pred = model.predict(X)

# Calculate metrics
results = {{}}
"#, dataset_path, model_path, model_path);

        let mut metric_calculations = String::new();
        for metric in metrics {
            match metric.as_str() {
                "mse" => {
                    metric_calculations.push_str("results['mse'] = mean_squared_error(y, y_pred)\n");
                }
                "mae" => {
                    metric_calculations.push_str("results['mae'] = mean_absolute_error(y, y_pred)\n");
                }
                "r2" => {
                    metric_calculations.push_str("results['r2'] = r2_score(y, y_pred)\n");
                }
                "rmse" => {
                    metric_calculations.push_str("results['rmse'] = np.sqrt(mean_squared_error(y, y_pred))\n");
                }
                _ => {}
            }
        }

        let final_script = format!("{}{}
# Print results
for metric, value in results.items():
    print(f'{{metric}}: {{value:.4f}}')

# Save results
with open('evaluation_results.json', 'w') as f:
    json.dump(results, f, indent=2)
", script, metric_calculations);

        Ok(final_script)
    }

    fn parse_evaluation_results(&self, output: &str, metrics: &[String]) -> UmbraResult<EvaluationResults> {
        let mut results = HashMap::new();
        
        // Parse output for metric values
        for line in output.lines() {
            for metric in metrics {
                if line.starts_with(&format!("{}:", metric)) {
                    if let Some(value_str) = line.split(':').nth(1) {
                        if let Ok(value) = value_str.trim().parse::<f64>() {
                            results.insert(metric.clone(), value);
                        }
                    }
                }
            }
        }

        // Try to load results from JSON file if available
        let results_path = format!("{}/evaluation_results.json", self.working_dir);
        if std::path::Path::new(&results_path).exists() {
            if let Ok(file) = File::open(&results_path) {
                if let Ok(json_results) = serde_json::from_reader::<_, HashMap<String, f64>>(file) {
                    results.extend(json_results);
                }
            }
        }

        Ok(EvaluationResults {
            model_name: "test_model".to_string(),
            dataset_name: "test_dataset".to_string(),
            task_type: TaskType::BinaryClassification,
            metrics: results,
            confusion_matrix: None, // Could be implemented for classification
            classification_report: None,
            regression_metrics: None,
            cross_validation_scores: None,
            feature_importance: None, // Could be implemented for tree-based models
            prediction_distribution: None,
            evaluation_time: Duration::from_secs(0),
            model_size_mb: 0.0,
            inference_time_ms: 0.0,
        })
    }

    /// Generate evaluation report
    pub fn generate_report(
        &self,
        model: &Model,
        results: &EvaluationResults,
        output_path: &str,
    ) -> UmbraResult<()> {
        let mut report = String::new();
        
        report.push_str(&format!("# Model Evaluation Report\n\n"));
        report.push_str(&format!("**Model Name:** {}\n", model.name));
        report.push_str(&format!("**Model Type:** {:?}\n", model.model_type));
        report.push_str(&format!("**Architecture:** {} layers\n", model.architecture.layers.len()));
        report.push_str(&format!("\n## Evaluation Metrics\n\n"));
        
        for (metric, value) in &results.metrics {
            report.push_str(&format!("- **{}:** {:.4}\n", metric, value));
        }
        
        if let Some(ref confusion_matrix) = results.confusion_matrix {
            report.push_str(&format!("\n## Confusion Matrix\n\n"));
            report.push_str(&format!("```\n{:?}\n```\n", confusion_matrix));
        }
        
        report.push_str(&format!("\n## Model Summary\n\n"));
        report.push_str(&format!("```\n{}\n```\n", model.summary()));

        let mut file = File::create(output_path)
            .map_err(|e| UmbraError::Runtime(format!("Failed to create report file: {}", e)))?;
        
        file.write_all(report.as_bytes())
            .map_err(|e| UmbraError::Runtime(format!("Failed to write report: {}", e)))?;

        Ok(())
    }

    /// Cross-validation evaluation
    pub fn cross_validate(
        &self,
        model: &Model,
        dataset: &Dataset,
        folds: usize,
        metrics: &[String],
    ) -> UmbraResult<CrossValidationResults> {
        if folds < 2 {
            return Err(UmbraError::Runtime("Number of folds must be at least 2".to_string()));
        }

        let mut fold_results = Vec::new();
        let fold_size = dataset.shape.0 / folds;

        for fold in 0..folds {
            let start_idx = fold * fold_size;
            let end_idx = if fold == folds - 1 { dataset.shape.0 } else { (fold + 1) * fold_size };

            // Create train/validation split for this fold
            let mut train_data = Vec::new();
            let mut train_labels = Vec::new();
            let mut val_data = Vec::new();
            let mut val_labels = Vec::new();

            for (i, row) in dataset.data.iter().enumerate() {
                if i >= start_idx && i < end_idx {
                    val_data.push(row.clone());
                    if let Some(ref labels) = dataset.labels {
                        val_labels.push(labels[i]);
                    }
                } else {
                    train_data.push(row.clone());
                    if let Some(ref labels) = dataset.labels {
                        train_labels.push(labels[i]);
                    }
                }
            }

            // Create validation dataset
            let val_dataset = Dataset {
                name: format!("{}_fold_{}", dataset.name, fold),
                data: val_data,
                labels: if val_labels.is_empty() { None } else { Some(val_labels) },
                feature_names: dataset.feature_names.clone(),
                shape: (end_idx - start_idx, dataset.shape.1),
            };

            // Evaluate on this fold (simplified - in practice you'd retrain)
            let model_path = format!("{}/model_fold_{}.h5", self.working_dir, fold);
            let results = self.evaluate_model(model, &val_dataset, &model_path, metrics)?;
            fold_results.push(results);
        }

        // Calculate mean and std for each metric
        let mut mean_metrics = HashMap::new();
        let mut std_metrics = HashMap::new();

        for metric in metrics {
            let values: Vec<f64> = fold_results.iter()
                .filter_map(|r| r.metrics.get(metric))
                .copied()
                .collect();

            if !values.is_empty() {
                let mean = values.iter().sum::<f64>() / values.len() as f64;
                let variance = values.iter().map(|x| (x - mean).powi(2)).sum::<f64>() / values.len() as f64;
                let std = variance.sqrt();

                mean_metrics.insert(metric.clone(), mean);
                std_metrics.insert(metric.clone(), std);
            }
        }

        Ok(CrossValidationResults {
            scores: fold_results.iter().flat_map(|r| r.metrics.values().copied()).collect(),
            mean_score: mean_metrics.values().next().copied().unwrap_or(0.0),
            std_score: std_metrics.values().next().copied().unwrap_or(0.0),
            confidence_interval: (0.0, 1.0), // Simplified
            fold_count: folds,
        })
    }

    /// Generate comprehensive evaluation script with advanced metrics
    fn generate_comprehensive_evaluation_script(
        &self,
        model: &Model,
        dataset_path: &str,
        model_path: &str,
        task_type: &TaskType,
        cross_validate: bool,
        cv_folds: usize,
    ) -> UmbraResult<String> {
        let script = format!(r#"
import numpy as np
import pandas as pd
import json
import time
import os
from sklearn.metrics import *
from sklearn.model_selection import cross_val_score, StratifiedKFold, KFold
from sklearn.preprocessing import LabelEncoder
import joblib
try:
    import tensorflow as tf
    TF_AVAILABLE = True
except ImportError:
    TF_AVAILABLE = False
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

print("🔄 Loading model and data for comprehensive evaluation...")

# Load dataset
df = pd.read_csv('{dataset_path}')
X = df.iloc[:, :-1].values
y = df.iloc[:, -1].values

print(f"📊 Dataset shape: {{X.shape}}")
print(f"📊 Task type: {task_type:?}")

# Load model
try:
    if '{model_path}'.endswith('.h5') or '{model_path}'.endswith('.keras'):
        if TF_AVAILABLE:
            model = tf.keras.models.load_model('{model_path}')
            model_type = 'tensorflow'
        else:
            raise ImportError("TensorFlow not available")
    else:
        model = joblib.load('{model_path}')
        model_type = 'sklearn'
except Exception as e:
    print(f"❌ Error loading model: {{e}}")
    exit(1)

# Determine if classification or regression
is_classification = '{task_type}' != 'Regression'

if is_classification:
    # Encode labels for classification
    label_encoder = LabelEncoder()
    y_encoded = label_encoder.fit_transform(y)
    unique_classes = len(label_encoder.classes_)
    class_names = label_encoder.classes_.tolist()
else:
    y_encoded = y
    unique_classes = 1
    class_names = []

print(f"📊 Number of classes/targets: {{unique_classes}}")

# Make predictions
start_time = time.time()
if model_type == 'tensorflow':
    if is_classification and unique_classes > 2:
        y_pred_proba = model.predict(X, verbose=0)
        y_pred = np.argmax(y_pred_proba, axis=1)
    else:
        y_pred = model.predict(X, verbose=0)
        if is_classification:
            y_pred = (y_pred > 0.5).astype(int).flatten()
        else:
            y_pred = y_pred.flatten()
else:
    y_pred = model.predict(X)
    if is_classification and hasattr(model, 'predict_proba'):
        y_pred_proba = model.predict_proba(X)

inference_time = (time.time() - start_time) * 1000 / len(X)  # ms per sample

print(f"⚡ Average inference time: {{inference_time:.2f}} ms per sample")

# Initialize results dictionary
results = {{
    'task_type': '{task_type}',
    'metrics': {{}},
    'evaluation_time': 0,
    'model_size_mb': 0,
    'inference_time_ms': inference_time
}}

# Calculate model size
try:
    model_size = os.path.getsize('{model_path}') / (1024 * 1024)  # MB
    results['model_size_mb'] = model_size
    print(f"💾 Model size: {{model_size:.2f}} MB")
except:
    results['model_size_mb'] = 0

if is_classification:
    print("📊 Computing classification metrics...")

    # Basic classification metrics
    accuracy = accuracy_score(y_encoded, y_pred)
    precision = precision_score(y_encoded, y_pred, average='weighted', zero_division=0)
    recall = recall_score(y_encoded, y_pred, average='weighted', zero_division=0)
    f1 = f1_score(y_encoded, y_pred, average='weighted', zero_division=0)

    results['metrics'].update({{
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1_score': f1,
    }})

    print(f"✅ Accuracy: {{accuracy:.4f}}")
    print(f"✅ Precision: {{precision:.4f}}")
    print(f"✅ Recall: {{recall:.4f}}")
    print(f"✅ F1-Score: {{f1:.4f}}")

else:
    print("📊 Computing regression metrics...")

    # Basic regression metrics
    mse = mean_squared_error(y, y_pred)
    rmse = np.sqrt(mse)
    mae = mean_absolute_error(y, y_pred)
    r2 = r2_score(y, y_pred)

    results['metrics'].update({{
        'mse': mse,
        'rmse': rmse,
        'mae': mae,
        'r2': r2,
    }})

    print(f"✅ MSE: {{mse:.4f}}")
    print(f"✅ RMSE: {{rmse:.4f}}")
    print(f"✅ MAE: {{mae:.4f}}")
    print(f"✅ R²: {{r2:.4f}}")

# Save results
with open('{working_dir}/evaluation_results.json', 'w') as f:
    json.dump(results, f, indent=2)

print("✅ Comprehensive evaluation completed!")
print(f"📁 Results saved to evaluation_results.json")
"#,
            dataset_path = dataset_path,
            model_path = model_path,
            task_type = format!("{:?}", task_type),
            working_dir = self.working_dir,
        );

        Ok(script)
    }
}

// Duplicate struct definitions removed - using the ones defined earlier in the file

impl Default for EvaluationManager {
    fn default() -> Self {
        Self::new("./umbra_evaluation".to_string())
    }
}
