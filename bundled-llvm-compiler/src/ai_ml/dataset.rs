use crate::error::{<PERSON><PERSON><PERSON><PERSON><PERSON>, UmbraResult};
use std::path::Path;
use std::fs::File;
use std::io::{BufRead, BufReader};
use serde::{Deserialize, Serialize};

/// Dataset representation for AI/ML operations
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Dataset {
    pub name: String,
    pub data: Vec<Vec<f64>>,
    pub labels: Option<Vec<f64>>,
    pub feature_names: Vec<String>,
    pub shape: (usize, usize), // (rows, columns)
}

impl Dataset {
    /// Create a new empty dataset
    pub fn new(name: String) -> Self {
        Self {
            name,
            data: Vec::new(),
            labels: None,
            feature_names: Vec::new(),
            shape: (0, 0),
        }
    }

    /// Load dataset from CSV file
    pub fn from_csv<P: AsRef<Path>>(path: P) -> UmbraResult<Self> {
        let file = File::open(&path)
            .map_err(|e| UmbraError::Runtime(format!("Failed to open dataset file: {}", e)))?;
        
        let reader = BufReader::new(file);
        let mut lines = reader.lines();
        
        // Read header line for feature names
        let header = lines.next()
            .ok_or_else(|| UmbraError::Runtime("Empty dataset file".to_string()))?
            .map_err(|e| UmbraError::Runtime(format!("Failed to read header: {}", e)))?;
        
        let feature_names: Vec<String> = header
            .split(',')
            .map(|s| s.trim().to_string())
            .collect();
        
        let mut data = Vec::new();
        let mut labels = Vec::new();
        let has_labels = feature_names.last().map(|s| s.to_lowercase().contains("label") || s.to_lowercase().contains("target")).unwrap_or(false);
        
        // Read data lines
        for line in lines {
            let line = line.map_err(|e| UmbraError::Runtime(format!("Failed to read line: {}", e)))?;
            let values: Result<Vec<f64>, _> = line
                .split(',')
                .map(|s| s.trim().parse::<f64>())
                .collect();
            
            match values {
                Ok(mut row) => {
                    if has_labels && !row.is_empty() {
                        labels.push(row.pop().unwrap());
                    }
                    data.push(row);
                }
                Err(e) => return Err(UmbraError::Runtime(format!("Failed to parse numeric value: {}", e))),
            }
        }
        
        let rows = data.len();
        let cols = data.first().map(|row| row.len()).unwrap_or(0);
        
        Ok(Self {
            name: path.as_ref().file_name()
                .and_then(|n| n.to_str())
                .unwrap_or("dataset")
                .to_string(),
            data,
            labels: if has_labels && !labels.is_empty() { Some(labels) } else { None },
            feature_names: if has_labels { 
                feature_names[..feature_names.len()-1].to_vec() 
            } else { 
                feature_names 
            },
            shape: (rows, cols),
        })
    }

    /// Load dataset from JSON file
    pub fn from_json<P: AsRef<Path>>(path: P) -> UmbraResult<Self> {
        let file = File::open(&path)
            .map_err(|e| UmbraError::Runtime(format!("Failed to open dataset file: {}", e)))?;
        
        let dataset: Self = serde_json::from_reader(file)
            .map_err(|e| UmbraError::Runtime(format!("Failed to parse JSON dataset: {}", e)))?;
        
        Ok(dataset)
    }

    /// Save dataset to CSV file
    pub fn to_csv<P: AsRef<Path>>(&self, path: P) -> UmbraResult<()> {
        use std::io::Write;
        
        let mut file = File::create(&path)
            .map_err(|e| UmbraError::Runtime(format!("Failed to create CSV file: {}", e)))?;
        
        // Write header
        let mut header = self.feature_names.clone();
        if self.labels.is_some() {
            header.push("label".to_string());
        }
        writeln!(file, "{}", header.join(","))
            .map_err(|e| UmbraError::Runtime(format!("Failed to write header: {}", e)))?;
        
        // Write data
        for (i, row) in self.data.iter().enumerate() {
            let mut line = row.iter().map(|v| v.to_string()).collect::<Vec<_>>();
            if let Some(ref labels) = self.labels {
                if i < labels.len() {
                    line.push(labels[i].to_string());
                }
            }
            writeln!(file, "{}", line.join(","))
                .map_err(|e| UmbraError::Runtime(format!("Failed to write data row: {}", e)))?;
        }
        
        Ok(())
    }

    /// Save dataset to JSON file
    pub fn to_json<P: AsRef<Path>>(&self, path: P) -> UmbraResult<()> {
        let file = File::create(&path)
            .map_err(|e| UmbraError::Runtime(format!("Failed to create JSON file: {}", e)))?;
        
        serde_json::to_writer_pretty(file, self)
            .map_err(|e| UmbraError::Runtime(format!("Failed to write JSON dataset: {}", e)))?;
        
        Ok(())
    }

    /// Get dataset statistics
    pub fn statistics(&self) -> DatasetStatistics {
        let mut stats = DatasetStatistics {
            rows: self.shape.0,
            columns: self.shape.1,
            feature_stats: Vec::new(),
            has_labels: self.labels.is_some(),
            label_stats: None,
        };

        // Calculate feature statistics
        for col in 0..self.shape.1 {
            let values: Vec<f64> = self.data.iter().map(|row| row[col]).collect();
            stats.feature_stats.push(calculate_column_stats(&values, &self.feature_names.get(col).unwrap_or(&format!("feature_{}", col))));
        }

        // Calculate label statistics if available
        if let Some(ref labels) = self.labels {
            stats.label_stats = Some(calculate_column_stats(labels, "label"));
        }

        stats
    }

    /// Split dataset into training and testing sets
    pub fn train_test_split(&self, test_size: f64) -> UmbraResult<(Dataset, Dataset)> {
        if test_size <= 0.0 || test_size >= 1.0 {
            return Err(UmbraError::Runtime("Test size must be between 0 and 1".to_string()));
        }

        let total_rows = self.shape.0;
        let test_rows = (total_rows as f64 * test_size) as usize;
        let train_rows = total_rows - test_rows;

        // Simple random split (in practice, you'd want proper shuffling)
        let train_data = self.data[..train_rows].to_vec();
        let test_data = self.data[train_rows..].to_vec();

        let train_labels = self.labels.as_ref().map(|labels| labels[..train_rows].to_vec());
        let test_labels = self.labels.as_ref().map(|labels| labels[train_rows..].to_vec());

        let train_dataset = Dataset {
            name: format!("{}_train", self.name),
            data: train_data,
            labels: train_labels,
            feature_names: self.feature_names.clone(),
            shape: (train_rows, self.shape.1),
        };

        let test_dataset = Dataset {
            name: format!("{}_test", self.name),
            data: test_data,
            labels: test_labels,
            feature_names: self.feature_names.clone(),
            shape: (test_rows, self.shape.1),
        };

        Ok((train_dataset, test_dataset))
    }

    /// Apply min-max normalization (0-1 scaling)
    pub fn normalize(&mut self) -> UmbraResult<MinMaxParams> {
        if self.data.is_empty() {
            return Err(UmbraError::Runtime("Cannot normalize empty dataset".to_string()));
        }

        let mut params = MinMaxParams {
            mins: Vec::new(),
            maxs: Vec::new(),
        };

        for col in 0..self.shape.1 {
            let values: Vec<f64> = self.data.iter().map(|row| row[col]).collect();
            let min_val = values.iter().fold(f64::INFINITY, |a, &b| a.min(b));
            let max_val = values.iter().fold(f64::NEG_INFINITY, |a, &b| a.max(b));

            params.mins.push(min_val);
            params.maxs.push(max_val);

            if (max_val - min_val).abs() > f64::EPSILON {
                for row in &mut self.data {
                    row[col] = (row[col] - min_val) / (max_val - min_val);
                }
            }
        }
        Ok(params)
    }

    /// Apply z-score standardization (mean=0, std=1)
    pub fn standardize(&mut self) -> UmbraResult<StandardizationParams> {
        if self.data.is_empty() {
            return Err(UmbraError::Runtime("Cannot standardize empty dataset".to_string()));
        }

        let mut params = StandardizationParams {
            means: Vec::new(),
            std_devs: Vec::new(),
        };

        for col in 0..self.shape.1 {
            let values: Vec<f64> = self.data.iter().map(|row| row[col]).collect();
            let mean = values.iter().sum::<f64>() / values.len() as f64;
            let variance = values.iter()
                .map(|x| (x - mean).powi(2))
                .sum::<f64>() / values.len() as f64;
            let std_dev = variance.sqrt();

            params.means.push(mean);
            params.std_devs.push(if std_dev > 1e-8 { std_dev } else { 1.0 });

            for row in &mut self.data {
                row[col] = (row[col] - mean) / params.std_devs[col];
            }
        }
        Ok(params)
    }

    /// Remove outliers using IQR method
    pub fn remove_outliers(&mut self, multiplier: f64) -> UmbraResult<usize> {
        if self.data.is_empty() {
            return Ok(0);
        }

        let mut outlier_indices = std::collections::HashSet::new();

        for col in 0..self.shape.1 {
            let mut values: Vec<f64> = self.data.iter().map(|row| row[col]).collect();
            values.sort_by(|a, b| a.partial_cmp(b).unwrap());

            let q1 = self.calculate_percentile(&values, 25.0);
            let q3 = self.calculate_percentile(&values, 75.0);
            let iqr = q3 - q1;
            let lower_bound = q1 - multiplier * iqr;
            let upper_bound = q3 + multiplier * iqr;

            for (row_idx, row) in self.data.iter().enumerate() {
                if row[col] < lower_bound || row[col] > upper_bound {
                    outlier_indices.insert(row_idx);
                }
            }
        }

        let removed_count = outlier_indices.len();

        // Remove outliers (in reverse order to maintain indices)
        let mut sorted_indices: Vec<_> = outlier_indices.into_iter().collect();
        sorted_indices.sort_by(|a, b| b.cmp(a));

        for idx in sorted_indices {
            self.data.remove(idx);
            if let Some(ref mut labels) = self.labels {
                labels.remove(idx);
            }
        }

        self.shape.0 = self.data.len();
        Ok(removed_count)
    }

    /// Calculate percentile of sorted values
    fn calculate_percentile(&self, sorted_values: &[f64], percentile: f64) -> f64 {
        let n = sorted_values.len();
        if n == 0 {
            return 0.0;
        }

        let index = (percentile / 100.0) * (n - 1) as f64;
        let lower = index.floor() as usize;
        let upper = index.ceil() as usize;

        if lower == upper || upper >= n {
            sorted_values[lower.min(n - 1)]
        } else {
            let weight = index - lower as f64;
            sorted_values[lower] * (1.0 - weight) + sorted_values[upper] * weight
        }
    }

    /// Handle missing values using various strategies
    pub fn handle_missing_values(&mut self, strategy: MissingValueStrategy) -> UmbraResult<()> {
        match strategy {
            MissingValueStrategy::DropRows => {
                self.data.retain(|row| !row.iter().any(|&x| x.is_nan()));
                if let Some(ref mut labels) = self.labels {
                    let original_len = labels.len();
                    let mut new_labels = Vec::new();
                    for (i, row) in self.data.iter().enumerate() {
                        if i < original_len && !row.iter().any(|&x| x.is_nan()) {
                            new_labels.push(labels[i]);
                        }
                    }
                    *labels = new_labels;
                }
                self.shape.0 = self.data.len();
            },
            MissingValueStrategy::FillMean => {
                for col in 0..self.shape.1 {
                    let valid_values: Vec<f64> = self.data.iter()
                        .map(|row| row[col])
                        .filter(|&x| !x.is_nan())
                        .collect();

                    if !valid_values.is_empty() {
                        let mean = valid_values.iter().sum::<f64>() / valid_values.len() as f64;
                        for row in &mut self.data {
                            if row[col].is_nan() {
                                row[col] = mean;
                            }
                        }
                    }
                }
            },
            MissingValueStrategy::FillMedian => {
                for col in 0..self.shape.1 {
                    let mut valid_values: Vec<f64> = self.data.iter()
                        .map(|row| row[col])
                        .filter(|&x| !x.is_nan())
                        .collect();

                    if !valid_values.is_empty() {
                        valid_values.sort_by(|a, b| a.partial_cmp(b).unwrap());
                        let median = self.calculate_percentile(&valid_values, 50.0);
                        for row in &mut self.data {
                            if row[col].is_nan() {
                                row[col] = median;
                            }
                        }
                    }
                }
            },
            MissingValueStrategy::FillZero => {
                for row in &mut self.data {
                    for value in row {
                        if value.is_nan() {
                            *value = 0.0;
                        }
                    }
                }
            },
        }
        Ok(())
    }
}

#[derive(Debug, Clone)]
pub struct DatasetStatistics {
    pub rows: usize,
    pub columns: usize,
    pub feature_stats: Vec<ColumnStatistics>,
    pub has_labels: bool,
    pub label_stats: Option<ColumnStatistics>,
}

#[derive(Debug, Clone)]
pub struct ColumnStatistics {
    pub name: String,
    pub mean: f64,
    pub std: f64,
    pub min: f64,
    pub max: f64,
    pub median: f64,
}

fn calculate_column_stats(values: &[f64], name: &str) -> ColumnStatistics {
    let n = values.len() as f64;
    let mean = values.iter().sum::<f64>() / n;
    let variance = values.iter().map(|x| (x - mean).powi(2)).sum::<f64>() / n;
    let std = variance.sqrt();
    
    let mut sorted_values = values.to_vec();
    sorted_values.sort_by(|a, b| a.partial_cmp(b).unwrap());
    
    let min = sorted_values.first().copied().unwrap_or(0.0);
    let max = sorted_values.last().copied().unwrap_or(0.0);
    let median = if sorted_values.len() % 2 == 0 {
        let mid = sorted_values.len() / 2;
        (sorted_values[mid - 1] + sorted_values[mid]) / 2.0
    } else {
        sorted_values[sorted_values.len() / 2]
    };

    ColumnStatistics {
        name: name.to_string(),
        mean,
        std,
        min,
        max,
        median,
    }
}

/// Parameters for min-max normalization
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MinMaxParams {
    pub mins: Vec<f64>,
    pub maxs: Vec<f64>,
}

/// Parameters for z-score standardization
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StandardizationParams {
    pub means: Vec<f64>,
    pub std_devs: Vec<f64>,
}

/// Strategy for handling missing values
#[derive(Debug, Clone)]
pub enum MissingValueStrategy {
    DropRows,
    FillMean,
    FillMedian,
    FillZero,
}
