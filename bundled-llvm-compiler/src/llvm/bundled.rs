//! Bundled LLVM Tools Module
//! This module provides access to bundled LLVM tools

use std::path::{Path, PathBuf};
use std::process::Command;
use std::fs;
use std::io::Write;
use tempfile::TempDir;
use include_dir::{include_dir, Dir};

// Embed the LLVM tools directory
#[cfg(has_embedded_llvm)]
static LLVM_TOOLS: Dir = include_dir!("llvm-tools");

pub struct BundledLLVM {
    temp_dir: Option<TempDir>,
    tools_path: PathBuf,
}

impl BundledLLVM {
    pub fn new() -> Result<Self, Box<dyn std::error::Error>> {
        #[cfg(has_embedded_llvm)]
        {
            // Extract bundled tools to temporary directory
            let temp_dir = TempDir::new()?;
            let tools_path = temp_dir.path().join("llvm-tools");
            
            // Extract embedded LLVM tools
            extract_embedded_tools(&tools_path)?;
            
            Ok(BundledLLVM {
                temp_dir: Some(temp_dir),
                tools_path,
            })
        }
        
        #[cfg(not(has_embedded_llvm))]
        {
            // Fallback to system LLVM
            Ok(BundledLLVM {
                temp_dir: None,
                tools_path: PathBuf::from("/usr/bin"),
            })
        }
    }
    
    pub fn get_tool_path(&self, tool: &str) -> PathBuf {
        self.tools_path.join("bin").join(tool)
    }
    
    pub fn run_llc(&self, args: &[&str]) -> Result<std::process::Output, std::io::Error> {
        let llc_path = self.get_tool_path("llc");
        Command::new(llc_path).args(args).output()
    }
    
    pub fn run_opt(&self, args: &[&str]) -> Result<std::process::Output, std::io::Error> {
        let opt_path = self.get_tool_path("opt");
        Command::new(opt_path).args(args).output()
    }
    
    pub fn run_clang(&self, args: &[&str]) -> Result<std::process::Output, std::io::Error> {
        let clang_path = self.get_tool_path("clang");
        Command::new(clang_path).args(args).output()
    }
    
    pub fn is_available(&self) -> bool {
        self.get_tool_path("llc").exists() || 
        which::which("llc").is_ok()
    }
}

#[cfg(has_embedded_llvm)]
fn extract_embedded_tools(target_path: &Path) -> Result<(), Box<dyn std::error::Error>> {
    fs::create_dir_all(target_path)?;
    
    // Extract all embedded files
    extract_dir(&LLVM_TOOLS, target_path)?;
    
    // Make binaries executable
    let bin_dir = target_path.join("bin");
    if bin_dir.exists() {
        for entry in fs::read_dir(&bin_dir)? {
            let entry = entry?;
            let path = entry.path();
            if path.is_file() {
                #[cfg(unix)]
                {
                    use std::os::unix::fs::PermissionsExt;
                    let mut perms = fs::metadata(&path)?.permissions();
                    perms.set_mode(0o755);
                    fs::set_permissions(&path, perms)?;
                }
            }
        }
    }
    
    Ok(())
}

#[cfg(has_embedded_llvm)]
fn extract_dir(dir: &Dir, target_path: &Path) -> Result<(), Box<dyn std::error::Error>> {
    for file in dir.files() {
        let file_path = target_path.join(file.path());
        if let Some(parent) = file_path.parent() {
            fs::create_dir_all(parent)?;
        }
        let mut output_file = fs::File::create(&file_path)?;
        output_file.write_all(file.contents())?;
    }
    
    for subdir in dir.dirs() {
        let subdir_path = target_path.join(subdir.path());
        fs::create_dir_all(&subdir_path)?;
        extract_dir(subdir, &subdir_path)?;
    }
    
    Ok(())
}

impl Drop for BundledLLVM {
    fn drop(&mut self) {
        // Temporary directory is automatically cleaned up
    }
}
