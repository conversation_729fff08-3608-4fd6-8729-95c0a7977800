/// Object-Relational Mapping (ORM) for Umbra
/// Provides automatic struct-to-table mapping and CRUD operations

use crate::error::{UmbraError, UmbraResult};
use crate::database::connection::{DatabaseConnection, DatabaseValue, Row};
use std::collections::HashMap;
use std::sync::Arc;

/// ORM entity trait that all mapped structs must implement
pub trait Entity: Send + Sync {
    /// Get the table name for this entity
    fn table_name() -> &'static str;
    
    /// Get the primary key field name
    fn primary_key() -> &'static str;
    
    /// Convert entity to database values for insertion/update
    fn to_values(&self) -> HashMap<String, DatabaseValue>;
    
    /// Create entity from database row
    fn from_row(row: &Row) -> UmbraResult<Self> where Self: Sized;
    
    /// Get field mappings (struct field -> database column)
    fn field_mappings() -> HashMap<String, String>;
    
    /// Get validation rules
    fn validation_rules() -> Vec<ValidationRule>;
}

/// Validation rule for entity fields
#[derive(Debug, Clone)]
pub struct ValidationRule {
    pub field_name: String,
    pub rule_type: ValidationType,
    pub message: String,
    pub parameters: HashMap<String, String>,
}

#[derive(Debug, Clone)]
pub enum ValidationType {
    Required,
    MinLength(usize),
    MaxLength(usize),
    Pattern(String),
    Range(i64, i64),
    Email,
    Url,
    Custom(String), // Custom validation function name
}

/// Relationship types between entities
#[derive(Debug, Clone)]
pub enum RelationshipType {
    OneToOne {
        foreign_key: String,
        referenced_table: String,
        referenced_key: String,
    },
    OneToMany {
        foreign_key: String,
        referenced_table: String,
        referenced_key: String,
    },
    ManyToMany {
        junction_table: String,
        local_key: String,
        foreign_key: String,
        referenced_table: String,
        referenced_key: String,
    },
}

/// Relationship definition
#[derive(Debug, Clone)]
pub struct Relationship {
    pub name: String,
    pub relationship_type: RelationshipType,
    pub cascade_delete: bool,
    pub cascade_update: bool,
    pub lazy_loading: bool,
}

/// Query builder for type-safe database queries
pub struct QueryBuilder<T: Entity> {
    connection: Arc<dyn DatabaseConnection>,
    table_name: String,
    select_fields: Vec<String>,
    where_conditions: Vec<WhereCondition>,
    joins: Vec<JoinClause>,
    order_by: Vec<OrderByClause>,
    group_by: Vec<String>,
    having_conditions: Vec<WhereCondition>,
    limit: Option<u32>,
    offset: Option<u32>,
    _phantom: std::marker::PhantomData<T>,
}

#[derive(Debug, Clone)]
pub struct WhereCondition {
    pub field: String,
    pub operator: WhereOperator,
    pub value: DatabaseValue,
    pub logical_op: LogicalOperator,
}

#[derive(Debug, Clone)]
pub enum WhereOperator {
    Equal,
    NotEqual,
    GreaterThan,
    GreaterThanOrEqual,
    LessThan,
    LessThanOrEqual,
    Like,
    NotLike,
    In,
    NotIn,
    IsNull,
    IsNotNull,
    Between,
}

#[derive(Debug, Clone)]
pub enum LogicalOperator {
    And,
    Or,
}

#[derive(Debug, Clone)]
pub struct JoinClause {
    pub join_type: JoinType,
    pub table: String,
    pub on_condition: String,
}

#[derive(Debug, Clone)]
pub enum JoinType {
    Inner,
    Left,
    Right,
    Full,
}

#[derive(Debug, Clone)]
pub struct OrderByClause {
    pub field: String,
    pub direction: OrderDirection,
}

#[derive(Debug, Clone)]
pub enum OrderDirection {
    Asc,
    Desc,
}

impl<T: Entity> QueryBuilder<T> {
    pub fn new(connection: Arc<dyn DatabaseConnection>) -> Self {
        Self {
            connection,
            table_name: T::table_name().to_string(),
            select_fields: vec!["*".to_string()],
            where_conditions: vec![],
            joins: vec![],
            order_by: vec![],
            group_by: vec![],
            having_conditions: vec![],
            limit: None,
            offset: None,
            _phantom: std::marker::PhantomData,
        }
    }

    /// Select specific fields
    pub fn select(mut self, fields: Vec<String>) -> Self {
        self.select_fields = fields;
        self
    }

    /// Add WHERE condition
    pub fn where_eq(mut self, field: String, value: DatabaseValue) -> Self {
        self.where_conditions.push(WhereCondition {
            field,
            operator: WhereOperator::Equal,
            value,
            logical_op: LogicalOperator::And,
        });
        self
    }

    /// Add WHERE condition with custom operator
    pub fn where_op(mut self, field: String, operator: WhereOperator, value: DatabaseValue) -> Self {
        self.where_conditions.push(WhereCondition {
            field,
            operator,
            value,
            logical_op: LogicalOperator::And,
        });
        self
    }

    /// Add OR WHERE condition
    pub fn or_where(mut self, field: String, operator: WhereOperator, value: DatabaseValue) -> Self {
        self.where_conditions.push(WhereCondition {
            field,
            operator,
            value,
            logical_op: LogicalOperator::Or,
        });
        self
    }

    /// Add JOIN clause
    pub fn join(mut self, table: String, on_condition: String) -> Self {
        self.joins.push(JoinClause {
            join_type: JoinType::Inner,
            table,
            on_condition,
        });
        self
    }

    /// Add LEFT JOIN clause
    pub fn left_join(mut self, table: String, on_condition: String) -> Self {
        self.joins.push(JoinClause {
            join_type: JoinType::Left,
            table,
            on_condition,
        });
        self
    }

    /// Add ORDER BY clause
    pub fn order_by(mut self, field: String, direction: OrderDirection) -> Self {
        self.order_by.push(OrderByClause { field, direction });
        self
    }

    /// Add GROUP BY clause
    pub fn group_by(mut self, fields: Vec<String>) -> Self {
        self.group_by = fields;
        self
    }

    /// Add LIMIT clause
    pub fn limit(mut self, limit: u32) -> Self {
        self.limit = Some(limit);
        self
    }

    /// Add OFFSET clause
    pub fn offset(mut self, offset: u32) -> Self {
        self.offset = Some(offset);
        self
    }

    /// Execute query and return results
    pub fn get(self) -> UmbraResult<Vec<T>> {
        let query = self.build_select_query();
        let params = self.build_parameters();
        
        let rows = self.connection.query(&query, &params)?;
        let mut results = Vec::new();
        
        for row in rows {
            results.push(T::from_row(&row)?);
        }
        
        Ok(results)
    }

    /// Execute query and return first result
    pub fn first(self) -> UmbraResult<Option<T>> {
        let mut results = self.limit(1).get()?;
        Ok(results.pop())
    }

    /// Execute query and return single result (error if not exactly one)
    pub fn single(self) -> UmbraResult<T> {
        let results = self.limit(2).get()?;
        match results.len() {
            0 => Err(UmbraError::Database("No results found".to_string())),
            1 => Ok(results.into_iter().next().unwrap()),
            _ => Err(UmbraError::Database("Multiple results found".to_string())),
        }
    }

    /// Count results
    pub fn count(mut self) -> UmbraResult<u64> {
        self.select_fields = vec!["COUNT(*)".to_string()];
        let query = self.build_select_query();
        let params = self.build_parameters();
        
        let rows = self.connection.query(&query, &params)?;
        if let Some(row) = rows.first() {
            if let Some(DatabaseValue::Int64(count)) = row.columns.values().next() {
                return Ok(*count as u64);
            }
        }
        
        Err(UmbraError::Database("Failed to get count".to_string()))
    }

    fn build_select_query(&self) -> String {
        let mut query = format!("SELECT {} FROM {}", 
            self.select_fields.join(", "), 
            self.table_name
        );

        // Add JOINs
        for join in &self.joins {
            let join_type = match join.join_type {
                JoinType::Inner => "INNER JOIN",
                JoinType::Left => "LEFT JOIN",
                JoinType::Right => "RIGHT JOIN",
                JoinType::Full => "FULL JOIN",
            };
            query.push_str(&format!(" {} {} ON {}", join_type, join.table, join.on_condition));
        }

        // Add WHERE conditions
        if !self.where_conditions.is_empty() {
            query.push_str(" WHERE ");
            for (i, condition) in self.where_conditions.iter().enumerate() {
                if i > 0 {
                    let logical_op = match condition.logical_op {
                        LogicalOperator::And => "AND",
                        LogicalOperator::Or => "OR",
                    };
                    query.push_str(&format!(" {} ", logical_op));
                }
                
                let operator = match condition.operator {
                    WhereOperator::Equal => "=",
                    WhereOperator::NotEqual => "!=",
                    WhereOperator::GreaterThan => ">",
                    WhereOperator::GreaterThanOrEqual => ">=",
                    WhereOperator::LessThan => "<",
                    WhereOperator::LessThanOrEqual => "<=",
                    WhereOperator::Like => "LIKE",
                    WhereOperator::NotLike => "NOT LIKE",
                    WhereOperator::In => "IN",
                    WhereOperator::NotIn => "NOT IN",
                    WhereOperator::IsNull => "IS NULL",
                    WhereOperator::IsNotNull => "IS NOT NULL",
                    WhereOperator::Between => "BETWEEN",
                };
                
                query.push_str(&format!("{} {} ?", condition.field, operator));
            }
        }

        // Add GROUP BY
        if !self.group_by.is_empty() {
            query.push_str(&format!(" GROUP BY {}", self.group_by.join(", ")));
        }

        // Add HAVING
        if !self.having_conditions.is_empty() {
            query.push_str(" HAVING ");
            // Similar logic to WHERE conditions
        }

        // Add ORDER BY
        if !self.order_by.is_empty() {
            query.push_str(" ORDER BY ");
            let order_clauses: Vec<String> = self.order_by.iter().map(|clause| {
                let direction = match clause.direction {
                    OrderDirection::Asc => "ASC",
                    OrderDirection::Desc => "DESC",
                };
                format!("{} {}", clause.field, direction)
            }).collect();
            query.push_str(&order_clauses.join(", "));
        }

        // Add LIMIT and OFFSET
        if let Some(limit) = self.limit {
            query.push_str(&format!(" LIMIT {}", limit));
        }
        if let Some(offset) = self.offset {
            query.push_str(&format!(" OFFSET {}", offset));
        }

        query
    }

    fn build_parameters(&self) -> Vec<DatabaseValue> {
        let mut params = Vec::new();
        
        for condition in &self.where_conditions {
            if !matches!(condition.operator, WhereOperator::IsNull | WhereOperator::IsNotNull) {
                params.push(condition.value.clone());
            }
        }
        
        params
    }
}

/// Repository pattern for CRUD operations
pub struct Repository<T: Entity> {
    connection: Arc<dyn DatabaseConnection>,
    _phantom: std::marker::PhantomData<T>,
}

impl<T: Entity> Repository<T> {
    pub fn new(connection: Arc<dyn DatabaseConnection>) -> Self {
        Self {
            connection,
            _phantom: std::marker::PhantomData,
        }
    }

    /// Create a new entity
    pub fn create(&self, entity: &T) -> UmbraResult<()> {
        let values = entity.to_values();
        let fields: Vec<String> = values.keys().cloned().collect();
        let placeholders: Vec<String> = (0..fields.len()).map(|_| "?".to_string()).collect();
        let params: Vec<DatabaseValue> = fields.iter().map(|f| values[f].clone()).collect();

        let query = format!(
            "INSERT INTO {} ({}) VALUES ({})",
            T::table_name(),
            fields.join(", "),
            placeholders.join(", ")
        );

        self.connection.execute(&query, &params)?;
        Ok(())
    }

    /// Find entity by primary key
    pub fn find(&self, id: DatabaseValue) -> UmbraResult<Option<T>> {
        QueryBuilder::new(Arc::clone(&self.connection))
            .where_eq(T::primary_key().to_string(), id)
            .first()
    }

    /// Find all entities
    pub fn find_all(&self) -> UmbraResult<Vec<T>> {
        QueryBuilder::new(Arc::clone(&self.connection)).get()
    }

    /// Update an entity
    pub fn update(&self, entity: &T) -> UmbraResult<()> {
        let values = entity.to_values();
        let pk_value = values.get(T::primary_key())
            .ok_or_else(|| UmbraError::Database("Primary key not found".to_string()))?;

        let mut set_clauses = Vec::new();
        let mut params = Vec::new();

        for (field, value) in values.iter() {
            if field != T::primary_key() {
                set_clauses.push(format!("{} = ?", field));
                params.push(value.clone());
            }
        }

        params.push(pk_value.clone());

        let query = format!(
            "UPDATE {} SET {} WHERE {} = ?",
            T::table_name(),
            set_clauses.join(", "),
            T::primary_key()
        );

        self.connection.execute(&query, &params)?;
        Ok(())
    }

    /// Delete an entity by primary key
    pub fn delete(&self, id: DatabaseValue) -> UmbraResult<()> {
        let query = format!(
            "DELETE FROM {} WHERE {} = ?",
            T::table_name(),
            T::primary_key()
        );

        self.connection.execute(&query, &[id])?;
        Ok(())
    }

    /// Get query builder for complex queries
    pub fn query(&self) -> QueryBuilder<T> {
        QueryBuilder::new(Arc::clone(&self.connection))
    }
}

/// ORM configuration and metadata
pub struct OrmConfig {
    pub auto_create_tables: bool,
    pub auto_migrate: bool,
    pub naming_convention: NamingConvention,
    pub default_string_length: u32,
    pub enable_soft_deletes: bool,
    pub enable_timestamps: bool,
}

#[derive(Debug, Clone)]
pub enum NamingConvention {
    SnakeCase,
    CamelCase,
    PascalCase,
    KebabCase,
}

impl Default for OrmConfig {
    fn default() -> Self {
        Self {
            auto_create_tables: false,
            auto_migrate: false,
            naming_convention: NamingConvention::SnakeCase,
            default_string_length: 255,
            enable_soft_deletes: false,
            enable_timestamps: true,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::database::{DatabaseConfig, DatabaseType};
    use crate::database::connection::ConnectionFactory;

    // Mock entity for testing
    struct TestUser {
        id: i64,
        name: String,
        email: String,
    }

    impl Entity for TestUser {
        fn table_name() -> &'static str {
            "users"
        }

        fn primary_key() -> &'static str {
            "id"
        }

        fn to_values(&self) -> HashMap<String, DatabaseValue> {
            let mut values = HashMap::new();
            values.insert("id".to_string(), DatabaseValue::Int64(self.id));
            values.insert("name".to_string(), DatabaseValue::String(self.name.clone()));
            values.insert("email".to_string(), DatabaseValue::String(self.email.clone()));
            values
        }

        fn from_row(row: &Row) -> UmbraResult<Self> {
            Ok(Self {
                id: row.get_int("id")?,
                name: row.get_string("name")?,
                email: row.get_string("email")?,
            })
        }

        fn field_mappings() -> HashMap<String, String> {
            let mut mappings = HashMap::new();
            mappings.insert("id".to_string(), "id".to_string());
            mappings.insert("name".to_string(), "name".to_string());
            mappings.insert("email".to_string(), "email".to_string());
            mappings
        }

        fn validation_rules() -> Vec<ValidationRule> {
            vec![
                ValidationRule {
                    field_name: "name".to_string(),
                    rule_type: ValidationType::Required,
                    message: "Name is required".to_string(),
                    parameters: HashMap::new(),
                },
                ValidationRule {
                    field_name: "email".to_string(),
                    rule_type: ValidationType::Email,
                    message: "Invalid email format".to_string(),
                    parameters: HashMap::new(),
                },
            ]
        }
    }

    #[test]
    fn test_query_builder() {
        let config = DatabaseConfig::new(DatabaseType::SQLite);
        let connection = ConnectionFactory::create_connection(&config).unwrap();
        
        let query_builder = QueryBuilder::<TestUser>::new(connection)
            .where_eq("name".to_string(), DatabaseValue::String("John".to_string()))
            .order_by("id".to_string(), OrderDirection::Asc)
            .limit(10);

        let query = query_builder.build_select_query();
        assert!(query.contains("SELECT * FROM users"));
        assert!(query.contains("WHERE name = ?"));
        assert!(query.contains("ORDER BY id ASC"));
        assert!(query.contains("LIMIT 10"));
    }

    #[test]
    fn test_repository_operations() {
        let config = DatabaseConfig::new(DatabaseType::SQLite);
        let connection = ConnectionFactory::create_connection(&config).unwrap();
        let repo = Repository::<TestUser>::new(connection);

        let user = TestUser {
            id: 1,
            name: "John Doe".to_string(),
            email: "<EMAIL>".to_string(),
        };

        // Test create operation
        assert!(repo.create(&user).is_ok());
    }
}
