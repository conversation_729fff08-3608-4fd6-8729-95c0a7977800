/// Database integration module for Umbra
/// Provides comprehensive database support including connections, ORM, and SQL integration

pub mod connection;
pub mod orm;
pub mod query;
pub mod migration;
pub mod error;
pub mod pool;
pub mod transaction;
pub mod schema;
pub mod annotations;
pub mod async_support;
pub mod testing;

use crate::error::{UmbraError, UmbraResult};
use std::collections::HashMap;
use std::sync::{Arc, Mutex};

/// Supported database types
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub enum DatabaseType {
    SQLite,
    PostgreSQL,
    MySQL,
    MongoDB,
}

impl DatabaseType {
    pub fn from_str(s: &str) -> UmbraResult<Self> {
        match s.to_lowercase().as_str() {
            "sqlite" | "sqlite3" => Ok(DatabaseType::SQLite),
            "postgresql" | "postgres" | "pg" => Ok(DatabaseType::PostgreSQL),
            "mysql" | "mariadb" => Ok(DatabaseType::MySQL),
            "mongodb" | "mongo" => Ok(DatabaseType::MongoDB),
            _ => Err(UmbraError::Database(format!("Unsupported database type: {}", s))),
        }
    }

    pub fn default_port(&self) -> u16 {
        match self {
            DatabaseType::SQLite => 0, // File-based, no port
            DatabaseType::PostgreSQL => 5432,
            DatabaseType::MySQL => 3306,
            DatabaseType::MongoDB => 27017,
        }
    }

    pub fn supports_transactions(&self) -> bool {
        match self {
            DatabaseType::SQLite | DatabaseType::PostgreSQL | DatabaseType::MySQL => true,
            DatabaseType::MongoDB => false, // MongoDB uses different transaction model
        }
    }
}

/// Database configuration
#[derive(Debug, Clone)]
pub struct DatabaseConfig {
    pub db_type: DatabaseType,
    pub host: String,
    pub port: u16,
    pub database: String,
    pub username: Option<String>,
    pub password: Option<String>,
    pub connection_string: Option<String>,
    pub pool_size: usize,
    pub timeout: std::time::Duration,
    pub ssl_mode: SslMode,
    pub options: HashMap<String, String>,
}

#[derive(Debug, Clone)]
pub enum SslMode {
    Disable,
    Allow,
    Prefer,
    Require,
    VerifyCA,
    VerifyFull,
}

impl Default for DatabaseConfig {
    fn default() -> Self {
        Self {
            db_type: DatabaseType::SQLite,
            host: "localhost".to_string(),
            port: 0,
            database: "database.db".to_string(),
            username: None,
            password: None,
            connection_string: None,
            pool_size: 10,
            timeout: std::time::Duration::from_secs(30),
            ssl_mode: SslMode::Prefer,
            options: HashMap::new(),
        }
    }
}

impl DatabaseConfig {
    pub fn new(db_type: DatabaseType) -> Self {
        let mut config = Self::default();
        config.db_type = db_type.clone();
        config.port = db_type.default_port();
        config
    }

    pub fn from_connection_string(conn_str: &str) -> UmbraResult<Self> {
        // Parse connection string format: "type://user:pass@host:port/database?options"
        let url = url::Url::parse(conn_str)
            .map_err(|e| UmbraError::Database(format!("Invalid connection string: {}", e)))?;

        let db_type = DatabaseType::from_str(url.scheme())?;
        let mut config = Self::new(db_type);

        config.host = url.host_str().unwrap_or("localhost").to_string();
        if let Some(port) = url.port() {
            config.port = port;
        }

        config.database = url.path().trim_start_matches('/').to_string();
        config.username = if url.username().is_empty() {
            None
        } else {
            Some(url.username().to_string())
        };
        config.password = url.password().map(|p| p.to_string());

        // Parse query parameters as options
        for (key, value) in url.query_pairs() {
            config.options.insert(key.to_string(), value.to_string());
        }

        config.connection_string = Some(conn_str.to_string());
        Ok(config)
    }

    pub fn to_connection_string(&self) -> String {
        if let Some(ref conn_str) = self.connection_string {
            return conn_str.clone();
        }

        let scheme = match self.db_type {
            DatabaseType::SQLite => "sqlite",
            DatabaseType::PostgreSQL => "postgresql",
            DatabaseType::MySQL => "mysql",
            DatabaseType::MongoDB => "mongodb",
        };

        if self.db_type == DatabaseType::SQLite {
            return format!("{}://{}", scheme, self.database);
        }

        let mut url = format!("{}://", scheme);

        if let (Some(ref user), Some(ref pass)) = (&self.username, &self.password) {
            url.push_str(&format!("{}:{}@", user, pass));
        } else if let Some(ref user) = &self.username {
            url.push_str(&format!("{}@", user));
        }

        url.push_str(&format!("{}:{}/{}", self.host, self.port, self.database));

        if !self.options.is_empty() {
            url.push('?');
            let params: Vec<String> = self.options
                .iter()
                .map(|(k, v)| format!("{}={}", k, v))
                .collect();
            url.push_str(&params.join("&"));
        }

        url
    }
}

/// Global database registry for managing multiple database connections
pub struct DatabaseRegistry {
    connections: Arc<Mutex<HashMap<String, Arc<dyn connection::DatabaseConnection>>>>,
    default_connection: Arc<Mutex<Option<String>>>,
}

impl DatabaseRegistry {
    pub fn new() -> Self {
        Self {
            connections: Arc::new(Mutex::new(HashMap::new())),
            default_connection: Arc::new(Mutex::new(None)),
        }
    }

    pub fn register_connection(
        &self,
        name: String,
        connection: Arc<dyn connection::DatabaseConnection>,
    ) -> UmbraResult<()> {
        let mut connections = self.connections.lock().unwrap();
        connections.insert(name.clone(), connection);

        // Set as default if it's the first connection
        let mut default = self.default_connection.lock().unwrap();
        if default.is_none() {
            *default = Some(name);
        }

        Ok(())
    }

    pub fn get_connection(&self, name: &str) -> UmbraResult<Arc<dyn connection::DatabaseConnection>> {
        let connections = self.connections.lock().unwrap();
        connections
            .get(name)
            .cloned()
            .ok_or_else(|| UmbraError::Database(format!("Connection '{}' not found", name)))
    }

    pub fn get_default_connection(&self) -> UmbraResult<Arc<dyn connection::DatabaseConnection>> {
        let default = self.default_connection.lock().unwrap();
        if let Some(ref name) = *default {
            self.get_connection(name)
        } else {
            Err(UmbraError::Database("No default database connection".to_string()))
        }
    }

    pub fn set_default_connection(&self, name: String) -> UmbraResult<()> {
        {
            let connections = self.connections.lock().unwrap();
            if !connections.contains_key(&name) {
                return Err(UmbraError::Database(format!("Connection '{}' not found", name)));
            }
        }

        let mut default = self.default_connection.lock().unwrap();
        *default = Some(name);
        Ok(())
    }

    pub fn list_connections(&self) -> Vec<String> {
        let connections = self.connections.lock().unwrap();
        connections.keys().cloned().collect()
    }

    pub fn remove_connection(&self, name: &str) -> UmbraResult<()> {
        let mut connections = self.connections.lock().unwrap();
        connections.remove(name);

        // Clear default if it was the removed connection
        let mut default = self.default_connection.lock().unwrap();
        if let Some(ref default_name) = *default {
            if default_name == name {
                *default = None;
            }
        }

        Ok(())
    }
}

// Global database registry instance
lazy_static::lazy_static! {
    pub static ref DATABASE_REGISTRY: DatabaseRegistry = DatabaseRegistry::new();
}

/// Database integration initialization
pub fn initialize_database_support() -> UmbraResult<()> {
    // Initialize database drivers and connection pools
    log::info!("Initializing Umbra database support");
    
    // Register built-in database types
    register_database_drivers()?;
    
    // Initialize connection pools
    pool::initialize_connection_pools()?;
    
    // Setup migration system
    migration::initialize_migration_system()?;
    
    log::info!("Database support initialized successfully");
    Ok(())
}

fn register_database_drivers() -> UmbraResult<()> {
    // This would register actual database drivers in a real implementation
    log::debug!("Registering database drivers");
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_database_type_from_str() {
        assert_eq!(DatabaseType::from_str("sqlite").unwrap(), DatabaseType::SQLite);
        assert_eq!(DatabaseType::from_str("postgresql").unwrap(), DatabaseType::PostgreSQL);
        assert_eq!(DatabaseType::from_str("mysql").unwrap(), DatabaseType::MySQL);
        assert_eq!(DatabaseType::from_str("mongodb").unwrap(), DatabaseType::MongoDB);
        
        assert!(DatabaseType::from_str("invalid").is_err());
    }

    #[test]
    fn test_database_config_connection_string() {
        let config = DatabaseConfig::from_connection_string(
            "postgresql://user:pass@localhost:5432/mydb?sslmode=require"
        ).unwrap();
        
        assert_eq!(config.db_type, DatabaseType::PostgreSQL);
        assert_eq!(config.host, "localhost");
        assert_eq!(config.port, 5432);
        assert_eq!(config.database, "mydb");
        assert_eq!(config.username, Some("user".to_string()));
        assert_eq!(config.password, Some("pass".to_string()));
        assert_eq!(config.options.get("sslmode"), Some(&"require".to_string()));
    }

    #[test]
    fn test_database_registry() {
        let registry = DatabaseRegistry::new();
        
        // Test that no default connection exists initially
        assert!(registry.get_default_connection().is_err());
        
        // Test listing empty connections
        assert_eq!(registry.list_connections().len(), 0);
    }
}
