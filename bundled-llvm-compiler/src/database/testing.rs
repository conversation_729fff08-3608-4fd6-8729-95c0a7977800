/// Database testing utilities for Umbra
/// Provides mocking, fixtures, and testing helpers for database operations

use crate::error::{UmbraError, UmbraResult};
use crate::database::connection::{DatabaseConnection, DatabaseValue, QueryResult, Row, PreparedStatement, Transaction, ConnectionMetadata, DatabaseSchema};
use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use std::time::Duration;

/// Mock database connection for testing
pub struct MockDatabaseConnection {
    pub name: String,
    pub query_results: Arc<Mutex<HashMap<String, Vec<Row>>>>,
    pub execute_results: Arc<Mutex<HashMap<String, QueryResult>>>,
    pub query_log: Arc<Mutex<Vec<QueryLogEntry>>>,
    pub should_fail: Arc<Mutex<bool>>,
    pub failure_message: Arc<Mutex<Option<String>>>,
    pub delay: Arc<Mutex<Option<Duration>>>,
}

#[derive(Debug, Clone)]
pub struct QueryLogEntry {
    pub query: String,
    pub params: Vec<DatabaseValue>,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub execution_time: Duration,
}

impl MockDatabaseConnection {
    pub fn new(name: &str) -> Self {
        Self {
            name: name.to_string(),
            query_results: Arc::new(Mutex::new(HashMap::new())),
            execute_results: Arc::new(Mutex::new(HashMap::new())),
            query_log: Arc::new(Mutex::new(Vec::new())),
            should_fail: Arc::new(Mutex::new(false)),
            failure_message: Arc::new(Mutex::new(None)),
            delay: Arc::new(Mutex::new(None)),
        }
    }

    /// Set mock query result
    pub fn set_query_result(&self, query: &str, rows: Vec<Row>) {
        self.query_results.lock().unwrap().insert(query.to_string(), rows);
    }

    /// Set mock execute result
    pub fn set_execute_result(&self, query: &str, result: QueryResult) {
        self.execute_results.lock().unwrap().insert(query.to_string(), result);
    }

    /// Make connection fail on next operation
    pub fn set_should_fail(&self, should_fail: bool, message: Option<String>) {
        *self.should_fail.lock().unwrap() = should_fail;
        *self.failure_message.lock().unwrap() = message;
    }

    /// Set artificial delay for operations
    pub fn set_delay(&self, delay: Option<Duration>) {
        *self.delay.lock().unwrap() = delay;
    }

    /// Get query log
    pub fn get_query_log(&self) -> Vec<QueryLogEntry> {
        self.query_log.lock().unwrap().clone()
    }

    /// Clear query log
    pub fn clear_query_log(&self) {
        self.query_log.lock().unwrap().clear();
    }

    /// Get last executed query
    pub fn get_last_query(&self) -> Option<QueryLogEntry> {
        self.query_log.lock().unwrap().last().cloned()
    }

    /// Check if query was executed
    pub fn was_query_executed(&self, query: &str) -> bool {
        self.query_log.lock().unwrap()
            .iter()
            .any(|entry| entry.query.contains(query))
    }

    /// Get query execution count
    pub fn get_query_count(&self, query: &str) -> usize {
        self.query_log.lock().unwrap()
            .iter()
            .filter(|entry| entry.query.contains(query))
            .count()
    }

    fn log_query(&self, query: &str, params: &[DatabaseValue], execution_time: Duration) {
        let entry = QueryLogEntry {
            query: query.to_string(),
            params: params.to_vec(),
            timestamp: chrono::Utc::now(),
            execution_time,
        };
        self.query_log.lock().unwrap().push(entry);
    }

    fn check_failure(&self) -> UmbraResult<()> {
        if *self.should_fail.lock().unwrap() {
            let message = self.failure_message.lock().unwrap()
                .clone()
                .unwrap_or_else(|| "Mock connection failure".to_string());
            return Err(UmbraError::Database(message));
        }
        Ok(())
    }

    fn apply_delay(&self) {
        if let Some(delay) = *self.delay.lock().unwrap() {
            std::thread::sleep(delay);
        }
    }
}

impl DatabaseConnection for MockDatabaseConnection {
    fn execute(&self, query: &str, params: &[DatabaseValue]) -> UmbraResult<QueryResult> {
        let start_time = std::time::Instant::now();
        
        self.check_failure()?;
        self.apply_delay();

        let result = self.execute_results.lock().unwrap()
            .get(query)
            .cloned()
            .unwrap_or_else(|| QueryResult {
                rows_affected: 1,
                last_insert_id: Some(1),
                execution_time: Duration::from_millis(10),
            });

        let execution_time = start_time.elapsed();
        self.log_query(query, params, execution_time);

        Ok(result)
    }

    fn query(&self, query: &str, params: &[DatabaseValue]) -> UmbraResult<Vec<Row>> {
        let start_time = std::time::Instant::now();
        
        self.check_failure()?;
        self.apply_delay();

        let rows = self.query_results.lock().unwrap()
            .get(query)
            .cloned()
            .unwrap_or_else(|| {
                let mut row = Row::new();
                row.insert("id".to_string(), DatabaseValue::Int64(1));
                row.insert("name".to_string(), DatabaseValue::String("Test".to_string()));
                vec![row]
            });

        let execution_time = start_time.elapsed();
        self.log_query(query, params, execution_time);

        Ok(rows)
    }

    fn execute_prepared(&self, stmt: &PreparedStatement, params: &[DatabaseValue]) -> UmbraResult<QueryResult> {
        self.execute(&stmt.query, params)
    }

    fn prepare(&self, query: &str) -> UmbraResult<PreparedStatement> {
        self.check_failure()?;
        
        Ok(PreparedStatement {
            id: uuid::Uuid::new_v4().to_string(),
            query: query.to_string(),
            parameter_count: 0,
            parameter_types: vec![],
        })
    }

    fn begin_transaction(&self) -> UmbraResult<Box<dyn Transaction>> {
        self.check_failure()?;
        Ok(Box::new(MockTransaction::new()))
    }

    fn ping(&self) -> UmbraResult<()> {
        self.check_failure()?;
        self.apply_delay();
        Ok(())
    }

    fn get_metadata(&self) -> ConnectionMetadata {
        ConnectionMetadata {
            database_type: crate::database::DatabaseType::SQLite,
            server_version: "Mock 1.0.0".to_string(),
            database_name: "test_db".to_string(),
            username: Some("test_user".to_string()),
            connection_id: self.name.clone(),
            connected_at: chrono::Utc::now(),
            last_activity: chrono::Utc::now(),
        }
    }

    fn close(&self) -> UmbraResult<()> {
        Ok(())
    }

    fn get_schema(&self) -> UmbraResult<DatabaseSchema> {
        Ok(DatabaseSchema {
            tables: vec![],
            views: vec![],
            indexes: vec![],
            constraints: vec![],
        })
    }
}

/// Mock transaction for testing
pub struct MockTransaction {
    is_active: Arc<Mutex<bool>>,
    operations: Arc<Mutex<Vec<String>>>,
}

impl MockTransaction {
    pub fn new() -> Self {
        Self {
            is_active: Arc::new(Mutex::new(true)),
            operations: Arc::new(Mutex::new(Vec::new())),
        }
    }

    pub fn get_operations(&self) -> Vec<String> {
        self.operations.lock().unwrap().clone()
    }
}

impl Transaction for MockTransaction {
    fn commit(&self) -> UmbraResult<()> {
        *self.is_active.lock().unwrap() = false;
        self.operations.lock().unwrap().push("COMMIT".to_string());
        Ok(())
    }

    fn rollback(&self) -> UmbraResult<()> {
        *self.is_active.lock().unwrap() = false;
        self.operations.lock().unwrap().push("ROLLBACK".to_string());
        Ok(())
    }

    fn execute(&self, query: &str, _params: &[DatabaseValue]) -> UmbraResult<QueryResult> {
        if !self.is_active() {
            return Err(UmbraError::Database("Transaction is not active".to_string()));
        }

        self.operations.lock().unwrap().push(query.to_string());
        
        Ok(QueryResult {
            rows_affected: 1,
            last_insert_id: Some(1),
            execution_time: Duration::from_millis(5),
        })
    }

    fn query(&self, query: &str, _params: &[DatabaseValue]) -> UmbraResult<Vec<Row>> {
        if !self.is_active() {
            return Err(UmbraError::Database("Transaction is not active".to_string()));
        }

        self.operations.lock().unwrap().push(query.to_string());
        Ok(vec![])
    }

    fn is_active(&self) -> bool {
        *self.is_active.lock().unwrap()
    }
}

/// Database fixture manager for testing
pub struct FixtureManager {
    connection: Arc<dyn DatabaseConnection>,
    fixtures: HashMap<String, Vec<String>>,
    cleanup_queries: Vec<String>,
}

impl FixtureManager {
    pub fn new(connection: Arc<dyn DatabaseConnection>) -> Self {
        Self {
            connection,
            fixtures: HashMap::new(),
            cleanup_queries: Vec::new(),
        }
    }

    /// Load fixture from SQL file
    pub fn load_fixture(&mut self, name: &str, sql_file: &str) -> UmbraResult<()> {
        let content = std::fs::read_to_string(sql_file)
            .map_err(|e| UmbraError::Database(format!("Failed to read fixture file: {}", e)))?;

        let statements = self.parse_sql_statements(&content);
        self.fixtures.insert(name.to_string(), statements);
        Ok(())
    }

    /// Add fixture from SQL string
    pub fn add_fixture(&mut self, name: &str, sql: &str) {
        let statements = self.parse_sql_statements(sql);
        self.fixtures.insert(name.to_string(), statements);
    }

    /// Execute fixture
    pub fn execute_fixture(&mut self, name: &str) -> UmbraResult<()> {
        let statements = self.fixtures.get(name)
            .ok_or_else(|| UmbraError::Database(format!("Fixture '{}' not found", name)))?
            .clone();

        for statement in statements {
            self.connection.execute(&statement, &[])?;
        }

        Ok(())
    }

    /// Add cleanup query
    pub fn add_cleanup(&mut self, query: &str) {
        self.cleanup_queries.push(query.to_string());
    }

    /// Execute cleanup
    pub fn cleanup(&self) -> UmbraResult<()> {
        for query in &self.cleanup_queries {
            let _ = self.connection.execute(query, &[]); // Ignore errors during cleanup
        }
        Ok(())
    }

    /// Parse SQL statements from string
    fn parse_sql_statements(&self, sql: &str) -> Vec<String> {
        sql.split(';')
            .map(|s| s.trim())
            .filter(|s| !s.is_empty())
            .map(|s| s.to_string())
            .collect()
    }
}

/// Database test helper
pub struct DatabaseTestHelper {
    pub connection: Arc<MockDatabaseConnection>,
    pub fixture_manager: FixtureManager,
}

impl DatabaseTestHelper {
    pub fn new(name: &str) -> Self {
        let connection = Arc::new(MockDatabaseConnection::new(name));
        let fixture_manager = FixtureManager::new(connection.clone() as Arc<dyn DatabaseConnection>);
        
        Self {
            connection,
            fixture_manager,
        }
    }

    /// Setup test data
    pub fn setup(&mut self) -> UmbraResult<()> {
        // Create test tables
        self.fixture_manager.add_fixture("test_tables", r#"
            CREATE TABLE users (
                id INTEGER PRIMARY KEY,
                name TEXT NOT NULL,
                email TEXT UNIQUE
            );
            
            CREATE TABLE posts (
                id INTEGER PRIMARY KEY,
                user_id INTEGER,
                title TEXT NOT NULL,
                content TEXT,
                FOREIGN KEY (user_id) REFERENCES users(id)
            );
        "#);

        self.fixture_manager.execute_fixture("test_tables")?;

        // Add cleanup
        self.fixture_manager.add_cleanup("DROP TABLE IF EXISTS posts");
        self.fixture_manager.add_cleanup("DROP TABLE IF EXISTS users");

        Ok(())
    }

    /// Teardown test data
    pub fn teardown(&self) -> UmbraResult<()> {
        self.fixture_manager.cleanup()
    }

    /// Insert test user
    pub fn insert_test_user(&self, id: i64, name: &str, email: &str) -> UmbraResult<()> {
        let query = format!("INSERT INTO users (name, email) VALUES ('{}', '{}')", name, email);
        let result = QueryResult {
            rows_affected: 1,
            last_insert_id: Some(id),
            execution_time: Duration::from_millis(5),
        };

        // Set up the expected result
        self.connection.set_execute_result(&query, result);

        // Actually execute the query to register it as executed
        let params = vec![
            DatabaseValue::String(name.to_string()),
            DatabaseValue::String(email.to_string())
        ];
        let _ = self.connection.execute(&query, &params)?;

        Ok(())
    }

    /// Set user query result
    pub fn set_user_query_result(&self, users: Vec<(i64, String, String)>) {
        let rows: Vec<Row> = users.into_iter().map(|(id, name, email)| {
            let mut row = Row::new();
            row.insert("id".to_string(), DatabaseValue::Int64(id));
            row.insert("name".to_string(), DatabaseValue::String(name));
            row.insert("email".to_string(), DatabaseValue::String(email));
            row
        }).collect();

        self.connection.set_query_result("SELECT * FROM users", rows);
    }

    /// Assert query was executed
    pub fn assert_query_executed(&self, query: &str) {
        assert!(self.connection.was_query_executed(query), 
                "Query '{}' was not executed", query);
    }

    /// Assert query execution count
    pub fn assert_query_count(&self, query: &str, expected_count: usize) {
        let actual_count = self.connection.get_query_count(query);
        assert_eq!(actual_count, expected_count, 
                   "Query '{}' was executed {} times, expected {}", 
                   query, actual_count, expected_count);
    }

    /// Get query log for debugging
    pub fn get_query_log(&self) -> Vec<QueryLogEntry> {
        self.connection.get_query_log()
    }
}

/// Test database factory
pub struct TestDatabaseFactory;

impl TestDatabaseFactory {
    /// Create in-memory SQLite database for testing
    pub fn create_in_memory_sqlite() -> UmbraResult<Arc<dyn DatabaseConnection>> {
        // In a real implementation, this would create an actual in-memory SQLite connection
        Ok(Arc::new(MockDatabaseConnection::new("in_memory_sqlite")))
    }

    /// Create test database with schema
    pub fn create_with_schema(schema_sql: &str) -> UmbraResult<Arc<dyn DatabaseConnection>> {
        let connection = Self::create_in_memory_sqlite()?;
        connection.execute(schema_sql, &[])?;
        Ok(connection)
    }

    /// Create test database with fixtures
    pub fn create_with_fixtures(fixtures: HashMap<String, String>) -> UmbraResult<Arc<dyn DatabaseConnection>> {
        let connection = Self::create_in_memory_sqlite()?;
        
        for (name, sql) in fixtures {
            connection.execute(&sql, &[])?;
        }
        
        Ok(connection)
    }
}

/// Performance testing utilities
pub struct DatabasePerformanceTester {
    connection: Arc<dyn DatabaseConnection>,
    results: Vec<PerformanceResult>,
}

#[derive(Debug, Clone)]
pub struct PerformanceResult {
    pub test_name: String,
    pub query: String,
    pub execution_time: Duration,
    pub rows_affected: u64,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

impl DatabasePerformanceTester {
    pub fn new(connection: Arc<dyn DatabaseConnection>) -> Self {
        Self {
            connection,
            results: Vec::new(),
        }
    }

    /// Run performance test
    pub fn run_test(&mut self, test_name: &str, query: &str, params: &[DatabaseValue]) -> UmbraResult<PerformanceResult> {
        let start_time = std::time::Instant::now();
        let result = self.connection.execute(query, params)?;
        let execution_time = start_time.elapsed();

        let perf_result = PerformanceResult {
            test_name: test_name.to_string(),
            query: query.to_string(),
            execution_time,
            rows_affected: result.rows_affected,
            timestamp: chrono::Utc::now(),
        };

        self.results.push(perf_result.clone());
        Ok(perf_result)
    }

    /// Run multiple iterations of a test
    pub fn run_benchmark(&mut self, test_name: &str, query: &str, params: &[DatabaseValue], iterations: usize) -> UmbraResult<BenchmarkResult> {
        let mut execution_times = Vec::new();
        let mut total_rows_affected = 0;

        for i in 0..iterations {
            let test_name_iter = format!("{}_{}", test_name, i);
            let result = self.run_test(&test_name_iter, query, params)?;
            execution_times.push(result.execution_time);
            total_rows_affected += result.rows_affected;
        }

        let avg_time = execution_times.iter().sum::<Duration>() / iterations as u32;
        let min_time = execution_times.iter().min().unwrap().clone();
        let max_time = execution_times.iter().max().unwrap().clone();

        Ok(BenchmarkResult {
            test_name: test_name.to_string(),
            iterations,
            avg_execution_time: avg_time,
            min_execution_time: min_time,
            max_execution_time: max_time,
            total_rows_affected,
        })
    }

    /// Get all results
    pub fn get_results(&self) -> &[PerformanceResult] {
        &self.results
    }

    /// Clear results
    pub fn clear_results(&mut self) {
        self.results.clear();
    }
}

#[derive(Debug, Clone)]
pub struct BenchmarkResult {
    pub test_name: String,
    pub iterations: usize,
    pub avg_execution_time: Duration,
    pub min_execution_time: Duration,
    pub max_execution_time: Duration,
    pub total_rows_affected: u64,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_mock_connection() {
        let mock = MockDatabaseConnection::new("test");
        
        // Test query result mocking
        let mut row = Row::new();
        row.insert("id".to_string(), DatabaseValue::Int64(1));
        row.insert("name".to_string(), DatabaseValue::String("John".to_string()));
        
        mock.set_query_result("SELECT * FROM users", vec![row]);
        
        let results = mock.query("SELECT * FROM users", &[]).unwrap();
        assert_eq!(results.len(), 1);
        assert_eq!(results[0].get_int("id").unwrap(), 1);
        assert_eq!(results[0].get_string("name").unwrap(), "John");
    }

    #[test]
    fn test_query_logging() {
        let mock = MockDatabaseConnection::new("test");
        
        mock.execute("INSERT INTO users VALUES (1, 'John')", &[]).unwrap();
        mock.query("SELECT * FROM users", &[]).unwrap();
        
        let log = mock.get_query_log();
        assert_eq!(log.len(), 2);
        assert!(log[0].query.contains("INSERT"));
        assert!(log[1].query.contains("SELECT"));
    }

    #[test]
    fn test_failure_simulation() {
        let mock = MockDatabaseConnection::new("test");
        mock.set_should_fail(true, Some("Simulated failure".to_string()));
        
        let result = mock.execute("SELECT 1", &[]);
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("Simulated failure"));
    }

    #[test]
    fn test_database_test_helper() {
        let mut helper = DatabaseTestHelper::new("test");
        helper.setup().unwrap();

        helper.insert_test_user(1, "John", "<EMAIL>").unwrap();
        helper.assert_query_executed("INSERT INTO users (name, email) VALUES ('John', '<EMAIL>')");

        helper.teardown().unwrap();
    }
}
