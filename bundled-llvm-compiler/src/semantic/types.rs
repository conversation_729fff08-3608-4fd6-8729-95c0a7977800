/// Type system for Umbra semantic analysis
/// 
/// This module defines the type system used during semantic analysis,
/// including type checking, inference, and validation.

use crate::error::{Umbra<PERSON><PERSON><PERSON>, UmbraResult};
use std::collections::HashMap;
use std::fmt;

/// Represents a type in the Umbra type system
#[derive(Debug, <PERSON>lone, PartialEq, Eq)]
pub enum Type {
    /// Primitive types
    Integer,
    Float,
    String,
    Boolean,
    Void,
    
    /// Collection types
    List(Box<Type>),
    Map(Box<Type>, Box<Type>),
    Tuple(Vec<Type>),
    
    /// Optional and Result types
    Option(Box<Type>),
    Result(Box<Type>, Box<Type>),
    
    /// Function types
    Function {
        params: Vec<Type>,
        return_type: Box<Type>,
    },
    
    /// User-defined types
    Struct {
        name: String,
        fields: HashMap<String, Type>,
    },
    Enum {
        name: String,
        variants: HashMap<String, Option<Type>>,
    },
    Interface {
        name: String,
        methods: HashMap<String, Type>,
    },
    
    /// Generic types
    Generic {
        name: String,
        constraints: Vec<TypeConstraint>,
    },
    
    /// Type variables for inference
    Variable(String),
    
    /// Unknown type (for inference)
    Unknown,
    
    /// Any type (for dynamic typing)
    Any,
}

/// Type constraints for generics
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum TypeConstraint {
    /// Type must implement a trait
    Implements(String),
    /// Type must be a subtype of another type
    Subtype(Type),
    /// Type must support specific operations
    Supports(Vec<String>),
}

/// Type environment for type checking
#[derive(Debug, Clone)]
pub struct TypeEnvironment {
    /// Variable types
    variables: HashMap<String, Type>,
    /// Function types
    functions: HashMap<String, Type>,
    /// Type definitions
    types: HashMap<String, TypeDefinition>,
    /// Generic type parameters
    generics: HashMap<String, Vec<TypeConstraint>>,
    /// Parent environment (for scoping)
    parent: Option<Box<TypeEnvironment>>,
}

/// Type definition
#[derive(Debug, Clone)]
pub enum TypeDefinition {
    Struct {
        fields: HashMap<String, Type>,
        methods: HashMap<String, Type>,
    },
    Enum {
        variants: HashMap<String, Option<Type>>,
    },
    Interface {
        methods: HashMap<String, Type>,
    },
    Alias(Type),
}

/// Type checker
pub struct TypeChecker {
    /// Current type environment
    env: TypeEnvironment,
    /// Type variable counter for inference
    var_counter: usize,
    /// Substitutions for type variables
    substitutions: HashMap<String, Type>,
}

impl Type {
    /// Check if this type is compatible with another type
    pub fn is_compatible_with(&self, other: &Type) -> bool {
        match (self, other) {
            // Exact matches
            (Type::Integer, Type::Integer) |
            (Type::Float, Type::Float) |
            (Type::String, Type::String) |
            (Type::Boolean, Type::Boolean) |
            (Type::Void, Type::Void) => true,
            
            // Numeric compatibility
            (Type::Integer, Type::Float) | (Type::Float, Type::Integer) => true,
            
            // Collection compatibility
            (Type::List(a), Type::List(b)) => a.is_compatible_with(b),
            (Type::Map(k1, v1), Type::Map(k2, v2)) => {
                k1.is_compatible_with(k2) && v1.is_compatible_with(v2)
            }
            (Type::Tuple(types1), Type::Tuple(types2)) => {
                types1.len() == types2.len() &&
                types1.iter().zip(types2.iter()).all(|(t1, t2)| t1.is_compatible_with(t2))
            }
            
            // Optional and Result compatibility
            (Type::Option(a), Type::Option(b)) => a.is_compatible_with(b),
            (Type::Result(ok1, err1), Type::Result(ok2, err2)) => {
                ok1.is_compatible_with(ok2) && err1.is_compatible_with(err2)
            }
            
            // Function compatibility
            (Type::Function { params: p1, return_type: r1 }, 
             Type::Function { params: p2, return_type: r2 }) => {
                p1.len() == p2.len() &&
                p1.iter().zip(p2.iter()).all(|(t1, t2)| t1.is_compatible_with(t2)) &&
                r1.is_compatible_with(r2)
            }
            
            // Any type is compatible with everything
            (Type::Any, _) | (_, Type::Any) => true,
            
            // Unknown types are compatible during inference
            (Type::Unknown, _) | (_, Type::Unknown) => true,
            
            // Variable types need substitution
            (Type::Variable(_), _) | (_, Type::Variable(_)) => true,
            
            _ => false,
        }
    }
    
    /// Get the size of this type in bytes (for code generation)
    pub fn size_bytes(&self) -> usize {
        match self {
            Type::Integer => 8,
            Type::Float => 8,
            Type::Boolean => 1,
            Type::String => 8, // Pointer to string data
            Type::List(_) => 8, // Pointer to list data
            Type::Map(_, _) => 8, // Pointer to map data
            Type::Tuple(types) => types.iter().map(|t| t.size_bytes()).sum(),
            Type::Option(_) => 9, // 1 byte for tag + 8 bytes for value
            Type::Result(_, _) => 9, // 1 byte for tag + 8 bytes for value
            Type::Function { .. } => 8, // Function pointer
            Type::Struct { fields, .. } => {
                fields.values().map(|t| t.size_bytes()).sum()
            }
            Type::Enum { .. } => 8, // Tag + largest variant
            Type::Void => 0,
            _ => 8, // Default pointer size
        }
    }
    
    /// Check if this is a primitive type
    pub fn is_primitive(&self) -> bool {
        matches!(self, Type::Integer | Type::Float | Type::String | Type::Boolean | Type::Void)
    }
    
    /// Check if this is a numeric type
    pub fn is_numeric(&self) -> bool {
        matches!(self, Type::Integer | Type::Float)
    }
    
    /// Check if this is a collection type
    pub fn is_collection(&self) -> bool {
        matches!(self, Type::List(_) | Type::Map(_, _) | Type::Tuple(_))
    }
    
    /// Get the element type for collections
    pub fn element_type(&self) -> Option<&Type> {
        match self {
            Type::List(element_type) => Some(element_type),
            Type::Option(inner_type) => Some(inner_type),
            _ => None,
        }
    }
}

impl TypeEnvironment {
    /// Create a new empty type environment
    pub fn new() -> Self {
        Self {
            variables: HashMap::new(),
            functions: HashMap::new(),
            types: HashMap::new(),
            generics: HashMap::new(),
            parent: None,
        }
    }
    
    /// Create a child environment
    pub fn child(&self) -> Self {
        Self {
            variables: HashMap::new(),
            functions: HashMap::new(),
            types: HashMap::new(),
            generics: HashMap::new(),
            parent: Some(Box::new(self.clone())),
        }
    }
    
    /// Define a variable type
    pub fn define_variable(&mut self, name: String, var_type: Type) {
        self.variables.insert(name, var_type);
    }
    
    /// Get a variable type
    pub fn get_variable(&self, name: &str) -> Option<&Type> {
        self.variables.get(name).or_else(|| {
            self.parent.as_ref().and_then(|parent| parent.get_variable(name))
        })
    }
    
    /// Define a function type
    pub fn define_function(&mut self, name: String, func_type: Type) {
        self.functions.insert(name, func_type);
    }
    
    /// Get a function type
    pub fn get_function(&self, name: &str) -> Option<&Type> {
        self.functions.get(name).or_else(|| {
            self.parent.as_ref().and_then(|parent| parent.get_function(name))
        })
    }
    
    /// Define a type
    pub fn define_type(&mut self, name: String, type_def: TypeDefinition) {
        self.types.insert(name, type_def);
    }
    
    /// Get a type definition
    pub fn get_type(&self, name: &str) -> Option<&TypeDefinition> {
        self.types.get(name).or_else(|| {
            self.parent.as_ref().and_then(|parent| parent.get_type(name))
        })
    }
}

impl TypeChecker {
    /// Create a new type checker
    pub fn new() -> Self {
        let mut env = TypeEnvironment::new();
        
        // Add built-in types and functions
        env.define_function("print".to_string(), Type::Function {
            params: vec![Type::Any],
            return_type: Box::new(Type::Void),
        });
        
        env.define_function("len".to_string(), Type::Function {
            params: vec![Type::Any],
            return_type: Box::new(Type::Integer),
        });
        
        Self {
            env,
            var_counter: 0,
            substitutions: HashMap::new(),
        }
    }
    
    /// Generate a fresh type variable
    pub fn fresh_type_var(&mut self) -> Type {
        let var_name = format!("T{}", self.var_counter);
        self.var_counter += 1;
        Type::Variable(var_name)
    }
    
    /// Apply substitutions to a type
    pub fn apply_substitutions(&self, type_expr: &Type) -> Type {
        match type_expr {
            Type::Variable(name) => {
                self.substitutions.get(name).cloned().unwrap_or_else(|| type_expr.clone())
            }
            Type::List(element_type) => {
                Type::List(Box::new(self.apply_substitutions(element_type)))
            }
            Type::Map(key_type, value_type) => {
                Type::Map(
                    Box::new(self.apply_substitutions(key_type)),
                    Box::new(self.apply_substitutions(value_type))
                )
            }
            Type::Function { params, return_type } => {
                Type::Function {
                    params: params.iter().map(|p| self.apply_substitutions(p)).collect(),
                    return_type: Box::new(self.apply_substitutions(return_type)),
                }
            }
            _ => type_expr.clone(),
        }
    }
    
    /// Unify two types
    pub fn unify(&mut self, type1: &Type, type2: &Type) -> UmbraResult<()> {
        match (type1, type2) {
            // Same types unify
            (a, b) if a == b => Ok(()),
            
            // Variable unification
            (Type::Variable(name), other) | (other, Type::Variable(name)) => {
                if let Some(existing) = self.substitutions.get(name).cloned() {
                    self.unify(&existing, other)
                } else {
                    self.substitutions.insert(name.clone(), other.clone());
                    Ok(())
                }
            }
            
            // Unknown types can unify with anything
            (Type::Unknown, other) | (other, Type::Unknown) => {
                // In a real implementation, we might want to record this for inference
                Ok(())
            }
            
            // Collection unification
            (Type::List(a), Type::List(b)) => self.unify(a, b),
            (Type::Map(k1, v1), Type::Map(k2, v2)) => {
                self.unify(k1, k2)?;
                self.unify(v1, v2)
            }
            
            // Function unification
            (Type::Function { params: p1, return_type: r1 }, 
             Type::Function { params: p2, return_type: r2 }) => {
                if p1.len() != p2.len() {
                    return Err(UmbraError::Type {
                        message: format!("Function parameter count mismatch: {} vs {}", p1.len(), p2.len()),
                        line: 0,
                        column: 0,
                        expected_type: None,
                        actual_type: None,
                    });
                }
                
                for (param1, param2) in p1.iter().zip(p2.iter()) {
                    self.unify(param1, param2)?;
                }
                
                self.unify(r1, r2)
            }
            
            // Numeric compatibility
            (Type::Integer, Type::Float) | (Type::Float, Type::Integer) => Ok(()),
            
            _ => Err(UmbraError::Type {
                message: format!("Cannot unify types: {:?} and {:?}", type1, type2),
                line: 0,
                column: 0,
                expected_type: Some(format!("{:?}", type1)),
                actual_type: Some(format!("{:?}", type2)),
            }),
        }
    }
    
    /// Get the current type environment
    pub fn env(&self) -> &TypeEnvironment {
        &self.env
    }
    
    /// Get a mutable reference to the type environment
    pub fn env_mut(&mut self) -> &mut TypeEnvironment {
        &mut self.env
    }
}

impl fmt::Display for Type {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            Type::Integer => write!(f, "Integer"),
            Type::Float => write!(f, "Float"),
            Type::String => write!(f, "String"),
            Type::Boolean => write!(f, "Boolean"),
            Type::Void => write!(f, "void"),
            Type::List(element_type) => write!(f, "List<{}>", element_type),
            Type::Map(key_type, value_type) => write!(f, "Map<{}, {}>", key_type, value_type),
            Type::Tuple(types) => {
                write!(f, "(")?;
                for (i, t) in types.iter().enumerate() {
                    if i > 0 { write!(f, ", ")?; }
                    write!(f, "{}", t)?;
                }
                write!(f, ")")
            }
            Type::Option(inner) => write!(f, "Option<{}>", inner),
            Type::Result(ok, err) => write!(f, "Result<{}, {}>", ok, err),
            Type::Function { params, return_type } => {
                write!(f, "(")?;
                for (i, param) in params.iter().enumerate() {
                    if i > 0 { write!(f, ", ")?; }
                    write!(f, "{}", param)?;
                }
                write!(f, ") -> {}", return_type)
            }
            Type::Struct { name, .. } => write!(f, "{}", name),
            Type::Enum { name, .. } => write!(f, "{}", name),
            Type::Interface { name, .. } => write!(f, "{}", name),
            Type::Generic { name, .. } => write!(f, "{}", name),
            Type::Variable(name) => write!(f, "{}", name),
            Type::Unknown => write!(f, "?"),
            Type::Any => write!(f, "any"),
        }
    }
}

impl Default for TypeEnvironment {
    fn default() -> Self {
        Self::new()
    }
}

impl Default for TypeChecker {
    fn default() -> Self {
        Self::new()
    }
}
