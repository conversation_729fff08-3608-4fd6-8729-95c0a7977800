use crate::error::SourceLocation;
use crate::parser::ast::{BasicType, Type, TraitDef, ImplDef};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// Types of symbols in the symbol table
#[derive(Debug, <PERSON>lone, PartialEq)]
pub enum SymbolType {
    Variable,
    Function,
    Structure,
    Parameter,
    Trait,
    Implementation,
}

/// Symbol information stored in the symbol table
#[derive(Debu<PERSON>, <PERSON>lone)]
pub struct Symbol {
    pub name: String,
    pub symbol_type: SymbolType,
    pub type_annotation: Type,
    #[allow(dead_code)]
    pub location: SourceLocation,
    #[allow(dead_code)]
    pub is_mutable: bool,
    pub is_initialized: bool,
    pub visibility: Visibility,
    pub module_path: Option<String>,
    // For functions: parameter types and return type
    pub function_signature: Option<FunctionSignature>,
    // For structures: field information
    pub structure_fields: Option<Vec<StructureField>>,
    // For traits: trait definition
    pub trait_definition: Option<TraitDef>,
    // For implementations: implementation definition
    pub impl_definition: Option<ImplDef>,
}

/// Visibility levels for symbols
#[derive(Debu<PERSON>, Clone, PartialEq)]
#[derive(Default)]
pub enum Visibility {
    Public,
    #[default]
    Private,
    Module,  // Visible within the same module
}


impl Default for Symbol {
    fn default() -> Self {
        Self {
            name: String::new(),
            symbol_type: SymbolType::Variable,
            type_annotation: Type::Basic(BasicType::Void),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: false,
            visibility: Visibility::default(),
            module_path: None,
            function_signature: None,
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }
}

#[derive(Debug, Clone)]
pub struct FunctionSignature {
    pub parameters: Vec<Type>,
    #[allow(dead_code)]
    pub return_type: Type,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct StructureField {
    pub name: String,
    pub field_type: Type,
}

impl Symbol {
    pub fn new_variable(
        name: String,
        type_annotation: Type,
        location: SourceLocation,
        is_mutable: bool,
    ) -> Self {
        Self {
            name,
            symbol_type: SymbolType::Variable,
            type_annotation,
            location,
            is_mutable,
            is_initialized: false,
            visibility: Visibility::Private,
            module_path: None,
            function_signature: None,
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    pub fn new_variable_with_visibility(
        name: String,
        type_annotation: Type,
        location: SourceLocation,
        is_mutable: bool,
        visibility: Visibility,
        module_path: Option<String>,
    ) -> Self {
        Self {
            name,
            symbol_type: SymbolType::Variable,
            type_annotation,
            location,
            is_mutable,
            is_initialized: false,
            visibility,
            module_path,
            function_signature: None,
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    pub fn new_function(
        name: String,
        parameters: Vec<Type>,
        return_type: Type,
        location: SourceLocation,
    ) -> Self {
        Self {
            name,
            symbol_type: SymbolType::Function,
            type_annotation: return_type.clone(),
            location,
            is_mutable: false,
            is_initialized: true,
            visibility: Visibility::Private,
            module_path: None,
            function_signature: Some(FunctionSignature {
                parameters,
                return_type,
            }),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    pub fn new_function_with_visibility(
        name: String,
        parameters: Vec<Type>,
        return_type: Type,
        location: SourceLocation,
        visibility: Visibility,
        module_path: Option<String>,
    ) -> Self {
        Self {
            name,
            symbol_type: SymbolType::Function,
            type_annotation: return_type.clone(),
            location,
            is_mutable: false,
            is_initialized: true,
            visibility,
            module_path,
            function_signature: Some(FunctionSignature {
                parameters,
                return_type,
            }),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    pub fn new_structure(
        name: String,
        fields: Vec<StructureField>,
        location: SourceLocation,
    ) -> Self {
        Self {
            name,
            symbol_type: SymbolType::Structure,
            type_annotation: Type::Basic(BasicType::Void), // Structures don't have a type per se
            location,
            is_mutable: false,
            is_initialized: true,
            visibility: Visibility::Private,
            module_path: None,
            function_signature: None,
            structure_fields: Some(fields),
            trait_definition: None,
            impl_definition: None,
        }
    }

    pub fn new_structure_with_visibility(
        name: String,
        fields: Vec<StructureField>,
        location: SourceLocation,
        visibility: Visibility,
        module_path: Option<String>,
    ) -> Self {
        Self {
            name,
            symbol_type: SymbolType::Structure,
            type_annotation: Type::Basic(BasicType::Void),
            location,
            is_mutable: false,
            is_initialized: true,
            visibility,
            module_path,
            function_signature: None,
            structure_fields: Some(fields),
            trait_definition: None,
            impl_definition: None,
        }
    }

    pub fn new_parameter(name: String, type_annotation: Type, location: SourceLocation) -> Self {
        Self {
            name,
            symbol_type: SymbolType::Parameter,
            type_annotation,
            location,
            is_mutable: false,
            is_initialized: true,
            visibility: Visibility::Private,
            module_path: None,
            function_signature: None,
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    /// Check if this symbol is visible from the given module
    pub fn is_visible_from(&self, current_module: Option<&str>) -> bool {
        match self.visibility {
            Visibility::Public => true,
            Visibility::Private => {
                // Private symbols are only visible within the same module
                match (&self.module_path, current_module) {
                    (Some(symbol_module), Some(current)) => symbol_module == current,
                    (None, None) => true, // Both in global scope
                    _ => false,
                }
            }
            Visibility::Module => {
                // Module-visible symbols are visible within the same module
                match (&self.module_path, current_module) {
                    (Some(symbol_module), Some(current)) => symbol_module == current,
                    (None, None) => true,
                    _ => false,
                }
            }
        }
    }

    pub fn new_trait(
        name: String,
        trait_def: TraitDef,
        location: SourceLocation,
    ) -> Self {
        Self {
            name,
            symbol_type: SymbolType::Trait,
            type_annotation: Type::Basic(BasicType::Void), // Traits don't have a type per se
            location,
            is_mutable: false,
            is_initialized: true,
            visibility: Visibility::default(),
            module_path: None,
            function_signature: None,
            structure_fields: None,
            trait_definition: Some(trait_def),
            impl_definition: None,
        }
    }

    pub fn new_implementation(
        name: String,
        impl_def: ImplDef,
        location: SourceLocation,
    ) -> Self {
        Self {
            name,
            symbol_type: SymbolType::Implementation,
            type_annotation: Type::Basic(BasicType::Void), // Impls don't have a type per se
            location,
            is_mutable: false,
            is_initialized: true,
            visibility: Visibility::default(),
            module_path: None,
            function_signature: None,
            structure_fields: None,
            trait_definition: None,
            impl_definition: Some(impl_def),
        }
    }

    /// Get the symbol type
    pub fn symbol_type(&self) -> &SymbolType {
        &self.symbol_type
    }
}

/// Symbol table for managing scopes and symbol resolution
#[derive(Debug)]
#[derive(Clone)]
pub struct SymbolTable {
    scopes: Vec<HashMap<String, Symbol>>,
    current_scope: usize,
}

impl SymbolTable {
    pub fn new() -> Self {
        Self {
            scopes: vec![HashMap::new()], // Global scope
            current_scope: 0,
        }
    }

    /// Create a new symbol table with pre-allocated capacity for large programs
    pub fn with_capacity(estimated_symbols: usize) -> Self {
        let global_scope = HashMap::with_capacity(estimated_symbols);
        Self {
            scopes: vec![global_scope],
            current_scope: 0,
        }
    }

    /// Reserve additional capacity in the current scope
    pub fn reserve_capacity(&mut self, additional: usize) {
        if let Some(current_scope) = self.scopes.get_mut(self.current_scope) {
            current_scope.reserve(additional);
        }
    }

    /// Enter a new scope
    pub fn enter_scope(&mut self) {
        self.scopes.push(HashMap::new());
        self.current_scope += 1;
    }

    /// Exit the current scope
    pub fn exit_scope(&mut self) {
        if self.current_scope > 0 {
            self.scopes.pop();
            self.current_scope -= 1;
        }
    }

    /// Define a symbol in the current scope
    pub fn define(&mut self, symbol: Symbol) -> Result<(), String> {
        let current_scope = &mut self.scopes[self.current_scope];

        if current_scope.contains_key(&symbol.name) {
            return Err(format!(
                "Symbol '{}' already defined in current scope",
                symbol.name
            ));
        }

        current_scope.insert(symbol.name.clone(), symbol);
        Ok(())
    }

    /// Define multiple symbols in batch for better performance
    pub fn define_batch(&mut self, symbols: Vec<Symbol>) -> Result<(), Vec<String>> {
        let mut errors = Vec::new();
        let current_scope = &mut self.scopes[self.current_scope];

        // Pre-check for conflicts
        for symbol in &symbols {
            if current_scope.contains_key(&symbol.name) {
                errors.push(format!(
                    "Symbol '{}' already defined in current scope",
                    symbol.name
                ));
            }
        }

        if !errors.is_empty() {
            return Err(errors);
        }

        // Insert all symbols
        for symbol in symbols {
            current_scope.insert(symbol.name.clone(), symbol);
        }

        Ok(())
    }

    /// Look up a symbol by name, searching from current scope to global
    pub fn lookup(&self, name: &str) -> Option<&Symbol> {
        // Search from current scope backwards to global scope
        for scope in self.scopes.iter().rev() {
            if let Some(symbol) = scope.get(name) {
                return Some(symbol);
            }
        }
        None
    }

    /// Look up a symbol with visibility checking
    pub fn lookup_with_visibility(&self, name: &str, current_module: Option<&str>) -> Option<&Symbol> {
        // Search from current scope backwards to global scope
        for scope in self.scopes.iter().rev() {
            if let Some(symbol) = scope.get(name) {
                if symbol.is_visible_from(current_module) {
                    return Some(symbol);
                }
            }
        }
        None
    }

    /// Look up a symbol only in the current scope
    #[allow(dead_code)]
    pub fn lookup_current_scope(&self, name: &str) -> Option<&Symbol> {
        self.scopes[self.current_scope].get(name)
    }

    /// Get all symbols in the current scope
    #[allow(dead_code)]
    pub fn current_scope_symbols(&self) -> impl Iterator<Item = (&String, &Symbol)> {
        self.scopes[self.current_scope].iter()
    }

    /// Check if we're in the global scope
    #[allow(dead_code)]
    pub fn is_global_scope(&self) -> bool {
        self.current_scope == 0
    }

    /// Get the current scope level
    #[allow(dead_code)]
    pub fn scope_level(&self) -> usize {
        self.current_scope
    }

    /// Get all symbols from all scopes
    #[allow(dead_code)]
    pub fn get_all_symbols(&self) -> HashMap<String, Symbol> {
        let mut all_symbols = HashMap::new();

        // Collect symbols from all scopes, with inner scopes overriding outer ones
        for scope in &self.scopes {
            for (name, symbol) in scope {
                all_symbols.insert(name.clone(), symbol.clone());
            }
        }

        all_symbols
    }

    /// Initialize built-in symbols (standard library functions, types, etc.)
    pub fn initialize_builtins(&mut self) {
        use crate::stdlib::BuiltinFunctions;

        let builtins = BuiltinFunctions::get_all();
        for builtin in builtins {
            let _ = self.define(builtin); // Ignore errors for builtins
        }
    }

    /// Get all symbols from all scopes
    pub fn symbols(&self) -> impl Iterator<Item = (&String, &Symbol)> {
        self.scopes.iter().flat_map(|scope| scope.iter())
    }

    /// Define a trait in the symbol table
    pub fn define_trait(&mut self, name: String, trait_def: TraitDef) -> Result<(), String> {
        let symbol = Symbol::new_trait(name.clone(), trait_def, SourceLocation::new(0, 0));
        self.define(symbol)
    }

    /// Define an implementation in the symbol table
    pub fn define_implementation(&mut self, name: String, impl_def: ImplDef) -> Result<(), String> {
        let symbol = Symbol::new_implementation(name.clone(), impl_def, SourceLocation::new(0, 0));
        self.define(symbol)
    }

    /// Check if a trait exists
    pub fn trait_exists(&self, trait_name: &str) -> bool {
        for scope in &self.scopes {
            if let Some(symbol) = scope.get(trait_name) {
                if matches!(symbol.symbol_type, SymbolType::Trait) {
                    return true;
                }
            }
        }
        false
    }

    /// Get trait definition
    pub fn get_trait(&self, trait_name: &str) -> Option<&TraitDef> {
        for scope in &self.scopes {
            if let Some(symbol) = scope.get(trait_name) {
                if matches!(symbol.symbol_type, SymbolType::Trait) {
                    return symbol.trait_definition.as_ref();
                }
            }
        }
        None
    }

    /// Get implementation definition
    pub fn get_implementation(&self, impl_name: &str) -> Option<&ImplDef> {
        for scope in &self.scopes {
            if let Some(symbol) = scope.get(impl_name) {
                if matches!(symbol.symbol_type, SymbolType::Implementation) {
                    return symbol.impl_definition.as_ref();
                }
            }
        }
        None
    }
}

impl Default for SymbolTable {
    fn default() -> Self {
        let mut table = Self::new();
        table.initialize_builtins();
        table
    }




}
