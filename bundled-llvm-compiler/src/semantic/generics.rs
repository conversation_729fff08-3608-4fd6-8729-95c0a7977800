/// Complete Generic Type System and Type Inference for Umbra
///
/// This module provides comprehensive support for generic types, type parameters,
/// type inference, constraint solving, and monomorphization for LLVM backend.

use crate::error::{<PERSON>bra<PERSON><PERSON><PERSON>, UmbraResult};
use crate::parser::ast::{Type, BasicType, Expression, FunctionDef};
use std::collections::{HashMap, VecDeque};
use std::fmt;

/// Advanced Type inference engine for generic types with monomorphization
pub struct TypeInference {
    /// Type variable counter for generating unique type variables
    type_var_counter: usize,
    /// Type constraints collected during inference
    constraints: Vec<TypeConstraint>,
    /// Substitution map for type variables
    substitutions: HashMap<String, Type>,
    /// Generic type definitions
    generic_types: HashMap<String, GenericTypeDefinition>,
    /// Type parameter bounds
    type_bounds: HashMap<String, Vec<String>>,
    /// Monomorphization cache for generated concrete types
    monomorphized_functions: HashMap<String, Vec<MonomorphizedFunction>>,
    /// Trait implementations registry
    trait_impls: HashMap<String, Vec<TraitImplementation>>,
    /// Higher-kinded type support
    higher_kinded_types: HashMap<String, HigherKindedType>,
    /// Type alias definitions
    type_aliases: HashMap<String, TypeAlias>,
    /// Constraint solver state
    solver_state: ConstraintSolverState,
}

/// Monomorphized function instance
#[derive(Debug, Clone)]
pub struct MonomorphizedFunction {
    pub original_name: String,
    pub mangled_name: String,
    pub type_args: Vec<Type>,
    pub signature: FunctionSignature,
    pub body: Option<Expression>, // For inline expansion
}

/// Function signature with full generic support
#[derive(Debug, Clone, PartialEq)]
pub struct FunctionSignature {
    pub name: String,
    pub type_params: Vec<TypeParameter>,
    pub params: Vec<(String, Type)>,
    pub return_type: Type,
    pub constraints: Vec<TypeConstraint>,
}

/// Trait implementation record
#[derive(Debug, Clone)]
pub struct TraitImplementation {
    pub trait_name: String,
    pub implementing_type: Type,
    pub type_params: Vec<TypeParameter>,
    pub methods: HashMap<String, FunctionSignature>,
    pub associated_types: HashMap<String, Type>,
}

/// Higher-kinded type definition (like Functor, Monad)
#[derive(Debug, Clone)]
pub struct HigherKindedType {
    pub name: String,
    pub kind: Kind,
    pub associated_types: Vec<String>,
    pub methods: Vec<FunctionSignature>,
}

/// Type alias definition
#[derive(Debug, Clone)]
pub struct TypeAlias {
    pub name: String,
    pub type_params: Vec<TypeParameter>,
    pub target_type: Type,
}

/// Kind system for higher-kinded types
#[derive(Debug, Clone, PartialEq)]
pub enum Kind {
    Type,                           // *
    Function(Box<Kind>, Box<Kind>), // * -> *
    Higher(Vec<Kind>),              // (* -> *) -> *
}

/// Constraint solver state for advanced inference
#[derive(Debug, Clone)]
pub struct ConstraintSolverState {
    pub pending_constraints: VecDeque<TypeConstraint>,
    pub solved_constraints: Vec<TypeConstraint>,
    pub inference_stack: Vec<InferenceFrame>,
    pub unification_depth: usize,
}

/// Inference frame for backtracking
#[derive(Debug, Clone)]
pub struct InferenceFrame {
    pub substitutions: HashMap<String, Type>,
    pub constraints: Vec<TypeConstraint>,
    pub context: String,
}

/// Advanced type constraint for sophisticated constraint solving
#[derive(Debug, Clone, PartialEq)]
pub enum TypeConstraint {
    /// Two types must be equal
    Equal(Type, Type),
    /// Type must implement a trait
    Implements(Type, String),
    /// Type must be a subtype of another
    Subtype(Type, Type),
    /// Type must have a specific field
    HasField(Type, String, Type),
    /// Type must be callable with specific arguments
    Callable(Type, Vec<Type>, Type),
    /// Type must satisfy multiple constraints (conjunction)
    And(Vec<TypeConstraint>),
    /// Type must satisfy at least one constraint (disjunction)
    Or(Vec<TypeConstraint>),
    /// Type must not satisfy a constraint (negation)
    Not(Box<TypeConstraint>),
    /// Type must be constructible with given arguments
    Constructible(Type, Vec<Type>),
    /// Type must support specific operations
    SupportsOperation(Type, String, Vec<Type>, Type),
    /// Type must be convertible to another type
    Convertible(Type, Type),
    /// Type must have specific associated type
    HasAssociatedType(Type, String, Type),
    /// Type must satisfy lifetime constraints
    Lifetime(Type, String, Vec<String>),
    /// Type must be Send (thread-safe)
    Send(Type),
    /// Type must be Sync (shareable between threads)
    Sync(Type),
    /// Type must be Copy (bitwise copyable)
    Copy(Type),
    /// Type must be Clone (explicitly cloneable)
    Clone(Type),
    /// Type must be Default (has default value)
    Default(Type),
    /// Type must be Debug (debuggable)
    Debug(Type),
    /// Type must be PartialEq (partially comparable)
    PartialEq(Type),
    /// Type must be Eq (fully comparable)
    Eq(Type),
    /// Type must be PartialOrd (partially orderable)
    PartialOrd(Type),
    /// Type must be Ord (fully orderable)
    Ord(Type),
    /// Type must be Hash (hashable)
    Hash(Type),
    /// Type must be Serialize (serializable)
    Serialize(Type),
    /// Type must be Deserialize (deserializable)
    Deserialize(Type),
}

/// Generic type definition
#[derive(Debug, Clone)]
pub struct GenericTypeDefinition {
    pub name: String,
    pub type_parameters: Vec<TypeParameter>,
    pub constraints: Vec<TypeConstraint>,
    pub definition: Type,
}

/// Advanced type parameter with comprehensive bounds and variance
#[derive(Debug, Clone, PartialEq)]
pub struct TypeParameter {
    pub name: String,
    pub bounds: Vec<TypeBound>,
    pub default: Option<Type>,
    pub variance: Variance,
    pub kind: Kind,
    pub lifetime_bounds: Vec<String>,
}

/// Type bound for type parameters
#[derive(Debug, Clone, PartialEq)]
pub enum TypeBound {
    /// Must implement a trait
    Trait(String),
    /// Must be a subtype
    Subtype(Type),
    /// Must have specific lifetime
    Lifetime(String),
    /// Must satisfy custom constraint
    Custom(TypeConstraint),
    /// Higher-ranked trait bound (for<'a> F: Fn(&'a str))
    HigherRanked(Vec<String>, Box<TypeBound>),
}

/// Variance annotation for type parameters
#[derive(Debug, Clone, PartialEq)]
pub enum Variance {
    Covariant,     // +T
    Contravariant, // -T
    Invariant,     // T
    Bivariant,     // *T (rare)
}

impl TypeInference {
    pub fn new() -> Self {
        let mut inference = Self {
            type_var_counter: 0,
            constraints: Vec::new(),
            substitutions: HashMap::new(),
            generic_types: HashMap::new(),
            type_bounds: HashMap::new(),
            monomorphized_functions: HashMap::new(),
            trait_impls: HashMap::new(),
            higher_kinded_types: HashMap::new(),
            type_aliases: HashMap::new(),
            solver_state: ConstraintSolverState {
                pending_constraints: VecDeque::new(),
                solved_constraints: Vec::new(),
                inference_stack: Vec::new(),
                unification_depth: 0,
            },
        };

        inference.register_builtin_generics();
        inference.register_builtin_traits();
        inference.register_higher_kinded_types();
        inference
    }

    /// Register built-in trait implementations
    fn register_builtin_traits(&mut self) {
        // Register common trait implementations for primitive types
        let primitive_types = vec![
            Type::Basic(BasicType::Integer),
            Type::Basic(BasicType::Float),
            Type::Basic(BasicType::String),
            Type::Basic(BasicType::Boolean),
        ];

        for prim_type in primitive_types {
            // Clone trait
            self.register_trait_impl(TraitImplementation {
                trait_name: "Clone".to_string(),
                implementing_type: prim_type.clone(),
                type_params: vec![],
                methods: HashMap::new(),
                associated_types: HashMap::new(),
            });

            // Copy trait (except String)
            if !matches!(prim_type, Type::Basic(BasicType::String)) {
                self.register_trait_impl(TraitImplementation {
                    trait_name: "Copy".to_string(),
                    implementing_type: prim_type.clone(),
                    type_params: vec![],
                    methods: HashMap::new(),
                    associated_types: HashMap::new(),
                });
            }

            // Debug trait
            self.register_trait_impl(TraitImplementation {
                trait_name: "Debug".to_string(),
                implementing_type: prim_type.clone(),
                type_params: vec![],
                methods: HashMap::new(),
                associated_types: HashMap::new(),
            });

            // PartialEq trait
            self.register_trait_impl(TraitImplementation {
                trait_name: "PartialEq".to_string(),
                implementing_type: prim_type.clone(),
                type_params: vec![],
                methods: HashMap::new(),
                associated_types: HashMap::new(),
            });

            // Eq trait
            self.register_trait_impl(TraitImplementation {
                trait_name: "Eq".to_string(),
                implementing_type: prim_type.clone(),
                type_params: vec![],
                methods: HashMap::new(),
                associated_types: HashMap::new(),
            });
        }

        // Hash trait for hashable types
        let hashable_types = vec![
            Type::Basic(BasicType::Integer),
            Type::Basic(BasicType::String),
            Type::Basic(BasicType::Boolean),
        ];

        for hash_type in hashable_types {
            self.register_trait_impl(TraitImplementation {
                trait_name: "Hash".to_string(),
                implementing_type: hash_type,
                type_params: vec![],
                methods: HashMap::new(),
                associated_types: HashMap::new(),
            });
        }
    }

    /// Register higher-kinded types like Functor, Monad
    fn register_higher_kinded_types(&mut self) {
        // Functor
        self.higher_kinded_types.insert("Functor".to_string(), HigherKindedType {
            name: "Functor".to_string(),
            kind: Kind::Function(Box::new(Kind::Type), Box::new(Kind::Type)),
            associated_types: vec![],
            methods: vec![
                FunctionSignature {
                    name: "map".to_string(),
                    type_params: vec![
                        TypeParameter {
                            name: "A".to_string(),
                            bounds: vec![],
                            default: None,
                            variance: Variance::Covariant,
                            kind: Kind::Type,
                            lifetime_bounds: vec![],
                        },
                        TypeParameter {
                            name: "B".to_string(),
                            bounds: vec![],
                            default: None,
                            variance: Variance::Covariant,
                            kind: Kind::Type,
                            lifetime_bounds: vec![],
                        },
                    ],
                    params: vec![
                        ("self".to_string(), Type::TypeParameter { name: "F".to_string(), bounds: vec![] }),
                        ("f".to_string(), Type::Function(
                            vec![Type::TypeParameter { name: "A".to_string(), bounds: vec![] }],
                            Box::new(Type::TypeParameter { name: "B".to_string(), bounds: vec![] }),
                        )),
                    ],
                    return_type: Type::TypeParameter { name: "F".to_string(), bounds: vec![] },
                    constraints: vec![],
                }
            ],
        });

        // Monad
        self.higher_kinded_types.insert("Monad".to_string(), HigherKindedType {
            name: "Monad".to_string(),
            kind: Kind::Function(Box::new(Kind::Type), Box::new(Kind::Type)),
            associated_types: vec![],
            methods: vec![
                FunctionSignature {
                    name: "bind".to_string(),
                    type_params: vec![
                        TypeParameter {
                            name: "A".to_string(),
                            bounds: vec![],
                            default: None,
                            variance: Variance::Covariant,
                            kind: Kind::Type,
                            lifetime_bounds: vec![],
                        },
                        TypeParameter {
                            name: "B".to_string(),
                            bounds: vec![],
                            default: None,
                            variance: Variance::Covariant,
                            kind: Kind::Type,
                            lifetime_bounds: vec![],
                        },
                    ],
                    params: vec![
                        ("self".to_string(), Type::TypeParameter { name: "M".to_string(), bounds: vec![] }),
                        ("f".to_string(), Type::Function(
                            vec![Type::TypeParameter { name: "A".to_string(), bounds: vec![] }],
                            Box::new(Type::TypeParameter { name: "M".to_string(), bounds: vec![] }),
                        )),
                    ],
                    return_type: Type::TypeParameter { name: "M".to_string(), bounds: vec![] },
                    constraints: vec![],
                }
            ],
        });
    }

    /// Register built-in generic types
    fn register_builtin_generics(&mut self) {
        // List[T]
        self.register_generic_type(GenericTypeDefinition {
            name: "List".to_string(),
            type_parameters: vec![TypeParameter {
                name: "T".to_string(),
                bounds: vec![],
                default: None,
                variance: Variance::Covariant,
                kind: Kind::Type,
                lifetime_bounds: vec![],
            }],
            constraints: vec![],
            definition: Type::List(Box::new(Type::TypeParameter {
                name: "T".to_string(),
                bounds: vec![],
            })),
        });

        // Map[K, V]
        self.register_generic_type(GenericTypeDefinition {
            name: "Map".to_string(),
            type_parameters: vec![
                TypeParameter {
                    name: "K".to_string(),
                    bounds: vec![
                        TypeBound::Trait("Hash".to_string()),
                        TypeBound::Trait("Eq".to_string()),
                    ],
                    default: None,
                    variance: Variance::Invariant,
                    kind: Kind::Type,
                    lifetime_bounds: vec![],
                },
                TypeParameter {
                    name: "V".to_string(),
                    bounds: vec![],
                    default: None,
                    variance: Variance::Covariant,
                    kind: Kind::Type,
                    lifetime_bounds: vec![],
                },
            ],
            constraints: vec![
                TypeConstraint::Hash(Type::TypeParameter { name: "K".to_string(), bounds: vec![] }),
                TypeConstraint::Eq(Type::TypeParameter { name: "K".to_string(), bounds: vec![] }),
            ],
            definition: Type::HashMap(
                Box::new(Type::TypeParameter { name: "K".to_string(), bounds: vec![] }),
                Box::new(Type::TypeParameter { name: "V".to_string(), bounds: vec![] }),
            ),
        });

        // Set[T]
        self.register_generic_type(GenericTypeDefinition {
            name: "Set".to_string(),
            type_parameters: vec![TypeParameter {
                name: "T".to_string(),
                bounds: vec![
                    TypeBound::Trait("Hash".to_string()),
                    TypeBound::Trait("Eq".to_string()),
                ],
                default: None,
                variance: Variance::Invariant,
                kind: Kind::Type,
                lifetime_bounds: vec![],
            }],
            constraints: vec![
                TypeConstraint::Implements(
                    Type::TypeParameter { name: "T".to_string(), bounds: vec![] },
                    "Hash".to_string()
                ),
                TypeConstraint::Implements(
                    Type::TypeParameter { name: "T".to_string(), bounds: vec![] },
                    "Eq".to_string()
                ),
            ],
            definition: Type::HashSet(Box::new(Type::TypeParameter {
                name: "T".to_string(),
                bounds: vec![],
            })),
        });

        // Option[T]
        self.register_generic_type(GenericTypeDefinition {
            name: "Option".to_string(),
            type_parameters: vec![TypeParameter {
                name: "T".to_string(),
                bounds: vec![],
                default: None,
                variance: Variance::Covariant,
                kind: Kind::Type,
                lifetime_bounds: vec![],
            }],
            constraints: vec![],
            definition: Type::Optional(Box::new(Type::TypeParameter {
                name: "T".to_string(),
                bounds: vec![],
            })),
        });

        // Result[T, E]
        self.register_generic_type(GenericTypeDefinition {
            name: "Result".to_string(),
            type_parameters: vec![
                TypeParameter {
                    name: "T".to_string(),
                    bounds: vec![],
                    default: None,
                    variance: Variance::Covariant,
                    kind: Kind::Type,
                    lifetime_bounds: vec![],
                },
                TypeParameter {
                    name: "E".to_string(),
                    bounds: vec![TypeBound::Trait("Error".to_string())],
                    default: Some(Type::Basic(BasicType::String)),
                    variance: Variance::Covariant,
                    kind: Kind::Type,
                    lifetime_bounds: vec![],
                },
            ],
            constraints: vec![
                TypeConstraint::Implements(
                    Type::TypeParameter { name: "E".to_string(), bounds: vec![] },
                    "Error".to_string()
                ),
            ],
            definition: Type::Result(
                Box::new(Type::TypeParameter { name: "T".to_string(), bounds: vec![] }),
                Box::new(Type::TypeParameter { name: "E".to_string(), bounds: vec![] }),
            ),
        });
    }

    /// Register a generic type definition
    pub fn register_generic_type(&mut self, definition: GenericTypeDefinition) {
        self.generic_types.insert(definition.name.clone(), definition);
    }

    /// Register a trait implementation
    pub fn register_trait_impl(&mut self, implementation: TraitImplementation) {
        self.trait_impls
            .entry(implementation.trait_name.clone())
            .or_insert_with(Vec::new)
            .push(implementation);
    }

    /// Monomorphize a generic function with concrete type arguments
    pub fn monomorphize_function(
        &mut self,
        function_name: &str,
        type_args: &[Type],
        function_decl: &FunctionDef,
    ) -> UmbraResult<MonomorphizedFunction> {
        // Generate mangled name for the monomorphized function
        let mangled_name = self.mangle_function_name(function_name, type_args);

        // Check if already monomorphized
        if let Some(existing_functions) = self.monomorphized_functions.get(function_name) {
            for existing in existing_functions {
                if existing.type_args == type_args {
                    return Ok(existing.clone());
                }
            }
        }

        // Create type substitution map
        let mut substitutions = HashMap::new();
        if function_decl.type_params.len() != type_args.len() {
            return Err(UmbraError::Type {
                message: format!(
                    "Function {} expects {} type arguments, got {}",
                    function_name,
                    function_decl.type_params.len(),
                    type_args.len()
                ),
                line: 0,
                column: 0,
                expected_type: Some(format!("{} type arguments", function_decl.type_params.len())),
                actual_type: Some(format!("{} type arguments", type_args.len())),
            });
        }

        for (param, arg) in function_decl.type_params.iter().zip(type_args.iter()) {
            substitutions.insert(param.name.clone(), arg.clone());
        }

        // Substitute types in function signature
        let mut monomorphized_params = Vec::new();
        for param in &function_decl.parameters {
            let substituted_type = self.substitute_type_in_type(&param.type_annotation, &substitutions);
            monomorphized_params.push((param.name.clone(), substituted_type));
        }

        let monomorphized_return_type = self.substitute_type_in_type(&function_decl.return_type, &substitutions);

        let signature = FunctionSignature {
            name: mangled_name.clone(),
            type_params: vec![], // No type params in monomorphized version
            params: monomorphized_params,
            return_type: monomorphized_return_type,
            constraints: vec![], // Constraints should be resolved
        };

        let monomorphized = MonomorphizedFunction {
            original_name: function_name.to_string(),
            mangled_name,
            type_args: type_args.to_vec(),
            signature,
            body: None, // Body would be converted from Vec<Statement> to Expression during code generation
        };

        // Cache the monomorphized function
        self.monomorphized_functions
            .entry(function_name.to_string())
            .or_insert_with(Vec::new)
            .push(monomorphized.clone());

        Ok(monomorphized)
    }

    /// Generate mangled name for monomorphized function
    pub fn mangle_function_name(&self, function_name: &str, type_args: &[Type]) -> String {
        let mut mangled = format!("_U{}{}", function_name.len(), function_name);

        for type_arg in type_args {
            mangled.push_str(&self.mangle_type(type_arg));
        }

        mangled
    }

    /// Mangle a type for name generation
    fn mangle_type(&self, typ: &Type) -> String {
        match typ {
            Type::Basic(BasicType::Integer) => "i".to_string(),
            Type::Basic(BasicType::Float) => "f".to_string(),
            Type::Basic(BasicType::String) => "s".to_string(),
            Type::Basic(BasicType::Boolean) => "b".to_string(),
            Type::List(inner) => format!("L{}", self.mangle_type(inner)),
            Type::HashMap(k, v) => format!("M{}{}", self.mangle_type(k), self.mangle_type(v)),
            Type::HashSet(inner) => format!("S{}", self.mangle_type(inner)),
            Type::Optional(inner) => format!("O{}", self.mangle_type(inner)),
            Type::Result(ok, err) => format!("R{}{}", self.mangle_type(ok), self.mangle_type(err)),
            Type::TypeParameter { name, .. } => format!("T{}{}", name.len(), name),
            Type::Generic(name, args) => {
                let mut mangled = format!("G{}{}", name.len(), name);
                for arg in args {
                    mangled.push_str(&self.mangle_type(arg));
                }
                mangled
            }
            _ => "X".to_string(), // Unknown type
        }
    }

    /// Substitute type parameters in a type
    fn substitute_type_in_type(&self, typ: &Type, substitutions: &HashMap<String, Type>) -> Type {
        match typ {
            Type::TypeParameter { name, .. } => {
                substitutions.get(name).cloned().unwrap_or_else(|| typ.clone())
            }
            Type::List(inner) => Type::List(Box::new(self.substitute_type_in_type(inner, substitutions))),
            Type::HashMap(k, v) => Type::HashMap(
                Box::new(self.substitute_type_in_type(k, substitutions)),
                Box::new(self.substitute_type_in_type(v, substitutions)),
            ),
            Type::HashSet(inner) => Type::HashSet(Box::new(self.substitute_type_in_type(inner, substitutions))),
            Type::Optional(inner) => Type::Optional(Box::new(self.substitute_type_in_type(inner, substitutions))),
            Type::Result(ok, err) => Type::Result(
                Box::new(self.substitute_type_in_type(ok, substitutions)),
                Box::new(self.substitute_type_in_type(err, substitutions)),
            ),
            Type::Generic(name, args) => Type::Generic(
                name.clone(),
                args.iter().map(|arg| self.substitute_type_in_type(arg, substitutions)).collect(),
            ),
            _ => typ.clone(),
        }
    }

    /// Generate a fresh type variable
    pub fn fresh_type_var(&mut self) -> Type {
        let var_id = self.type_var_counter as u32;
        self.type_var_counter += 1;
        Type::TypeVariable(var_id)
    }

    /// Infer the type of an expression
    pub fn infer_expression_type(&mut self, expr: &Expression) -> UmbraResult<Type> {
        match expr {
            Expression::Literal(lit) => Ok(self.infer_literal_type(lit)),
            Expression::Identifier(id) => self.infer_identifier_type(id),
            Expression::Binary(bin) => self.infer_binary_type(bin),
            Expression::Call(call) => self.infer_call_type(call),
            Expression::List(list) => self.infer_list_type(list),
            Expression::IndexAccess(index) => self.infer_index_type(index),
            _ => Ok(Type::Auto), // Fallback for unimplemented expressions
        }
    }

    /// Infer type of a literal
    fn infer_literal_type(&self, literal: &crate::parser::ast::Literal) -> Type {
        match literal {
            crate::parser::ast::Literal::Integer(_) => Type::Basic(BasicType::Integer),
            crate::parser::ast::Literal::Float(_) => Type::Basic(BasicType::Float),
            crate::parser::ast::Literal::String(_) => Type::Basic(BasicType::String),
            crate::parser::ast::Literal::Boolean(_) => Type::Basic(BasicType::Boolean),
        }
    }

    /// Infer type of an identifier
    fn infer_identifier_type(&self, _id: &crate::parser::ast::Identifier) -> UmbraResult<Type> {
        // This would look up the identifier in the symbol table
        // For now, return a type variable
        Ok(Type::Auto)
    }

    /// Infer type of a binary expression
    fn infer_binary_type(&mut self, _bin: &crate::parser::ast::BinaryOp) -> UmbraResult<Type> {
        // This would infer types of operands and determine result type
        Ok(Type::Auto)
    }

    /// Infer type of a function call
    fn infer_call_type(&mut self, _call: &crate::parser::ast::FunctionCall) -> UmbraResult<Type> {
        // This would infer the function type and apply it to arguments
        Ok(Type::Auto)
    }

    /// Infer type of a list expression
    fn infer_list_type(&mut self, list: &crate::parser::ast::ListLiteral) -> UmbraResult<Type> {
        if list.elements.is_empty() {
            // Empty list gets a fresh type variable
            let element_type = self.fresh_type_var();
            return Ok(Type::List(Box::new(element_type)));
        }

        // Infer type of first element
        let first_type = self.infer_expression_type(&list.elements[0])?;
        
        // All elements must have the same type
        for element in &list.elements[1..] {
            let element_type = self.infer_expression_type(element)?;
            self.add_constraint(TypeConstraint::Equal(first_type.clone(), element_type));
        }

        Ok(Type::List(Box::new(first_type)))
    }

    /// Infer type of an index expression
    fn infer_index_type(&mut self, _index: &crate::parser::ast::IndexAccess) -> UmbraResult<Type> {
        // This would infer the container type and index type
        Ok(Type::Auto)
    }

    /// Add a type constraint
    pub fn add_constraint(&mut self, constraint: TypeConstraint) {
        self.constraints.push(constraint);
    }

    /// Advanced constraint solver with backtracking and sophisticated inference
    pub fn solve_constraints(&mut self) -> UmbraResult<()> {
        // Initialize solver state
        self.solver_state.pending_constraints.extend(self.constraints.drain(..));

        while let Some(constraint) = self.solver_state.pending_constraints.pop_front() {
            if self.solver_state.solved_constraints.contains(&constraint) {
                continue;
            }

            // Create inference frame for backtracking
            let frame = InferenceFrame {
                substitutions: self.substitutions.clone(),
                constraints: vec![constraint.clone()],
                context: format!("Solving constraint: {:?}", constraint),
            };
            self.solver_state.inference_stack.push(frame);

            match self.solve_single_constraint(&constraint) {
                Ok(true) => {
                    self.solver_state.solved_constraints.push(constraint);
                    self.solver_state.inference_stack.pop();
                }
                Ok(false) => {
                    // Constraint not yet solvable, re-queue
                    self.solver_state.pending_constraints.push_back(constraint);
                    self.solver_state.inference_stack.pop();
                }
                Err(e) => {
                    // Try backtracking
                    if self.try_backtrack() {
                        continue;
                    } else {
                        return Err(e);
                    }
                }
            }

            // Prevent infinite loops
            if self.solver_state.unification_depth > 1000 {
                return Err(UmbraError::Type {
                    message: "Type inference exceeded maximum depth".to_string(),
                    line: 0,
                    column: 0,
                    expected_type: None,
                    actual_type: None,
                });
            }
        }

        Ok(())
    }

    /// Solve a single constraint
    fn solve_single_constraint(&mut self, constraint: &TypeConstraint) -> UmbraResult<bool> {
        match constraint {
            TypeConstraint::Equal(t1, t2) => {
                self.solver_state.unification_depth += 1;
                let result = self.unify_advanced(t1, t2);
                self.solver_state.unification_depth -= 1;
                result.map(|_| true)
            }
            TypeConstraint::Implements(typ, trait_name) => {
                Ok(self.check_trait_implementation_advanced(typ, trait_name))
            }
            TypeConstraint::Subtype(sub, sup) => {
                Ok(self.check_subtype_relation(sub, sup))
            }
            TypeConstraint::HasField(typ, field_name, field_type) => {
                Ok(self.check_field_access(typ, field_name, field_type))
            }
            TypeConstraint::Callable(func_type, arg_types, return_type) => {
                Ok(self.check_callable(func_type, arg_types, return_type))
            }
            TypeConstraint::And(constraints) => {
                for sub_constraint in constraints {
                    if !self.solve_single_constraint(sub_constraint)? {
                        return Ok(false);
                    }
                }
                Ok(true)
            }
            TypeConstraint::Or(constraints) => {
                for sub_constraint in constraints {
                    if self.solve_single_constraint(sub_constraint).unwrap_or(false) {
                        return Ok(true);
                    }
                }
                Ok(false)
            }
            TypeConstraint::Not(constraint) => {
                Ok(!self.solve_single_constraint(constraint).unwrap_or(false))
            }
            TypeConstraint::Hash(typ) => {
                Ok(self.check_trait_implementation_advanced(typ, "Hash"))
            }
            TypeConstraint::Clone(typ) => {
                Ok(self.check_trait_implementation_advanced(typ, "Clone"))
            }
            TypeConstraint::Copy(typ) => {
                Ok(self.check_trait_implementation_advanced(typ, "Copy"))
            }
            TypeConstraint::Debug(typ) => {
                Ok(self.check_trait_implementation_advanced(typ, "Debug"))
            }
            TypeConstraint::PartialEq(typ) => {
                Ok(self.check_trait_implementation_advanced(typ, "PartialEq"))
            }
            TypeConstraint::Eq(typ) => {
                Ok(self.check_trait_implementation_advanced(typ, "Eq"))
            }
            TypeConstraint::Send(typ) => {
                Ok(self.check_send_trait(typ))
            }
            TypeConstraint::Sync(typ) => {
                Ok(self.check_sync_trait(typ))
            }
            _ => {
                // For now, assume other constraints are satisfied
                Ok(true)
            }
        }
    }

    /// Try backtracking when constraint solving fails
    fn try_backtrack(&mut self) -> bool {
        if let Some(frame) = self.solver_state.inference_stack.pop() {
            // Restore previous state
            self.substitutions = frame.substitutions;
            // Try alternative approach or skip constraint
            true
        } else {
            false
        }
    }

    /// Advanced unification with occurs check and variance
    fn unify_advanced(&mut self, t1: &Type, t2: &Type) -> UmbraResult<()> {
        // Occurs check to prevent infinite types
        if self.occurs_check(t1, t2) || self.occurs_check(t2, t1) {
            return Err(UmbraError::Type {
                message: "Occurs check failed - would create infinite type".to_string(),
                line: 0,
                column: 0,
                expected_type: Some(format!("{:?}", t1)),
                actual_type: Some(format!("{:?}", t2)),
            });
        }

        match (t1, t2) {
            (Type::Auto, _) | (_, Type::Auto) => Ok(()),
            (Type::TypeParameter { name: n1, .. }, Type::TypeParameter { name: n2, .. }) if n1 == n2 => Ok(()),
            (Type::TypeParameter { name, .. }, t) | (t, Type::TypeParameter { name, .. }) => {
                self.substitutions.insert(name.clone(), t.clone());
                Ok(())
            }
            (Type::TypeVariable(id1), Type::TypeVariable(id2)) if id1 == id2 => Ok(()),
            (Type::TypeVariable(id), t) | (t, Type::TypeVariable(id)) => {
                let var_key = format!("'{}", id);
                self.substitutions.insert(var_key, t.clone());
                Ok(())
            }
            (Type::Basic(b1), Type::Basic(b2)) if b1 == b2 => Ok(()),
            (Type::List(t1), Type::List(t2)) => self.unify_advanced(t1, t2),
            (Type::HashMap(k1, v1), Type::HashMap(k2, v2)) => {
                self.unify_advanced(k1, k2)?;
                self.unify_advanced(v1, v2)
            }
            (Type::HashSet(t1), Type::HashSet(t2)) => self.unify_advanced(t1, t2),
            (Type::Optional(t1), Type::Optional(t2)) => self.unify_advanced(t1, t2),
            (Type::Result(ok1, err1), Type::Result(ok2, err2)) => {
                self.unify_advanced(ok1, ok2)?;
                self.unify_advanced(err1, err2)
            }
            (Type::Generic(name1, args1), Type::Generic(name2, args2)) if name1 == name2 => {
                if args1.len() != args2.len() {
                    return Err(UmbraError::Type {
                        message: format!("Generic type {} has different number of arguments", name1),
                        line: 0,
                        column: 0,
                        expected_type: Some(format!("{} arguments", args1.len())),
                        actual_type: Some(format!("{} arguments", args2.len())),
                    });
                }
                for (arg1, arg2) in args1.iter().zip(args2.iter()) {
                    self.unify_advanced(arg1, arg2)?;
                }
                Ok(())
            }
            _ => Err(UmbraError::Type {
                message: format!("Cannot unify types: {:?} and {:?}", t1, t2),
                line: 0,
                column: 0,
                expected_type: Some(format!("{:?}", t1)),
                actual_type: Some(format!("{:?}", t2)),
            }),
        }
    }

    /// Occurs check to prevent infinite types
    fn occurs_check(&self, var_type: &Type, check_type: &Type) -> bool {
        if let Type::TypeParameter { name: var_name, .. } = var_type {
            self.occurs_in_type(var_name, check_type)
        } else {
            false
        }
    }

    /// Check if a type variable occurs in a type
    fn occurs_in_type(&self, var_name: &str, typ: &Type) -> bool {
        match typ {
            Type::TypeParameter { name, .. } => name == var_name,
            Type::List(inner) => self.occurs_in_type(var_name, inner),
            Type::HashMap(k, v) => self.occurs_in_type(var_name, k) || self.occurs_in_type(var_name, v),
            Type::HashSet(inner) => self.occurs_in_type(var_name, inner),
            Type::Optional(inner) => self.occurs_in_type(var_name, inner),
            Type::Result(ok, err) => self.occurs_in_type(var_name, ok) || self.occurs_in_type(var_name, err),
            Type::Generic(_, args) => args.iter().any(|arg| self.occurs_in_type(var_name, arg)),
            _ => false,
        }
    }

    /// Unify two types
    fn unify(&mut self, t1: &Type, t2: &Type) -> UmbraResult<bool> {
        match (t1, t2) {
            (Type::Auto, _) | (_, Type::Auto) => Ok(true),
            (Type::TypeParameter { name: n1, .. }, Type::TypeParameter { name: n2, .. }) if n1 == n2 => Ok(false),
            (Type::TypeParameter { name, .. }, t) | (t, Type::TypeParameter { name, .. }) => {
                self.substitutions.insert(name.clone(), t.clone());
                Ok(true)
            }
            (Type::Basic(b1), Type::Basic(b2)) => Ok(b1 == b2),
            (Type::List(t1), Type::List(t2)) => self.unify(t1, t2),
            (Type::HashMap(k1, v1), Type::HashMap(k2, v2)) => {
                let k_unified = self.unify(k1, k2)?;
                let v_unified = self.unify(v1, v2)?;
                Ok(k_unified || v_unified)
            }
            (Type::HashSet(t1), Type::HashSet(t2)) => self.unify(t1, t2),
            (Type::Optional(t1), Type::Optional(t2)) => self.unify(t1, t2),
            (Type::Result(ok1, err1), Type::Result(ok2, err2)) => {
                let ok_unified = self.unify(ok1, ok2)?;
                let err_unified = self.unify(err1, err2)?;
                Ok(ok_unified || err_unified)
            }
            _ => Ok(false),
        }
    }

    /// Advanced trait implementation checking
    fn check_trait_implementation_advanced(&self, typ: &Type, trait_name: &str) -> bool {
        // Check registered trait implementations
        if let Some(implementations) = self.trait_impls.get(trait_name) {
            for impl_record in implementations {
                if self.type_matches_impl(typ, &impl_record.implementing_type) {
                    return true;
                }
            }
        }

        // Check built-in trait implementations
        match trait_name {
            "Clone" => self.check_clone_trait(typ),
            "Copy" => self.check_copy_trait(typ),
            "Debug" => self.check_debug_trait(typ),
            "PartialEq" => self.check_partial_eq_trait(typ),
            "Eq" => self.check_eq_trait(typ),
            "Hash" => self.check_hash_trait(typ),
            "Send" => self.check_send_trait(typ),
            "Sync" => self.check_sync_trait(typ),
            _ => false,
        }
    }

    /// Check if a type matches a trait implementation
    fn type_matches_impl(&self, typ: &Type, impl_type: &Type) -> bool {
        match (typ, impl_type) {
            (Type::Basic(b1), Type::Basic(b2)) => b1 == b2,
            (Type::List(t1), Type::List(t2)) => self.type_matches_impl(t1, t2),
            (Type::HashMap(k1, v1), Type::HashMap(k2, v2)) => {
                self.type_matches_impl(k1, k2) && self.type_matches_impl(v1, v2)
            }
            (Type::TypeParameter { .. }, _) => true, // Type parameters can match anything
            _ => typ == impl_type,
        }
    }

    /// Check Clone trait implementation
    fn check_clone_trait(&self, typ: &Type) -> bool {
        match typ {
            Type::Basic(_) => true, // All basic types are Clone
            Type::List(inner) => self.check_clone_trait(inner),
            Type::HashMap(k, v) => self.check_clone_trait(k) && self.check_clone_trait(v),
            Type::HashSet(inner) => self.check_clone_trait(inner),
            Type::Optional(inner) => self.check_clone_trait(inner),
            Type::Result(ok, err) => self.check_clone_trait(ok) && self.check_clone_trait(err),
            Type::TypeParameter { .. } => true, // Assume type parameters are Clone
            _ => false,
        }
    }

    /// Check Copy trait implementation
    fn check_copy_trait(&self, typ: &Type) -> bool {
        match typ {
            Type::Basic(BasicType::Integer) | Type::Basic(BasicType::Float) | Type::Basic(BasicType::Boolean) => true,
            Type::Basic(BasicType::String) => false, // String is not Copy
            Type::TypeParameter { .. } => false, // Conservative assumption
            _ => false, // Most complex types are not Copy
        }
    }

    /// Check Debug trait implementation
    fn check_debug_trait(&self, typ: &Type) -> bool {
        match typ {
            Type::Basic(_) => true,
            Type::List(inner) => self.check_debug_trait(inner),
            Type::HashMap(k, v) => self.check_debug_trait(k) && self.check_debug_trait(v),
            Type::HashSet(inner) => self.check_debug_trait(inner),
            Type::Optional(inner) => self.check_debug_trait(inner),
            Type::Result(ok, err) => self.check_debug_trait(ok) && self.check_debug_trait(err),
            Type::TypeParameter { .. } => true,
            _ => false,
        }
    }

    /// Check PartialEq trait implementation
    fn check_partial_eq_trait(&self, typ: &Type) -> bool {
        match typ {
            Type::Basic(_) => true,
            Type::List(inner) => self.check_partial_eq_trait(inner),
            Type::HashMap(k, v) => self.check_partial_eq_trait(k) && self.check_partial_eq_trait(v),
            Type::HashSet(inner) => self.check_partial_eq_trait(inner),
            Type::Optional(inner) => self.check_partial_eq_trait(inner),
            Type::Result(ok, err) => self.check_partial_eq_trait(ok) && self.check_partial_eq_trait(err),
            Type::TypeParameter { .. } => true,
            _ => false,
        }
    }

    /// Check Eq trait implementation
    fn check_eq_trait(&self, typ: &Type) -> bool {
        // Eq requires PartialEq and additional properties
        self.check_partial_eq_trait(typ) && match typ {
            Type::Basic(BasicType::Float) => false, // Float is not Eq due to NaN
            _ => true,
        }
    }

    /// Check Hash trait implementation
    fn check_hash_trait(&self, typ: &Type) -> bool {
        match typ {
            Type::Basic(BasicType::Integer) | Type::Basic(BasicType::String) | Type::Basic(BasicType::Boolean) => true,
            Type::Basic(BasicType::Float) => false, // Float is not Hash
            Type::List(inner) => self.check_hash_trait(inner),
            Type::HashSet(inner) => self.check_hash_trait(inner),
            Type::TypeParameter { .. } => false, // Conservative
            _ => false,
        }
    }

    /// Check Send trait implementation (thread safety)
    fn check_send_trait(&self, typ: &Type) -> bool {
        match typ {
            Type::Basic(_) => true, // Basic types are Send
            Type::List(inner) => self.check_send_trait(inner),
            Type::HashMap(k, v) => self.check_send_trait(k) && self.check_send_trait(v),
            Type::HashSet(inner) => self.check_send_trait(inner),
            Type::Optional(inner) => self.check_send_trait(inner),
            Type::Result(ok, err) => self.check_send_trait(ok) && self.check_send_trait(err),
            Type::TypeParameter { .. } => false, // Conservative
            _ => false,
        }
    }

    /// Check Sync trait implementation (shareable between threads)
    fn check_sync_trait(&self, typ: &Type) -> bool {
        match typ {
            Type::Basic(_) => true, // Basic types are Sync
            Type::List(inner) => self.check_sync_trait(inner),
            Type::HashMap(k, v) => self.check_sync_trait(k) && self.check_sync_trait(v),
            Type::HashSet(inner) => self.check_sync_trait(inner),
            Type::Optional(inner) => self.check_sync_trait(inner),
            Type::Result(ok, err) => self.check_sync_trait(ok) && self.check_sync_trait(err),
            Type::TypeParameter { .. } => false, // Conservative
            _ => false,
        }
    }

    /// Check subtype relation
    fn check_subtype_relation(&self, sub: &Type, sup: &Type) -> bool {
        // Basic subtyping rules
        match (sub, sup) {
            (Type::Basic(BasicType::Integer), Type::Basic(BasicType::Float)) => true,
            (a, b) if a == b => true,
            _ => false,
        }
    }

    /// Check field access
    fn check_field_access(&self, _typ: &Type, _field_name: &str, _field_type: &Type) -> bool {
        // This would check struct field access
        // For now, assume all field accesses are valid
        true
    }

    /// Check if type is callable
    fn check_callable(&self, func_type: &Type, _arg_types: &[Type], _return_type: &Type) -> bool {
        matches!(func_type, Type::Function { .. })
    }

    /// Apply substitutions to a type
    pub fn apply_substitutions(&self, typ: &Type) -> Type {
        match typ {
            Type::TypeParameter { name, bounds } => {
                if let Some(substitution) = self.substitutions.get(name) {
                    self.apply_substitutions(substitution)
                } else {
                    Type::TypeParameter {
                        name: name.clone(),
                        bounds: bounds.clone(),
                    }
                }
            }
            Type::List(inner) => Type::List(Box::new(self.apply_substitutions(inner))),
            Type::HashMap(k, v) => Type::HashMap(
                Box::new(self.apply_substitutions(k)),
                Box::new(self.apply_substitutions(v)),
            ),
            Type::HashSet(inner) => Type::HashSet(Box::new(self.apply_substitutions(inner))),
            Type::Optional(inner) => Type::Optional(Box::new(self.apply_substitutions(inner))),
            Type::Result(ok, err) => Type::Result(
                Box::new(self.apply_substitutions(ok)),
                Box::new(self.apply_substitutions(err)),
            ),
            _ => typ.clone(),
        }
    }

    /// Convert type to string representation
    fn type_to_string(&self, typ: &Type) -> String {
        match typ {
            Type::Basic(basic) => format!("{:?}", basic),
            Type::List(inner) => format!("List[{}]", self.type_to_string(inner)),
            Type::HashMap(k, v) => format!("Map[{}, {}]", self.type_to_string(k), self.type_to_string(v)),
            Type::HashSet(inner) => format!("Set[{}]", self.type_to_string(inner)),
            Type::Optional(inner) => format!("Option[{}]", self.type_to_string(inner)),
            Type::Result(ok, err) => format!("Result[{}, {}]", self.type_to_string(ok), self.type_to_string(err)),
            Type::TypeParameter { name, .. } => name.clone(),
            Type::Auto => "auto".to_string(),
            _ => format!("{:?}", typ),
        }
    }

    /// Instantiate a generic type with concrete type arguments
    pub fn instantiate_generic(&mut self, name: &str, type_args: &[Type]) -> UmbraResult<Type> {
        if let Some(definition) = self.generic_types.get(name).cloned() {
            if type_args.len() != definition.type_parameters.len() {
                return Err(UmbraError::Type {
                    message: format!("Generic type {} expects {} type arguments, got {}",
                        name, definition.type_parameters.len(), type_args.len()),
                    line: 0,
                    column: 0,
                    expected_type: Some(format!("{} type arguments", definition.type_parameters.len())),
                    actual_type: Some(format!("{} type arguments", type_args.len())),
                });
            }

            // Validate type arguments against bounds
            for (param, arg) in definition.type_parameters.iter().zip(type_args.iter()) {
                self.validate_type_bounds(arg, &param.bounds)?;
            }

            // Create substitution map
            let mut substitutions = HashMap::new();
            for (param, arg) in definition.type_parameters.iter().zip(type_args.iter()) {
                substitutions.insert(param.name.clone(), arg.clone());
            }

            // Apply substitutions to the definition
            let instantiated = self.substitute_in_type(&definition.definition, &substitutions);
            Ok(instantiated)
        } else {
            Err(UmbraError::Type {
                message: format!("Unknown generic type: {}", name),
                line: 0,
                column: 0,
                expected_type: None,
                actual_type: Some(name.to_string()),
            })
        }
    }

    /// Validate type arguments against bounds
    fn validate_type_bounds(&mut self, typ: &Type, bounds: &[TypeBound]) -> UmbraResult<()> {
        for bound in bounds {
            match bound {
                TypeBound::Trait(trait_name) => {
                    if !self.check_trait_implementation_advanced(typ, trait_name) {
                        return Err(UmbraError::Type {
                            message: format!("Type {} does not implement required trait {}",
                                self.type_to_string(typ), trait_name),
                            line: 0,
                            column: 0,
                            expected_type: Some(trait_name.clone()),
                            actual_type: Some(self.type_to_string(typ)),
                        });
                    }
                }
                TypeBound::Subtype(super_type) => {
                    if !self.check_subtype_relation(typ, super_type) {
                        return Err(UmbraError::Type {
                            message: format!("Type {} is not a subtype of {}",
                                self.type_to_string(typ), self.type_to_string(super_type)),
                            line: 0,
                            column: 0,
                            expected_type: Some(self.type_to_string(super_type)),
                            actual_type: Some(self.type_to_string(typ)),
                        });
                    }
                }
                TypeBound::Lifetime(_) => {
                    // Lifetime bounds would be checked by a lifetime checker
                    // For now, assume they're satisfied
                }
                TypeBound::Custom(constraint) => {
                    if !self.solve_single_constraint(constraint).unwrap_or(false) {
                        return Err(UmbraError::Type {
                            message: format!("Type {} does not satisfy custom constraint",
                                self.type_to_string(typ)),
                            line: 0,
                            column: 0,
                            expected_type: None,
                            actual_type: Some(self.type_to_string(typ)),
                        });
                    }
                }
                TypeBound::HigherRanked(_, _) => {
                    // Higher-ranked trait bounds are complex and would need special handling
                    // For now, assume they're satisfied
                }
            }
        }
        Ok(())
    }

    /// Get all monomorphized functions for a given generic function
    pub fn get_monomorphized_functions(&self, function_name: &str) -> Vec<&MonomorphizedFunction> {
        self.monomorphized_functions
            .get(function_name)
            .map(|functions| functions.iter().collect())
            .unwrap_or_default()
    }

    /// Check if a type is fully concrete (no type parameters)
    pub fn is_concrete_type(&self, typ: &Type) -> bool {
        match typ {
            Type::TypeParameter { .. } => false,
            Type::List(inner) => self.is_concrete_type(inner),
            Type::HashMap(k, v) => self.is_concrete_type(k) && self.is_concrete_type(v),
            Type::HashSet(inner) => self.is_concrete_type(inner),
            Type::Optional(inner) => self.is_concrete_type(inner),
            Type::Result(ok, err) => self.is_concrete_type(ok) && self.is_concrete_type(err),
            Type::Generic(_, args) => args.iter().all(|arg| self.is_concrete_type(arg)),
            _ => true,
        }
    }

    /// Infer type arguments for a generic function call
    pub fn infer_type_arguments(
        &mut self,
        function_signature: &FunctionSignature,
        arg_types: &[Type],
    ) -> UmbraResult<Vec<Type>> {
        let mut type_var_map = HashMap::new();

        // Create fresh type variables for each type parameter
        for type_param in &function_signature.type_params {
            let fresh_var = self.fresh_type_var();
            type_var_map.insert(type_param.name.clone(), fresh_var);
        }

        // Unify argument types with parameter types
        if function_signature.params.len() != arg_types.len() {
            return Err(UmbraError::Type {
                message: format!("Function expects {} arguments, got {}",
                    function_signature.params.len(), arg_types.len()),
                line: 0,
                column: 0,
                expected_type: Some(format!("{} arguments", function_signature.params.len())),
                actual_type: Some(format!("{} arguments", arg_types.len())),
            });
        }

        for ((_, param_type), arg_type) in function_signature.params.iter().zip(arg_types.iter()) {
            let substituted_param_type = self.substitute_type_in_type(param_type, &type_var_map);
            self.unify_advanced(&substituted_param_type, arg_type)?;
        }

        // Extract inferred types in order
        let mut result = Vec::new();
        for type_param in &function_signature.type_params {
            if let Some(type_var) = type_var_map.get(&type_param.name) {
                // Look up the resolved type for this type variable
                let resolved_type = self.resolve_type_variable(type_var);
                result.push(resolved_type);
            } else {
                return Err(UmbraError::Type {
                    message: format!("Could not infer type for parameter {}", type_param.name),
                    line: 0,
                    column: 0,
                    expected_type: None,
                    actual_type: None,
                });
            }
        }

        Ok(result)
    }

    /// Resolve a type variable to its concrete type
    fn resolve_type_variable(&self, type_var: &Type) -> Type {
        match type_var {
            Type::TypeVariable(var_id) => {
                // Convert the u32 ID to a string key for the substitutions map
                let var_key = format!("'{}", var_id);
                if let Some(resolved) = self.substitutions.get(&var_key) {
                    // Recursively resolve in case of chained substitutions
                    self.resolve_type_variable(resolved)
                } else {
                    // If no substitution found, return the type variable itself
                    type_var.clone()
                }
            }
            _ => type_var.clone(),
        }
    }

    /// Substitute type parameters in a type
    fn substitute_in_type(&self, typ: &Type, substitutions: &HashMap<String, Type>) -> Type {
        match typ {
            Type::TypeParameter { name, .. } => {
                substitutions.get(name).cloned().unwrap_or_else(|| typ.clone())
            }
            Type::List(inner) => Type::List(Box::new(self.substitute_in_type(inner, substitutions))),
            Type::HashMap(k, v) => Type::HashMap(
                Box::new(self.substitute_in_type(k, substitutions)),
                Box::new(self.substitute_in_type(v, substitutions)),
            ),
            Type::HashSet(inner) => Type::HashSet(Box::new(self.substitute_in_type(inner, substitutions))),
            Type::Optional(inner) => Type::Optional(Box::new(self.substitute_in_type(inner, substitutions))),
            Type::Result(ok, err) => Type::Result(
                Box::new(self.substitute_in_type(ok, substitutions)),
                Box::new(self.substitute_in_type(err, substitutions)),
            ),
            _ => typ.clone(),
        }
    }
}

impl Default for TypeInference {
    fn default() -> Self {
        Self::new()
    }
}

impl fmt::Display for TypeConstraint {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            TypeConstraint::Equal(t1, t2) => write!(f, "{} = {}", t1, t2),
            TypeConstraint::Implements(typ, trait_name) => write!(f, "{}: {}", typ, trait_name),
            TypeConstraint::Subtype(sub, sup) => write!(f, "{} <: {}", sub, sup),
            TypeConstraint::HasField(typ, field, field_type) => write!(f, "{}.{}: {}", typ, field, field_type),
            TypeConstraint::Callable(func, args, ret) => {
                write!(f, "{}(", func)?;
                for (i, arg) in args.iter().enumerate() {
                    if i > 0 { write!(f, ", ")?; }
                    write!(f, "{}", arg)?;
                }
                write!(f, ") -> {}", ret)
            }
            TypeConstraint::And(constraints) => {
                write!(f, "(")?;
                for (i, constraint) in constraints.iter().enumerate() {
                    if i > 0 { write!(f, " ∧ ")?; }
                    write!(f, "{}", constraint)?;
                }
                write!(f, ")")
            }
            TypeConstraint::Or(constraints) => {
                write!(f, "(")?;
                for (i, constraint) in constraints.iter().enumerate() {
                    if i > 0 { write!(f, " ∨ ")?; }
                    write!(f, "{}", constraint)?;
                }
                write!(f, ")")
            }
            TypeConstraint::Not(constraint) => write!(f, "¬{}", constraint),
            TypeConstraint::Hash(typ) => write!(f, "{}: Hash", typ),
            TypeConstraint::Clone(typ) => write!(f, "{}: Clone", typ),
            TypeConstraint::Copy(typ) => write!(f, "{}: Copy", typ),
            TypeConstraint::Debug(typ) => write!(f, "{}: Debug", typ),
            TypeConstraint::PartialEq(typ) => write!(f, "{}: PartialEq", typ),
            TypeConstraint::Eq(typ) => write!(f, "{}: Eq", typ),
            TypeConstraint::Send(typ) => write!(f, "{}: Send", typ),
            TypeConstraint::Sync(typ) => write!(f, "{}: Sync", typ),
            _ => write!(f, "{:?}", self), // Fallback for other constraints
        }
    }
}

impl fmt::Display for TypeParameter {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.name)?;
        if !self.bounds.is_empty() {
            write!(f, ": ")?;
            for (i, bound) in self.bounds.iter().enumerate() {
                if i > 0 { write!(f, " + ")?; }
                write!(f, "{}", bound)?;
            }
        }
        if let Some(default) = &self.default {
            write!(f, " = {}", default)?;
        }
        Ok(())
    }
}

impl fmt::Display for TypeBound {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            TypeBound::Trait(name) => write!(f, "{}", name),
            TypeBound::Subtype(typ) => write!(f, "<: {}", typ),
            TypeBound::Lifetime(name) => write!(f, "'{}", name),
            TypeBound::Custom(constraint) => write!(f, "{}", constraint),
            TypeBound::HigherRanked(lifetimes, bound) => {
                write!(f, "for<")?;
                for (i, lifetime) in lifetimes.iter().enumerate() {
                    if i > 0 { write!(f, ", ")?; }
                    write!(f, "'{}", lifetime)?;
                }
                write!(f, "> {}", bound)
            }
        }
    }
}

impl fmt::Display for Variance {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            Variance::Covariant => write!(f, "+"),
            Variance::Contravariant => write!(f, "-"),
            Variance::Invariant => write!(f, ""),
            Variance::Bivariant => write!(f, "*"),
        }
    }
}

impl fmt::Display for Kind {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            Kind::Type => write!(f, "*"),
            Kind::Function(from, to) => write!(f, "{} -> {}", from, to),
            Kind::Higher(kinds) => {
                write!(f, "(")?;
                for (i, kind) in kinds.iter().enumerate() {
                    if i > 0 { write!(f, ", ")?; }
                    write!(f, "{}", kind)?;
                }
                write!(f, ")")
            }
        }
    }
}

impl fmt::Display for FunctionSignature {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "define {}", self.name)?;

        if !self.type_params.is_empty() {
            write!(f, "<")?;
            for (i, param) in self.type_params.iter().enumerate() {
                if i > 0 { write!(f, ", ")?; }
                write!(f, "{}", param)?;
            }
            write!(f, ">")?;
        }

        write!(f, "(")?;
        for (i, (name, typ)) in self.params.iter().enumerate() {
            if i > 0 { write!(f, ", ")?; }
            write!(f, "{}: {}", name, typ)?;
        }
        write!(f, ") -> {}", self.return_type)
    }
}

// Additional helper methods for the type system
impl TypeInference {
    /// Create a type alias
    pub fn create_type_alias(&mut self, name: String, type_params: Vec<TypeParameter>, target_type: Type) {
        let alias = TypeAlias {
            name: name.clone(),
            type_params,
            target_type,
        };
        self.type_aliases.insert(name, alias);
    }

    /// Resolve a type alias
    pub fn resolve_type_alias(&self, name: &str, type_args: &[Type]) -> Option<Type> {
        if let Some(alias) = self.type_aliases.get(name) {
            if type_args.len() != alias.type_params.len() {
                return None;
            }

            let mut substitutions = HashMap::new();
            for (param, arg) in alias.type_params.iter().zip(type_args.iter()) {
                substitutions.insert(param.name.clone(), arg.clone());
            }

            Some(self.substitute_type_in_type(&alias.target_type, &substitutions))
        } else {
            None
        }
    }

    /// Get the kind of a type
    pub fn get_type_kind(&self, typ: &Type) -> Kind {
        match typ {
            Type::Basic(_) => Kind::Type,
            Type::List(_) => Kind::Function(Box::new(Kind::Type), Box::new(Kind::Type)),
            Type::HashMap(_, _) => Kind::Function(
                Box::new(Kind::Type),
                Box::new(Kind::Function(Box::new(Kind::Type), Box::new(Kind::Type)))
            ),
            Type::TypeParameter { .. } => Kind::Type, // Default to Type kind
            _ => Kind::Type,
        }
    }

    /// Check kind compatibility
    pub fn check_kind_compatibility(&self, expected: &Kind, actual: &Kind) -> bool {
        match (expected, actual) {
            (Kind::Type, Kind::Type) => true,
            (Kind::Function(from1, to1), Kind::Function(from2, to2)) => {
                self.check_kind_compatibility(from1, from2) && self.check_kind_compatibility(to1, to2)
            }
            _ => false,
        }
    }

    /// Generate comprehensive type error with context
    pub fn generate_type_error(&self, message: &str, expected: Option<&Type>, actual: Option<&Type>) -> UmbraError {
        UmbraError::Type {
            message: message.to_string(),
            line: 0,
            column: 0,
            expected_type: expected.map(|t| self.type_to_string(t)),
            actual_type: actual.map(|t| self.type_to_string(t)),
        }
    }
}
