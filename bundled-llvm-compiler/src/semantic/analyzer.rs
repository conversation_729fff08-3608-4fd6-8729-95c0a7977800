use super::symbol_table::{StructureField, Symbol, SymbolTable, SymbolType};
use super::type_checker::<PERSON><PERSON><PERSON><PERSON>;
use super::generics::{TypeInference, FunctionSignature, MonomorphizedFunction};
use crate::error::{DiagnosticBag, SourceLocation, UmbraError, UmbraResult};
use crate::parser::ast::*;
use std::collections::{HashSet, HashMap};

/// Semantic analyzer for Umbra programs with advanced generics support
pub struct SemanticAnalyzer {
    symbol_table: SymbolTable,
    type_checker: TypeChe<PERSON>,
    type_inference: TypeInference,
    diagnostics: DiagnosticBag,
    current_function: Option<String>,
    // Dead code elimination tracking
    used_functions: HashSet<String>,
    used_variables: HashSet<String>,
    used_types: HashSet<String>,
    // Performance optimization fields
    analysis_depth: usize,
    max_analysis_depth: usize,
    function_cache: HashMap<String, bool>, // Cache for analyzed functions
    // Generic function support
    generic_functions: HashMap<String, FunctionSignature>,
    monomorphized_functions: HashMap<String, Vec<MonomorphizedFunction>>,
}

impl SemanticAnalyzer {
    pub fn new() -> Self {
        Self {
            symbol_table: SymbolTable::default(),
            type_checker: TypeChecker::new(),
            type_inference: TypeInference::new(),
            diagnostics: DiagnosticBag::new(),
            current_function: None,
            used_functions: HashSet::new(),
            used_variables: HashSet::new(),
            used_types: HashSet::new(),
            analysis_depth: 0,
            max_analysis_depth: 1000, // Prevent stack overflow
            function_cache: HashMap::new(),
            generic_functions: HashMap::new(),
            monomorphized_functions: HashMap::new(),
        }
    }

    /// Create analyzer optimized for large programs
    pub fn new_for_large_program(estimated_functions: usize) -> Self {
        let mut symbol_table = SymbolTable::with_capacity(estimated_functions * 2);
        symbol_table.initialize_builtins(); // Make sure built-ins are loaded

        Self {
            symbol_table,
            type_checker: TypeChecker::new(),
            type_inference: TypeInference::new(),
            diagnostics: DiagnosticBag::new(),
            current_function: None,
            used_functions: HashSet::with_capacity(estimated_functions),
            used_variables: HashSet::with_capacity(estimated_functions * 5),
            used_types: HashSet::with_capacity(estimated_functions / 10),
            analysis_depth: 0,
            max_analysis_depth: 2000, // Higher limit for large programs
            function_cache: std::collections::HashMap::with_capacity(estimated_functions),
            generic_functions: HashMap::new(),
            monomorphized_functions: HashMap::new(),
        }
    }

    /// Analyze a complete program
    pub fn analyze(&mut self, program: &Program) -> UmbraResult<()> {
        self.diagnostics.clear();

        // Optimize for large programs by checking size
        let function_count = program.statements.iter()
            .filter(|s| matches!(s, Statement::Function(_)))
            .count();

        if function_count > 100 {
            // For large programs, use optimized analysis
            self.optimize_for_large_program(function_count);
            self.analyze_large_program(program)
        } else {
            self.analyze_standard_program(program)
        }
    }

    /// Standard analysis for normal-sized programs
    fn analyze_standard_program(&mut self, program: &Program) -> UmbraResult<()> {
        // First pass: collect all function and structure declarations
        for statement in &program.statements {
            match statement {
                Statement::Function(func) => self.declare_function(func)?,
                Statement::Structure(struct_def) => self.declare_structure(struct_def)?,
                _ => {}
            }
        }

        // Second pass: analyze all statements
        for statement in &program.statements {
            self.analyze_statement(statement)?;
        }

        if self.diagnostics.has_errors() {
            return Err(UmbraError::Semantic {
                message: format!(
                    "Semantic analysis failed with {} errors",
                    self.diagnostics.error_count()
                ),
                line: 0,
                column: 0,
            });
        }

        Ok(())
    }

    /// Optimized analysis for large programs with many functions
    fn analyze_large_program(&mut self, program: &Program) -> UmbraResult<()> {
        // Pre-allocate collections to avoid frequent reallocations
        let mut functions = Vec::with_capacity(program.statements.len());
        let mut structures = Vec::with_capacity(program.statements.len() / 10);
        let mut other_statements = Vec::with_capacity(program.statements.len() / 10);

        // Separate statements by type for batch processing
        for statement in &program.statements {
            match statement {
                Statement::Function(func) => functions.push(func),
                Statement::Structure(struct_def) => structures.push(struct_def),
                _ => other_statements.push(statement),
            }
        }

        // Batch declare structures first
        for struct_def in structures {
            self.declare_structure(struct_def)?;
        }

        // Batch declare functions
        for func in &functions {
            self.declare_function(func)?;
        }

        // Process functions in chunks to manage memory
        const CHUNK_SIZE: usize = 50;
        for chunk in functions.chunks(CHUNK_SIZE) {
            for func in chunk {
                self.analyze_statement(&Statement::Function((*func).clone()))?;
            }
        }

        // Process other statements
        for statement in other_statements {
            self.analyze_statement(statement)?;
        }

        if self.diagnostics.has_errors() {
            return Err(UmbraError::Semantic {
                message: format!(
                    "Semantic analysis failed with {} errors",
                    self.diagnostics.error_count()
                ),
                line: 0,
                column: 0,
            });
        }

        Ok(())
    }

    /// Optimize analyzer settings for large programs
    fn optimize_for_large_program(&mut self, function_count: usize) {
        // Increase analysis depth limit for large programs
        self.max_analysis_depth = (function_count * 2).max(2000);

        // Pre-allocate collections
        self.used_functions.reserve(function_count);
        self.used_variables.reserve(function_count * 5);
        self.used_types.reserve(function_count / 10);
        self.function_cache.reserve(function_count);

        // Reserve symbol table capacity
        self.symbol_table.reserve_capacity(function_count * 2);
    }

    /// Analyze a program with optional optimizations
    pub fn analyze_with_optimization(&mut self, program: &mut Program, eliminate_dead_code: bool, constant_folding: bool, inline_functions: bool, optimize_loops: bool, optimize_memory: bool) -> UmbraResult<()> {
        // First: run normal semantic analysis
        self.analyze(program)?;

        // Second: constant folding if requested
        if constant_folding {
            self.fold_constants(program)?;
        }

        // Third: function inlining if requested
        if inline_functions {
            self.inline_functions(program)?;
        }

        // Fourth: loop optimizations if requested
        if optimize_loops {
            self.optimize_loops(program)?;
        }

        // Fifth: memory optimizations if requested
        if optimize_memory {
            self.optimize_memory_management(program)?;
        }

        // Sixth: dead code elimination if requested (after other optimizations)
        if eliminate_dead_code {
            self.eliminate_dead_code(program)?;
        }

        Ok(())
    }

    fn declare_function(&mut self, func: &FunctionDef) -> UmbraResult<()> {
        // Handle generic functions
        if !func.type_params.is_empty() {
            // For generic functions, store the generic signature
            let generic_signature = FunctionSignature {
                name: func.name.clone(),
                type_params: func.type_params.iter().map(|tp| {
                    super::generics::TypeParameter {
                        name: tp.name.clone(),
                        bounds: tp.bounds.iter().map(|b| match b {
                            TypeBound::Trait(name) =>
                                super::generics::TypeBound::Trait(name.clone()),
                            TypeBound::Subtype(typ) =>
                                super::generics::TypeBound::Subtype(typ.clone()),
                            TypeBound::Lifetime(name) =>
                                super::generics::TypeBound::Lifetime(name.clone()),
                            TypeBound::HigherRanked(lifetimes, _bound) =>
                                super::generics::TypeBound::HigherRanked(
                                    lifetimes.clone(),
                                    Box::new(super::generics::TypeBound::Trait("Any".to_string()))
                                ),
                        }).collect(),
                        default: tp.default.clone(),
                        variance: match tp.variance {
                            Some(Variance::Covariant) =>
                                super::generics::Variance::Covariant,
                            Some(Variance::Contravariant) =>
                                super::generics::Variance::Contravariant,
                            Some(Variance::Bivariant) =>
                                super::generics::Variance::Bivariant,
                            _ => super::generics::Variance::Invariant,
                        },
                        kind: super::generics::Kind::Type,
                        lifetime_bounds: vec![],
                    }
                }).collect(),
                params: func.parameters.iter().map(|p| (p.name.clone(), p.type_annotation.clone())).collect(),
                return_type: func.return_type.clone(),
                constraints: vec![], // Convert constraints if needed
            };

            // Store generic function signature
            self.generic_functions.insert(func.name.clone(), generic_signature);
        }

        let param_types: Vec<Type> = func
            .parameters
            .iter()
            .map(|p| p.type_annotation.clone())
            .collect();

        let symbol = Symbol::new_function(
            func.name.clone(),
            param_types,
            func.return_type.clone(),
            func.location,
        );

        self.symbol_table
            .define(symbol)
            .map_err(|msg| UmbraError::Semantic {
                message: msg,
                line: func.location.line,
                column: func.location.column,
            })?;

        Ok(())
    }

    fn declare_structure(&mut self, struct_def: &StructureDef) -> UmbraResult<()> {
        let fields: Vec<StructureField> = struct_def
            .fields
            .iter()
            .map(|f| StructureField {
                name: f.name.clone(),
                field_type: f.field_type.clone(),
            })
            .collect();

        let symbol = Symbol::new_structure(struct_def.name.clone(), fields, struct_def.location);

        self.symbol_table
            .define(symbol)
            .map_err(|msg| UmbraError::Semantic {
                message: msg,
                line: struct_def.location.line,
                column: struct_def.location.column,
            })?;

        Ok(())
    }

    fn analyze_statement(&mut self, statement: &Statement) -> UmbraResult<()> {
        match statement {
            Statement::Module(stmt) => self.analyze_module_declaration(stmt),
            Statement::Import(stmt) => self.analyze_import(stmt),
            Statement::Export(stmt) => self.analyze_new_export_statement(stmt),
            Statement::Function(stmt) => self.analyze_function(stmt),
            Statement::Structure(_) => Ok(()), // Already handled in declaration phase
            Statement::Variable(stmt) => self.analyze_variable_declaration(stmt),
            Statement::Assignment(stmt) => self.analyze_assignment(stmt),
            Statement::Expression(stmt) => {
                self.analyze_expression(&stmt.expression)?;
                Ok(())
            }
            Statement::When(stmt) => self.analyze_when_statement(stmt),
            Statement::Repeat(stmt) => self.analyze_repeat_statement(stmt),
            Statement::Return(stmt) => self.analyze_return_statement(stmt),
            Statement::Train(stmt) => self.analyze_train_statement(stmt),
            Statement::Evaluate(stmt) => self.analyze_evaluate_statement(stmt),
            Statement::Visualize(stmt) => self.analyze_visualize_statement(stmt),
            Statement::Predict(stmt) => self.analyze_predict_statement(stmt),
            // Error handling statements
            Statement::Try(stmt) => self.analyze_try_statement(stmt),
            Statement::Throw(stmt) => self.analyze_throw_statement(stmt),
            Statement::Panic(stmt) => self.analyze_panic_statement(stmt),
            Statement::Error(stmt) => self.analyze_error_definition(stmt),
            Statement::Trait(stmt) => self.analyze_trait_definition(stmt),
            Statement::Implementation(stmt) => self.analyze_implementation(stmt),
            Statement::Query(_) => Ok(()), // TODO: Implement database query analysis
            Statement::Transaction(_) => Ok(()), // TODO: Implement transaction analysis
            Statement::Migration(_) => Ok(()), // TODO: Implement migration analysis
        }
    }

    fn analyze_module_declaration(&mut self, _stmt: &ModuleDeclaration) -> UmbraResult<()> {
        // Module declarations define the current module's namespace
        // For now, we just record it but don't process it
        // In a full implementation, this would set up the module context
        Ok(())
    }

    fn analyze_import(&mut self, _stmt: &ImportStatement) -> UmbraResult<()> {
        // For now, imports are just recorded but not processed
        // In a full implementation, we'd load and process the imported module
        Ok(())
    }

    fn analyze_new_export_statement(&mut self, _stmt: &ExportStatement) -> UmbraResult<()> {
        // For now, exports are just recorded but not processed
        // In a full implementation, this would mark symbols as exported
        Ok(())
    }

    fn analyze_function(&mut self, func: &FunctionDef) -> UmbraResult<()> {
        // Check analysis depth to prevent stack overflow
        if self.analysis_depth >= self.max_analysis_depth {
            return Err(UmbraError::Semantic {
                message: format!("Analysis depth limit exceeded in function '{}'", func.name),
                line: func.location.line,
                column: func.location.column,
            });
        }

        // Check cache first
        if let Some(&already_analyzed) = self.function_cache.get(&func.name) {
            if already_analyzed {
                return Ok(());
            }
        }

        self.analysis_depth += 1;

        // Register function in symbol table
        let param_types: Vec<Type> = func.parameters.iter()
            .map(|p| p.type_annotation.clone())
            .collect();
        let return_type = func.return_type.clone();

        let function_symbol = Symbol::new_function(
            func.name.clone(),
            param_types,
            return_type,
            func.location,
        );

        if let Err(e) = self.symbol_table.define(function_symbol) {
            // For trait impl methods, it's okay if they already exist
            if !e.contains("already defined") {
                return Err(UmbraError::Semantic {
                    message: e,
                    line: func.location.line,
                    column: func.location.column,
                });
            }
        }

        // Enter function scope
        self.symbol_table.enter_scope();
        let previous_function = self.current_function.clone();
        self.current_function = Some(func.name.clone());

        let result = self.analyze_function_body(func);

        // Restore state
        self.current_function = previous_function;
        self.symbol_table.exit_scope();
        self.analysis_depth -= 1;

        // Cache result
        self.function_cache.insert(func.name.clone(), result.is_ok());

        result
    }

    fn analyze_function_body(&mut self, func: &FunctionDef) -> UmbraResult<()> {
        // Add parameters to scope
        for param in &func.parameters {
            let symbol = Symbol::new_parameter(
                param.name.clone(),
                param.type_annotation.clone(),
                param.location,
            );
            self.symbol_table
                .define(symbol)
                .map_err(|msg| UmbraError::Semantic {
                    message: format!("In function '{}': {}", func.name, msg),
                    line: param.location.line,
                    column: param.location.column,
                })?;
        }

        // Analyze function body
        for statement in &func.body {
            self.analyze_statement(statement)?;
        }

        Ok(())
    }

    fn analyze_variable_declaration(&mut self, var_decl: &VariableDecl) -> UmbraResult<()> {
        // Analyze the value expression
        let value_type = self.analyze_expression(&var_decl.value)?;

        // Check type compatibility - the type checker already handles Union types
        let types_compatible = self.type_checker.types_compatible(&var_decl.type_annotation, &value_type);

        if !types_compatible {
            return Err(UmbraError::Semantic {
                message: format!(
                    "Type mismatch: cannot assign {} to variable '{}' of type {}",
                    self.type_checker.type_to_string(&value_type),
                    var_decl.name,
                    self.type_checker.type_to_string(&var_decl.type_annotation)
                ),
                line: var_decl.location.line,
                column: var_decl.location.column,
            });
        }

        // Use the more specific type for lambda functions
        let actual_type = if matches!(var_decl.type_annotation, Type::Function(_, _))
                             && matches!(value_type, Type::Function(_, _)) {
            // If both are function types, use the more specific inferred type
            value_type
        } else {
            var_decl.type_annotation.clone()
        };

        // Add variable to current scope
        let mut symbol = Symbol::new_variable(
            var_decl.name.clone(),
            actual_type,
            var_decl.location,
            true, // Variables are mutable by default in Umbra
        );
        symbol.is_initialized = true;

        self.symbol_table
            .define(symbol)
            .map_err(|msg| UmbraError::Semantic {
                message: msg,
                line: var_decl.location.line,
                column: var_decl.location.column,
            })?;

        Ok(())
    }

    fn analyze_assignment(&mut self, assignment: &Assignment) -> UmbraResult<()> {
        // Check if variable exists and get its type
        let symbol_type = {
            let symbol =
                self.symbol_table
                    .lookup(&assignment.name)
                    .ok_or_else(|| UmbraError::Semantic {
                        message: format!("Undefined variable: '{}'", assignment.name),
                        line: assignment.location.line,
                        column: assignment.location.column,
                    })?;

            // Check if it's assignable
            if !matches!(
                symbol.symbol_type,
                SymbolType::Variable | SymbolType::Parameter
            ) {
                return Err(UmbraError::Semantic {
                    message: format!(
                        "Cannot assign to {}: '{}'",
                        match symbol.symbol_type {
                            SymbolType::Function => "function",
                            SymbolType::Structure => "structure",
                            _ => "non-variable",
                        },
                        assignment.name
                    ),
                    line: assignment.location.line,
                    column: assignment.location.column,
                });
            }

            symbol.type_annotation.clone()
        };

        // Analyze the value expression
        let value_type = self.analyze_expression(&assignment.value)?;

        // Check type compatibility based on assignment operator
        match assignment.operator {
            AssignmentOperator::Assign => {
                // Simple assignment: check type compatibility - the type checker already handles Union types
                if !self.type_checker.types_compatible(&symbol_type, &value_type) {
                    return Err(UmbraError::Semantic {
                        message: format!(
                            "Type mismatch: cannot assign {} to variable '{}' of type {}",
                            self.type_checker.type_to_string(&value_type),
                            assignment.name,
                            self.type_checker.type_to_string(&symbol_type)
                        ),
                        line: assignment.location.line,
                        column: assignment.location.column,
                    });
                }
            }
            AssignmentOperator::AddAssign
            | AssignmentOperator::SubAssign
            | AssignmentOperator::MulAssign
            | AssignmentOperator::DivAssign => {
                // Compound assignment: check that the operation is valid
                let binary_op = match assignment.operator {
                    AssignmentOperator::AddAssign => BinaryOperator::Add,
                    AssignmentOperator::SubAssign => BinaryOperator::Subtract,
                    AssignmentOperator::MulAssign => BinaryOperator::Multiply,
                    AssignmentOperator::DivAssign => BinaryOperator::Divide,
                    _ => unreachable!(),
                };

                // Check if the binary operation is valid between variable type and value type
                let result_type = self.type_checker.check_binary_operation(
                    &symbol_type,
                    &binary_op,
                    &value_type,
                )?;

                // The result type should be compatible with the variable type
                if !self
                    .type_checker
                    .types_compatible(&symbol_type, &result_type)
                {
                    return Err(UmbraError::Semantic {
                        message: format!(
                            "Type mismatch in compound assignment: operation {} {} {} results in type {}, but variable '{}' is of type {}",
                            self.type_checker.type_to_string(&symbol_type),
                            binary_op,
                            self.type_checker.type_to_string(&value_type),
                            self.type_checker.type_to_string(&result_type),
                            assignment.name,
                            self.type_checker.type_to_string(&symbol_type)
                        ),
                        line: assignment.location.line,
                        column: assignment.location.column,
                    });
                }
            }
        }

        Ok(())
    }

    fn analyze_when_statement(&mut self, when_stmt: &WhenStatement) -> UmbraResult<()> {
        // Analyze condition
        let condition_type = self.analyze_expression(&when_stmt.condition)?;

        // Check that condition is boolean
        if !self.type_checker.is_boolean_context_valid(&condition_type) {
            return Err(UmbraError::Semantic {
                message: format!(
                    "When condition must be Boolean, got {}",
                    self.type_checker.type_to_string(&condition_type)
                ),
                line: when_stmt.location.line,
                column: when_stmt.location.column,
            });
        }

        // Analyze then body
        self.symbol_table.enter_scope();
        for statement in &when_stmt.then_body {
            self.analyze_statement(statement)?;
        }
        self.symbol_table.exit_scope();

        // Analyze else body if present
        if let Some(else_body) = &when_stmt.else_body {
            self.symbol_table.enter_scope();
            for statement in else_body {
                self.analyze_statement(statement)?;
            }
            self.symbol_table.exit_scope();
        }

        Ok(())
    }

    fn analyze_repeat_statement(&mut self, repeat_stmt: &RepeatStatement) -> UmbraResult<()> {
        // Analyze iterable
        let iterable_type = self.analyze_expression(&repeat_stmt.iterable)?;

        // Check that iterable is a list
        if !self.type_checker.is_iterable_type(&iterable_type) {
            return Err(UmbraError::Semantic {
                message: format!(
                    "Repeat iterable must be a List, got {}",
                    self.type_checker.type_to_string(&iterable_type)
                ),
                line: repeat_stmt.location.line,
                column: repeat_stmt.location.column,
            });
        }

        let element_type = self
            .type_checker
            .get_element_type(&iterable_type)
            .unwrap_or(Type::Basic(BasicType::Integer)); // Fallback

        // Enter loop scope
        self.symbol_table.enter_scope();

        // Add loop variable to scope
        let loop_var_symbol = Symbol::new_variable(
            repeat_stmt.variable.clone(),
            element_type,
            repeat_stmt.location,
            false, // Loop variables are immutable
        );
        self.symbol_table
            .define(loop_var_symbol)
            .map_err(|msg| UmbraError::Semantic {
                message: msg,
                line: repeat_stmt.location.line,
                column: repeat_stmt.location.column,
            })?;

        // Analyze loop body
        for statement in &repeat_stmt.body {
            self.analyze_statement(statement)?;
        }

        // Exit loop scope
        self.symbol_table.exit_scope();

        Ok(())
    }

    fn analyze_return_statement(&mut self, return_stmt: &ReturnStatement) -> UmbraResult<()> {
        if self.current_function.is_none() {
            return Err(UmbraError::Semantic {
                message: "Return statement outside of function".to_string(),
                line: return_stmt.location.line,
                column: return_stmt.location.column,
            });
        }

        let function_name = self.current_function.as_ref().unwrap().clone();
        let expected_type = {
            if let Some(function_symbol) = self.symbol_table.lookup(&function_name) {
                function_symbol.type_annotation.clone()
            } else {
                // If function not found in symbol table (e.g., trait impl methods),
                // assume Void return type for now
                Type::Basic(BasicType::Void)
            }
        };

        if let Some(value) = &return_stmt.value {
            let actual_type = self.analyze_expression(value)?;
            if !self
                .type_checker
                .types_compatible(&expected_type, &actual_type)
            {
                return Err(UmbraError::Semantic {
                    message: format!(
                        "Return type mismatch: expected {}, got {}",
                        self.type_checker.type_to_string(&expected_type),
                        self.type_checker.type_to_string(&actual_type)
                    ),
                    line: return_stmt.location.line,
                    column: return_stmt.location.column,
                });
            }
        } else {
            // No return value
            if !matches!(expected_type, Type::Basic(BasicType::Void)) {
                return Err(UmbraError::Semantic {
                    message: format!(
                        "Function '{}' must return a value of type {}",
                        function_name,
                        self.type_checker.type_to_string(&expected_type)
                    ),
                    line: return_stmt.location.line,
                    column: return_stmt.location.column,
                });
            }
        }

        Ok(())
    }

    // AI/ML statement analysis
    fn analyze_train_statement(&mut self, train_stmt: &TrainStatement) -> UmbraResult<()> {
        // Check model exists and is of type Model
        let model_symbol =
            self.symbol_table
                .lookup(&train_stmt.model)
                .ok_or_else(|| UmbraError::Semantic {
                    message: format!("Undefined model: '{}'", train_stmt.model),
                    line: train_stmt.location.line,
                    column: train_stmt.location.column,
                })?;

        if !self.type_checker.types_compatible(
            &Type::Basic(BasicType::Model),
            &model_symbol.type_annotation,
        ) {
            return Err(UmbraError::Semantic {
                message: format!("'{}' is not a Model", train_stmt.model),
                line: train_stmt.location.line,
                column: train_stmt.location.column,
            });
        }

        // Check dataset exists and is of type Dataset
        let dataset_symbol = self
            .symbol_table
            .lookup(&train_stmt.dataset)
            .ok_or_else(|| UmbraError::Semantic {
                message: format!("Undefined dataset: '{}'", train_stmt.dataset),
                line: train_stmt.location.line,
                column: train_stmt.location.column,
            })?;

        if !self.type_checker.types_compatible(
            &Type::Basic(BasicType::Dataset),
            &dataset_symbol.type_annotation,
        ) {
            return Err(UmbraError::Semantic {
                message: format!("'{}' is not a Dataset", train_stmt.dataset),
                line: train_stmt.location.line,
                column: train_stmt.location.column,
            });
        }

        // Analyze training parameters
        for param in &train_stmt.config {
            self.analyze_expression(&param.value)?;
        }

        Ok(())
    }

    fn analyze_evaluate_statement(&mut self, eval_stmt: &EvaluateStatement) -> UmbraResult<()> {
        // Similar to train statement
        let model_symbol =
            self.symbol_table
                .lookup(&eval_stmt.model)
                .ok_or_else(|| UmbraError::Semantic {
                    message: format!("Undefined model: '{}'", eval_stmt.model),
                    line: eval_stmt.location.line,
                    column: eval_stmt.location.column,
                })?;

        if !self.type_checker.types_compatible(
            &Type::Basic(BasicType::Model),
            &model_symbol.type_annotation,
        ) {
            return Err(UmbraError::Semantic {
                message: format!("'{}' is not a Model", eval_stmt.model),
                line: eval_stmt.location.line,
                column: eval_stmt.location.column,
            });
        }

        let dataset_symbol = self
            .symbol_table
            .lookup(&eval_stmt.dataset)
            .ok_or_else(|| UmbraError::Semantic {
                message: format!("Undefined dataset: '{}'", eval_stmt.dataset),
                line: eval_stmt.location.line,
                column: eval_stmt.location.column,
            })?;

        if !self.type_checker.types_compatible(
            &Type::Basic(BasicType::Dataset),
            &dataset_symbol.type_annotation,
        ) {
            return Err(UmbraError::Semantic {
                message: format!("'{}' is not a Dataset", eval_stmt.dataset),
                line: eval_stmt.location.line,
                column: eval_stmt.location.column,
            });
        }

        Ok(())
    }

    fn analyze_visualize_statement(&mut self, _viz_stmt: &VisualizeStatement) -> UmbraResult<()> {
        // For now, just accept any identifiers for visualization
        // In a full implementation, we'd validate metric and dimension names
        Ok(())
    }

    fn analyze_export_statement(&mut self, export_stmt: &ExportStatement) -> UmbraResult<()> {
        // This is the legacy export statement analysis for AI/ML exports
        // The new module system exports are handled by analyze_new_export_statement
        match &export_stmt.export_type {
            ExportType::Function(name) => {
                // Check if the function exists
                let _symbol = self
                    .symbol_table
                    .lookup(name)
                    .ok_or_else(|| UmbraError::Semantic {
                        message: format!("Undefined function: '{name}'"),
                        line: export_stmt.location.line,
                        column: export_stmt.location.column,
                    })?;
                Ok(())
            }
            _ => {
                // For now, just accept other export types
                Ok(())
            }
        }
    }

    fn analyze_predict_statement(&mut self, predict_stmt: &PredictStatement) -> UmbraResult<()> {
        // Sample can be a variable or a string literal
        // If it's not found as a variable, assume it's a literal
        if let Some(_sample_symbol) = self.symbol_table.lookup(&predict_stmt.sample) {
            // Variable exists, that's fine
        }
        // Otherwise assume it's a string literal

        let model_symbol = self
            .symbol_table
            .lookup(&predict_stmt.model)
            .ok_or_else(|| UmbraError::Semantic {
                message: format!("Undefined model: '{}'", predict_stmt.model),
                line: predict_stmt.location.line,
                column: predict_stmt.location.column,
            })?;

        if !self.type_checker.types_compatible(
            &Type::Basic(BasicType::Model),
            &model_symbol.type_annotation,
        ) {
            return Err(UmbraError::Semantic {
                message: format!("'{}' is not a Model", predict_stmt.model),
                line: predict_stmt.location.line,
                column: predict_stmt.location.column,
            });
        }

        Ok(())
    }

    // Expression analysis
    fn analyze_expression(&mut self, expression: &Expression) -> UmbraResult<Type> {
        match expression {
            Expression::Literal(literal) => Ok(self.type_checker.infer_literal_type(literal)),
            Expression::Identifier(identifier) => {
                // Handle special case of 'Self' identifier
                if identifier.name == "Self" {
                    // For now, return SelfType - in a full implementation, this would resolve
                    // to the current trait or impl context
                    return Ok(Type::SelfType);
                }

                let symbol = self.symbol_table.lookup(&identifier.name).ok_or_else(|| {
                    UmbraError::Semantic {
                        message: format!("Undefined identifier: '{}'", identifier.name),
                        line: identifier.location.line,
                        column: identifier.location.column,
                    }
                })?;
                Ok(symbol.type_annotation.clone())
            }
            Expression::QualifiedIdentifier(qualified_id) => {
                // For now, treat qualified identifiers as regular identifiers
                // In a full implementation, this would resolve the module path
                let full_name = qualified_id.path.to_string();
                let symbol = self.symbol_table.lookup(&full_name).ok_or_else(|| {
                    UmbraError::Semantic {
                        message: format!("Undefined qualified identifier: '{full_name}'"),
                        line: qualified_id.location.line,
                        column: qualified_id.location.column,
                    }
                })?;
                Ok(symbol.type_annotation.clone())
            }
            Expression::Binary(binary_op) => {
                let left_type = self.analyze_expression(&binary_op.left)?;
                let right_type = self.analyze_expression(&binary_op.right)?;
                self.type_checker.check_binary_operation(
                    &left_type,
                    &binary_op.operator,
                    &right_type,
                )
            }
            Expression::Unary(unary_op) => {
                let operand_type = self.analyze_expression(&unary_op.operand)?;
                self.type_checker
                    .check_unary_operation(&unary_op.operator, &operand_type)
            }
            Expression::Call(function_call) => self.analyze_function_call(function_call),
            Expression::MethodCall(method_call) => self.analyze_method_call(method_call),
            Expression::FieldAccess(field_access) => self.analyze_field_access(field_access),
            Expression::IndexAccess(index_access) => self.analyze_index_access(index_access),
            Expression::List(list_literal) => self.analyze_list_literal(list_literal),
            Expression::Struct(struct_literal) => self.analyze_struct_literal(struct_literal),
            Expression::Match(match_expr) => self.analyze_match_expression(match_expr),
            Expression::Some(some_expr) => self.analyze_some_expression(some_expr),
            Expression::None(none_expr) => self.analyze_none_expression(none_expr),
            Expression::Ok(ok_expr) => self.analyze_ok_expression(ok_expr),
            Expression::Err(err_expr) => self.analyze_err_expression(err_expr),
            // Error handling expressions
            Expression::TryExpression(try_expr) => self.analyze_try_expression(try_expr),
            Expression::ErrorPropagation(prop_expr) => self.analyze_error_propagation(prop_expr),
            // Lambda expressions
            Expression::Lambda(lambda_expr) => self.analyze_lambda_expression(lambda_expr),
        }
    }

    fn analyze_function_call(&mut self, func_call: &FunctionCall) -> UmbraResult<Type> {
        // Check if this is a generic function call
        if let Some(generic_signature) = self.generic_functions.get(&func_call.name).cloned() {
            return self.analyze_generic_function_call(func_call, &generic_signature);
        }

        let (_func_symbol_type, func_signature, return_type) = {
            let func_symbol =
                self.symbol_table
                    .lookup(&func_call.name)
                    .ok_or_else(|| UmbraError::Semantic {
                        message: format!("Undefined function: '{}'", func_call.name),
                        line: func_call.location.line,
                        column: func_call.location.column,
                    })?;

            // Check if it's a regular function or a lambda variable
            match func_symbol.symbol_type {
                SymbolType::Function => {
                    (
                        func_symbol.symbol_type.clone(),
                        func_symbol.function_signature.clone(),
                        func_symbol.type_annotation.clone(),
                    )
                }
                SymbolType::Variable => {
                    // Check if the variable has a function type (lambda)
                    if let Type::Function(param_types, return_type) = &func_symbol.type_annotation {
                        // Create a function signature from the function type
                        let signature = crate::semantic::symbol_table::FunctionSignature {
                            parameters: param_types.clone(),
                            return_type: (**return_type).clone(),
                        };

                        (
                            SymbolType::Function, // Treat as function for the rest of the analysis
                            Some(signature),
                            (**return_type).clone(),
                        )
                    } else {
                        return Err(UmbraError::Semantic {
                            message: format!("'{}' is not a function", func_call.name),
                            line: func_call.location.line,
                            column: func_call.location.column,
                        });
                    }
                }
                _ => {
                    return Err(UmbraError::Semantic {
                        message: format!("'{}' is not a function", func_call.name),
                        line: func_call.location.line,
                        column: func_call.location.column,
                    });
                }
            }
        };

        // Special handling for built-in functions with variable arguments
        if func_call.name == "show" || func_call.name == "println!" || func_call.name == "print!" {
            // These functions accept any number of arguments of any type
            for arg in &func_call.arguments {
                self.analyze_expression(arg)?;
            }
        } else if let Some(signature) = &func_signature {
            // Check argument count
            if func_call.arguments.len() != signature.parameters.len() {
                return Err(UmbraError::Semantic {
                    message: format!(
                        "Function '{}' expects {} arguments, got {}",
                        func_call.name,
                        signature.parameters.len(),
                        func_call.arguments.len()
                    ),
                    line: func_call.location.line,
                    column: func_call.location.column,
                });
            }

            // Check argument types
            for (i, (arg, expected_type)) in func_call
                .arguments
                .iter()
                .zip(&signature.parameters)
                .enumerate()
            {
                let arg_type = self.analyze_expression(arg)?;

                // Special handling for functions that expect String (pointer) but get other types
                let types_compatible = if (func_call.name == "list_length" ||
                                         func_call.name == "list_get" ||
                                         func_call.name == "list_append" ||
                                         func_call.name == "gc_retain" ||
                                         func_call.name == "gc_release") &&
                                         matches!(expected_type, Type::Basic(BasicType::String)) &&
                                         (matches!(arg_type, Type::List(_)) ||
                                          matches!(arg_type, Type::Basic(BasicType::Integer)) ||
                                          matches!(arg_type, Type::Basic(BasicType::Float)) ||
                                          matches!(arg_type, Type::Basic(BasicType::Boolean)) ||
                                          matches!(arg_type, Type::Basic(BasicType::String))) {
                    true // Allow various types to be passed to functions expecting String (pointer)
                } else {
                    self.type_checker.types_compatible(&expected_type, &arg_type)
                };

                if !types_compatible {
                    return Err(UmbraError::Semantic {
                        message: format!(
                            "Argument {} to function '{}': expected {}, got {}",
                            i + 1,
                            func_call.name,
                            self.type_checker.type_to_string(&expected_type),
                            self.type_checker.type_to_string(&arg_type)
                        ),
                        line: func_call.location.line,
                        column: func_call.location.column,
                    });
                }
            }
        }

        Ok(return_type)
    }

    /// Analyze generic function call with type inference and monomorphization
    fn analyze_generic_function_call(
        &mut self,
        func_call: &FunctionCall,
        generic_signature: &FunctionSignature
    ) -> UmbraResult<Type> {
        // Analyze argument types
        let mut arg_types = Vec::new();
        for arg in &func_call.arguments {
            let arg_type = self.analyze_expression(arg)?;
            arg_types.push(arg_type);
        }

        // Infer type arguments from the call
        let inferred_type_args = self.type_inference
            .infer_type_arguments(generic_signature, &arg_types)
            .map_err(|e| UmbraError::Semantic {
                message: format!("Type inference failed for function '{}': {}", func_call.name, e),
                line: func_call.location.line,
                column: func_call.location.column,
            })?;

        // Check if we already have a monomorphized version
        let mangled_name = self.type_inference.mangle_function_name(&func_call.name, &inferred_type_args);

        // Check if this monomorphization already exists
        if let Some(existing_functions) = self.monomorphized_functions.get(&func_call.name) {
            for existing in existing_functions {
                if existing.type_args == inferred_type_args {
                    // Use existing monomorphized function
                    return Ok(existing.signature.return_type.clone());
                }
            }
        }

        // Create new monomorphized function
        let monomorphized = MonomorphizedFunction {
            original_name: func_call.name.clone(),
            mangled_name: mangled_name.clone(),
            type_args: inferred_type_args.clone(),
            signature: FunctionSignature {
                name: mangled_name,
                type_params: vec![], // No type params in monomorphized version
                params: generic_signature.params.iter().map(|(name, typ)| {
                    let substituted_type = self.substitute_type_parameters(typ, &generic_signature.type_params, &inferred_type_args);
                    (name.clone(), substituted_type)
                }).collect(),
                return_type: self.substitute_type_parameters(&generic_signature.return_type, &generic_signature.type_params, &inferred_type_args),
                constraints: vec![],
            },
            body: None, // Body would be filled in during code generation
        };

        // Store the monomorphized function
        self.monomorphized_functions
            .entry(func_call.name.clone())
            .or_insert_with(Vec::new)
            .push(monomorphized.clone());

        // Validate argument types against monomorphized signature
        if monomorphized.signature.params.len() != arg_types.len() {
            return Err(UmbraError::Semantic {
                message: format!(
                    "Function '{}' expects {} arguments, got {}",
                    func_call.name,
                    monomorphized.signature.params.len(),
                    arg_types.len()
                ),
                line: func_call.location.line,
                column: func_call.location.column,
            });
        }

        for (i, ((_, expected_type), actual_type)) in monomorphized.signature.params.iter().zip(arg_types.iter()).enumerate() {
            if !self.type_checker.is_assignable(actual_type, expected_type) {
                return Err(UmbraError::Semantic {
                    message: format!(
                        "Argument {} of function '{}' expects type '{}', got '{}'",
                        i + 1,
                        func_call.name,
                        self.type_to_string(expected_type),
                        self.type_to_string(actual_type)
                    ),
                    line: func_call.location.line,
                    column: func_call.location.column,
                });
            }
        }

        Ok(monomorphized.signature.return_type)
    }

    /// Substitute type parameters in a type with concrete type arguments
    fn substitute_type_parameters(
        &self,
        typ: &Type,
        type_params: &[super::generics::TypeParameter],
        type_args: &[Type],
    ) -> Type {
        match typ {
            Type::TypeParameter { name, .. } => {
                // Find the corresponding type argument
                for (i, param) in type_params.iter().enumerate() {
                    if param.name == *name {
                        return type_args.get(i).cloned().unwrap_or_else(|| typ.clone());
                    }
                }
                typ.clone()
            }
            Type::List(inner) => Type::List(Box::new(self.substitute_type_parameters(inner, type_params, type_args))),
            Type::HashMap(k, v) => Type::HashMap(
                Box::new(self.substitute_type_parameters(k, type_params, type_args)),
                Box::new(self.substitute_type_parameters(v, type_params, type_args)),
            ),
            Type::HashSet(inner) => Type::HashSet(Box::new(self.substitute_type_parameters(inner, type_params, type_args))),
            Type::Optional(inner) => Type::Optional(Box::new(self.substitute_type_parameters(inner, type_params, type_args))),
            Type::Result(ok, err) => Type::Result(
                Box::new(self.substitute_type_parameters(ok, type_params, type_args)),
                Box::new(self.substitute_type_parameters(err, type_params, type_args)),
            ),
            Type::Function(params, return_type) => Type::Function(
                params.iter().map(|p| self.substitute_type_parameters(p, type_params, type_args)).collect(),
                Box::new(self.substitute_type_parameters(return_type, type_params, type_args)),
            ),
            Type::Generic(name, args) => Type::Generic(
                name.clone(),
                args.iter().map(|arg| self.substitute_type_parameters(arg, type_params, type_args)).collect(),
            ),
            _ => typ.clone(),
        }
    }

    /// Get all monomorphized functions for code generation
    pub fn get_monomorphized_functions(&self) -> &HashMap<String, Vec<MonomorphizedFunction>> {
        &self.monomorphized_functions
    }

    /// Helper method to convert type to string for error messages
    fn type_to_string(&self, typ: &Type) -> String {
        match typ {
            Type::Basic(BasicType::Integer) => "Integer".to_string(),
            Type::Basic(BasicType::Float) => "Float".to_string(),
            Type::Basic(BasicType::String) => "String".to_string(),
            Type::Basic(BasicType::Boolean) => "Boolean".to_string(),
            Type::Basic(BasicType::Void) => "Void".to_string(),
            Type::List(inner) => format!("List<{}>", self.type_to_string(inner)),
            Type::HashMap(k, v) => format!("Map<{}, {}>", self.type_to_string(k), self.type_to_string(v)),
            Type::HashSet(inner) => format!("Set<{}>", self.type_to_string(inner)),
            Type::Optional(inner) => format!("Optional<{}>", self.type_to_string(inner)),
            Type::Result(ok, err) => format!("Result<{}, {}>", self.type_to_string(ok), self.type_to_string(err)),
            Type::TypeParameter { name, .. } => name.clone(),
            Type::Generic(name, args) => {
                if args.is_empty() {
                    name.clone()
                } else {
                    format!("{}<{}>", name, args.iter().map(|arg| self.type_to_string(arg)).collect::<Vec<_>>().join(", "))
                }
            }
            Type::Function(params, return_type) => {
                format!("({}) -> {}",
                    params.iter().map(|p| self.type_to_string(p)).collect::<Vec<_>>().join(", "),
                    self.type_to_string(return_type)
                )
            }
            _ => format!("{:?}", typ),
        }
    }

    fn analyze_method_call(&mut self, method_call: &MethodCall) -> UmbraResult<Type> {
        let object_type = self.analyze_expression(&method_call.object)?;

        // Handle Optional type methods
        if let Type::Optional(inner_type) = &object_type {
            match method_call.method_name.as_str() {
                "is_some" => {
                    // is_some() -> Boolean
                    if !method_call.arguments.is_empty() {
                        return Err(UmbraError::Semantic {
                            message: "is_some() takes no arguments".to_string(),
                            line: method_call.location.line,
                            column: method_call.location.column,
                        });
                    }
                    Ok(Type::Basic(BasicType::Boolean))
                }
                "is_none" => {
                    // is_none() -> Boolean
                    if !method_call.arguments.is_empty() {
                        return Err(UmbraError::Semantic {
                            message: "is_none() takes no arguments".to_string(),
                            line: method_call.location.line,
                            column: method_call.location.column,
                        });
                    }
                    Ok(Type::Basic(BasicType::Boolean))
                }
                "unwrap" => {
                    // unwrap() -> T (the inner type)
                    if !method_call.arguments.is_empty() {
                        return Err(UmbraError::Semantic {
                            message: "unwrap() takes no arguments".to_string(),
                            line: method_call.location.line,
                            column: method_call.location.column,
                        });
                    }
                    Ok((**inner_type).clone())
                }
                "map" => {
                    // map(function) -> Optional[U] where function: T -> U
                    if method_call.arguments.len() != 1 {
                        return Err(UmbraError::Semantic {
                            message: "map() takes exactly one argument (a function)".to_string(),
                            line: method_call.location.line,
                            column: method_call.location.column,
                        });
                    }

                    // For now, we'll return Optional[Auto] since we don't have full function type analysis
                    // In a complete implementation, we'd analyze the function argument and determine the return type
                    let _function_arg = self.analyze_expression(&method_call.arguments[0])?;
                    Ok(Type::Optional(Box::new(Type::Auto)))
                }
                "filter" => {
                    // filter(predicate) -> Optional[T] where predicate: T -> Boolean
                    if method_call.arguments.len() != 1 {
                        return Err(UmbraError::Semantic {
                            message: "filter() takes exactly one argument (a predicate function)".to_string(),
                            line: method_call.location.line,
                            column: method_call.location.column,
                        });
                    }

                    let _predicate_arg = self.analyze_expression(&method_call.arguments[0])?;
                    Ok(object_type.clone()) // Returns the same Optional[T] type
                }
                _ => {
                    Err(UmbraError::Semantic {
                        message: format!(
                            "Unknown method '{}' for Optional type",
                            method_call.method_name
                        ),
                        line: method_call.location.line,
                        column: method_call.location.column,
                    })
                }
            }
        }
        // Handle Result type methods
        else if let Type::Result(ok_type, err_type) = &object_type {
            match method_call.method_name.as_str() {
                "is_ok" => {
                    // is_ok() -> Boolean
                    if !method_call.arguments.is_empty() {
                        return Err(UmbraError::Semantic {
                            message: "is_ok() takes no arguments".to_string(),
                            line: method_call.location.line,
                            column: method_call.location.column,
                        });
                    }
                    Ok(Type::Basic(BasicType::Boolean))
                }
                "is_err" => {
                    // is_err() -> Boolean
                    if !method_call.arguments.is_empty() {
                        return Err(UmbraError::Semantic {
                            message: "is_err() takes no arguments".to_string(),
                            line: method_call.location.line,
                            column: method_call.location.column,
                        });
                    }
                    Ok(Type::Basic(BasicType::Boolean))
                }
                "unwrap" => {
                    // unwrap() -> T (the Ok type)
                    if !method_call.arguments.is_empty() {
                        return Err(UmbraError::Semantic {
                            message: "unwrap() takes no arguments".to_string(),
                            line: method_call.location.line,
                            column: method_call.location.column,
                        });
                    }
                    Ok((**ok_type).clone())
                }
                "unwrap_err" => {
                    // unwrap_err() -> E (the Err type)
                    if !method_call.arguments.is_empty() {
                        return Err(UmbraError::Semantic {
                            message: "unwrap_err() takes no arguments".to_string(),
                            line: method_call.location.line,
                            column: method_call.location.column,
                        });
                    }
                    Ok((**err_type).clone())
                }
                "map" => {
                    // map(function) -> Result[U, E] where function: T -> U
                    if method_call.arguments.len() != 1 {
                        return Err(UmbraError::Semantic {
                            message: "map() takes exactly one argument (a function)".to_string(),
                            line: method_call.location.line,
                            column: method_call.location.column,
                        });
                    }

                    // For now, we'll return Result[Auto, E] since we don't have full function type analysis
                    let _function_arg = self.analyze_expression(&method_call.arguments[0])?;
                    Ok(Type::Result(Box::new(Type::Auto), err_type.clone()))
                }
                "map_err" => {
                    // map_err(function) -> Result[T, F] where function: E -> F
                    if method_call.arguments.len() != 1 {
                        return Err(UmbraError::Semantic {
                            message: "map_err() takes exactly one argument (a function)".to_string(),
                            line: method_call.location.line,
                            column: method_call.location.column,
                        });
                    }

                    // For now, we'll return Result[T, Auto] since we don't have full function type analysis
                    let _function_arg = self.analyze_expression(&method_call.arguments[0])?;
                    Ok(Type::Result(ok_type.clone(), Box::new(Type::Auto)))
                }
                _ => {
                    Err(UmbraError::Semantic {
                        message: format!(
                            "Unknown method '{}' for Result type",
                            method_call.method_name
                        ),
                        line: method_call.location.line,
                        column: method_call.location.column,
                    })
                }
            }
        } else {
            // Handle built-in methods for basic types
            match (&object_type, method_call.method_name.as_str()) {
                // to_string() method for basic types
                (Type::Basic(BasicType::Integer), "to_string") => {
                    if !method_call.arguments.is_empty() {
                        return Err(UmbraError::Semantic {
                            message: "to_string() takes no arguments".to_string(),
                            line: method_call.location.line,
                            column: method_call.location.column,
                        });
                    }
                    Ok(Type::Basic(BasicType::String))
                }
                (Type::Basic(BasicType::Float), "to_string") => {
                    if !method_call.arguments.is_empty() {
                        return Err(UmbraError::Semantic {
                            message: "to_string() takes no arguments".to_string(),
                            line: method_call.location.line,
                            column: method_call.location.column,
                        });
                    }
                    Ok(Type::Basic(BasicType::String))
                }
                (Type::Basic(BasicType::Boolean), "to_string") => {
                    if !method_call.arguments.is_empty() {
                        return Err(UmbraError::Semantic {
                            message: "to_string() takes no arguments".to_string(),
                            line: method_call.location.line,
                            column: method_call.location.column,
                        });
                    }
                    Ok(Type::Basic(BasicType::String))
                }
                _ => {
                    // Check if the type implements any traits that provide this method
                    if let Some(return_type) = self.find_trait_method(&object_type, &method_call.method_name) {
                        Ok(return_type)
                    } else {
                        Err(UmbraError::Semantic {
                            message: format!(
                                "Type {} does not have method '{}'",
                                self.type_checker.type_to_string(&object_type),
                                method_call.method_name
                            ),
                            line: method_call.location.line,
                            column: method_call.location.column,
                        })
                    }
                }
            }
        }
    }

    /// Find a method in trait implementations for the given type
    fn find_trait_method(&self, object_type: &Type, method_name: &str) -> Option<Type> {
        // Get the type name for lookup
        let type_name = match object_type {
            Type::Struct(name) => name.clone(),
            Type::Basic(basic_type) => format!("{:?}", basic_type),
            _ => return None,
        };

        // Look through all implementations to find one that matches this type and has the method
        for (_, symbol) in self.symbol_table.symbols() {
                if let SymbolType::Implementation = symbol.symbol_type {
                    if let Some(impl_def) = &symbol.impl_definition {
                        // Check if this implementation is for our type
                        let impl_type_name = match &impl_def.implementing_type {
                            Type::Struct(name) => name.clone(),
                            Type::Basic(basic_type) => format!("{:?}", basic_type),
                            _ => continue,
                        };

                        if impl_type_name == type_name {
                            // First check if this implementation has the method directly
                            for method in &impl_def.methods {
                                if method.name == method_name {
                                    return Some(method.return_type.clone());
                                }
                            }

                            // If implementing a trait, check if the trait has the method
                            if let Some(trait_name) = &impl_def.trait_name {
                                if let Some(trait_def) = self.symbol_table.get_trait(trait_name) {
                                    for trait_method in &trait_def.methods {
                                        if trait_method.name == method_name {
                                            return Some(trait_method.return_type.clone());
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        None
    }

    fn analyze_field_access(&mut self, field_access: &FieldAccess) -> UmbraResult<Type> {
        let object_type = self.analyze_expression(&field_access.object)?;

        match &object_type {
            // Handle built-in types with predefined fields
            Type::Basic(BasicType::Dataset)
            | Type::Basic(BasicType::Model)
            | Type::Basic(BasicType::Tensor) => {
                // Built-in types have predefined fields - for now just return String
                Ok(Type::Basic(BasicType::String))
            }
            // Handle user-defined struct types
            Type::Struct(struct_name) => {
                // Look up the struct definition
                let struct_symbol = self.symbol_table.lookup(struct_name).ok_or_else(|| {
                    UmbraError::Semantic {
                        message: format!("Undefined struct: '{struct_name}'"),
                        line: field_access.location.line,
                        column: field_access.location.column,
                    }
                })?;

                if struct_symbol.symbol_type != SymbolType::Structure {
                    return Err(UmbraError::Semantic {
                        message: format!("'{struct_name}' is not a struct"),
                        line: field_access.location.line,
                        column: field_access.location.column,
                    });
                }

                // Find the field in the struct definition
                if let Some(struct_fields) = &struct_symbol.structure_fields {
                    for field in struct_fields {
                        if field.name == field_access.field {
                            return Ok(field.field_type.clone());
                        }
                    }

                    // Field not found
                    Err(UmbraError::Semantic {
                        message: format!(
                            "Field '{}' not found in struct '{}'",
                            field_access.field,
                            struct_name
                        ),
                        line: field_access.location.line,
                        column: field_access.location.column,
                    })
                } else {
                    Err(UmbraError::Semantic {
                        message: format!("Struct '{struct_name}' has no fields defined"),
                        line: field_access.location.line,
                        column: field_access.location.column,
                    })
                }
            }
            // Handle Self type - for now, assume it's a struct with the field
            Type::SelfType => {
                // In a full implementation, we would resolve Self to the current implementation context
                // For now, we'll assume the field exists and return a basic type
                // This is a placeholder - in reality we'd need to track the current impl context
                match field_access.field.as_str() {
                    "x" | "y" => Ok(Type::Basic(BasicType::Integer)),
                    _ => Ok(Type::Basic(BasicType::String)), // Default fallback
                }
            }
            _ => {
                Err(UmbraError::Semantic {
                    message: format!(
                        "Cannot access field '{}' on type {}",
                        field_access.field,
                        self.type_checker.type_to_string(&object_type)
                    ),
                    line: field_access.location.line,
                    column: field_access.location.column,
                })
            }
        }
    }

    fn analyze_list_literal(&mut self, list_literal: &ListLiteral) -> UmbraResult<Type> {
        if list_literal.elements.is_empty() {
            // Empty list - we'll need type inference or explicit typing
            return Ok(Type::List(Box::new(Type::Basic(BasicType::Integer)))); // Default to Integer list
        }

        // Infer type from first element
        let first_type = self.analyze_expression(&list_literal.elements[0])?;

        // Check all elements have the same type
        for element in &list_literal.elements[1..] {
            let element_type = self.analyze_expression(element)?;
            if !self
                .type_checker
                .types_compatible(&first_type, &element_type)
            {
                return Err(UmbraError::Semantic {
                    message: format!(
                        "List elements must have the same type: {} vs {}",
                        self.type_checker.type_to_string(&first_type),
                        self.type_checker.type_to_string(&element_type)
                    ),
                    line: list_literal.location.line,
                    column: list_literal.location.column,
                });
            }
        }

        Ok(Type::List(Box::new(first_type)))
    }

    fn check_function_returns(&mut self, _func: &FunctionDef) -> UmbraResult<()> {
        // This is a simplified check - a full implementation would do control flow analysis
        // For now, we'll just assume functions are correctly implemented
        Ok(())
    }

    fn analyze_index_access(&mut self, index_access: &IndexAccess) -> UmbraResult<Type> {
        // Analyze the object being indexed
        let object_type = self.analyze_expression(&index_access.object)?;

        // Check if the object is indexable (List type)
        match &object_type {
            Type::List(element_type) => {
                // Analyze the index expression
                let index_type = self.analyze_expression(&index_access.index)?;

                // Check that index is an integer
                if !matches!(index_type, Type::Basic(BasicType::Integer)) {
                    return Err(UmbraError::Semantic {
                        message: format!(
                            "List index must be Integer, got {}",
                            self.type_checker.type_to_string(&index_type)
                        ),
                        line: index_access.location.line,
                        column: index_access.location.column,
                    });
                }

                // Return the element type
                Ok((**element_type).clone())
            }
            _ => Err(UmbraError::Semantic {
                message: format!(
                    "Cannot index into type {}",
                    self.type_checker.type_to_string(&object_type)
                ),
                line: index_access.location.line,
                column: index_access.location.column,
            }),
        }
    }

    /// Get the diagnostics collected during analysis
    #[allow(dead_code)]
    pub fn diagnostics(&self) -> &DiagnosticBag {
        &self.diagnostics
    }

    fn analyze_struct_literal(&mut self, struct_literal: &StructLiteral) -> UmbraResult<Type> {
        // Look up the struct definition and clone the necessary data to avoid borrowing issues
        let (struct_fields, struct_name) = {
            let struct_symbol = self.symbol_table.lookup(&struct_literal.struct_name).ok_or_else(|| {
                UmbraError::Semantic {
                    message: format!("Undefined struct: '{}'", struct_literal.struct_name),
                    line: struct_literal.location.line,
                    column: struct_literal.location.column,
                }
            })?;

            if struct_symbol.symbol_type != SymbolType::Structure {
                return Err(UmbraError::Semantic {
                    message: format!("'{}' is not a struct", struct_literal.struct_name),
                    line: struct_literal.location.line,
                    column: struct_literal.location.column,
                });
            }

            (struct_symbol.structure_fields.clone(), struct_literal.struct_name.clone())
        };

        // Check that all required fields are provided and have correct types
        if let Some(struct_fields) = struct_fields {
            for struct_field in &struct_fields {
                let provided_field = struct_literal.fields.iter()
                    .find(|f| f.name == struct_field.name);

                if let Some(field_init) = provided_field {
                    let field_type = self.analyze_expression(&field_init.value)?;
                    if !self.type_checker.types_compatible(&struct_field.field_type, &field_type) {
                        return Err(UmbraError::Semantic {
                            message: format!(
                                "Field '{}' expects type {}, got {}",
                                struct_field.name,
                                self.type_checker.type_to_string(&struct_field.field_type),
                                self.type_checker.type_to_string(&field_type)
                            ),
                            line: field_init.location.line,
                            column: field_init.location.column,
                        });
                    }
                } else {
                    return Err(UmbraError::Semantic {
                        message: format!("Missing field '{}' in struct literal", struct_field.name),
                        line: struct_literal.location.line,
                        column: struct_literal.location.column,
                    });
                }
            }

            // Check for extra fields
            for field_init in &struct_literal.fields {
                if !struct_fields.iter().any(|f| f.name == field_init.name) {
                    return Err(UmbraError::Semantic {
                        message: format!("Unknown field '{}' in struct '{}'", field_init.name, struct_name),
                        line: field_init.location.line,
                        column: field_init.location.column,
                    });
                }
            }
        }

        Ok(Type::Struct(struct_name))
    }

    fn analyze_match_expression(&mut self, match_expr: &MatchExpression) -> UmbraResult<Type> {
        let expr_type = self.analyze_expression(&match_expr.expression)?;

        if match_expr.arms.is_empty() {
            return Err(UmbraError::Semantic {
                message: "Match expression must have at least one arm".to_string(),
                line: match_expr.location.line,
                column: match_expr.location.column,
            });
        }

        let mut arm_types = Vec::new();

        // Analyze each arm in its own scope
        for arm in &match_expr.arms {
            // Create a new scope for this arm
            self.symbol_table.enter_scope();

            // Analyze pattern and bind variables
            self.analyze_pattern(&arm.pattern, &expr_type)?;

            // Analyze guard if present
            if let Some(guard) = &arm.guard {
                let guard_type = self.analyze_expression(guard)?;
                if !matches!(guard_type, Type::Basic(BasicType::Boolean)) {
                    return Err(UmbraError::Semantic {
                        message: "Guard expression must be boolean".to_string(),
                        line: arm.location.line,
                        column: arm.location.column,
                    });
                }
            }

            // Analyze arm body
            let arm_type = self.analyze_expression(&arm.body)?;
            arm_types.push(arm_type);

            // Exit arm scope
            self.symbol_table.exit_scope();
        }

        // Check that all arms have compatible return types
        let first_arm_type = &arm_types[0];
        for (i, arm_type) in arm_types.iter().enumerate().skip(1) {
            if !self.type_checker.types_compatible(first_arm_type, arm_type) {
                return Err(UmbraError::Semantic {
                    message: format!(
                        "Match arm returns type {}, expected {}",
                        self.type_checker.type_to_string(arm_type),
                        self.type_checker.type_to_string(first_arm_type)
                    ),
                    line: match_expr.arms[i].location.line,
                    column: match_expr.arms[i].location.column,
                });
            }
        }

        // Check pattern exhaustiveness
        let patterns: Vec<Pattern> = match_expr.arms.iter().map(|arm| arm.pattern.clone()).collect();
        if !self.check_pattern_exhaustiveness(&patterns, &expr_type) {
            return Err(UmbraError::Semantic {
                message: format!(
                    "Match expression is not exhaustive for type {}. Consider adding a wildcard pattern '_'",
                    self.type_checker.type_to_string(&expr_type)
                ),
                line: match_expr.location.line,
                column: match_expr.location.column,
            });
        }

        Ok(first_arm_type.clone())
    }

    fn analyze_pattern(&mut self, pattern: &Pattern, expected_type: &Type) -> UmbraResult<()> {
        match pattern {
            Pattern::Some(inner_pattern) => {
                if let Type::Optional(inner_type) = expected_type {
                    self.analyze_pattern(inner_pattern, inner_type)?;
                } else {
                    return Err(UmbraError::Semantic {
                        message: format!(
                            "Some pattern can only match Optional types, got {}",
                            self.type_checker.type_to_string(expected_type)
                        ),
                        line: 0, // TODO: Add location to patterns
                        column: 0,
                    });
                }
            }
            Pattern::None => {
                if !matches!(expected_type, Type::Optional(_)) {
                    return Err(UmbraError::Semantic {
                        message: format!(
                            "None pattern can only match Optional types, got {}",
                            self.type_checker.type_to_string(expected_type)
                        ),
                        line: 0, // TODO: Add location to patterns
                        column: 0,
                    });
                }
            }
            Pattern::Ok(inner_pattern) => {
                if let Type::Result(ok_type, _) = expected_type {
                    self.analyze_pattern(inner_pattern, ok_type)?;
                } else {
                    return Err(UmbraError::Semantic {
                        message: format!(
                            "Ok pattern can only match Result types, got {}",
                            self.type_checker.type_to_string(expected_type)
                        ),
                        line: 0, // TODO: Add location to patterns
                        column: 0,
                    });
                }
            }
            Pattern::Err(inner_pattern) => {
                if let Type::Result(_, err_type) = expected_type {
                    self.analyze_pattern(inner_pattern, err_type)?;
                } else {
                    return Err(UmbraError::Semantic {
                        message: format!(
                            "Err pattern can only match Result types, got {}",
                            self.type_checker.type_to_string(expected_type)
                        ),
                        line: 0, // TODO: Add location to patterns
                        column: 0,
                    });
                }
            }
            Pattern::Union(inner_pattern, variant_index) => {
                if let Type::Union(types) = expected_type {
                    if *variant_index < types.len() {
                        self.analyze_pattern(inner_pattern, &types[*variant_index])?;
                    } else {
                        return Err(UmbraError::Semantic {
                            message: format!(
                                "Union variant index {} out of bounds for union with {} types",
                                variant_index,
                                types.len()
                            ),
                            line: 0, // TODO: Add location to patterns
                            column: 0,
                        });
                    }
                } else {
                    return Err(UmbraError::Semantic {
                        message: format!(
                            "Union pattern can only match Union types, got {}",
                            self.type_checker.type_to_string(expected_type)
                        ),
                        line: 0, // TODO: Add location to patterns
                        column: 0,
                    });
                }
            }
            Pattern::Identifier(var_name) => {
                // Identifier patterns can match any type (they bind the value)
                // Add the variable to the symbol table with the expected type
                let symbol = Symbol::new_variable(
                    var_name.clone(),
                    expected_type.clone(),
                    SourceLocation::new(0, 0), // TODO: Add location to patterns
                    false, // Pattern variables are immutable by default
                );

                // Add the pattern variable to the current scope
                self.symbol_table.define(symbol).map_err(|msg| {
                    UmbraError::Semantic {
                        message: format!("Failed to bind pattern variable '{}': {}", var_name, msg),
                        line: 0, // TODO: Add location to patterns
                        column: 0,
                    }
                })?;
            }
            Pattern::Literal(literal) => {
                let literal_type = self.type_checker.infer_literal_type(literal);
                if !self.type_checker.types_compatible(expected_type, &literal_type) {
                    return Err(UmbraError::Semantic {
                        message: format!(
                            "Literal pattern type {} doesn't match expected type {}",
                            self.type_checker.type_to_string(&literal_type),
                            self.type_checker.type_to_string(expected_type)
                        ),
                        line: 0, // TODO: Add location to patterns
                        column: 0,
                    });
                }
            }
            Pattern::Struct(_, _) => {
                // TODO: Implement struct pattern matching
            }
            Pattern::Wildcard => {
                // Wildcard patterns can match any type
            }
        }
        Ok(())
    }

    fn analyze_some_expression(&mut self, some_expr: &SomeExpression) -> UmbraResult<Type> {
        let value_type = self.analyze_expression(&some_expr.value)?;
        Ok(Type::Optional(Box::new(value_type)))
    }

    fn analyze_none_expression(&mut self, _none_expr: &NoneExpression) -> UmbraResult<Type> {
        // None represents an empty Optional of unknown type
        // In a full implementation, we'd need type inference to determine the inner type
        // For now, we'll return a generic Optional type
        Ok(Type::Optional(Box::new(Type::Auto)))
    }

    fn analyze_ok_expression(&mut self, ok_expr: &OkExpression) -> UmbraResult<Type> {
        let value_type = self.analyze_expression(&ok_expr.value)?;
        // Ok(value) creates a Result[T, Auto] where T is the type of value
        // In a full implementation, we'd need type inference to determine the error type
        Ok(Type::Result(Box::new(value_type), Box::new(Type::Auto)))
    }

    fn analyze_err_expression(&mut self, err_expr: &ErrExpression) -> UmbraResult<Type> {
        let error_type = self.analyze_expression(&err_expr.error)?;
        // Err(error) creates a Result[Auto, E] where E is the type of error
        // In a full implementation, we'd need type inference to determine the success type
        Ok(Type::Result(Box::new(Type::Auto), Box::new(error_type)))
    }

    /// Check if a set of patterns is exhaustive for the given type
    #[allow(dead_code)]
    fn check_pattern_exhaustiveness(&self, patterns: &[Pattern], match_type: &Type) -> bool {
        // Check for wildcard pattern (always exhaustive)
        if patterns.iter().any(|p| matches!(p, Pattern::Wildcard)) {
            return true;
        }

        match match_type {
            Type::Basic(BasicType::Boolean) => {
                // Boolean requires both true and false patterns
                let has_true = patterns.iter().any(|p| {
                    matches!(p, Pattern::Literal(Literal::Boolean(true)))
                });
                let has_false = patterns.iter().any(|p| {
                    matches!(p, Pattern::Literal(Literal::Boolean(false)))
                });
                has_true && has_false
            }
            Type::Optional(_) => {
                // Optional requires both Some and None patterns
                let has_some = patterns.iter().any(|p| matches!(p, Pattern::Some(_)));
                let has_none = patterns.iter().any(|p| matches!(p, Pattern::None));
                has_some && has_none
            }
            Type::Result(_, _) => {
                // Result requires both Ok and Err patterns
                let has_ok = patterns.iter().any(|p| matches!(p, Pattern::Ok(_)));
                let has_err = patterns.iter().any(|p| matches!(p, Pattern::Err(_)));
                has_ok && has_err
            }
            Type::Union(types) => {
                // Union requires patterns for all constituent types
                // This is a simplified check - a full implementation would be more complex
                types.iter().all(|union_type| {
                    patterns.iter().any(|pattern| {
                        self.pattern_matches_type(pattern, union_type)
                    })
                })
            }
            _ => {
                // For other types, we assume they're exhaustive if there's at least one pattern
                // A full implementation would have more sophisticated checking
                !patterns.is_empty()
            }
        }
    }

    /// Check if a pattern can match a given type
    #[allow(dead_code)]
    fn pattern_matches_type(&self, pattern: &Pattern, pattern_type: &Type) -> bool {
        match (pattern, pattern_type) {
            (Pattern::Wildcard, _) => true,
            (Pattern::Identifier(_), _) => true, // Variables can match any type
            (Pattern::Literal(lit), Type::Basic(basic_type)) => {
                match (lit, basic_type) {
                    (Literal::Boolean(_), BasicType::Boolean) => true,
                    (Literal::Integer(_), BasicType::Integer) => true,
                    (Literal::Float(_), BasicType::Float) => true,
                    (Literal::String(_), BasicType::String) => true,
                    _ => false,
                }
            }
            (Pattern::Some(_), Type::Optional(_)) => true,
            (Pattern::None, Type::Optional(_)) => true,
            (Pattern::Ok(_), Type::Result(_, _)) => true,
            (Pattern::Err(_), Type::Result(_, _)) => true,
            (Pattern::Struct(struct_name, _), Type::Struct(type_name)) => struct_name == type_name,
            _ => false,
        }
    }

    // Error handling analysis methods

    fn analyze_try_statement(&mut self, try_stmt: &TryStatement) -> UmbraResult<()> {
        // Analyze the try block
        for statement in &try_stmt.try_block {
            self.analyze_statement(statement)?;
        }

        // Analyze catch clauses
        for catch_clause in &try_stmt.catch_clauses {
            // Create new scope for catch block
            self.symbol_table.enter_scope();

            // If there's an error variable, add it to the scope
            if let (Some(error_type), Some(error_var)) = (&catch_clause.error_type, &catch_clause.error_variable) {
                let symbol = Symbol::new_variable(
                    error_var.clone(),
                    error_type.clone(),
                    catch_clause.location,
                    false,
                );
                self.symbol_table.define(symbol).map_err(|e| UmbraError::Semantic {
                    message: e,
                    line: catch_clause.location.line,
                    column: catch_clause.location.column,
                })?;
            }

            // Analyze catch block
            for statement in &catch_clause.catch_block {
                self.analyze_statement(statement)?;
            }
            self.symbol_table.exit_scope();
        }

        // Analyze finally block if present
        if let Some(finally_block) = &try_stmt.finally_block {
            for statement in finally_block {
                self.analyze_statement(statement)?;
            }
        }

        Ok(())
    }

    fn analyze_throw_statement(&mut self, throw_stmt: &ThrowStatement) -> UmbraResult<()> {
        // Analyze the error expression
        let error_type = self.analyze_expression(&throw_stmt.error_expression)?;

        // Verify that the expression is throwable (implements Error trait or is a string)
        match error_type {
            Type::Basic(BasicType::String) => Ok(()), // Strings are always throwable
            Type::Struct(_) => Ok(()), // Struct types might be errors
            _ => Err(UmbraError::Semantic {
                message: "Thrown expression must be an error type or string".to_string(),
                line: throw_stmt.location.line,
                column: throw_stmt.location.column,
            }),
        }
    }

    fn analyze_panic_statement(&mut self, panic_stmt: &PanicStatement) -> UmbraResult<()> {
        // Analyze the panic message if present
        if let Some(message) = &panic_stmt.message {
            let message_type = self.analyze_expression(message)?;

            // Verify that the message is a string
            if !self.type_checker.types_compatible(&Type::Basic(BasicType::String), &message_type) {
                return Err(UmbraError::Semantic {
                    message: "Panic message must be a string".to_string(),
                    line: panic_stmt.location.line,
                    column: panic_stmt.location.column,
                });
            }
        }

        Ok(())
    }

    fn analyze_try_expression(&mut self, try_expr: &TryExpression) -> UmbraResult<Type> {
        // Analyze the inner expression
        let inner_type = self.analyze_expression(&try_expr.expression)?;

        // Try expressions should work with Result types
        match &inner_type {
            Type::Result(ok_type, _) => Ok((**ok_type).clone()),
            _ => Err(UmbraError::Semantic {
                message: "Try expression can only be used with Result types".to_string(),
                line: try_expr.location.line,
                column: try_expr.location.column,
            }),
        }
    }

    fn analyze_error_propagation(&mut self, prop_expr: &ErrorPropagationExpression) -> UmbraResult<Type> {
        // Analyze the inner expression
        let inner_type = self.analyze_expression(&prop_expr.expression)?;

        // Error propagation should work with Result and Optional types
        match &inner_type {
            Type::Result(ok_type, _) => Ok((**ok_type).clone()),
            Type::Optional(inner_type) => Ok((**inner_type).clone()),
            _ => Err(UmbraError::Semantic {
                message: "Error propagation operator (?) can only be used with Result or Optional types".to_string(),
                line: prop_expr.location.line,
                column: prop_expr.location.column,
            }),
        }
    }

    fn analyze_lambda_expression(&mut self, lambda_expr: &LambdaExpression) -> UmbraResult<Type> {
        // Create a new scope for lambda parameters
        self.symbol_table.enter_scope();

        let mut param_types = Vec::new();

        // Add parameters to the symbol table
        for param in &lambda_expr.parameters {
            let param_type = if let Some(annotation) = &param.type_annotation {
                annotation.clone()
            } else {
                // Use type inference for parameters without annotations
                Type::Auto
            };

            param_types.push(param_type.clone());

            // Add parameter to symbol table
            self.symbol_table.define(
                crate::semantic::symbol_table::Symbol {
                    name: param.name.clone(),
                    symbol_type: crate::semantic::symbol_table::SymbolType::Variable,
                    type_annotation: param_type,
                    location: param.location.clone(),
                    is_mutable: false,
                    is_initialized: true,
                    function_signature: None,
                    visibility: crate::semantic::symbol_table::Visibility::Private,
                    module_path: None,
                    structure_fields: None,
                    trait_definition: None,
                    impl_definition: None,
                },
            ).map_err(|e| UmbraError::Semantic {
                message: e,
                line: param.location.line,
                column: param.location.column,
            })?;
        }

        // Analyze the body expression
        let body_type = self.analyze_expression(&lambda_expr.body)?;

        // Check return type annotation if present
        let return_type = if let Some(annotation) = &lambda_expr.return_type {
            if !self.type_checker.types_compatible(annotation, &body_type) {
                return Err(UmbraError::Semantic {
                    message: format!(
                        "Lambda body returns type {}, but return type annotation specifies {}",
                        self.type_checker.type_to_string(&body_type),
                        self.type_checker.type_to_string(annotation)
                    ),
                    line: lambda_expr.location.line,
                    column: lambda_expr.location.column,
                });
            }
            annotation.clone()
        } else {
            body_type
        };

        // Pop the lambda scope
        self.symbol_table.exit_scope();

        // Return function type
        Ok(Type::Function(param_types, Box::new(return_type)))
    }

    fn analyze_error_definition(&mut self, error_def: &ErrorDefinition) -> UmbraResult<()> {
        // Check if error name is already defined
        if self.symbol_table.lookup(&error_def.name).is_some() {
            return Err(UmbraError::Semantic {
                message: format!("Error '{}' is already defined", error_def.name),
                line: error_def.location.line,
                column: error_def.location.column,
            });
        }

        // Validate parent error if specified
        if let Some(parent_name) = &error_def.parent_error {
            if self.symbol_table.lookup(parent_name).is_none() {
                return Err(UmbraError::Semantic {
                    message: format!("Parent error '{parent_name}' is not defined"),
                    line: error_def.location.line,
                    column: error_def.location.column,
                });
            }
        }

        // Validate field types
        for field in &error_def.fields {
            self.validate_type(&field.field_type)?;
        }

        // Create error symbol and add to symbol table
        let error_symbol = Symbol::new_structure(
            error_def.name.clone(),
            error_def.fields.clone(),
            error_def.location,
        );

        self.symbol_table.define(error_symbol).map_err(|e| UmbraError::Semantic {
            message: e,
            line: error_def.location.line,
            column: error_def.location.column,
        })?;

        Ok(())
    }

    /// Analyze trait definition
    fn analyze_trait_definition(&mut self, trait_def: &TraitDef) -> UmbraResult<()> {
        // Register the trait in the symbol table
        self.symbol_table.define_trait(
            trait_def.name.clone(),
            trait_def.clone(),
        ).map_err(|e| UmbraError::Type {
            message: e,
            line: trait_def.location.line,
            column: trait_def.location.column,
            expected_type: None,
            actual_type: None,
        })?;

        // Analyze trait methods
        for method in &trait_def.methods {
            self.analyze_trait_method(method)?;
        }

        // Analyze associated types
        for assoc_type in &trait_def.associated_types {
            self.analyze_associated_type(assoc_type)?;
        }

        Ok(())
    }

    /// Analyze trait method
    fn analyze_trait_method(&mut self, method: &TraitMethod) -> UmbraResult<()> {
        // Validate method signature
        for param in &method.parameters {
            self.validate_type(&param.type_annotation)?;
        }
        self.validate_type(&method.return_type)?;

        // If there's a default implementation, analyze it
        if let Some(body) = &method.default_body {
            for stmt in body {
                self.analyze_statement(stmt)?;
            }
        }

        Ok(())
    }

    /// Analyze associated type
    fn analyze_associated_type(&mut self, assoc_type: &AssociatedType) -> UmbraResult<()> {
        // Validate bounds
        for bound in &assoc_type.bounds {
            self.validate_type_bound(bound)?;
        }

        // Validate default type if present
        if let Some(default_type) = &assoc_type.default {
            self.validate_type(default_type)?;
        }

        Ok(())
    }

    /// Analyze implementation
    fn analyze_implementation(&mut self, impl_def: &ImplDef) -> UmbraResult<()> {
        // Validate the implementing type
        self.validate_type(&impl_def.implementing_type)?;

        // If implementing a trait, validate trait exists
        if let Some(trait_name) = &impl_def.trait_name {
            if !self.symbol_table.trait_exists(trait_name) {
                return Err(UmbraError::Type {
                    message: format!("Trait '{}' not found", trait_name),
                    line: impl_def.location.line,
                    column: impl_def.location.column,
                    expected_type: None,
                    actual_type: None,
                });
            }
        }

        // Analyze implementation methods
        for method in &impl_def.methods {
            self.analyze_function(method)?;
        }

        // Analyze associated type implementations
        for type_assoc in &impl_def.associated_types {
            self.validate_type(&type_assoc.type_value)?;
        }

        // Register the implementation in the symbol table
        let impl_name = if let Some(trait_name) = &impl_def.trait_name {
            format!("{}::{}", trait_name, self.type_to_string(&impl_def.implementing_type))
        } else {
            format!("impl::{}", self.type_to_string(&impl_def.implementing_type))
        };

        let impl_symbol = Symbol::new_implementation(
            impl_name,
            impl_def.clone(),
            impl_def.location,
        );

        if let Err(e) = self.symbol_table.define(impl_symbol) {
            return Err(UmbraError::Semantic {
                message: e,
                line: impl_def.location.line,
                column: impl_def.location.column,
            });
        }

        Ok(())
    }

    /// Validate type bound
    fn validate_type_bound(&mut self, bound: &TypeBound) -> UmbraResult<()> {
        match bound {
            TypeBound::Trait(trait_name) => {
                if !self.symbol_table.trait_exists(trait_name) {
                    return Err(UmbraError::Type {
                        message: format!("Trait '{}' not found", trait_name),
                        line: 0,
                        column: 0,
                        expected_type: None,
                        actual_type: None,
                    });
                }
            }
            TypeBound::Lifetime(_) => {
                // Lifetime bounds are always valid for now
            }
            TypeBound::Subtype(_) => {
                // Subtype bounds are always valid for now
            }
            TypeBound::HigherRanked(_, _) => {
                // Higher-ranked bounds are always valid for now
            }
        }
        Ok(())
    }

    fn validate_type(&self, type_annotation: &Type) -> UmbraResult<()> {
        match type_annotation {
            Type::Basic(_) => Ok(()),
            Type::List(inner_type) => self.validate_type(inner_type),
            Type::Optional(inner_type) => self.validate_type(inner_type),
            Type::Result(ok_type, err_type) => {
                self.validate_type(ok_type)?;
                self.validate_type(err_type)
            }
            Type::Struct(name) => {
                if self.symbol_table.lookup(name).is_none() {
                    Err(UmbraError::Semantic {
                        message: format!("Unknown type: {name}"),
                        line: 0,
                        column: 0,
                    })
                } else {
                    Ok(())
                }
            }
            Type::Union(_) => Ok(()), // Union types are validated elsewhere
            Type::Generic(_, _) => Ok(()), // Generic types are validated elsewhere
            Type::Auto => Ok(()), // Auto types are inferred
            Type::HashMap(k, v) => {
                self.validate_type(k)?;
                self.validate_type(v)
            }
            Type::HashSet(t) => self.validate_type(t),
            Type::Box(t) => self.validate_type(t),
            Type::Rc(t) => self.validate_type(t),
            Type::Arc(t) => self.validate_type(t),
            Type::Weak(t) => self.validate_type(t),
            Type::RefCell(t) => self.validate_type(t),
            Type::Mutex(t) => self.validate_type(t),
            Type::TypeParameter { .. } => Ok(()),
            Type::GenericInstance { base: _, type_args } => {
                // base is a String, not a Type, so we don't validate it
                for arg in type_args {
                    self.validate_type(arg)?;
                }
                Ok(())
            },
            Type::AssociatedType { .. } => Ok(()), // trait_name and type_name are Strings
            Type::HigherKinded { .. } => Ok(()),
            Type::Function(params, ret) => {
                for param in params {
                    self.validate_type(param)?;
                }
                self.validate_type(ret)
            },
            Type::Tuple(types) => {
                for t in types {
                    self.validate_type(t)?;
                }
                Ok(())
            },
            Type::Array(t, _) => self.validate_type(t),
            Type::Slice(t) => self.validate_type(t),
            Type::Reference(t) => self.validate_type(t),
            Type::MutableReference(t) => self.validate_type(t),
            Type::RawPointer(t) => self.validate_type(t),
            Type::MutableRawPointer(t) => self.validate_type(t),
            Type::TypeVariable(_) => Ok(()),
            Type::SelfType => Ok(()), // Self type is always valid in trait context // Type variables are always valid
        }
    }

    // Constant folding methods

    /// Perform constant folding on the program
    pub fn fold_constants(&mut self, program: &mut Program) -> UmbraResult<()> {
        for statement in &mut program.statements {
            self.fold_constants_in_statement(statement)?;
        }
        Ok(())
    }

    /// Fold constants in a statement
    fn fold_constants_in_statement(&mut self, statement: &mut Statement) -> UmbraResult<()> {
        match statement {
            Statement::Variable(var) => {
                self.fold_constants_in_expression(&mut var.value)?;
            }
            Statement::Function(func) => {
                for stmt in &mut func.body {
                    self.fold_constants_in_statement(stmt)?;
                }
            }
            Statement::Expression(expr) => {
                self.fold_constants_in_expression(&mut expr.expression)?;
            }
            Statement::Return(ret) => {
                if let Some(value) = &mut ret.value {
                    self.fold_constants_in_expression(value)?;
                }
            }
            Statement::When(when) => {
                self.fold_constants_in_expression(&mut when.condition)?;
                for stmt in &mut when.then_body {
                    self.fold_constants_in_statement(stmt)?;
                }
                if let Some(else_body) = &mut when.else_body {
                    for stmt in else_body {
                        self.fold_constants_in_statement(stmt)?;
                    }
                }
            }
            Statement::Assignment(assign) => {
                self.fold_constants_in_expression(&mut assign.value)?;
            }
            _ => {
                // Other statements don't contain expressions to fold
            }
        }
        Ok(())
    }

    /// Fold constants in an expression
    fn fold_constants_in_expression(&mut self, expression: &mut Expression) -> UmbraResult<()> {
        match expression {
            Expression::Binary(binop) => {
                // First, fold constants in operands
                self.fold_constants_in_expression(&mut binop.left)?;
                self.fold_constants_in_expression(&mut binop.right)?;

                // Then try to fold the binary operation itself
                if let Some(folded) = self.try_fold_binary_operation(binop)? {
                    *expression = folded;
                }
            }
            Expression::Unary(unop) => {
                // First, fold constants in operand
                self.fold_constants_in_expression(&mut unop.operand)?;

                // Then try to fold the unary operation itself
                if let Some(folded) = self.try_fold_unary_operation(unop)? {
                    *expression = folded;
                }
            }
            Expression::Call(call) => {
                // Fold constants in function arguments
                for arg in &mut call.arguments {
                    self.fold_constants_in_expression(arg)?;
                }
            }
            Expression::List(list) => {
                // Fold constants in list elements
                for element in &mut list.elements {
                    self.fold_constants_in_expression(element)?;
                }
            }
            Expression::IndexAccess(index) => {
                self.fold_constants_in_expression(&mut index.object)?;
                self.fold_constants_in_expression(&mut index.index)?;
            }
            Expression::FieldAccess(field) => {
                self.fold_constants_in_expression(&mut field.object)?;
            }
            Expression::MethodCall(method) => {
                self.fold_constants_in_expression(&mut method.object)?;
                for arg in &mut method.arguments {
                    self.fold_constants_in_expression(arg)?;
                }
            }
            _ => {
                // Literals and identifiers don't need folding
            }
        }
        Ok(())
    }

    /// Try to fold a binary operation if both operands are constants
    fn try_fold_binary_operation(&self, binop: &BinaryOp) -> UmbraResult<Option<Expression>> {
        use crate::parser::ast::BinaryOperator;

        match (&*binop.left, &*binop.right) {
            (Expression::Literal(Literal::Integer(left)), Expression::Literal(Literal::Integer(right))) => {
                let result = match binop.operator {
                    BinaryOperator::Add => left + right,
                    BinaryOperator::Subtract => left - right,
                    BinaryOperator::Multiply => left * right,
                    BinaryOperator::Divide => {
                        if *right == 0 {
                            return Ok(None); // Don't fold division by zero
                        }
                        left / right
                    }
                    BinaryOperator::Modulo => {
                        if *right == 0 {
                            return Ok(None); // Don't fold modulo by zero
                        }
                        left % right
                    }
                    _ => return Ok(None), // Don't fold comparison or logical operations for now
                };

                Ok(Some(Expression::Literal(Literal::Integer(result))))
            }
            (Expression::Literal(Literal::Float(left)), Expression::Literal(Literal::Float(right))) => {
                let result = match binop.operator {
                    BinaryOperator::Add => left + right,
                    BinaryOperator::Subtract => left - right,
                    BinaryOperator::Multiply => left * right,
                    BinaryOperator::Divide => {
                        if *right == 0.0 {
                            return Ok(None); // Don't fold division by zero
                        }
                        left / right
                    }
                    BinaryOperator::Modulo => {
                        if *right == 0.0 {
                            return Ok(None); // Don't fold modulo by zero
                        }
                        left % right
                    }
                    _ => return Ok(None), // Don't fold comparison or logical operations for now
                };

                Ok(Some(Expression::Literal(Literal::Float(result))))
            }
            (Expression::Literal(Literal::Boolean(left)), Expression::Literal(Literal::Boolean(right))) => {
                let result = match binop.operator {
                    BinaryOperator::And => *left && *right,
                    BinaryOperator::Or => *left || *right,
                    BinaryOperator::Equal => left == right,
                    BinaryOperator::NotEqual => left != right,
                    _ => return Ok(None), // Don't fold other operations on booleans
                };

                Ok(Some(Expression::Literal(Literal::Boolean(result))))
            }
            (Expression::Literal(Literal::String(left)), Expression::Literal(Literal::String(right))) => {
                match binop.operator {
                    BinaryOperator::Add => {
                        // String concatenation
                        Ok(Some(Expression::Literal(Literal::String(format!("{left}{right}")))))
                    }
                    _ => Ok(None), // Don't fold other operations on strings
                }
            }
            _ => Ok(None), // Can't fold mixed types or non-literals
        }
    }

    /// Try to fold a unary operation if the operand is a constant
    fn try_fold_unary_operation(&self, unop: &UnaryOp) -> UmbraResult<Option<Expression>> {
        use crate::parser::ast::UnaryOperator;

        match &*unop.operand {
            Expression::Literal(Literal::Integer(value)) => {
                let result = match unop.operator {
                    UnaryOperator::Minus => -value,
                    _ => return Ok(None), // Don't fold logical not on integers
                };

                Ok(Some(Expression::Literal(Literal::Integer(result))))
            }
            Expression::Literal(Literal::Float(value)) => {
                let result = match unop.operator {
                    UnaryOperator::Minus => -value,
                    _ => return Ok(None), // Don't fold logical not on floats
                };

                Ok(Some(Expression::Literal(Literal::Float(result))))
            }
            Expression::Literal(Literal::Boolean(value)) => {
                match unop.operator {
                    UnaryOperator::Not => {
                        Ok(Some(Expression::Literal(Literal::Boolean(!value))))
                    }
                    _ => Ok(None), // Don't fold arithmetic operations on booleans
                }
            }
            _ => Ok(None), // Can't fold non-literals
        }
    }

    // Loop optimization methods

    /// Perform loop optimizations on the program
    pub fn optimize_loops(&mut self, program: &mut Program) -> UmbraResult<()> {
        for statement in &mut program.statements {
            self.optimize_loops_in_statement(statement)?;
        }
        Ok(())
    }

    /// Optimize loops in a statement
    fn optimize_loops_in_statement(&mut self, statement: &mut Statement) -> UmbraResult<()> {
        match statement {
            Statement::Function(func) => {
                for stmt in &mut func.body {
                    self.optimize_loops_in_statement(stmt)?;
                }
            }
            Statement::Repeat(repeat) => {
                // Try to optimize this repeat loop
                self.optimize_repeat_loop(repeat)?;

                // Recursively optimize nested loops
                for stmt in &mut repeat.body {
                    self.optimize_loops_in_statement(stmt)?;
                }
            }
            Statement::When(when) => {
                // Optimize loops in conditional branches
                for stmt in &mut when.then_body {
                    self.optimize_loops_in_statement(stmt)?;
                }
                if let Some(else_body) = &mut when.else_body {
                    for stmt in else_body {
                        self.optimize_loops_in_statement(stmt)?;
                    }
                }
            }
            _ => {
                // Other statements don't contain loops
            }
        }
        Ok(())
    }

    /// Optimize a repeat loop
    fn optimize_repeat_loop(&mut self, repeat: &mut RepeatStatement) -> UmbraResult<()> {
        // Check if this loop can be unrolled
        if let Some(unrolled) = self.try_unroll_loop(repeat)? {
            // Replace the loop body with unrolled statements
            repeat.body = unrolled;
        }

        // Check for loop invariant code motion
        self.move_loop_invariants(repeat)?;

        // Check for strength reduction opportunities
        self.apply_strength_reduction(repeat)?;

        Ok(())
    }

    /// Try to unroll a loop if it has a small, constant iteration count
    fn try_unroll_loop(&self, repeat: &RepeatStatement) -> UmbraResult<Option<Vec<Statement>>> {
        const MAX_UNROLL_ITERATIONS: i64 = 8;
        const MAX_UNROLL_BODY_SIZE: usize = 5;

        // Only unroll small loops
        if repeat.body.len() > MAX_UNROLL_BODY_SIZE {
            return Ok(None);
        }

        // Try to determine if this is a simple counting loop
        if let Some(iterations) = self.analyze_loop_iterations(repeat)? {
            if iterations > 0 && iterations <= MAX_UNROLL_ITERATIONS {
                // Unroll the loop
                let mut unrolled = Vec::new();

                for i in 0..iterations {
                    // Clone the loop body and substitute the loop variable
                    for stmt in &repeat.body {
                        let mut unrolled_stmt = stmt.clone();
                        self.substitute_loop_variable(&mut unrolled_stmt, i)?;
                        unrolled.push(unrolled_stmt);
                    }
                }

                return Ok(Some(unrolled));
            }
        }

        Ok(None)
    }

    /// Analyze loop to determine iteration count
    fn analyze_loop_iterations(&self, repeat: &RepeatStatement) -> UmbraResult<Option<i64>> {
        // For now, only handle simple numeric repeat loops
        // TODO: Add more sophisticated loop analysis

        match &repeat.iterable {
            Expression::Literal(Literal::Integer(count)) => {
                Ok(Some(*count))
            }
            Expression::List(list) => {
                // If iterating over a literal list, we know the count
                Ok(Some(list.elements.len() as i64))
            }
            _ => Ok(None), // Can't determine iteration count
        }
    }

    /// Substitute loop variable in unrolled statements
    fn substitute_loop_variable(&self, statement: &mut Statement, iteration: i64) -> UmbraResult<()> {
        // This is a simplified implementation
        // In a real compiler, we'd need more sophisticated variable tracking
        match statement {
            Statement::Variable(var) => {
                self.substitute_loop_variable_in_expression(&mut var.value, iteration)?;
            }
            Statement::Assignment(assign) => {
                self.substitute_loop_variable_in_expression(&mut assign.value, iteration)?;
            }
            Statement::Expression(expr) => {
                self.substitute_loop_variable_in_expression(&mut expr.expression, iteration)?;
            }
            Statement::Return(ret) => {
                if let Some(value) = &mut ret.value {
                    self.substitute_loop_variable_in_expression(value, iteration)?;
                }
            }
            _ => {
                // Other statements don't need substitution for now
            }
        }
        Ok(())
    }

    /// Substitute loop variable in expressions
    fn substitute_loop_variable_in_expression(&self, expression: &mut Expression, iteration: i64) -> UmbraResult<()> {
        match expression {
            Expression::Identifier(id) => {
                // If this is a loop index variable, replace with the current iteration
                if id.name == "i" || id.name == "index" {
                    *expression = Expression::Literal(Literal::Integer(iteration));
                }
            }
            Expression::Binary(binop) => {
                self.substitute_loop_variable_in_expression(&mut binop.left, iteration)?;
                self.substitute_loop_variable_in_expression(&mut binop.right, iteration)?;
            }
            Expression::Unary(unop) => {
                self.substitute_loop_variable_in_expression(&mut unop.operand, iteration)?;
            }
            Expression::Call(call) => {
                for arg in &mut call.arguments {
                    self.substitute_loop_variable_in_expression(arg, iteration)?;
                }
            }
            Expression::List(list) => {
                for element in &mut list.elements {
                    self.substitute_loop_variable_in_expression(element, iteration)?;
                }
            }
            _ => {
                // Other expressions don't need substitution
            }
        }
        Ok(())
    }

    /// Move loop-invariant code out of loops
    fn move_loop_invariants(&mut self, repeat: &mut RepeatStatement) -> UmbraResult<()> {
        let mut invariant_statements = Vec::new();
        let mut remaining_statements = Vec::new();

        for statement in &repeat.body {
            if self.is_loop_invariant(statement, repeat)? {
                invariant_statements.push(statement.clone());
            } else {
                remaining_statements.push(statement.clone());
            }
        }

        // For now, we don't actually move the statements out of the loop
        // In a real implementation, we'd need to track the loop context
        // and move invariants to the appropriate scope

        Ok(())
    }

    /// Check if a statement is loop-invariant
    fn is_loop_invariant(&self, statement: &Statement, _repeat: &RepeatStatement) -> UmbraResult<bool> {
        // Simplified check: a statement is loop-invariant if it doesn't
        // reference any variables that are modified in the loop

        match statement {
            Statement::Variable(var) => {
                // Variable declarations with constant initializers are invariant
                self.is_expression_loop_invariant(&var.value)
            }
            Statement::Assignment(_) => {
                // Assignments are generally not invariant
                Ok(false)
            }
            Statement::Expression(expr) => {
                // Expression statements are invariant if the expression is invariant
                self.is_expression_loop_invariant(&expr.expression)
            }
            _ => Ok(false),
        }
    }

    /// Check if an expression is loop-invariant
    fn is_expression_loop_invariant(&self, expression: &Expression) -> UmbraResult<bool> {
        match expression {
            Expression::Literal(_) => Ok(true), // Literals are always invariant
            Expression::Identifier(id) => {
                // For now, assume all identifiers might be modified in the loop
                // In a real implementation, we'd track variable modifications
                Ok(id.name.starts_with("CONST_") || id.name.chars().all(|c| c.is_uppercase() || c == '_'))
            }
            Expression::Binary(binop) => {
                // Binary operations are invariant if both operands are invariant
                let left_invariant = self.is_expression_loop_invariant(&binop.left)?;
                let right_invariant = self.is_expression_loop_invariant(&binop.right)?;
                Ok(left_invariant && right_invariant)
            }
            Expression::Unary(unop) => {
                // Unary operations are invariant if the operand is invariant
                self.is_expression_loop_invariant(&unop.operand)
            }
            Expression::Call(_) => {
                // Function calls are generally not invariant (might have side effects)
                Ok(false)
            }
            _ => Ok(false),
        }
    }

    /// Apply strength reduction optimizations
    fn apply_strength_reduction(&mut self, repeat: &mut RepeatStatement) -> UmbraResult<()> {
        // Strength reduction replaces expensive operations with cheaper ones
        // For example: i * 4 in a loop can be replaced with addition

        for statement in &mut repeat.body {
            self.apply_strength_reduction_to_statement(statement)?;
        }

        Ok(())
    }

    /// Apply strength reduction to a statement
    fn apply_strength_reduction_to_statement(&mut self, statement: &mut Statement) -> UmbraResult<()> {
        match statement {
            Statement::Variable(var) => {
                self.apply_strength_reduction_to_expression(&mut var.value)?;
            }
            Statement::Assignment(assign) => {
                self.apply_strength_reduction_to_expression(&mut assign.value)?;
            }
            Statement::Expression(expr) => {
                self.apply_strength_reduction_to_expression(&mut expr.expression)?;
            }
            Statement::Return(ret) => {
                if let Some(value) = &mut ret.value {
                    self.apply_strength_reduction_to_expression(value)?;
                }
            }
            _ => {
                // Other statements don't need strength reduction
            }
        }
        Ok(())
    }

    /// Apply strength reduction to an expression
    fn apply_strength_reduction_to_expression(&mut self, expression: &mut Expression) -> UmbraResult<()> {
        match expression {
            Expression::Binary(binop) => {
                // Recursively apply to operands first
                self.apply_strength_reduction_to_expression(&mut binop.left)?;
                self.apply_strength_reduction_to_expression(&mut binop.right)?;

                // Look for strength reduction opportunities
                use crate::parser::ast::BinaryOperator;
                match binop.operator {
                    BinaryOperator::Multiply => {
                        // Check for multiplication by powers of 2
                        if let Expression::Literal(Literal::Integer(n)) = &*binop.right {
                            if Self::is_power_of_two(*n) && *n > 1 {
                                // Replace multiplication by power of 2 with left shift
                                // This would require adding a shift operator to the AST
                                // For now, we just mark it as optimizable
                            }
                        }
                    }
                    BinaryOperator::Divide => {
                        // Check for division by powers of 2
                        if let Expression::Literal(Literal::Integer(n)) = &*binop.right {
                            if Self::is_power_of_two(*n) && *n > 1 {
                                // Replace division by power of 2 with right shift
                                // This would require adding a shift operator to the AST
                                // For now, we just mark it as optimizable
                            }
                        }
                    }
                    _ => {}
                }
            }
            Expression::Unary(unop) => {
                self.apply_strength_reduction_to_expression(&mut unop.operand)?;
            }
            Expression::Call(call) => {
                for arg in &mut call.arguments {
                    self.apply_strength_reduction_to_expression(arg)?;
                }
            }
            _ => {
                // Other expressions don't need strength reduction
            }
        }
        Ok(())
    }

    /// Helper function to check if a number is a power of two
    fn is_power_of_two(n: i64) -> bool {
        n > 0 && (n & (n - 1)) == 0
    }

    // Memory management optimization methods

    /// Perform memory management optimizations on the program
    pub fn optimize_memory_management(&mut self, program: &mut Program) -> UmbraResult<()> {
        // Analyze memory allocation patterns
        self.analyze_memory_patterns(program)?;

        // Optimize variable lifetimes
        self.optimize_variable_lifetimes(program)?;

        // Identify opportunities for memory pooling
        self.identify_memory_pooling_opportunities(program)?;

        // Optimize string operations
        self.optimize_string_operations(program)?;

        // Reduce memory fragmentation
        self.reduce_memory_fragmentation(program)?;

        Ok(())
    }

    /// Analyze memory allocation patterns in the program
    fn analyze_memory_patterns(&mut self, program: &Program) -> UmbraResult<()> {
        for statement in &program.statements {
            self.analyze_memory_patterns_in_statement(statement)?;
        }
        Ok(())
    }

    /// Analyze memory patterns in a statement
    fn analyze_memory_patterns_in_statement(&mut self, statement: &Statement) -> UmbraResult<()> {
        match statement {
            Statement::Function(func) => {
                // Analyze function for memory allocation patterns
                for stmt in &func.body {
                    self.analyze_memory_patterns_in_statement(stmt)?;
                }
            }
            Statement::Variable(var) => {
                // Check if this variable allocates memory
                self.check_memory_allocation(&var.type_annotation, &var.value)?;
            }
            Statement::Assignment(assign) => {
                // Check if assignment involves memory allocation
                self.analyze_memory_patterns_in_expression(&assign.value)?;
            }
            Statement::Expression(expr) => {
                self.analyze_memory_patterns_in_expression(&expr.expression)?;
            }
            Statement::Return(ret) => {
                if let Some(value) = &ret.value {
                    self.analyze_memory_patterns_in_expression(value)?;
                }
            }
            Statement::When(when) => {
                self.analyze_memory_patterns_in_expression(&when.condition)?;
                for stmt in &when.then_body {
                    self.analyze_memory_patterns_in_statement(stmt)?;
                }
                if let Some(else_body) = &when.else_body {
                    for stmt in else_body {
                        self.analyze_memory_patterns_in_statement(stmt)?;
                    }
                }
            }
            Statement::Repeat(repeat) => {
                self.analyze_memory_patterns_in_expression(&repeat.iterable)?;
                for stmt in &repeat.body {
                    self.analyze_memory_patterns_in_statement(stmt)?;
                }
            }
            _ => {
                // Other statements don't typically involve memory allocation
            }
        }
        Ok(())
    }

    /// Analyze memory patterns in an expression
    fn analyze_memory_patterns_in_expression(&mut self, expression: &Expression) -> UmbraResult<()> {
        match expression {
            Expression::List(list) => {
                // List creation allocates memory
                for element in &list.elements {
                    self.analyze_memory_patterns_in_expression(element)?;
                }
            }
            Expression::Call(call) => {
                // Function calls might allocate memory
                for arg in &call.arguments {
                    self.analyze_memory_patterns_in_expression(arg)?;
                }
            }
            Expression::Binary(binop) => {
                self.analyze_memory_patterns_in_expression(&binop.left)?;
                self.analyze_memory_patterns_in_expression(&binop.right)?;

                // Check for string concatenation (memory allocation)
                if matches!(binop.operator, crate::parser::ast::BinaryOperator::Add) {
                    // This could be string concatenation
                }
            }
            Expression::Unary(unop) => {
                self.analyze_memory_patterns_in_expression(&unop.operand)?;
            }
            Expression::MethodCall(method) => {
                self.analyze_memory_patterns_in_expression(&method.object)?;
                for arg in &method.arguments {
                    self.analyze_memory_patterns_in_expression(arg)?;
                }
            }
            Expression::FieldAccess(field) => {
                self.analyze_memory_patterns_in_expression(&field.object)?;
            }
            Expression::IndexAccess(index) => {
                self.analyze_memory_patterns_in_expression(&index.object)?;
                self.analyze_memory_patterns_in_expression(&index.index)?;
            }
            _ => {
                // Literals and identifiers don't allocate memory
            }
        }
        Ok(())
    }

    /// Check if a variable declaration involves memory allocation
    fn check_memory_allocation(&mut self, type_annotation: &Type, initializer: &Expression) -> UmbraResult<()> {
        match type_annotation {
            Type::List(_) => {
                // Lists allocate memory
                // TODO: Track list allocations for pooling
            }
            Type::Struct(_) => {
                // Structs allocate memory
                // TODO: Track struct allocations
            }
            Type::Basic(basic_type) => {
                match basic_type {
                    crate::parser::ast::BasicType::String => {
                        // Strings allocate memory
                        // TODO: Track string allocations for interning
                    }
                    _ => {
                        // Primitive types don't allocate heap memory
                    }
                }
            }
            _ => {
                // Other types might allocate memory
            }
        }

        // Analyze the initializer expression
        self.analyze_memory_patterns_in_expression(initializer)?;

        Ok(())
    }

    /// Optimize variable lifetimes to reduce memory usage
    fn optimize_variable_lifetimes(&mut self, program: &mut Program) -> UmbraResult<()> {
        for statement in &mut program.statements {
            self.optimize_lifetimes_in_statement(statement)?;
        }
        Ok(())
    }

    /// Optimize lifetimes in a statement
    fn optimize_lifetimes_in_statement(&mut self, statement: &mut Statement) -> UmbraResult<()> {
        match statement {
            Statement::Function(func) => {
                // Analyze variable usage within the function
                self.analyze_variable_usage_in_function(func)?;
            }
            _ => {
                // Other statements don't need lifetime optimization at this level
            }
        }
        Ok(())
    }

    /// Analyze variable usage patterns in a function
    fn analyze_variable_usage_in_function(&mut self, func: &FunctionDef) -> UmbraResult<()> {
        // Build a map of variable usage
        let mut variable_usage = std::collections::HashMap::new();

        for (index, statement) in func.body.iter().enumerate() {
            self.track_variable_usage_in_statement(statement, index, &mut variable_usage)?;
        }

        // TODO: Use usage information to optimize variable placement and lifetime

        Ok(())
    }

    /// Track variable usage in a statement
    fn track_variable_usage_in_statement(
        &self,
        statement: &Statement,
        statement_index: usize,
        usage_map: &mut std::collections::HashMap<String, Vec<usize>>
    ) -> UmbraResult<()> {
        match statement {
            Statement::Variable(var) => {
                // Variable declaration
                usage_map.entry(var.name.clone()).or_default().push(statement_index);
            }
            Statement::Assignment(assign) => {
                // Variable assignment
                usage_map.entry(assign.name.clone()).or_default().push(statement_index);
                self.track_variable_usage_in_expression(&assign.value, statement_index, usage_map)?;
            }
            Statement::Expression(expr) => {
                self.track_variable_usage_in_expression(&expr.expression, statement_index, usage_map)?;
            }
            Statement::Return(ret) => {
                if let Some(value) = &ret.value {
                    self.track_variable_usage_in_expression(value, statement_index, usage_map)?;
                }
            }
            _ => {
                // Other statements don't directly use variables
            }
        }
        Ok(())
    }

    /// Track variable usage in an expression
    fn track_variable_usage_in_expression(
        &self,
        expression: &Expression,
        statement_index: usize,
        usage_map: &mut std::collections::HashMap<String, Vec<usize>>
    ) -> UmbraResult<()> {
        match expression {
            Expression::Identifier(id) => {
                usage_map.entry(id.name.clone()).or_default().push(statement_index);
            }
            Expression::Binary(binop) => {
                self.track_variable_usage_in_expression(&binop.left, statement_index, usage_map)?;
                self.track_variable_usage_in_expression(&binop.right, statement_index, usage_map)?;
            }
            Expression::Unary(unop) => {
                self.track_variable_usage_in_expression(&unop.operand, statement_index, usage_map)?;
            }
            Expression::Call(call) => {
                for arg in &call.arguments {
                    self.track_variable_usage_in_expression(arg, statement_index, usage_map)?;
                }
            }
            Expression::List(list) => {
                for element in &list.elements {
                    self.track_variable_usage_in_expression(element, statement_index, usage_map)?;
                }
            }
            _ => {
                // Other expressions don't directly reference variables
            }
        }
        Ok(())
    }

    /// Identify opportunities for memory pooling
    fn identify_memory_pooling_opportunities(&mut self, program: &Program) -> UmbraResult<()> {
        // Look for patterns where similar objects are allocated frequently
        for statement in &program.statements {
            self.analyze_pooling_opportunities_in_statement(statement)?;
        }
        Ok(())
    }

    /// Analyze pooling opportunities in a statement
    fn analyze_pooling_opportunities_in_statement(&mut self, statement: &Statement) -> UmbraResult<()> {
        if let Statement::Function(func) = statement {
            // Look for repeated allocations in loops
            for stmt in &func.body {
                if let Statement::Repeat(repeat) = stmt {
                    self.analyze_loop_allocations(repeat)?;
                }
            }
        }
        Ok(())
    }

    /// Analyze memory allocations within loops
    fn analyze_loop_allocations(&mut self, repeat: &RepeatStatement) -> UmbraResult<()> {
        for statement in &repeat.body {
            if let Statement::Variable(var) = statement {
                // Check if this variable allocation could benefit from pooling
                if self.is_poolable_type(&var.type_annotation) {
                    // TODO: Mark for memory pooling optimization
                }
            }
        }
        Ok(())
    }

    /// Check if a type is suitable for memory pooling
    fn is_poolable_type(&self, type_annotation: &Type) -> bool {
        match type_annotation {
            Type::List(_) => true,  // Lists can be pooled
            Type::Struct(_) => true, // Structs can be pooled
            Type::Basic(basic_type) => {
                matches!(basic_type, crate::parser::ast::BasicType::String)
            }
            _ => false,
        }
    }

    /// Optimize string operations to reduce allocations
    fn optimize_string_operations(&mut self, program: &mut Program) -> UmbraResult<()> {
        for statement in &mut program.statements {
            self.optimize_strings_in_statement(statement)?;
        }
        Ok(())
    }

    /// Optimize strings in a statement
    fn optimize_strings_in_statement(&mut self, statement: &mut Statement) -> UmbraResult<()> {
        match statement {
            Statement::Function(func) => {
                for stmt in &mut func.body {
                    self.optimize_strings_in_statement(stmt)?;
                }
            }
            Statement::Variable(var) => {
                self.optimize_strings_in_expression(&mut var.value)?;
            }
            Statement::Assignment(assign) => {
                self.optimize_strings_in_expression(&mut assign.value)?;
            }
            Statement::Expression(expr) => {
                self.optimize_strings_in_expression(&mut expr.expression)?;
            }
            Statement::Return(ret) => {
                if let Some(value) = &mut ret.value {
                    self.optimize_strings_in_expression(value)?;
                }
            }
            _ => {}
        }
        Ok(())
    }

    /// Optimize strings in an expression
    fn optimize_strings_in_expression(&mut self, expression: &mut Expression) -> UmbraResult<()> {
        match expression {
            Expression::Binary(binop) => {
                // Look for string concatenation chains
                if matches!(binop.operator, crate::parser::ast::BinaryOperator::Add) {
                    // TODO: Optimize string concatenation chains
                    // Could use StringBuilder pattern or string interning
                }

                self.optimize_strings_in_expression(&mut binop.left)?;
                self.optimize_strings_in_expression(&mut binop.right)?;
            }
            Expression::Call(call) => {
                for arg in &mut call.arguments {
                    self.optimize_strings_in_expression(arg)?;
                }
            }
            Expression::List(list) => {
                for element in &mut list.elements {
                    self.optimize_strings_in_expression(element)?;
                }
            }
            _ => {}
        }
        Ok(())
    }

    /// Reduce memory fragmentation through better allocation strategies
    fn reduce_memory_fragmentation(&mut self, program: &Program) -> UmbraResult<()> {
        // Analyze allocation patterns to suggest better strategies
        for statement in &program.statements {
            self.analyze_fragmentation_in_statement(statement)?;
        }
        Ok(())
    }

    /// Analyze fragmentation patterns in a statement
    fn analyze_fragmentation_in_statement(&mut self, statement: &Statement) -> UmbraResult<()> {
        if let Statement::Function(func) = statement {
            // Look for mixed allocation patterns that could cause fragmentation
            let mut allocation_sizes = Vec::new();

            for stmt in &func.body {
                if let Some(size) = self.estimate_allocation_size(stmt) {
                    allocation_sizes.push(size);
                }
            }

            // TODO: Analyze allocation patterns and suggest optimizations
        }
        Ok(())
    }

    /// Estimate the allocation size of a statement
    fn estimate_allocation_size(&self, statement: &Statement) -> Option<usize> {
        match statement {
            Statement::Variable(var) => {
                self.estimate_type_size(&var.type_annotation)
            }
            _ => None,
        }
    }

    /// Estimate the memory size of a type
    fn estimate_type_size(&self, type_annotation: &Type) -> Option<usize> {
        match type_annotation {
            Type::Basic(basic_type) => {
                Some(match basic_type {
                    crate::parser::ast::BasicType::Integer => 8,
                    crate::parser::ast::BasicType::Float => 8,
                    crate::parser::ast::BasicType::Boolean => 1,
                    crate::parser::ast::BasicType::String => 24, // Estimated string header size
                    crate::parser::ast::BasicType::Dataset => 64, // Estimated
                    crate::parser::ast::BasicType::Model => 128, // Estimated
                    crate::parser::ast::BasicType::Tensor => 32, // Estimated
                    crate::parser::ast::BasicType::Void => 0, // Void type has no size
                })
            }
            Type::List(_) => Some(24), // Estimated list header size
            Type::Struct(_) => Some(32), // Estimated struct size
            _ => None,
        }
    }

    // Function inlining methods

    /// Perform function inlining on the program
    pub fn inline_functions(&mut self, program: &mut Program) -> UmbraResult<()> {
        // Build a map of functions that are candidates for inlining
        let mut inline_candidates = std::collections::HashMap::new();

        for statement in &program.statements {
            if let Statement::Function(func) = statement {
                if self.is_inline_candidate(func) {
                    inline_candidates.insert(func.name.clone(), func.clone());
                }
            }
        }

        // Inline function calls in all functions
        for statement in &mut program.statements {
            if let Statement::Function(func) = statement {
                self.inline_function_calls_in_function(func, &inline_candidates)?;
            }
        }

        Ok(())
    }

    /// Check if a function is a candidate for inlining
    fn is_inline_candidate(&self, func: &FunctionDef) -> bool {
        // Only inline small, simple functions
        const MAX_STATEMENTS_FOR_INLINE: usize = 3;
        const MAX_COMPLEXITY_FOR_INLINE: usize = 5;

        // Don't inline recursive functions (basic check)
        if self.function_calls_itself(func) {
            return false;
        }

        // Don't inline functions with complex control flow
        if self.has_complex_control_flow(func) {
            return false;
        }

        // Check statement count
        if func.body.len() > MAX_STATEMENTS_FOR_INLINE {
            return false;
        }

        // Check complexity (rough estimate)
        let complexity = self.estimate_function_complexity(func);
        if complexity > MAX_COMPLEXITY_FOR_INLINE {
            return false;
        }

        true
    }

    /// Check if a function calls itself (basic recursion detection)
    fn function_calls_itself(&self, func: &FunctionDef) -> bool {
        for statement in &func.body {
            if self.statement_calls_function(statement, &func.name) {
                return true;
            }
        }
        false
    }

    /// Check if a statement calls a specific function
    fn statement_calls_function(&self, statement: &Statement, function_name: &str) -> bool {
        match statement {
            Statement::Expression(expr) => {
                self.expression_calls_function(&expr.expression, function_name)
            }
            Statement::Variable(var) => {
                self.expression_calls_function(&var.value, function_name)
            }
            Statement::Return(ret) => {
                if let Some(value) = &ret.value {
                    self.expression_calls_function(value, function_name)
                } else {
                    false
                }
            }
            Statement::Assignment(assign) => {
                self.expression_calls_function(&assign.value, function_name)
            }
            Statement::When(when) => {
                self.expression_calls_function(&when.condition, function_name) ||
                when.then_body.iter().any(|stmt| self.statement_calls_function(stmt, function_name)) ||
                when.else_body.as_ref().is_some_and(|else_body|
                    else_body.iter().any(|stmt| self.statement_calls_function(stmt, function_name)))
            }
            _ => false,
        }
    }

    /// Check if an expression calls a specific function
    fn expression_calls_function(&self, expression: &Expression, function_name: &str) -> bool {
        match expression {
            Expression::Call(call) => {
                call.name == function_name ||
                call.arguments.iter().any(|arg| self.expression_calls_function(arg, function_name))
            }
            Expression::Binary(binop) => {
                self.expression_calls_function(&binop.left, function_name) ||
                self.expression_calls_function(&binop.right, function_name)
            }
            Expression::Unary(unop) => {
                self.expression_calls_function(&unop.operand, function_name)
            }
            Expression::MethodCall(method) => {
                self.expression_calls_function(&method.object, function_name) ||
                method.arguments.iter().any(|arg| self.expression_calls_function(arg, function_name))
            }
            Expression::FieldAccess(field) => {
                self.expression_calls_function(&field.object, function_name)
            }
            Expression::IndexAccess(index) => {
                self.expression_calls_function(&index.object, function_name) ||
                self.expression_calls_function(&index.index, function_name)
            }
            Expression::List(list) => {
                list.elements.iter().any(|elem| self.expression_calls_function(elem, function_name))
            }
            _ => false,
        }
    }

    /// Check if a function has complex control flow
    fn has_complex_control_flow(&self, func: &FunctionDef) -> bool {
        for statement in &func.body {
            match statement {
                Statement::When(_) => return true, // Conditional statements add complexity
                Statement::Repeat(_) => return true, // Loops add complexity
                _ => {}
            }
        }
        false
    }

    /// Estimate the complexity of a function (rough heuristic)
    fn estimate_function_complexity(&self, func: &FunctionDef) -> usize {
        let mut complexity = 0;

        for statement in &func.body {
            complexity += match statement {
                Statement::Expression(_) => 1,
                Statement::Variable(_) => 1,
                Statement::Assignment(_) => 1,
                Statement::Return(_) => 1,
                Statement::When(_) => 3, // Conditionals are more complex
                Statement::Repeat(_) => 5, // Loops are very complex
                _ => 2,
            };
        }

        complexity
    }

    /// Inline function calls in a function
    fn inline_function_calls_in_function(&mut self, func: &mut FunctionDef, inline_candidates: &std::collections::HashMap<String, FunctionDef>) -> UmbraResult<()> {
        for statement in &mut func.body {
            self.inline_function_calls_in_statement(statement, inline_candidates)?;
        }
        Ok(())
    }

    /// Inline function calls in a statement
    fn inline_function_calls_in_statement(&mut self, statement: &mut Statement, inline_candidates: &std::collections::HashMap<String, FunctionDef>) -> UmbraResult<()> {
        match statement {
            Statement::Expression(expr) => {
                self.inline_function_calls_in_expression(&mut expr.expression, inline_candidates)?;
            }
            Statement::Variable(var) => {
                self.inline_function_calls_in_expression(&mut var.value, inline_candidates)?;
            }
            Statement::Return(ret) => {
                if let Some(value) = &mut ret.value {
                    self.inline_function_calls_in_expression(value, inline_candidates)?;
                }
            }
            Statement::Assignment(assign) => {
                self.inline_function_calls_in_expression(&mut assign.value, inline_candidates)?;
            }
            Statement::When(when) => {
                self.inline_function_calls_in_expression(&mut when.condition, inline_candidates)?;
                for stmt in &mut when.then_body {
                    self.inline_function_calls_in_statement(stmt, inline_candidates)?;
                }
                if let Some(else_body) = &mut when.else_body {
                    for stmt in else_body {
                        self.inline_function_calls_in_statement(stmt, inline_candidates)?;
                    }
                }
            }
            _ => {
                // Other statements don't contain expressions to inline
            }
        }
        Ok(())
    }

    /// Inline function calls in an expression
    fn inline_function_calls_in_expression(&mut self, expression: &mut Expression, inline_candidates: &std::collections::HashMap<String, FunctionDef>) -> UmbraResult<()> {
        match expression {
            Expression::Call(call) => {
                // First, inline any function calls in arguments
                for arg in &mut call.arguments {
                    self.inline_function_calls_in_expression(arg, inline_candidates)?;
                }

                // Then try to inline this function call
                if let Some(func) = inline_candidates.get(&call.name) {
                    if let Some(inlined) = self.try_inline_function_call(call, func)? {
                        *expression = inlined;
                    }
                }
            }
            Expression::Binary(binop) => {
                self.inline_function_calls_in_expression(&mut binop.left, inline_candidates)?;
                self.inline_function_calls_in_expression(&mut binop.right, inline_candidates)?;
            }
            Expression::Unary(unop) => {
                self.inline_function_calls_in_expression(&mut unop.operand, inline_candidates)?;
            }
            Expression::MethodCall(method) => {
                self.inline_function_calls_in_expression(&mut method.object, inline_candidates)?;
                for arg in &mut method.arguments {
                    self.inline_function_calls_in_expression(arg, inline_candidates)?;
                }
            }
            Expression::FieldAccess(field) => {
                self.inline_function_calls_in_expression(&mut field.object, inline_candidates)?;
            }
            Expression::IndexAccess(index) => {
                self.inline_function_calls_in_expression(&mut index.object, inline_candidates)?;
                self.inline_function_calls_in_expression(&mut index.index, inline_candidates)?;
            }
            Expression::List(list) => {
                for element in &mut list.elements {
                    self.inline_function_calls_in_expression(element, inline_candidates)?;
                }
            }
            _ => {
                // Literals and identifiers don't need inlining
            }
        }
        Ok(())
    }

    /// Try to inline a function call
    fn try_inline_function_call(&self, call: &FunctionCall, func: &FunctionDef) -> UmbraResult<Option<Expression>> {
        // For now, only inline very simple functions that return a single expression
        if func.body.len() != 1 {
            return Ok(None);
        }

        // Check if the function has a single return statement
        if let Statement::Return(ret) = &func.body[0] {
            if let Some(return_value) = &ret.value {
                // Create a parameter substitution map
                let mut substitutions = std::collections::HashMap::new();

                if func.parameters.len() != call.arguments.len() {
                    return Ok(None); // Parameter count mismatch
                }

                for (param, arg) in func.parameters.iter().zip(call.arguments.iter()) {
                    substitutions.insert(param.name.clone(), arg.clone());
                }

                // Substitute parameters in the return expression
                let mut inlined_expr = return_value.clone();
                self.substitute_parameters_in_expression(&mut inlined_expr, &substitutions);

                return Ok(Some(inlined_expr));
            }
        }

        Ok(None)
    }

    /// Substitute parameters in an expression
    fn substitute_parameters_in_expression(&self, expression: &mut Expression, substitutions: &std::collections::HashMap<String, Expression>) {
        match expression {
            Expression::Identifier(id) => {
                if let Some(substitution) = substitutions.get(&id.name) {
                    *expression = substitution.clone();
                }
            }
            Expression::Binary(binop) => {
                self.substitute_parameters_in_expression(&mut binop.left, substitutions);
                self.substitute_parameters_in_expression(&mut binop.right, substitutions);
            }
            Expression::Unary(unop) => {
                self.substitute_parameters_in_expression(&mut unop.operand, substitutions);
            }
            Expression::Call(call) => {
                for arg in &mut call.arguments {
                    self.substitute_parameters_in_expression(arg, substitutions);
                }
            }
            Expression::MethodCall(method) => {
                self.substitute_parameters_in_expression(&mut method.object, substitutions);
                for arg in &mut method.arguments {
                    self.substitute_parameters_in_expression(arg, substitutions);
                }
            }
            Expression::FieldAccess(field) => {
                self.substitute_parameters_in_expression(&mut field.object, substitutions);
            }
            Expression::IndexAccess(index) => {
                self.substitute_parameters_in_expression(&mut index.object, substitutions);
                self.substitute_parameters_in_expression(&mut index.index, substitutions);
            }
            Expression::List(list) => {
                for element in &mut list.elements {
                    self.substitute_parameters_in_expression(element, substitutions);
                }
            }
            _ => {
                // Literals don't need substitution
            }
        }
    }

    // Dead code elimination methods

    /// Mark a function as used
    fn mark_function_used(&mut self, name: &str) {
        self.used_functions.insert(name.to_string());
    }

    /// Mark a variable as used
    fn mark_variable_used(&mut self, name: &str) {
        self.used_variables.insert(name.to_string());
    }

    /// Mark a type as used
    fn mark_type_used(&mut self, name: &str) {
        self.used_types.insert(name.to_string());
    }

    /// Perform dead code elimination on the program
    pub fn eliminate_dead_code(&mut self, program: &mut Program) -> UmbraResult<()> {
        // First pass: mark all used symbols starting from main and exported functions
        self.mark_entry_points(program)?;

        // Second pass: propagate usage through the call graph
        self.propagate_usage(program)?;

        // Third pass: remove unused statements
        self.remove_unused_statements(program)?;

        Ok(())
    }

    /// Mark entry points (main function and exported functions) as used
    fn mark_entry_points(&mut self, program: &Program) -> UmbraResult<()> {
        for statement in &program.statements {
            match statement {
                Statement::Function(func) => {
                    // Mark main function and exported functions as used
                    if func.name == "main" || func.visibility == Visibility::Public {
                        self.mark_function_used(&func.name);
                    }
                }
                Statement::Export(export) => {
                    // Mark exported symbols as used
                    match &export.export_type {
                        ExportType::Function(name) => {
                            self.mark_function_used(name);
                        }
                        ExportType::Struct(name) => {
                            self.mark_type_used(name);
                        }
                        ExportType::Variable(name) => {
                            self.mark_variable_used(name);
                        }
                        ExportType::Multiple(names) => {
                            for name in names {
                                // Try to determine what type of symbol this is
                                if let Some(symbol) = self.symbol_table.lookup(name) {
                                    match symbol.symbol_type {
                                        SymbolType::Function => self.mark_function_used(name),
                                        SymbolType::Variable => self.mark_variable_used(name),
                                        SymbolType::Structure => self.mark_type_used(name),
                                        SymbolType::Parameter => {} // Parameters are local, not exported
                                        SymbolType::Trait => {},
                                        SymbolType::Implementation => {},
                                    }
                                }
                            }
                        }
                        ExportType::ReExport(_) | ExportType::ReExportSymbol(_, _) | ExportType::ReExportWildcard(_) => {
                            // Re-exports don't affect local dead code elimination
                        }
                    }
                }
                _ => {}
            }
        }
        Ok(())
    }

    /// Propagate usage through the call graph
    fn propagate_usage(&mut self, program: &Program) -> UmbraResult<()> {
        let mut changed = true;

        // Keep iterating until no new symbols are marked as used
        while changed {
            changed = false;
            let used_functions_snapshot = self.used_functions.clone();

            for statement in &program.statements {
                if let Statement::Function(func) = statement {
                    if used_functions_snapshot.contains(&func.name) {
                        // Analyze the function body to find used symbols
                        let old_size = self.used_functions.len() + self.used_variables.len() + self.used_types.len();
                        self.analyze_function_for_usage(func)?;
                        let new_size = self.used_functions.len() + self.used_variables.len() + self.used_types.len();

                        if new_size > old_size {
                            changed = true;
                        }
                    }
                }
            }
        }

        Ok(())
    }

    /// Analyze a function to find used symbols
    fn analyze_function_for_usage(&mut self, func: &FunctionDef) -> UmbraResult<()> {
        // Mark parameter types as used
        for param in &func.parameters {
            self.mark_type_used_from_type(&param.type_annotation);
        }

        // Mark return type as used
        self.mark_type_used_from_type(&func.return_type);

        // Analyze function body
        for statement in &func.body {
            self.analyze_statement_for_usage(statement)?;
        }

        Ok(())
    }

    /// Analyze a statement to find used symbols
    fn analyze_statement_for_usage(&mut self, statement: &Statement) -> UmbraResult<()> {
        match statement {
            Statement::Variable(var) => {
                self.mark_type_used_from_type(&var.type_annotation);
                self.analyze_expression_for_usage(&var.value)?;
            }
            Statement::Expression(expr) => {
                self.analyze_expression_for_usage(&expr.expression)?;
            }
            Statement::Return(ret) => {
                if let Some(expr) = &ret.value {
                    self.analyze_expression_for_usage(expr)?;
                }
            }
            Statement::When(when) => {
                self.analyze_expression_for_usage(&when.condition)?;
                for stmt in &when.then_body {
                    self.analyze_statement_for_usage(stmt)?;
                }
                if let Some(else_body) = &when.else_body {
                    for stmt in else_body {
                        self.analyze_statement_for_usage(stmt)?;
                    }
                }
            }
            Statement::Assignment(assign) => {
                // Mark the variable being assigned as used
                self.mark_variable_used(&assign.name);
                self.analyze_expression_for_usage(&assign.value)?;
            }
            _ => {
                // Handle other statement types as needed
            }
        }
        Ok(())
    }

    /// Analyze an expression to find used symbols
    fn analyze_expression_for_usage(&mut self, expression: &Expression) -> UmbraResult<()> {
        match expression {
            Expression::Identifier(id) => {
                // Check if it's a function call or variable reference
                if let Some(symbol) = self.symbol_table.lookup(&id.name) {
                    match symbol.symbol_type {
                        SymbolType::Function => self.mark_function_used(&id.name),
                        SymbolType::Variable => self.mark_variable_used(&id.name),
                        SymbolType::Structure => self.mark_type_used(&id.name),
                        SymbolType::Parameter => self.mark_variable_used(&id.name),
                        SymbolType::Trait => {},
                        SymbolType::Implementation => {},
                    }
                }
            }
            Expression::Call(call) => {
                self.mark_function_used(&call.name);
                for arg in &call.arguments {
                    self.analyze_expression_for_usage(arg)?;
                }
            }
            Expression::Binary(binop) => {
                self.analyze_expression_for_usage(&binop.left)?;
                self.analyze_expression_for_usage(&binop.right)?;
            }
            Expression::Unary(unop) => {
                self.analyze_expression_for_usage(&unop.operand)?;
            }
            _ => {
                // Handle other expression types as needed
            }
        }
        Ok(())
    }

    /// Mark types as used from a type annotation
    fn mark_type_used_from_type(&mut self, type_annotation: &Type) {
        match type_annotation {
            Type::Basic(_) => {
                // Built-in types are always considered used
            }
            Type::List(inner) => {
                self.mark_type_used_from_type(inner);
            }
            Type::Optional(inner) => {
                self.mark_type_used_from_type(inner);
            }
            Type::Result(ok_type, err_type) => {
                self.mark_type_used_from_type(ok_type);
                self.mark_type_used_from_type(err_type);
            }
            Type::Struct(name) => {
                self.mark_type_used(name);
            }
            Type::Union(types) => {
                for t in types {
                    self.mark_type_used_from_type(t);
                }
            }
            Type::Generic(name, args) => {
                self.mark_type_used(name);
                for arg in args {
                    self.mark_type_used_from_type(arg);
                }
            }
            Type::Auto => {
                // Auto types don't reference specific types
            }
            Type::HashMap(k, v) => {
                self.mark_type_used_from_type(k);
                self.mark_type_used_from_type(v);
            }
            Type::HashSet(t) => {
                self.mark_type_used_from_type(t);
            }
            Type::Box(t) => {
                self.mark_type_used_from_type(t);
            }
            Type::Rc(t) => {
                self.mark_type_used_from_type(t);
            }
            Type::Arc(t) => {
                self.mark_type_used_from_type(t);
            }
            Type::Weak(t) => {
                self.mark_type_used_from_type(t);
            }
            Type::RefCell(t) => {
                self.mark_type_used_from_type(t);
            }
            Type::Mutex(t) => {
                self.mark_type_used_from_type(t);
            }
            Type::TypeParameter { .. } => {},
            Type::GenericInstance { base: _, type_args } => {
                // base is a String, not a Type, so we don't mark it
                for arg in type_args {
                    self.mark_type_used_from_type(arg);
                }
            },
            Type::AssociatedType { .. } => {
                // trait_name and type_name are Strings, not Types
            },
            Type::HigherKinded { .. } => {},
            Type::Function(params, ret) => {
                for param in params {
                    self.mark_type_used_from_type(param);
                }
                self.mark_type_used_from_type(ret);
            },
            Type::Tuple(types) => {
                for t in types {
                    self.mark_type_used_from_type(t);
                }
            },
            Type::Array(t, _) => self.mark_type_used_from_type(t),
            Type::Slice(t) => self.mark_type_used_from_type(t),
            Type::Reference(t) => self.mark_type_used_from_type(t),
            Type::MutableReference(t) => self.mark_type_used_from_type(t),
            Type::RawPointer(t) => self.mark_type_used_from_type(t),
            Type::MutableRawPointer(t) => self.mark_type_used_from_type(t),
            Type::TypeVariable(_) => {},
            Type::SelfType => {}, // Self type doesn't reference other types // Type variables don't reference other types
        }
    }

    /// Remove unused statements from the program
    fn remove_unused_statements(&mut self, program: &mut Program) -> UmbraResult<()> {
        program.statements.retain(|statement| {
            match statement {
                Statement::Function(func) => {
                    // Keep used functions and main function
                    self.used_functions.contains(&func.name) || func.name == "main"
                }
                Statement::Structure(structure) => {
                    // Keep used structures
                    self.used_types.contains(&structure.name)
                }
                Statement::Variable(var) => {
                    // Keep used global variables
                    self.used_variables.contains(&var.name)
                }
                Statement::Error(error) => {
                    // Keep used error types
                    self.used_types.contains(&error.name)
                }
                Statement::Export(_) => {
                    // Always keep export statements
                    true
                }
                _ => {
                    // Keep other statements (imports, etc.)
                    true
                }
            }
        });

        Ok(())
    }

    /// Get dead code elimination statistics
    pub fn get_dead_code_stats(&self) -> (usize, usize, usize) {
        (
            self.used_functions.len(),
            self.used_variables.len(),
            self.used_types.len(),
        )
    }

    /// Get access to the symbol table
    pub fn symbol_table(&self) -> &SymbolTable {
        &self.symbol_table
    }
}

impl Default for SemanticAnalyzer {
    fn default() -> Self {
        Self::new()
    }
}
