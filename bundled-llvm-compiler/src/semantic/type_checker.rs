use crate::error::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>R<PERSON>ult};
use crate::parser::ast::{BasicType, BinaryOperator, Literal, Type, UnaryOperator};

/// Type checker for Umbra expressions and statements
pub struct TypeChecker;

impl TypeChecker {
    pub fn new() -> Self {
        Self
    }

    /// Check if a value of one type can be assigned to another type
    pub fn is_assignable(&self, from: &Type, to: &Type) -> bool {
        self.types_compatible(to, from)
    }

    /// Check if two types are compatible (can be assigned/compared)
    pub fn types_compatible(&self, expected: &Type, actual: &Type) -> bool {
        match (expected, actual) {
            (Type::Auto, _) | (_, Type::Auto) => true,
            (Type::Basic(a), Type::Basic(b)) => a == b,
            (Type::List(a), Type::List(b)) => self.types_compatible(a, b),
            (Type::Struct(a), Type::Struct(b)) => a == b,
            (Type::Optional(a), Type::Optional(b)) => self.types_compatible(a, b),
            (Type::Result(ok1, err1), Type::Result(ok2, err2)) => {
                self.types_compatible(ok1, ok2) && self.types_compatible(err1, err2)
            }
            (Type::Generic(name1, params1), Type::Generic(name2, params2)) => {
                name1 == name2 && params1.len() == params2.len() &&
                params1.iter().zip(params2.iter()).all(|(t1, t2)| self.types_compatible(t1, t2))
            }
            // Optional types can accept None
            (Type::Optional(_), Type::Basic(BasicType::Void)) => true, // None value
            // Union types can accept any of their constituent types
            (Type::Union(types), actual) => types.iter().any(|t| self.types_compatible(t, actual)),
            (expected, Type::Union(types)) => types.iter().any(|t| self.types_compatible(expected, t)),
            // Function types
            (Type::Function(params1, ret1), Type::Function(params2, ret2)) => {
                // Generic function type (Function with Auto params/return) accepts any function
                if (params1.len() == 1 && params1[0] == Type::Auto && **ret1 == Type::Auto) ||
                   (params2.len() == 1 && params2[0] == Type::Auto && **ret2 == Type::Auto) {
                    true
                } else {
                    params1.len() == params2.len() &&
                    params1.iter().zip(params2.iter()).all(|(p1, p2)| self.types_compatible(p1, p2)) &&
                    self.types_compatible(ret1, ret2)
                }
            }
            // Type variables are compatible with anything (for inference)
            (Type::TypeVariable(_), _) | (_, Type::TypeVariable(_)) => true,
            // Type parameters with the same name are compatible
            (Type::TypeParameter { name: name1, .. }, Type::TypeParameter { name: name2, .. }) => name1 == name2,
            // Self types are compatible with each other
            (Type::SelfType, Type::SelfType) => true,
            _ => false,
        }
    }

    /// Check if two types are compatible for numeric operations (allows int/float mixing)
    #[allow(dead_code)]
    pub fn numeric_types_compatible(&self, left: &Type, right: &Type) -> bool {
        match (left, right) {
            (Type::Auto, _) | (_, Type::Auto) => true,
            (Type::Basic(BasicType::Integer), Type::Basic(BasicType::Integer)) => true,
            (Type::Basic(BasicType::Float), Type::Basic(BasicType::Float)) => true,
            (Type::Basic(BasicType::Integer), Type::Basic(BasicType::Float)) => true,
            (Type::Basic(BasicType::Float), Type::Basic(BasicType::Integer)) => true,
            _ => false,
        }
    }

    /// Check if a type is numeric (Integer or Float)
    pub fn is_numeric_type(&self, type_annotation: &Type) -> bool {
        matches!(
            type_annotation,
            Type::Basic(BasicType::Integer) | Type::Basic(BasicType::Float)
        )
    }

    /// Check if a type is comparable (supports <, >, <=, >=)
    pub fn is_comparable_type(&self, type_annotation: &Type) -> bool {
        matches!(
            type_annotation,
            Type::Basic(BasicType::Integer)
                | Type::Basic(BasicType::Float)
                | Type::Basic(BasicType::String)
        )
    }

    /// Check if a type supports equality comparison (==, !=)
    pub fn supports_equality(&self, type_annotation: &Type) -> bool {
        // Most types support equality, except Void
        !matches!(type_annotation, Type::Basic(BasicType::Void))
    }

    /// Infer the type of a literal expression
    pub fn infer_literal_type(&self, literal: &Literal) -> Type {
        match literal {
            Literal::Integer(_) => Type::Basic(BasicType::Integer),
            Literal::Float(_) => Type::Basic(BasicType::Float),
            Literal::String(_) => Type::Basic(BasicType::String),
            Literal::Boolean(_) => Type::Basic(BasicType::Boolean),
        }
    }

    /// Check the result type of a binary operation
    pub fn check_binary_operation(
        &self,
        left_type: &Type,
        operator: &BinaryOperator,
        right_type: &Type,
    ) -> UmbraResult<Type> {
        match operator {
            // Arithmetic operations
            BinaryOperator::Add
            | BinaryOperator::Subtract
            | BinaryOperator::Multiply
            | BinaryOperator::Divide
            | BinaryOperator::Modulo => {
                if operator == &BinaryOperator::Add {
                    // Special case: string concatenation
                    if matches!(left_type, Type::Basic(BasicType::String))
                        && matches!(right_type, Type::Basic(BasicType::String))
                    {
                        return Ok(Type::Basic(BasicType::String));
                    }
                }

                // Numeric operations
                if self.is_numeric_type(left_type) && self.is_numeric_type(right_type) {
                    // If either operand is Float, result is Float
                    if matches!(left_type, Type::Basic(BasicType::Float))
                        || matches!(right_type, Type::Basic(BasicType::Float))
                    {
                        Ok(Type::Basic(BasicType::Float))
                    } else {
                        Ok(Type::Basic(BasicType::Integer))
                    }
                } else {
                    Err(UmbraError::Semantic {
                        message: format!(
                            "Invalid operands for '{operator}': {left_type} and {right_type}"
                        ),
                        line: 0,
                        column: 0,
                    })
                }
            }

            // Comparison operations
            BinaryOperator::Less
            | BinaryOperator::LessEqual
            | BinaryOperator::Greater
            | BinaryOperator::GreaterEqual => {
                if self.is_comparable_type(left_type) && self.is_comparable_type(right_type) {
                    // Allow numeric comparisons between int and float
                    if (self.is_numeric_type(left_type) && self.is_numeric_type(right_type))
                        || self.types_compatible(left_type, right_type)
                    {
                        Ok(Type::Basic(BasicType::Boolean))
                    } else {
                        Err(UmbraError::Semantic {
                            message: format!(
                                "Invalid operands for '{operator}': {left_type} and {right_type}"
                            ),
                            line: 0,
                            column: 0,
                        })
                    }
                } else {
                    Err(UmbraError::Semantic {
                        message: format!(
                            "Invalid operands for '{operator}': {left_type} and {right_type}"
                        ),
                        line: 0,
                        column: 0,
                    })
                }
            }

            // Equality operations
            BinaryOperator::Equal | BinaryOperator::NotEqual => {
                if self.supports_equality(left_type) && self.supports_equality(right_type) {
                    // Allow numeric equality comparisons between int and float
                    if (self.is_numeric_type(left_type) && self.is_numeric_type(right_type))
                        || self.types_compatible(left_type, right_type)
                    {
                        Ok(Type::Basic(BasicType::Boolean))
                    } else {
                        Err(UmbraError::Semantic {
                            message: format!("Cannot compare {left_type} and {right_type}"),
                            line: 0,
                            column: 0,
                        })
                    }
                } else {
                    Err(UmbraError::Semantic {
                        message: format!("Cannot compare {left_type} and {right_type}"),
                        line: 0,
                        column: 0,
                    })
                }
            }

            // Logical operations
            BinaryOperator::And | BinaryOperator::Or => {
                if matches!(left_type, Type::Basic(BasicType::Boolean))
                    && matches!(right_type, Type::Basic(BasicType::Boolean))
                {
                    Ok(Type::Basic(BasicType::Boolean))
                } else {
                    Err(UmbraError::Semantic {
                        message: format!(
                            "Logical operator '{operator}' requires Boolean operands, got {left_type} and {right_type}"
                        ),
                        line: 0,
                        column: 0,
                    })
                }
            }
        }
    }

    /// Check the result type of a unary operation
    pub fn check_unary_operation(
        &self,
        operator: &UnaryOperator,
        operand_type: &Type,
    ) -> UmbraResult<Type> {
        match operator {
            UnaryOperator::Minus => {
                if self.is_numeric_type(operand_type) {
                    Ok(operand_type.clone())
                } else {
                    Err(UmbraError::Semantic {
                        message: format!(
                            "Unary minus requires numeric operand, got {operand_type}"
                        ),
                        line: 0,
                        column: 0,
                    })
                }
            }
            UnaryOperator::Not => {
                if matches!(operand_type, Type::Basic(BasicType::Boolean)) {
                    Ok(Type::Basic(BasicType::Boolean))
                } else {
                    Err(UmbraError::Semantic {
                        message: format!(
                            "Logical not requires Boolean operand, got {operand_type}"
                        ),
                        line: 0,
                        column: 0,
                    })
                }
            }
        }
    }

    /// Check if a type can be used as an iterable in a repeat loop
    pub fn is_iterable_type(&self, type_annotation: &Type) -> bool {
        matches!(
            type_annotation,
            Type::List(_) | Type::Basic(BasicType::Integer)
        )
    }

    /// Get the element type of an iterable type
    pub fn get_element_type(&self, iterable_type: &Type) -> Option<Type> {
        match iterable_type {
            Type::List(element_type) => Some((**element_type).clone()),
            Type::Basic(BasicType::Integer) => Some(Type::Basic(BasicType::Integer)), // Range produces integers
            _ => None,
        }
    }

    /// Check if a type can be used in a boolean context (conditions)
    pub fn is_boolean_context_valid(&self, type_annotation: &Type) -> bool {
        matches!(type_annotation, Type::Basic(BasicType::Boolean))
    }

    /// Convert a type to its string representation for error messages
    pub fn type_to_string(&self, type_annotation: &Type) -> String {
        format!("{type_annotation}")
    }

    /// Check if a type is a struct type
    #[allow(dead_code)]
    pub fn is_struct_type(&self, type_annotation: &Type) -> bool {
        matches!(type_annotation, Type::Struct(_))
    }

    /// Check if a type is an optional type
    #[allow(dead_code)]
    pub fn is_optional_type(&self, type_annotation: &Type) -> bool {
        matches!(type_annotation, Type::Optional(_))
    }

    /// Check if a type is a result type
    #[allow(dead_code)]
    pub fn is_result_type(&self, type_annotation: &Type) -> bool {
        matches!(type_annotation, Type::Result(_, _))
    }

    /// Check if a type is a union type
    #[allow(dead_code)]
    pub fn is_union_type(&self, type_annotation: &Type) -> bool {
        matches!(type_annotation, Type::Union(_))
    }

    /// Get the inner type of an optional type
    #[allow(dead_code)]
    pub fn get_optional_inner_type<'a>(&self, type_annotation: &'a Type) -> Option<&'a Type> {
        match type_annotation {
            Type::Optional(inner) => Some(inner),
            _ => None,
        }
    }

    /// Get the Ok and Err types of a Result type
    #[allow(dead_code)]
    pub fn get_result_types<'a>(&self, type_annotation: &'a Type) -> Option<(&'a Type, &'a Type)> {
        match type_annotation {
            Type::Result(ok, err) => Some((ok, err)),
            _ => None,
        }
    }

    /// Get the constituent types of a union type
    #[allow(dead_code)]
    pub fn get_union_types<'a>(&self, type_annotation: &'a Type) -> Option<&'a Vec<Type>> {
        match type_annotation {
            Type::Union(types) => Some(types),
            _ => None,
        }
    }
}

impl Default for TypeChecker {
    fn default() -> Self {
        Self::new()
    }
}
