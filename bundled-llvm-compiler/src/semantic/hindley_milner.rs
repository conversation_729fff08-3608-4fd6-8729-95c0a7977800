use std::collections::HashMap;
use crate::error::{<PERSON><PERSON><PERSON><PERSON><PERSON>, UmbraResult};
use crate::parser::ast::{Type, Expression, LambdaExpression, BinaryOp, UnaryOp, FunctionCall, Identifier, Literal};

/// Type variable counter for generating unique type variables
static mut TYPE_VAR_COUNTER: u32 = 0;

/// Generate a fresh type variable
fn fresh_type_var() -> Type {
    unsafe {
        TYPE_VAR_COUNTER += 1;
        Type::TypeVariable(TYPE_VAR_COUNTER)
    }
}

/// Type substitution mapping type variables to types
#[derive(Debug, Clone)]
pub struct Substitution {
    map: HashMap<u32, Type>,
}

impl Substitution {
    pub fn new() -> Self {
        Self {
            map: HashMap::new(),
        }
    }

    pub fn singleton(var: u32, ty: Type) -> Self {
        let mut map = HashMap::new();
        map.insert(var, ty);
        Self { map }
    }

    pub fn apply(&self, ty: &Type) -> Type {
        match ty {
            Type::TypeVariable(var) => {
                if let Some(substituted) = self.map.get(var) {
                    self.apply(substituted)
                } else {
                    ty.clone()
                }
            }
            Type::Function(params, ret) => {
                let new_params = params.iter().map(|p| self.apply(p)).collect();
                let new_ret = Box::new(self.apply(ret));
                Type::Function(new_params, new_ret)
            }
            Type::List(inner) => Type::List(Box::new(self.apply(inner))),
            Type::Optional(inner) => Type::Optional(Box::new(self.apply(inner))),
            Type::Result(ok, err) => Type::Result(Box::new(self.apply(ok)), Box::new(self.apply(err))),
            Type::Tuple(types) => Type::Tuple(types.iter().map(|t| self.apply(t)).collect()),
            _ => ty.clone(),
        }
    }

    pub fn compose(&self, other: &Substitution) -> Substitution {
        let mut new_map = HashMap::new();
        
        // Apply self to all mappings in other
        for (var, ty) in &other.map {
            new_map.insert(*var, self.apply(ty));
        }
        
        // Add mappings from self that aren't in other
        for (var, ty) in &self.map {
            if !new_map.contains_key(var) {
                new_map.insert(*var, ty.clone());
            }
        }
        
        Substitution { map: new_map }
    }
}

/// Type constraint for unification
#[derive(Debug, Clone)]
pub struct Constraint {
    pub left: Type,
    pub right: Type,
}

impl Constraint {
    pub fn new(left: Type, right: Type) -> Self {
        Self { left, right }
    }
}

/// Type environment mapping variables to types
#[derive(Debug, Clone)]
pub struct TypeEnv {
    bindings: HashMap<String, Type>,
}

impl TypeEnv {
    pub fn new() -> Self {
        Self {
            bindings: HashMap::new(),
        }
    }

    pub fn extend(&self, name: String, ty: Type) -> Self {
        let mut new_bindings = self.bindings.clone();
        new_bindings.insert(name, ty);
        Self {
            bindings: new_bindings,
        }
    }

    pub fn lookup(&self, name: &str) -> Option<&Type> {
        self.bindings.get(name)
    }

    pub fn apply_substitution(&self, subst: &Substitution) -> Self {
        let new_bindings = self.bindings
            .iter()
            .map(|(name, ty)| (name.clone(), subst.apply(ty)))
            .collect();
        Self {
            bindings: new_bindings,
        }
    }
}

/// Hindley-Milner type inference engine
pub struct HindleyMilner {
    constraints: Vec<Constraint>,
}

impl HindleyMilner {
    pub fn new() -> Self {
        Self {
            constraints: Vec::new(),
        }
    }

    /// Infer the type of an expression
    pub fn infer(&mut self, env: &TypeEnv, expr: &Expression) -> UmbraResult<Type> {
        match expr {
            Expression::Literal(lit) => self.infer_literal(lit),
            Expression::Identifier(id) => self.infer_identifier(env, id),
            Expression::Lambda(lambda) => self.infer_lambda(env, lambda),
            Expression::Call(call) => self.infer_call(env, call),
            Expression::Binary(binary) => self.infer_binary(env, binary),
            Expression::Unary(unary) => self.infer_unary(env, unary),
            _ => Ok(fresh_type_var()), // Placeholder for other expressions
        }
    }

    fn infer_literal(&mut self, lit: &Literal) -> UmbraResult<Type> {
        match lit {
            Literal::Integer(_) => Ok(Type::Basic(crate::parser::ast::BasicType::Integer)),
            Literal::Float(_) => Ok(Type::Basic(crate::parser::ast::BasicType::Float)),
            Literal::String(_) => Ok(Type::Basic(crate::parser::ast::BasicType::String)),
            Literal::Boolean(_) => Ok(Type::Basic(crate::parser::ast::BasicType::Boolean)),
        }
    }

    fn infer_identifier(&mut self, env: &TypeEnv, id: &Identifier) -> UmbraResult<Type> {
        if let Some(ty) = env.lookup(&id.name) {
            Ok(ty.clone())
        } else {
            Err(UmbraError::TypeInference(format!("Unbound variable: {}", id.name)))
        }
    }

    fn infer_lambda(&mut self, env: &TypeEnv, lambda: &LambdaExpression) -> UmbraResult<Type> {
        let mut param_types = Vec::new();
        let mut new_env = env.clone();

        // Process parameters
        for param in &lambda.parameters {
            let param_type = if let Some(annotation) = &param.type_annotation {
                annotation.clone()
            } else {
                fresh_type_var()
            };
            param_types.push(param_type.clone());
            new_env = new_env.extend(param.name.clone(), param_type);
        }

        // Infer body type
        let body_type = self.infer(&new_env, &lambda.body)?;

        // Check return type annotation if present
        let return_type = if let Some(annotation) = &lambda.return_type {
            self.constraints.push(Constraint::new(body_type.clone(), annotation.clone()));
            annotation.clone()
        } else {
            body_type
        };

        Ok(Type::Function(param_types, Box::new(return_type)))
    }

    fn infer_call(&mut self, env: &TypeEnv, call: &FunctionCall) -> UmbraResult<Type> {
        // For now, assume function exists in environment
        // In a full implementation, we'd look up the function type
        let return_type = fresh_type_var();
        
        // Infer argument types (simplified)
        for _arg in &call.arguments {
            // Would infer each argument type and add constraints
        }
        
        Ok(return_type)
    }

    fn infer_binary(&mut self, env: &TypeEnv, binary: &BinaryOp) -> UmbraResult<Type> {
        let left_type = self.infer(env, &binary.left)?;
        let right_type = self.infer(env, &binary.right)?;
        
        // Add constraint that left and right types must be compatible
        self.constraints.push(Constraint::new(left_type.clone(), right_type.clone()));
        
        // Return type depends on operator
        match binary.operator {
            crate::parser::ast::BinaryOperator::Add |
            crate::parser::ast::BinaryOperator::Subtract |
            crate::parser::ast::BinaryOperator::Multiply |
            crate::parser::ast::BinaryOperator::Divide => Ok(left_type),
            
            crate::parser::ast::BinaryOperator::Equal |
            crate::parser::ast::BinaryOperator::NotEqual |
            crate::parser::ast::BinaryOperator::Less |
            crate::parser::ast::BinaryOperator::Greater |
            crate::parser::ast::BinaryOperator::LessEqual |
            crate::parser::ast::BinaryOperator::GreaterEqual => {
                Ok(Type::Basic(crate::parser::ast::BasicType::Boolean))
            }
            
            _ => Ok(fresh_type_var()),
        }
    }

    fn infer_unary(&mut self, env: &TypeEnv, unary: &UnaryOp) -> UmbraResult<Type> {
        let operand_type = self.infer(env, &unary.operand)?;
        
        match unary.operator {
            crate::parser::ast::UnaryOperator::Minus => Ok(operand_type),
            crate::parser::ast::UnaryOperator::Not => Ok(Type::Basic(crate::parser::ast::BasicType::Boolean)),
        }
    }

    /// Solve constraints using unification
    pub fn solve_constraints(&self) -> UmbraResult<Substitution> {
        let mut subst = Substitution::new();
        
        for constraint in &self.constraints {
            let unified = self.unify(&constraint.left, &constraint.right)?;
            subst = subst.compose(&unified);
        }
        
        Ok(subst)
    }

    /// Unification algorithm
    fn unify(&self, t1: &Type, t2: &Type) -> UmbraResult<Substitution> {
        match (t1, t2) {
            // Same types unify trivially
            (Type::Basic(b1), Type::Basic(b2)) if b1 == b2 => Ok(Substitution::new()),
            
            // Type variable unifies with any type
            (Type::TypeVariable(var), ty) | (ty, Type::TypeVariable(var)) => {
                if self.occurs_check(*var, ty) {
                    Err(UmbraError::TypeInference("Infinite type".to_string()))
                } else {
                    Ok(Substitution::singleton(*var, ty.clone()))
                }
            }
            
            // Function types unify if parameters and return types unify
            (Type::Function(params1, ret1), Type::Function(params2, ret2)) => {
                if params1.len() != params2.len() {
                    return Err(UmbraError::TypeInference("Function arity mismatch".to_string()));
                }
                
                let mut subst = Substitution::new();
                
                // Unify parameters
                for (p1, p2) in params1.iter().zip(params2.iter()) {
                    let param_subst = self.unify(p1, p2)?;
                    subst = subst.compose(&param_subst);
                }
                
                // Unify return types
                let ret_subst = self.unify(ret1, ret2)?;
                subst = subst.compose(&ret_subst);
                
                Ok(subst)
            }
            
            // Other cases fail to unify
            _ => Err(UmbraError::TypeInference(format!("Cannot unify {} and {}", t1, t2))),
        }
    }

    /// Occurs check to prevent infinite types
    fn occurs_check(&self, var: u32, ty: &Type) -> bool {
        match ty {
            Type::TypeVariable(v) => *v == var,
            Type::Function(params, ret) => {
                params.iter().any(|p| self.occurs_check(var, p)) || self.occurs_check(var, ret)
            }
            Type::List(inner) => self.occurs_check(var, inner),
            Type::Optional(inner) => self.occurs_check(var, inner),
            Type::Result(ok, err) => self.occurs_check(var, ok) || self.occurs_check(var, err),
            Type::Tuple(types) => types.iter().any(|t| self.occurs_check(var, t)),
            _ => false,
        }
    }
}
