use std::fs;
use std::path::Path;

use crate::error::{UmbraError, UmbraResult};
use crate::lexer::Lex<PERSON>;
use crate::parser::{Parser, ast::{Program, Statement, ImportType, ModulePath}};
use crate::module::{ModuleRegistry, ModuleResolver};

/// Loads and parses module files
#[derive(Debug)]
pub struct ModuleLoader {
    registry: ModuleRegistry,
    resolver: ModuleResolver,
}

impl ModuleLoader {
    pub fn new() -> Self {
        Self {
            registry: ModuleRegistry::new(),
            resolver: ModuleResolver::new(),
        }
    }

    /// Load a module from a file path
    pub fn load_module<P: AsRef<Path>>(&mut self, file_path: P) -> UmbraResult<String> {
        let file_path = file_path.as_ref();
        let module_name = self.path_to_module_name(file_path)?;

        // Check if already loaded
        if self.registry.is_registered(&module_name) {
            return Ok(module_name);
        }

        // Start loading (for cycle detection)
        self.registry.start_loading(module_name.clone());

        // Read and parse the file
        let source = fs::read_to_string(file_path)
            .map_err(|e| UmbraError::Module(format!("Failed to read module file '{}': {}", file_path.display(), e)))?;

        let mut lexer = Lexer::new(source);
        let tokens = lexer.tokenize()
            .map_err(|e| UmbraError::Module(format!("Failed to tokenize module '{module_name}': {e}")))?;

        let mut parser = Parser::new(tokens);
        let program = parser.parse()
            .map_err(|e| UmbraError::Module(format!("Failed to parse module '{module_name}': {e}")))?;

        // Extract dependencies from import statements
        let dependencies = self.extract_dependencies(&program);

        // Load dependencies first
        for dep in &dependencies {
            let dep_path = self.resolver.resolve_module_path(dep)?;
            let dep_name = self.load_module(dep_path)?;
            self.registry.add_dependency(module_name.clone(), dep_name)?;
        }

        // Register the module
        self.registry.register_module(module_name.clone(), program, file_path.to_path_buf())?;

        // Finish loading
        self.registry.finish_loading(&module_name);

        Ok(module_name)
    }

    /// Load a module by its module path
    pub fn load_module_by_path(&mut self, module_path: &ModulePath) -> UmbraResult<String> {
        let file_path = self.resolver.resolve_module_path(module_path)?;
        self.load_module(file_path)
    }

    /// Convert a file path to a module name
    fn path_to_module_name(&self, file_path: &Path) -> UmbraResult<String> {
        let stem = file_path.file_stem()
            .and_then(|s| s.to_str())
            .ok_or_else(|| UmbraError::Module(format!("Invalid module file path: {}", file_path.display())))?;

        // For now, just use the file stem as the module name
        // In a full implementation, this would consider the directory structure
        Ok(stem.to_string())
    }

    /// Extract module dependencies from import statements
    fn extract_dependencies(&self, program: &Program) -> Vec<ModulePath> {
        let mut dependencies = Vec::new();

        for statement in &program.statements {
            if let Statement::Import(import_stmt) = statement {
                match &import_stmt.import_type {
                    ImportType::Module(path) |
                    ImportType::ModuleAs(path, _) |
                    ImportType::Symbol(path, _) |
                    ImportType::SymbolAs(path, _, _) |
                    ImportType::Wildcard(path) |
                    ImportType::Multiple(path, _) => {
                        dependencies.push(path.clone());
                    }
                }
            }
        }

        dependencies
    }

    /// Get the module registry
    pub fn registry(&self) -> &ModuleRegistry {
        &self.registry
    }

    /// Get the module registry mutably
    pub fn registry_mut(&mut self) -> &mut ModuleRegistry {
        &mut self.registry
    }

    /// Get the module resolver
    pub fn resolver(&self) -> &ModuleResolver {
        &self.resolver
    }

    /// Get the module resolver mutably
    pub fn resolver_mut(&mut self) -> &mut ModuleResolver {
        &mut self.resolver
    }

    /// Load all modules in a directory
    pub fn load_directory<P: AsRef<Path>>(&mut self, dir_path: P) -> UmbraResult<Vec<String>> {
        let dir_path = dir_path.as_ref();
        let mut loaded_modules = Vec::new();

        if !dir_path.is_dir() {
            return Err(UmbraError::Module(format!("Path is not a directory: {}", dir_path.display())));
        }

        let entries = fs::read_dir(dir_path)
            .map_err(|e| UmbraError::Module(format!("Failed to read directory '{}': {}", dir_path.display(), e)))?;

        for entry in entries {
            let entry = entry
                .map_err(|e| UmbraError::Module(format!("Failed to read directory entry: {e}")))?;
            let path = entry.path();

            if path.is_file() && path.extension().and_then(|s| s.to_str()) == Some("umbra") {
                let module_name = self.load_module(&path)?;
                loaded_modules.push(module_name);
            }
        }

        Ok(loaded_modules)
    }

    /// Get compilation order for all loaded modules
    pub fn get_compilation_order(&self) -> UmbraResult<Vec<String>> {
        self.registry.get_compilation_order()
    }

    /// Check if a module is loaded
    pub fn is_module_loaded(&self, module_name: &str) -> bool {
        self.registry.is_registered(module_name)
    }

    /// Get a loaded module's AST
    pub fn get_module_ast(&self, module_name: &str) -> Option<&Program> {
        self.registry.get_module(module_name)
    }

    /// Add a search path for modules
    pub fn add_search_path<P: AsRef<Path>>(&mut self, path: P) {
        self.resolver.add_search_path(path);
    }
}
