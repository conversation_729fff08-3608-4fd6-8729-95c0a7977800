/// Comprehensive List module for Umbra standard library
/// 
/// This module provides a complete implementation of list operations
/// that can be used directly in Umbra code through the `bring` statement.

use crate::error::{UmbraError, UmbraResult};
use std::collections::HashSet;
use std::hash::Hash;
use std::fmt::Debug;

/// The main List type for Umbra
#[derive(Debug, Clone, PartialEq)]
pub struct UmbraList<T> {
    items: Vec<T>,
}

impl<T> UmbraList<T> {
    /// Create a new empty list
    pub fn new() -> Self {
        Self {
            items: Vec::new(),
        }
    }

    /// Create a new list with initial capacity
    pub fn with_capacity(capacity: usize) -> Self {
        Self {
            items: Vec::with_capacity(capacity),
        }
    }

    /// Create a list from a vector
    pub fn from_vec(vec: Vec<T>) -> Self {
        Self { items: vec }
    }

    /// Create a list from an iterator
    pub fn from_iter<I: IntoIterator<Item = T>>(iter: I) -> Self {
        Self {
            items: iter.into_iter().collect(),
        }
    }

    /// Get the length of the list
    pub fn len(&self) -> usize {
        self.items.len()
    }

    /// Check if the list is empty
    pub fn is_empty(&self) -> bool {
        self.items.is_empty()
    }

    /// Get the capacity of the list
    pub fn capacity(&self) -> usize {
        self.items.capacity()
    }

    /// Add an item to the end of the list
    pub fn push(&mut self, item: T) {
        self.items.push(item);
    }

    /// Remove and return the last item from the list
    pub fn pop(&mut self) -> Option<T> {
        self.items.pop()
    }

    /// Insert an item at a specific index
    pub fn insert(&mut self, index: usize, item: T) -> UmbraResult<()> {
        if index > self.items.len() {
            return Err(UmbraError::Runtime(format!(
                "Index {} out of bounds for list of length {}",
                index,
                self.items.len()
            )));
        }
        self.items.insert(index, item);
        Ok(())
    }

    /// Remove an item at a specific index
    pub fn remove(&mut self, index: usize) -> UmbraResult<T> {
        if index >= self.items.len() {
            return Err(UmbraError::Runtime(format!(
                "Index {} out of bounds for list of length {}",
                index,
                self.items.len()
            )));
        }
        Ok(self.items.remove(index))
    }

    /// Get an item at a specific index
    pub fn get(&self, index: usize) -> UmbraResult<&T> {
        self.items.get(index).ok_or_else(|| {
            UmbraError::Runtime(format!(
                "Index {} out of bounds for list of length {}",
                index,
                self.items.len()
            ))
        })
    }

    /// Get a mutable reference to an item at a specific index
    pub fn get_mut(&mut self, index: usize) -> UmbraResult<&mut T> {
        let len = self.items.len();
        self.items.get_mut(index).ok_or_else(|| {
            UmbraError::Runtime(format!(
                "Index {} out of bounds for list of length {}",
                index, len
            ))
        })
    }

    /// Set an item at a specific index
    pub fn set(&mut self, index: usize, item: T) -> UmbraResult<T> {
        if index >= self.items.len() {
            return Err(UmbraError::Runtime(format!(
                "Index {} out of bounds for list of length {}",
                index,
                self.items.len()
            )));
        }
        Ok(std::mem::replace(&mut self.items[index], item))
    }

    /// Get the first item in the list
    pub fn first(&self) -> Option<&T> {
        self.items.first()
    }

    /// Get the last item in the list
    pub fn last(&self) -> Option<&T> {
        self.items.last()
    }

    /// Clear all items from the list
    pub fn clear(&mut self) {
        self.items.clear();
    }

    /// Reserve additional capacity
    pub fn reserve(&mut self, additional: usize) {
        self.items.reserve(additional);
    }

    /// Shrink the capacity to fit the current length
    pub fn shrink_to_fit(&mut self) {
        self.items.shrink_to_fit();
    }

    /// Convert to a vector
    pub fn to_vec(self) -> Vec<T> {
        self.items
    }

    /// Get an iterator over the items
    pub fn iter(&self) -> std::slice::Iter<T> {
        self.items.iter()
    }

    /// Get a mutable iterator over the items
    pub fn iter_mut(&mut self) -> std::slice::IterMut<T> {
        self.items.iter_mut()
    }
}

impl<T: Clone> UmbraList<T> {
    /// Create a list with a repeated value
    pub fn repeat(item: T, count: usize) -> Self {
        Self {
            items: vec![item; count],
        }
    }

    /// Extend the list with items from another list
    pub fn extend(&mut self, other: &UmbraList<T>) {
        self.items.extend_from_slice(&other.items);
    }

    /// Append another list to this one
    pub fn append(&mut self, mut other: UmbraList<T>) {
        self.items.append(&mut other.items);
    }

    /// Create a new list by concatenating two lists
    pub fn concat(&self, other: &UmbraList<T>) -> Self {
        let mut result = self.clone();
        result.extend(other);
        result
    }

    /// Get a slice of the list
    pub fn slice(&self, start: usize, end: usize) -> UmbraResult<UmbraList<T>> {
        if start > end {
            return Err(UmbraError::Runtime(format!(
                "Start index {} cannot be greater than end index {}",
                start, end
            )));
        }
        if end > self.items.len() {
            return Err(UmbraError::Runtime(format!(
                "End index {} out of bounds for list of length {}",
                end,
                self.items.len()
            )));
        }
        Ok(UmbraList::from_vec(self.items[start..end].to_vec()))
    }

    /// Reverse the list in place
    pub fn reverse(&mut self) {
        self.items.reverse();
    }

    /// Create a new reversed list
    pub fn reversed(&self) -> Self {
        let mut result = self.clone();
        result.reverse();
        result
    }
}

impl<T: Clone + PartialEq> UmbraList<T> {
    /// Check if the list contains a specific item
    pub fn contains(&self, item: &T) -> bool {
        self.items.contains(item)
    }

    /// Find the index of the first occurrence of an item
    pub fn index_of(&self, item: &T) -> Option<usize> {
        self.items.iter().position(|x| x == item)
    }

    /// Find the index of the last occurrence of an item
    pub fn last_index_of(&self, item: &T) -> Option<usize> {
        self.items.iter().rposition(|x| x == item)
    }

    /// Remove the first occurrence of an item
    pub fn remove_item(&mut self, item: &T) -> bool {
        if let Some(pos) = self.index_of(item) {
            self.items.remove(pos);
            true
        } else {
            false
        }
    }

    /// Remove all occurrences of an item
    pub fn remove_all(&mut self, item: &T) -> usize {
        let original_len = self.items.len();
        self.items.retain(|x| x != item);
        original_len - self.items.len()
    }

    /// Count occurrences of an item
    pub fn count(&self, item: &T) -> usize {
        self.items.iter().filter(|&x| x == item).count()
    }
}

impl<T: Clone + Eq + Hash> UmbraList<T> {
    /// Remove duplicate items while preserving order
    pub fn unique(&self) -> UmbraList<T> {
        let mut seen = HashSet::new();
        let unique_items: Vec<T> = self
            .items
            .iter()
            .filter(|item| seen.insert((*item).clone()))
            .cloned()
            .collect();
        UmbraList::from_vec(unique_items)
    }

    /// Remove duplicates in place
    pub fn dedup(&mut self) {
        *self = self.unique();
    }
}

impl<T: Clone + PartialOrd> UmbraList<T> {
    /// Sort the list in place
    pub fn sort(&mut self) {
        self.items.sort_by(|a, b| a.partial_cmp(b).unwrap_or(std::cmp::Ordering::Equal));
    }

    /// Create a new sorted list
    pub fn sorted(&self) -> Self {
        let mut result = self.clone();
        result.sort();
        result
    }

    /// Find the minimum item
    pub fn min(&self) -> Option<&T> {
        self.items.iter().min_by(|a, b| a.partial_cmp(b).unwrap_or(std::cmp::Ordering::Equal))
    }

    /// Find the maximum item
    pub fn max(&self) -> Option<&T> {
        self.items.iter().max_by(|a, b| a.partial_cmp(b).unwrap_or(std::cmp::Ordering::Equal))
    }
}

// Iterator implementations
impl<T> IntoIterator for UmbraList<T> {
    type Item = T;
    type IntoIter = std::vec::IntoIter<T>;

    fn into_iter(self) -> Self::IntoIter {
        self.items.into_iter()
    }
}

impl<'a, T> IntoIterator for &'a UmbraList<T> {
    type Item = &'a T;
    type IntoIter = std::slice::Iter<'a, T>;

    fn into_iter(self) -> Self::IntoIter {
        self.items.iter()
    }
}

impl<'a, T> IntoIterator for &'a mut UmbraList<T> {
    type Item = &'a mut T;
    type IntoIter = std::slice::IterMut<'a, T>;

    fn into_iter(self) -> Self::IntoIter {
        self.items.iter_mut()
    }
}

// Index trait implementation
impl<T> std::ops::Index<usize> for UmbraList<T> {
    type Output = T;

    fn index(&self, index: usize) -> &Self::Output {
        &self.items[index]
    }
}

impl<T> std::ops::IndexMut<usize> for UmbraList<T> {
    fn index_mut(&mut self, index: usize) -> &mut Self::Output {
        &mut self.items[index]
    }
}

// Default implementation
impl<T> Default for UmbraList<T> {
    fn default() -> Self {
        Self::new()
    }
}

// From implementations
impl<T> From<Vec<T>> for UmbraList<T> {
    fn from(vec: Vec<T>) -> Self {
        Self::from_vec(vec)
    }
}

impl<T> From<UmbraList<T>> for Vec<T> {
    fn from(list: UmbraList<T>) -> Self {
        list.to_vec()
    }
}

/// Advanced list operations and utilities
impl<T> UmbraList<T> {
    /// Apply a function to each element and collect the results
    pub fn map<U, F>(&self, f: F) -> UmbraList<U>
    where
        F: Fn(&T) -> U,
    {
        UmbraList::from_vec(self.items.iter().map(f).collect())
    }

    /// Filter elements based on a predicate
    pub fn filter<F>(&self, predicate: F) -> UmbraList<T>
    where
        T: Clone,
        F: Fn(&T) -> bool,
    {
        UmbraList::from_vec(
            self.items
                .iter()
                .filter(|item| predicate(item))
                .cloned()
                .collect(),
        )
    }

    /// Reduce the list to a single value
    pub fn reduce<F>(&self, f: F) -> Option<T>
    where
        T: Clone,
        F: Fn(T, &T) -> T,
    {
        let mut iter = self.items.iter();
        let first = iter.next()?.clone();
        Some(iter.fold(first, f))
    }

    /// Fold the list with an initial value
    pub fn fold<U, F>(&self, init: U, f: F) -> U
    where
        F: Fn(U, &T) -> U,
    {
        self.items.iter().fold(init, f)
    }

    /// Check if any element satisfies a predicate
    pub fn any<F>(&self, predicate: F) -> bool
    where
        F: Fn(&T) -> bool,
    {
        self.items.iter().any(predicate)
    }

    /// Check if all elements satisfy a predicate
    pub fn all<F>(&self, predicate: F) -> bool
    where
        F: Fn(&T) -> bool,
    {
        self.items.iter().all(predicate)
    }

    /// Find the first element that satisfies a predicate
    pub fn find<F>(&self, predicate: F) -> Option<&T>
    where
        F: Fn(&T) -> bool,
    {
        self.items.iter().find(|item| predicate(item))
    }

    /// Find the position of the first element that satisfies a predicate
    pub fn find_position<F>(&self, predicate: F) -> Option<usize>
    where
        F: Fn(&T) -> bool,
    {
        self.items.iter().position(predicate)
    }

    /// Take the first n elements
    pub fn take(&self, n: usize) -> UmbraList<T>
    where
        T: Clone,
    {
        UmbraList::from_vec(self.items.iter().take(n).cloned().collect())
    }

    /// Skip the first n elements
    pub fn skip(&self, n: usize) -> UmbraList<T>
    where
        T: Clone,
    {
        UmbraList::from_vec(self.items.iter().skip(n).cloned().collect())
    }

    /// Take elements while a predicate is true
    pub fn take_while<F>(&self, predicate: F) -> UmbraList<T>
    where
        T: Clone,
        F: Fn(&T) -> bool,
    {
        UmbraList::from_vec(
            self.items
                .iter()
                .take_while(|item| predicate(item))
                .cloned()
                .collect(),
        )
    }

    /// Skip elements while a predicate is true
    pub fn skip_while<F>(&self, predicate: F) -> UmbraList<T>
    where
        T: Clone,
        F: Fn(&T) -> bool,
    {
        UmbraList::from_vec(
            self.items
                .iter()
                .skip_while(|item| predicate(item))
                .cloned()
                .collect(),
        )
    }

    /// Partition the list into two lists based on a predicate
    pub fn partition<F>(&self, predicate: F) -> (UmbraList<T>, UmbraList<T>)
    where
        T: Clone,
        F: Fn(&T) -> bool,
    {
        let mut true_items = Vec::new();
        let mut false_items = Vec::new();

        for item in &self.items {
            if predicate(item) {
                true_items.push(item.clone());
            } else {
                false_items.push(item.clone());
            }
        }

        (
            UmbraList::from_vec(true_items),
            UmbraList::from_vec(false_items),
        )
    }

    /// Group consecutive equal elements
    pub fn group(&self) -> UmbraList<UmbraList<T>>
    where
        T: Clone + PartialEq,
    {
        if self.is_empty() {
            return UmbraList::new();
        }

        let mut groups = Vec::new();
        let mut current_group = vec![self.items[0].clone()];

        for item in self.items.iter().skip(1) {
            if item == current_group.last().unwrap() {
                current_group.push(item.clone());
            } else {
                groups.push(UmbraList::from_vec(current_group));
                current_group = vec![item.clone()];
            }
        }
        groups.push(UmbraList::from_vec(current_group));

        UmbraList::from_vec(groups)
    }

    /// Chunk the list into sublists of specified size
    pub fn chunk(&self, size: usize) -> UmbraResult<UmbraList<UmbraList<T>>>
    where
        T: Clone,
    {
        if size == 0 {
            return Err(UmbraError::Runtime("Chunk size cannot be zero".to_string()));
        }

        let chunks: Vec<UmbraList<T>> = self
            .items
            .chunks(size)
            .map(|chunk| UmbraList::from_vec(chunk.to_vec()))
            .collect();

        Ok(UmbraList::from_vec(chunks))
    }

    /// Create windows of specified size
    pub fn windows(&self, size: usize) -> UmbraResult<UmbraList<UmbraList<T>>>
    where
        T: Clone,
    {
        if size == 0 {
            return Err(UmbraError::Runtime("Window size cannot be zero".to_string()));
        }

        if size > self.items.len() {
            return Ok(UmbraList::new());
        }

        let windows: Vec<UmbraList<T>> = self
            .items
            .windows(size)
            .map(|window| UmbraList::from_vec(window.to_vec()))
            .collect();

        Ok(UmbraList::from_vec(windows))
    }

    /// Flatten a nested list structure
    pub fn flatten(&self) -> UmbraList<T>
    where
        T: Clone,
        Self: Clone,
        for<'a> &'a T: IntoIterator<Item = &'a T>,
    {
        let mut result = Vec::new();
        for item in &self.items {
            for sub_item in item {
                result.push(sub_item.clone());
            }
        }
        UmbraList::from_vec(result)
    }

    /// Enumerate the list with indices
    pub fn enumerate(&self) -> UmbraList<(usize, T)>
    where
        T: Clone,
    {
        UmbraList::from_vec(
            self.items
                .iter()
                .enumerate()
                .map(|(i, item)| (i, item.clone()))
                .collect(),
        )
    }

    /// Zip with another list
    pub fn zip<U>(&self, other: &UmbraList<U>) -> UmbraList<(T, U)>
    where
        T: Clone,
        U: Clone,
    {
        UmbraList::from_vec(
            self.items
                .iter()
                .zip(other.items.iter())
                .map(|(a, b)| (a.clone(), b.clone()))
                .collect(),
        )
    }

    /// Interleave with another list
    pub fn interleave(&self, other: &UmbraList<T>) -> UmbraList<T>
    where
        T: Clone,
    {
        let mut result = Vec::new();
        let mut self_iter = self.items.iter();
        let mut other_iter = other.items.iter();

        loop {
            match (self_iter.next(), other_iter.next()) {
                (Some(a), Some(b)) => {
                    result.push(a.clone());
                    result.push(b.clone());
                }
                (Some(a), None) => {
                    result.push(a.clone());
                    result.extend(self_iter.cloned());
                    break;
                }
                (None, Some(b)) => {
                    result.push(b.clone());
                    result.extend(other_iter.cloned());
                    break;
                }
                (None, None) => break,
            }
        }

        UmbraList::from_vec(result)
    }

    /// Rotate the list left by n positions
    pub fn rotate_left(&mut self, n: usize) {
        if !self.items.is_empty() {
            let len = self.items.len();
            let n = n % len;
            self.items.rotate_left(n);
        }
    }

    /// Rotate the list right by n positions
    pub fn rotate_right(&mut self, n: usize) {
        if !self.items.is_empty() {
            let len = self.items.len();
            let n = n % len;
            self.items.rotate_right(n);
        }
    }

    /// Create a new list rotated left by n positions
    pub fn rotated_left(&self, n: usize) -> UmbraList<T>
    where
        T: Clone,
    {
        let mut result = self.clone();
        result.rotate_left(n);
        result
    }

    /// Create a new list rotated right by n positions
    pub fn rotated_right(&self, n: usize) -> UmbraList<T>
    where
        T: Clone,
    {
        let mut result = self.clone();
        result.rotate_right(n);
        result
    }

    /// Swap two elements at given indices
    pub fn swap(&mut self, a: usize, b: usize) -> UmbraResult<()> {
        if a >= self.items.len() || b >= self.items.len() {
            return Err(UmbraError::Runtime(format!(
                "Index out of bounds: list length is {}, but indices are {} and {}",
                self.items.len(),
                a,
                b
            )));
        }
        self.items.swap(a, b);
        Ok(())
    }

    /// Split the list at a given index
    pub fn split_at(&self, index: usize) -> UmbraResult<(UmbraList<T>, UmbraList<T>)>
    where
        T: Clone,
    {
        if index > self.items.len() {
            return Err(UmbraError::Runtime(format!(
                "Index {} out of bounds for list of length {}",
                index,
                self.items.len()
            )));
        }

        let (left, right) = self.items.split_at(index);
        Ok((
            UmbraList::from_vec(left.to_vec()),
            UmbraList::from_vec(right.to_vec()),
        ))
    }

    /// Join elements into a string (for Display types)
    pub fn join(&self, separator: &str) -> String
    where
        T: std::fmt::Display,
    {
        self.items
            .iter()
            .map(|item| item.to_string())
            .collect::<Vec<String>>()
            .join(separator)
    }
}

/// Static utility functions for lists
pub struct ListUtils;

impl ListUtils {
    /// Create a list from a range
    pub fn range(start: i32, end: i32, step: i32) -> UmbraResult<UmbraList<i32>> {
        if step == 0 {
            return Err(UmbraError::Runtime("Step cannot be zero".to_string()));
        }

        let mut result = Vec::new();
        if step > 0 {
            let mut current = start;
            while current < end {
                result.push(current);
                current += step;
            }
        } else {
            let mut current = start;
            while current > end {
                result.push(current);
                current += step;
            }
        }

        Ok(UmbraList::from_vec(result))
    }

    /// Create a list of repeated values
    pub fn repeat<T: Clone>(value: T, count: usize) -> UmbraList<T> {
        UmbraList::repeat(value, count)
    }

    /// Merge multiple lists into one
    pub fn merge<T: Clone>(lists: &[UmbraList<T>]) -> UmbraList<T> {
        let mut result = UmbraList::new();
        for list in lists {
            result.extend(list);
        }
        result
    }

    /// Find the intersection of two lists
    pub fn intersect<T: Clone + Eq + Hash>(
        list1: &UmbraList<T>,
        list2: &UmbraList<T>,
    ) -> UmbraList<T> {
        let set2: HashSet<&T> = list2.items.iter().collect();
        UmbraList::from_vec(
            list1
                .items
                .iter()
                .filter(|item| set2.contains(item))
                .cloned()
                .collect(),
        )
    }

    /// Find the union of two lists
    pub fn union<T: Clone + Eq + Hash>(
        list1: &UmbraList<T>,
        list2: &UmbraList<T>,
    ) -> UmbraList<T> {
        let mut result = list1.clone();
        let set1: HashSet<&T> = list1.items.iter().collect();

        for item in &list2.items {
            if !set1.contains(item) {
                result.push(item.clone());
            }
        }

        result
    }

    /// Find the difference between two lists (items in first but not second)
    pub fn difference<T: Clone + Eq + Hash>(
        list1: &UmbraList<T>,
        list2: &UmbraList<T>,
    ) -> UmbraList<T> {
        let set2: HashSet<&T> = list2.items.iter().collect();
        UmbraList::from_vec(
            list1
                .items
                .iter()
                .filter(|item| !set2.contains(item))
                .cloned()
                .collect(),
        )
    }

    /// Transpose a list of lists (matrix transpose)
    pub fn transpose<T: Clone>(lists: &UmbraList<UmbraList<T>>) -> UmbraResult<UmbraList<UmbraList<T>>> {
        if lists.is_empty() {
            return Ok(UmbraList::new());
        }

        let first_len = lists.items[0].len();
        for list in &lists.items {
            if list.len() != first_len {
                return Err(UmbraError::Runtime(
                    "All lists must have the same length for transpose".to_string(),
                ));
            }
        }

        let mut result = Vec::new();
        for i in 0..first_len {
            let mut column = Vec::new();
            for list in &lists.items {
                column.push(list.items[i].clone());
            }
            result.push(UmbraList::from_vec(column));
        }

        Ok(UmbraList::from_vec(result))
    }
}
