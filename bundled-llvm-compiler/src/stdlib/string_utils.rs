/// Advanced string utilities for Umbra standard library
/// 
/// This module provides comprehensive string manipulation functions
/// including formatting, parsing, validation, and transformation utilities.

use crate::error::{UmbraError, UmbraResult};
use std::collections::HashMap;
use base64::Engine;

/// Advanced string manipulation utilities
pub struct StringUtils;

impl StringUtils {
    /// Format a string with placeholders
    pub fn format(template: &str, args: &HashMap<String, String>) -> String {
        let mut result = template.to_string();
        for (key, value) in args {
            let placeholder = format!("{{{}}}", key);
            result = result.replace(&placeholder, value);
        }
        result
    }
    
    /// Capitalize the first letter of a string
    pub fn capitalize(s: &str) -> String {
        let mut chars: Vec<char> = s.chars().collect();
        if !chars.is_empty() {
            chars[0] = chars[0].to_uppercase().next().unwrap_or(chars[0]);
        }
        chars.into_iter().collect()
    }
    
    /// Convert string to title case
    pub fn title_case(s: &str) -> String {
        s.split_whitespace()
            .map(|word| Self::capitalize(word))
            .collect::<Vec<String>>()
            .join(" ")
    }
    
    /// Convert string to camelCase
    pub fn camel_case(s: &str) -> String {
        let words: Vec<&str> = s.split(|c: char| !c.is_alphanumeric()).collect();
        if words.is_empty() {
            return String::new();
        }
        
        let mut result = words[0].to_lowercase();
        for word in &words[1..] {
            if !word.is_empty() {
                result.push_str(&Self::capitalize(&word.to_lowercase()));
            }
        }
        result
    }
    
    /// Convert string to snake_case
    pub fn snake_case(s: &str) -> String {
        let mut result = String::new();
        let mut prev_was_upper = false;
        
        for (i, c) in s.chars().enumerate() {
            if c.is_uppercase() {
                if i > 0 && !prev_was_upper {
                    result.push('_');
                }
                result.push(c.to_lowercase().next().unwrap_or(c));
                prev_was_upper = true;
            } else if c.is_alphanumeric() {
                result.push(c);
                prev_was_upper = false;
            } else {
                if !result.is_empty() && !result.ends_with('_') {
                    result.push('_');
                }
                prev_was_upper = false;
            }
        }
        
        result
    }
    
    /// Convert string to kebab-case
    pub fn kebab_case(s: &str) -> String {
        Self::snake_case(s).replace('_', "-")
    }
    
    /// Pad string to the left with specified character
    pub fn pad_left(s: &str, width: usize, pad_char: char) -> String {
        if s.len() >= width {
            s.to_string()
        } else {
            let padding = pad_char.to_string().repeat(width - s.len());
            format!("{}{}", padding, s)
        }
    }
    
    /// Pad string to the right with specified character
    pub fn pad_right(s: &str, width: usize, pad_char: char) -> String {
        if s.len() >= width {
            s.to_string()
        } else {
            let padding = pad_char.to_string().repeat(width - s.len());
            format!("{}{}", s, padding)
        }
    }
    
    /// Center string within specified width
    pub fn center(s: &str, width: usize, pad_char: char) -> String {
        if s.len() >= width {
            s.to_string()
        } else {
            let total_padding = width - s.len();
            let left_padding = total_padding / 2;
            let right_padding = total_padding - left_padding;
            
            let left_pad = pad_char.to_string().repeat(left_padding);
            let right_pad = pad_char.to_string().repeat(right_padding);
            
            format!("{}{}{}", left_pad, s, right_pad)
        }
    }
    
    /// Truncate string to specified length with optional suffix
    pub fn truncate(s: &str, max_len: usize, suffix: Option<&str>) -> String {
        if s.len() <= max_len {
            s.to_string()
        } else {
            let suffix = suffix.unwrap_or("...");
            let truncate_len = max_len.saturating_sub(suffix.len());
            format!("{}{}", &s[..truncate_len], suffix)
        }
    }
    
    /// Count occurrences of substring in string
    pub fn count_occurrences(s: &str, pattern: &str) -> usize {
        if pattern.is_empty() {
            return 0;
        }
        
        let mut count = 0;
        let mut start = 0;
        
        while let Some(pos) = s[start..].find(pattern) {
            count += 1;
            start += pos + pattern.len();
        }
        
        count
    }
    
    /// Replace all occurrences of pattern with replacement
    pub fn replace_all(s: &str, pattern: &str, replacement: &str) -> String {
        s.replace(pattern, replacement)
    }
    
    /// Replace first n occurrences of pattern with replacement
    pub fn replace_n(s: &str, pattern: &str, replacement: &str, n: usize) -> String {
        if n == 0 || pattern.is_empty() {
            return s.to_string();
        }
        
        let mut result = String::new();
        let mut remaining = s;
        let mut replacements_made = 0;
        
        while replacements_made < n {
            if let Some(pos) = remaining.find(pattern) {
                result.push_str(&remaining[..pos]);
                result.push_str(replacement);
                remaining = &remaining[pos + pattern.len()..];
                replacements_made += 1;
            } else {
                break;
            }
        }
        
        result.push_str(remaining);
        result
    }
    
    /// Check if string is numeric
    pub fn is_numeric(s: &str) -> bool {
        !s.is_empty() && s.chars().all(|c| c.is_numeric())
    }
    
    /// Check if string is alphabetic
    pub fn is_alphabetic(s: &str) -> bool {
        !s.is_empty() && s.chars().all(|c| c.is_alphabetic())
    }
    
    /// Check if string is alphanumeric
    pub fn is_alphanumeric(s: &str) -> bool {
        !s.is_empty() && s.chars().all(|c| c.is_alphanumeric())
    }
    
    /// Check if string is whitespace only
    pub fn is_whitespace(s: &str) -> bool {
        !s.is_empty() && s.chars().all(|c| c.is_whitespace())
    }
    
    /// Remove all whitespace from string
    pub fn remove_whitespace(s: &str) -> String {
        s.chars().filter(|c| !c.is_whitespace()).collect()
    }
    
    /// Normalize whitespace (replace multiple whitespace with single space)
    pub fn normalize_whitespace(s: &str) -> String {
        s.split_whitespace().collect::<Vec<&str>>().join(" ")
    }
    
    /// Extract words from string
    pub fn extract_words(s: &str) -> Vec<String> {
        s.split(|c: char| !c.is_alphanumeric())
            .filter(|word| !word.is_empty())
            .map(|word| word.to_string())
            .collect()
    }
    
    /// Reverse string
    pub fn reverse(s: &str) -> String {
        s.chars().rev().collect()
    }
    
    /// Check if string is palindrome
    pub fn is_palindrome(s: &str) -> bool {
        let cleaned: String = s.chars()
            .filter(|c| c.is_alphanumeric())
            .map(|c| c.to_lowercase().next().unwrap_or(c))
            .collect();
        
        cleaned == Self::reverse(&cleaned)
    }
    
    /// Calculate Levenshtein distance between two strings
    pub fn levenshtein_distance(s1: &str, s2: &str) -> usize {
        let len1 = s1.chars().count();
        let len2 = s2.chars().count();
        
        if len1 == 0 {
            return len2;
        }
        if len2 == 0 {
            return len1;
        }
        
        let mut matrix = vec![vec![0; len2 + 1]; len1 + 1];
        
        // Initialize first row and column
        for i in 0..=len1 {
            matrix[i][0] = i;
        }
        for j in 0..=len2 {
            matrix[0][j] = j;
        }
        
        let chars1: Vec<char> = s1.chars().collect();
        let chars2: Vec<char> = s2.chars().collect();
        
        for i in 1..=len1 {
            for j in 1..=len2 {
                let cost = if chars1[i - 1] == chars2[j - 1] { 0 } else { 1 };
                
                matrix[i][j] = std::cmp::min(
                    std::cmp::min(
                        matrix[i - 1][j] + 1,      // deletion
                        matrix[i][j - 1] + 1       // insertion
                    ),
                    matrix[i - 1][j - 1] + cost    // substitution
                );
            }
        }
        
        matrix[len1][len2]
    }
    
    /// Calculate string similarity (0.0 to 1.0)
    pub fn similarity(s1: &str, s2: &str) -> f64 {
        let max_len = std::cmp::max(s1.len(), s2.len());
        if max_len == 0 {
            return 1.0;
        }
        
        let distance = Self::levenshtein_distance(s1, s2);
        1.0 - (distance as f64 / max_len as f64)
    }
    
    /// Generate a slug from string (URL-friendly)
    pub fn slugify(s: &str) -> String {
        s.to_lowercase()
            .chars()
            .map(|c| if c.is_alphanumeric() { c } else { '-' })
            .collect::<String>()
            .split('-')
            .filter(|part| !part.is_empty())
            .collect::<Vec<&str>>()
            .join("-")
    }
    
    /// Escape HTML characters
    pub fn escape_html(s: &str) -> String {
        s.replace('&', "&amp;")
            .replace('<', "&lt;")
            .replace('>', "&gt;")
            .replace('"', "&quot;")
            .replace('\'', "&#x27;")
    }
    
    /// Unescape HTML characters
    pub fn unescape_html(s: &str) -> String {
        s.replace("&amp;", "&")
            .replace("&lt;", "<")
            .replace("&gt;", ">")
            .replace("&quot;", "\"")
            .replace("&#x27;", "'")
    }
    
    /// Extract numbers from string
    pub fn extract_numbers(s: &str) -> Vec<f64> {
        let mut numbers = Vec::new();
        let mut current_number = String::new();
        let mut has_dot = false;
        
        for c in s.chars() {
            if c.is_numeric() {
                current_number.push(c);
            } else if c == '.' && !has_dot && !current_number.is_empty() {
                current_number.push(c);
                has_dot = true;
            } else if c == '-' && current_number.is_empty() {
                current_number.push(c);
            } else {
                if !current_number.is_empty() {
                    if let Ok(num) = current_number.parse::<f64>() {
                        numbers.push(num);
                    }
                    current_number.clear();
                    has_dot = false;
                }
            }
        }
        
        // Handle last number
        if !current_number.is_empty() {
            if let Ok(num) = current_number.parse::<f64>() {
                numbers.push(num);
            }
        }
        
        numbers
    }
    
    /// Wrap text to specified width
    pub fn wrap_text(s: &str, width: usize) -> Vec<String> {
        if width == 0 {
            return vec![s.to_string()];
        }
        
        let words: Vec<&str> = s.split_whitespace().collect();
        let mut lines = Vec::new();
        let mut current_line = String::new();
        
        for word in words {
            if current_line.is_empty() {
                current_line = word.to_string();
            } else if current_line.len() + 1 + word.len() <= width {
                current_line.push(' ');
                current_line.push_str(word);
            } else {
                lines.push(current_line);
                current_line = word.to_string();
            }
        }
        
        if !current_line.is_empty() {
            lines.push(current_line);
        }
        
        lines
    }

    /// Check if string contains substring
    pub fn contains(s: &str, pattern: &str) -> bool {
        s.contains(pattern)
    }

    /// Check if string starts with prefix
    pub fn starts_with(s: &str, prefix: &str) -> bool {
        s.starts_with(prefix)
    }

    /// Check if string ends with suffix
    pub fn ends_with(s: &str, suffix: &str) -> bool {
        s.ends_with(suffix)
    }

    /// Get substring from start to end index
    pub fn substring(s: &str, start: usize, end: usize) -> UmbraResult<String> {
        let chars: Vec<char> = s.chars().collect();
        if start > chars.len() || end > chars.len() || start > end {
            return Err(UmbraError::Runtime(format!(
                "Invalid substring indices: start={}, end={}, length={}",
                start, end, chars.len()
            )));
        }
        Ok(chars[start..end].iter().collect())
    }

    /// Get substring from start index to end of string
    pub fn substring_from(s: &str, start: usize) -> UmbraResult<String> {
        let chars: Vec<char> = s.chars().collect();
        if start > chars.len() {
            return Err(UmbraError::Runtime(format!(
                "Invalid substring start index: start={}, length={}",
                start, chars.len()
            )));
        }
        Ok(chars[start..].iter().collect())
    }

    /// Split string by delimiter
    pub fn split(s: &str, delimiter: &str) -> Vec<String> {
        if delimiter.is_empty() {
            return s.chars().map(|c| c.to_string()).collect();
        }
        s.split(delimiter).map(|s| s.to_string()).collect()
    }

    /// Split string by whitespace
    pub fn split_whitespace(s: &str) -> Vec<String> {
        s.split_whitespace().map(|s| s.to_string()).collect()
    }

    /// Split string by lines
    pub fn split_lines(s: &str) -> Vec<String> {
        s.lines().map(|s| s.to_string()).collect()
    }

    /// Join strings with delimiter
    pub fn join(strings: &[String], delimiter: &str) -> String {
        strings.join(delimiter)
    }

    /// Convert string to uppercase
    pub fn to_upper(s: &str) -> String {
        s.to_uppercase()
    }

    /// Convert string to lowercase
    pub fn to_lower(s: &str) -> String {
        s.to_lowercase()
    }

    /// Trim whitespace from both ends
    pub fn trim(s: &str) -> String {
        s.trim().to_string()
    }

    /// Trim whitespace from start
    pub fn trim_start(s: &str) -> String {
        s.trim_start().to_string()
    }

    /// Trim whitespace from end
    pub fn trim_end(s: &str) -> String {
        s.trim_end().to_string()
    }

    /// Get string length in characters
    pub fn length(s: &str) -> usize {
        s.chars().count()
    }

    /// Get string length in bytes
    pub fn byte_length(s: &str) -> usize {
        s.len()
    }

    /// Check if string is empty
    pub fn is_empty(s: &str) -> bool {
        s.is_empty()
    }

    /// Get character at index
    pub fn char_at(s: &str, index: usize) -> UmbraResult<char> {
        let chars: Vec<char> = s.chars().collect();
        if index >= chars.len() {
            return Err(UmbraError::Runtime(format!(
                "Index {} out of bounds for string of length {}",
                index, chars.len()
            )));
        }
        Ok(chars[index])
    }

    /// Find first occurrence of pattern
    pub fn find(s: &str, pattern: &str) -> Option<usize> {
        s.find(pattern)
    }

    /// Find last occurrence of pattern
    pub fn rfind(s: &str, pattern: &str) -> Option<usize> {
        s.rfind(pattern)
    }

    /// Find all occurrences of pattern
    pub fn find_all(s: &str, pattern: &str) -> Vec<usize> {
        let mut positions = Vec::new();
        let mut start = 0;

        while let Some(pos) = s[start..].find(pattern) {
            let absolute_pos = start + pos;
            positions.push(absolute_pos);
            start = absolute_pos + pattern.len();
        }

        positions
    }

    /// Insert string at index
    pub fn insert(s: &str, index: usize, insert_str: &str) -> UmbraResult<String> {
        let chars: Vec<char> = s.chars().collect();
        if index > chars.len() {
            return Err(UmbraError::Runtime(format!(
                "Index {} out of bounds for string of length {}",
                index, chars.len()
            )));
        }

        let mut result = String::new();
        result.extend(chars[..index].iter());
        result.push_str(insert_str);
        result.extend(chars[index..].iter());

        Ok(result)
    }

    /// Remove characters from start to end index
    pub fn remove(s: &str, start: usize, end: usize) -> UmbraResult<String> {
        let chars: Vec<char> = s.chars().collect();
        if start > chars.len() || end > chars.len() || start > end {
            return Err(UmbraError::Runtime(format!(
                "Invalid remove indices: start={}, end={}, length={}",
                start, end, chars.len()
            )));
        }

        let mut result = String::new();
        result.extend(chars[..start].iter());
        result.extend(chars[end..].iter());

        Ok(result)
    }

    /// Repeat string n times
    pub fn repeat(s: &str, n: usize) -> String {
        s.repeat(n)
    }

    /// Convert string to bytes
    pub fn to_bytes(s: &str) -> Vec<u8> {
        s.bytes().collect()
    }

    /// Convert bytes to string
    pub fn from_bytes(bytes: &[u8]) -> UmbraResult<String> {
        String::from_utf8(bytes.to_vec())
            .map_err(|e| UmbraError::Runtime(format!("Invalid UTF-8: {}", e)))
    }

    /// Encode string as base64
    pub fn to_base64(s: &str) -> String {
        base64::engine::general_purpose::STANDARD.encode(s)
    }

    /// Decode base64 string
    pub fn from_base64(s: &str) -> UmbraResult<String> {
        let bytes = base64::engine::general_purpose::STANDARD.decode(s)
            .map_err(|e| UmbraError::Runtime(format!("Invalid base64: {}", e)))?;
        String::from_utf8(bytes)
            .map_err(|e| UmbraError::Runtime(format!("Invalid UTF-8: {}", e)))
    }

    /// URL encode string
    pub fn url_encode(s: &str) -> String {
        urlencoding::encode(s).to_string()
    }

    /// URL decode string
    pub fn url_decode(s: &str) -> UmbraResult<String> {
        urlencoding::decode(s)
            .map(|s| s.to_string())
            .map_err(|e| UmbraError::Runtime(format!("Invalid URL encoding: {}", e)))
    }
}

/// Global string functions for easy access
pub fn contains(s: &str, pattern: &str) -> bool {
    StringUtils::contains(s, pattern)
}

pub fn starts_with(s: &str, prefix: &str) -> bool {
    StringUtils::starts_with(s, prefix)
}

pub fn ends_with(s: &str, suffix: &str) -> bool {
    StringUtils::ends_with(s, suffix)
}

pub fn substring(s: &str, start: usize, end: usize) -> UmbraResult<String> {
    StringUtils::substring(s, start, end)
}

pub fn split(s: &str, delimiter: &str) -> Vec<String> {
    StringUtils::split(s, delimiter)
}

pub fn join(strings: &[String], delimiter: &str) -> String {
    StringUtils::join(strings, delimiter)
}

pub fn to_upper(s: &str) -> String {
    StringUtils::to_upper(s)
}

pub fn to_lower(s: &str) -> String {
    StringUtils::to_lower(s)
}

pub fn trim(s: &str) -> String {
    StringUtils::trim(s)
}
