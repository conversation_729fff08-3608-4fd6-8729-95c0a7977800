/// Memory management functions for Umbra standard library
/// 
/// This module provides comprehensive memory management operations including
/// allocation, deallocation, garbage collection, and memory utilities.

use crate::error::{UmbraError, UmbraResult};
use std::alloc::{Layout, GlobalAlloc, System};

/// Global memory allocator wrapper
pub struct UmbraAllocator;

impl UmbraAllocator {
    /// Allocate memory block
    pub fn malloc(size: usize) -> UmbraResult<*mut u8> {
        if size == 0 {
            return Err(UmbraError::Memory("Cannot allocate zero bytes".to_string()));
        }
        
        let layout = Layout::from_size_align(size, 8)
            .map_err(|e| UmbraError::Memory(format!("Invalid layout: {}", e)))?;
        
        let ptr = unsafe { System.alloc(layout) };
        
        if ptr.is_null() {
            Err(UmbraError::Memory("Failed to allocate memory".to_string()))
        } else {
            Ok(ptr)
        }
    }

    /// Free memory block
    pub fn free(ptr: *mut u8, size: usize) -> UmbraResult<()> {
        if ptr.is_null() {
            return Err(UmbraError::Memory("Cannot free null pointer".to_string()));
        }
        
        if size == 0 {
            return Err(UmbraError::Memory("Cannot free zero-sized allocation".to_string()));
        }

        let layout = Layout::from_size_align(size, 8)
            .map_err(|e| UmbraError::Memory(format!("Invalid layout: {}", e)))?;
        
        unsafe { System.dealloc(ptr, layout) }
        Ok(())
    }

    /// Reallocate memory block
    pub fn realloc(ptr: *mut u8, old_size: usize, new_size: usize) -> UmbraResult<*mut u8> {
        if new_size == 0 {
            if !ptr.is_null() {
                Self::free(ptr, old_size)?;
            }
            return Ok(std::ptr::null_mut());
        }
        
        if ptr.is_null() {
            return Self::malloc(new_size);
        }
        
        let old_layout = Layout::from_size_align(old_size, 8)
            .map_err(|e| UmbraError::Memory(format!("Invalid old layout: {}", e)))?;
        
        let new_ptr = unsafe { System.realloc(ptr, old_layout, new_size) };
        
        if new_ptr.is_null() {
            Err(UmbraError::Memory("Failed to reallocate memory".to_string()))
        } else {
            Ok(new_ptr)
        }
    }

    /// Allocate zero-initialized memory
    pub fn calloc(count: usize, size: usize) -> UmbraResult<*mut u8> {
        let total_size = count.checked_mul(size)
            .ok_or_else(|| UmbraError::Memory("Integer overflow in calloc".to_string()))?;
        
        if total_size == 0 {
            return Ok(std::ptr::null_mut());
        }
        
        let layout = Layout::from_size_align(total_size, 8)
            .map_err(|e| UmbraError::Memory(format!("Invalid layout: {}", e)))?;
        
        let ptr = unsafe { System.alloc_zeroed(layout) };
        
        if ptr.is_null() {
            Err(UmbraError::Memory("Failed to allocate zeroed memory".to_string()))
        } else {
            Ok(ptr)
        }
    }

    /// Check if pointer is valid
    pub fn is_valid_pointer(ptr: *const u8) -> bool {
        !ptr.is_null()
    }

    /// Get pointer alignment
    pub fn pointer_alignment(ptr: *const u8) -> usize {
        (ptr as usize) & ((ptr as usize).wrapping_neg())
    }

    /// Copy memory block
    pub fn memcpy(dest: *mut u8, src: *const u8, size: usize) -> UmbraResult<()> {
        if dest.is_null() || src.is_null() {
            return Err(UmbraError::Memory("Null pointer in memcpy".to_string()));
        }
        
        unsafe {
            std::ptr::copy_nonoverlapping(src, dest, size);
        }
        
        Ok(())
    }

    /// Move memory block (handles overlapping regions)
    pub fn memmove(dest: *mut u8, src: *const u8, size: usize) -> UmbraResult<()> {
        if dest.is_null() || src.is_null() {
            return Err(UmbraError::Memory("Null pointer in memmove".to_string()));
        }
        
        unsafe {
            std::ptr::copy(src, dest, size);
        }
        
        Ok(())
    }

    /// Set memory block to value
    pub fn memset(ptr: *mut u8, value: u8, size: usize) -> UmbraResult<()> {
        if ptr.is_null() {
            return Err(UmbraError::Memory("Null pointer in memset".to_string()));
        }
        
        unsafe {
            std::ptr::write_bytes(ptr, value, size);
        }
        
        Ok(())
    }

    /// Compare memory blocks
    pub fn memcmp(ptr1: *const u8, ptr2: *const u8, size: usize) -> UmbraResult<i32> {
        if ptr1.is_null() || ptr2.is_null() {
            return Err(UmbraError::Memory("Null pointer in memcmp".to_string()));
        }
        
        let slice1 = unsafe { std::slice::from_raw_parts(ptr1, size) };
        let slice2 = unsafe { std::slice::from_raw_parts(ptr2, size) };
        
        Ok(match slice1.cmp(slice2) {
            std::cmp::Ordering::Less => -1,
            std::cmp::Ordering::Equal => 0,
            std::cmp::Ordering::Greater => 1,
        })
    }

    /// Get memory page size
    pub fn page_size() -> usize {
        // This is platform-specific, but 4KB is common
        4096
    }

    /// Align size to page boundary
    pub fn align_to_page(size: usize) -> usize {
        let page_size = Self::page_size();
        (size + page_size - 1) & !(page_size - 1)
    }

    /// Get system memory information
    pub fn system_memory_info() -> SystemMemoryInfo {
        // This would use platform-specific APIs to get actual memory info
        // For now, return placeholder values
        SystemMemoryInfo {
            total_physical: 8 * 1024 * 1024 * 1024, // 8GB
            available_physical: 4 * 1024 * 1024 * 1024, // 4GB
            total_virtual: 16 * 1024 * 1024 * 1024, // 16GB
            available_virtual: 8 * 1024 * 1024 * 1024, // 8GB
            page_size: Self::page_size(),
        }
    }
}

/// System memory information
#[derive(Debug, Clone)]
pub struct SystemMemoryInfo {
    pub total_physical: usize,
    pub available_physical: usize,
    pub total_virtual: usize,
    pub available_virtual: usize,
    pub page_size: usize,
}

/// Memory pool for efficient allocation of same-sized objects
pub struct MemoryPool {
    block_size: usize,
    blocks_per_chunk: usize,
    chunks: Vec<MemoryChunk>,
    free_blocks: Vec<*mut u8>,
}

struct MemoryChunk {
    memory: *mut u8,
    size: usize,
}

impl MemoryPool {
    /// Create a new memory pool
    pub fn new(block_size: usize, blocks_per_chunk: usize) -> Self {
        Self {
            block_size: block_size.max(std::mem::size_of::<*mut u8>()),
            blocks_per_chunk,
            chunks: Vec::new(),
            free_blocks: Vec::new(),
        }
    }

    /// Allocate a block from the pool
    pub fn allocate(&mut self) -> UmbraResult<*mut u8> {
        if self.free_blocks.is_empty() {
            self.allocate_new_chunk()?;
        }

        Ok(self.free_blocks.pop().unwrap())
    }

    /// Deallocate a block back to the pool
    pub fn deallocate(&mut self, ptr: *mut u8) -> UmbraResult<()> {
        if ptr.is_null() {
            return Err(UmbraError::Memory("Cannot deallocate null pointer".to_string()));
        }

        // TODO: Verify that the pointer belongs to this pool
        self.free_blocks.push(ptr);
        Ok(())
    }

    /// Allocate a new chunk of memory
    fn allocate_new_chunk(&mut self) -> UmbraResult<()> {
        let chunk_size = self.block_size * self.blocks_per_chunk;
        let layout = Layout::from_size_align(chunk_size, 8)
            .map_err(|e| UmbraError::Memory(format!("Invalid layout: {}", e)))?;

        let memory = unsafe { System.alloc(layout) };
        if memory.is_null() {
            return Err(UmbraError::Memory("Failed to allocate memory chunk".to_string()));
        }

        // Initialize free block list for this chunk
        for i in 0..self.blocks_per_chunk {
            let block_ptr = unsafe { memory.add(i * self.block_size) };
            self.free_blocks.push(block_ptr);
        }

        self.chunks.push(MemoryChunk {
            memory,
            size: chunk_size,
        });

        Ok(())
    }
}

impl Drop for MemoryPool {
    fn drop(&mut self) {
        for chunk in &self.chunks {
            let layout = Layout::from_size_align(chunk.size, 8).unwrap();
            unsafe { System.dealloc(chunk.memory, layout) };
        }
    }
}

/// Global memory management functions
pub fn malloc(size: usize) -> UmbraResult<*mut u8> {
    UmbraAllocator::malloc(size)
}

pub fn free(ptr: *mut u8, size: usize) -> UmbraResult<()> {
    UmbraAllocator::free(ptr, size)
}

pub fn realloc(ptr: *mut u8, old_size: usize, new_size: usize) -> UmbraResult<*mut u8> {
    UmbraAllocator::realloc(ptr, old_size, new_size)
}

pub fn calloc(count: usize, size: usize) -> UmbraResult<*mut u8> {
    UmbraAllocator::calloc(count, size)
}

pub fn memcpy(dest: *mut u8, src: *const u8, size: usize) -> UmbraResult<()> {
    UmbraAllocator::memcpy(dest, src, size)
}

pub fn memmove(dest: *mut u8, src: *const u8, size: usize) -> UmbraResult<()> {
    UmbraAllocator::memmove(dest, src, size)
}

pub fn memset(ptr: *mut u8, value: u8, size: usize) -> UmbraResult<()> {
    UmbraAllocator::memset(ptr, value, size)
}

pub fn memcmp(ptr1: *const u8, ptr2: *const u8, size: usize) -> UmbraResult<i32> {
    UmbraAllocator::memcmp(ptr1, ptr2, size)
}

pub fn is_valid_pointer(ptr: *const u8) -> bool {
    UmbraAllocator::is_valid_pointer(ptr)
}

pub fn get_system_memory_info() -> SystemMemoryInfo {
    UmbraAllocator::system_memory_info()
}
