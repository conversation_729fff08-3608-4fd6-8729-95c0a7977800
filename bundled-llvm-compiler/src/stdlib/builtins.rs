use crate::error::SourceLocation;
use crate::parser::ast::{BasicType, Type};
use crate::semantic::symbol_table::{FunctionSignature, Symbol, SymbolType};

/// Built-in functions for the Umbra standard library
pub struct BuiltinFunctions;

impl BuiltinFunctions {
    /// Get all built-in function symbols
    pub fn get_all() -> Vec<Symbol> {
        vec![
            // I/O functions
            Self::create_show_function(),
            Self::create_println_function(),
            Self::create_print_function(),
            Self::create_read_function(),
            Self::create_readln_function(),
            Self::create_input_function(),
            Self::create_write_function(),
            Self::create_read_file_function(),
            Self::create_write_file_function(),
            Self::create_file_exists_function(),
            // JSON functions
            Self::create_json_parse_function(),
            Self::create_json_stringify_function(),
            Self::create_json_stringify_string_function(),
            Self::create_json_parse_string_function(),
            // Random functions
            Self::create_random_function(),
            Self::create_random_int_function(),
            Self::create_random_float_function(),
            // Statistical functions
            Self::create_mean_function(),
            Self::create_std_function(),
            Self::create_variance_function(),
            // Math functions
            Self::create_range_function(),
            Self::create_abs_function(),
            Self::create_max_function(),
            Self::create_min_function(),
            // Advanced math functions
            Self::create_sqrt_function(),
            Self::create_sin_function(),
            Self::create_cos_function(),
            Self::create_tan_function(),
            Self::create_log_function(),
            Self::create_exp_function(),
            Self::create_pow_function(),
            Self::create_floor_function(),
            Self::create_ceil_function(),
            Self::create_round_function(),
            // AI/ML functions
            Self::create_load_dataset_function(),
            Self::create_create_model_function(),
            // String functions
            Self::create_len_function(),
            // Collection functions
            Self::create_vec_new_function(),
            Self::create_vec_push_function(),
            Self::create_hashmap_new_function(),
            Self::create_hashset_new_function(),
            Self::create_str_len_function(),
            Self::create_substring_function(),
            Self::create_split_function(),
            Self::create_array_get_function(),
            Self::create_join_function(),
            Self::create_to_upper_function(),
            Self::create_to_lower_function(),
            Self::create_to_int_function(),
            Self::create_to_float_function(),
            Self::create_to_string_function(),
            Self::create_newline_function(),
            Self::create_trim_function(),
            Self::create_contains_function(),
            Self::create_starts_with_function(),
            Self::create_ends_with_function(),
            Self::create_string_replace_function(),
            Self::create_string_repeat_function(),
            Self::create_string_reverse_function(),
            // Memory management functions
            Self::create_malloc_function(),
            Self::create_free_function(),
            // List functions
            Self::create_list_create_function(),
            Self::create_list_get_function(),
            Self::create_list_length_function(),
            Self::create_list_append_function(),
            // Garbage collection functions
            Self::create_gc_collect_function(),
            Self::create_gc_retain_function(),
            Self::create_gc_release_function(),
            // Additional collection functions
            Self::create_list_insert_function(),
            Self::create_list_remove_function(),
            Self::create_list_clear_function(),
            Self::create_list_contains_function(),
            Self::create_list_index_of_function(),
            Self::create_list_reverse_function(),
            Self::create_list_sort_function(),
            // Additional string functions
            Self::create_string_pad_left_function(),
            Self::create_string_pad_right_function(),
            // Additional math functions
            Self::create_clamp_function(),
            Self::create_sign_function(),
            Self::create_degrees_function(),
            Self::create_radians_function(),
        ];

        // Add functional programming functions
        let mut all_functions = vec![
            // I/O functions
            Self::create_show_function(),
            Self::create_println_function(),
            Self::create_print_function(),
            Self::create_read_function(),
            Self::create_readln_function(),
            Self::create_input_function(),
            Self::create_write_function(),
            Self::create_read_file_function(),
            Self::create_write_file_function(),
            Self::create_file_exists_function(),
            // JSON functions
            Self::create_json_parse_function(),
            Self::create_json_stringify_function(),
            // Type conversion functions
            Self::create_to_int_function(),
            Self::create_to_float_function(),
            Self::create_to_string_function(),
            // Memory management functions
            Self::create_malloc_function(),
            Self::create_free_function(),
            Self::create_gc_retain_function(),
            Self::create_gc_release_function(),
            // List functions
            Self::create_list_create_function(),
            Self::create_list_get_function(),
            Self::create_list_length_function(),
            Self::create_list_append_function(),
            // AI/ML functions
            Self::create_load_dataset_function(),
            Self::create_create_model_function(),
            // Math functions
            Self::create_sqrt_function(),
            Self::create_sin_function(),
            Self::create_cos_function(),
            Self::create_tan_function(),
            Self::create_log_function(),
            Self::create_exp_function(),
            Self::create_pow_function(),
            Self::create_abs_function(),
            Self::create_floor_function(),
            Self::create_ceil_function(),
            Self::create_round_function(),
            Self::create_min_function(),
            Self::create_max_function(),
            Self::create_random_function(),
            // Statistical functions
            Self::create_mean_function(),
            Self::create_variance_function(),
            Self::create_std_function(),
            Self::create_len_function(),
            Self::create_array_get_function(),
            Self::create_hashmap_new_function(),
            Self::create_hashset_new_function(),
            Self::create_str_len_function(),
            Self::create_substring_function(),
            Self::create_split_function(),
            Self::create_join_function(),
            Self::create_to_upper_function(),
            Self::create_to_lower_function(),
            Self::create_newline_function(),
            Self::create_trim_function(),
            Self::create_contains_function(),
            Self::create_starts_with_function(),
            Self::create_ends_with_function(),
            Self::create_string_replace_function(),
            Self::create_string_repeat_function(),
            Self::create_string_reverse_function(),
            // Additional string functions
            Self::create_string_pad_left_function(),
            Self::create_string_pad_right_function(),
            // Additional math functions
            Self::create_clamp_function(),
            Self::create_sign_function(),
            Self::create_degrees_function(),
            Self::create_radians_function(),
        ];
        all_functions.extend(crate::stdlib::functional::FunctionalLibrary::get_functions());

        all_functions
    }

    fn create_show_function() -> Symbol {
        // show() is variadic - accepts any number of arguments of any type
        Symbol {
            name: "show".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::Void),
            is_initialized: true,
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            function_signature: Some(FunctionSignature {
                parameters: vec![], // Variadic - handled specially
                return_type: Type::Basic(BasicType::Void),
            }),
            ..Default::default()
        }
    }

    fn create_println_function() -> Symbol {
        // println!() is a macro-like function that prints with newline
        Symbol {
            name: "println!".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::Void),
            is_initialized: true,
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            function_signature: Some(FunctionSignature {
                parameters: vec![], // Variadic - handled specially
                return_type: Type::Basic(BasicType::Void),
            }),
            ..Default::default()
        }
    }

    fn create_print_function() -> Symbol {
        // print!() is a macro-like function that prints without newline
        Symbol {
            name: "print!".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::Void),
            is_initialized: true,
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            function_signature: Some(FunctionSignature {
                parameters: vec![], // Variadic - handled specially
                return_type: Type::Basic(BasicType::Void),
            }),
            ..Default::default()
        }
    }

    fn create_read_function() -> Symbol {
        Symbol {
            name: "read".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::String),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![],
                return_type: Type::Basic(BasicType::String),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_readln_function() -> Symbol {
        Symbol {
            name: "readln".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::String),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![],
                return_type: Type::Basic(BasicType::String),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_input_function() -> Symbol {
        Symbol {
            name: "input".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::String),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![],
                return_type: Type::Basic(BasicType::String),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_write_function() -> Symbol {
        Symbol {
            name: "write".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::Void),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![
                    Type::Basic(BasicType::String), // filename
                    Type::Basic(BasicType::String), // content
                ],
                return_type: Type::Basic(BasicType::Void),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_range_function() -> Symbol {
        Symbol {
            name: "range".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::List(Box::new(Type::Basic(BasicType::Integer))),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![
                    Type::Basic(BasicType::Integer), // start
                    Type::Basic(BasicType::Integer), // end
                ],
                return_type: Type::List(Box::new(Type::Basic(BasicType::Integer))),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_abs_function() -> Symbol {
        Symbol {
            name: "abs".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::Integer),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![Type::Basic(BasicType::Integer)],
                return_type: Type::Basic(BasicType::Integer),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_max_function() -> Symbol {
        Symbol {
            name: "max".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::Integer),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![
                    Type::Basic(BasicType::Integer),
                    Type::Basic(BasicType::Integer),
                ],
                return_type: Type::Basic(BasicType::Integer),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_min_function() -> Symbol {
        Symbol {
            name: "min".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::Integer),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![
                    Type::Basic(BasicType::Integer),
                    Type::Basic(BasicType::Integer),
                ],
                return_type: Type::Basic(BasicType::Integer),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_load_dataset_function() -> Symbol {
        Symbol {
            name: "load_dataset".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::Dataset),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![Type::Basic(BasicType::String)], // path
                return_type: Type::Basic(BasicType::Dataset),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_create_model_function() -> Symbol {
        Symbol {
            name: "create_model".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::Model),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![Type::Basic(BasicType::String)], // model_type
                return_type: Type::Basic(BasicType::Model),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_len_function() -> Symbol {
        Symbol {
            name: "len".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::Integer),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![Type::List(Box::new(Type::Auto))], // any list type
                return_type: Type::Basic(BasicType::Integer),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_str_len_function() -> Symbol {
        Symbol {
            name: "str_len".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::Integer),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![Type::Basic(BasicType::String)],
                return_type: Type::Basic(BasicType::Integer),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    /// Check if a function name is a built-in function
    #[allow(dead_code)]
    pub fn is_builtin(name: &str) -> bool {
        matches!(
            name,
            "show"
                | "read"
                | "write"
                | "range"
                | "abs"
                | "max"
                | "min"
                | "load_dataset"
                | "create_model"
                | "len"
                | "str_len"
                | "random"
                | "random_int"
                | "random_float"
                | "sqrt"
                | "sin"
                | "cos"
                | "tan"
                | "log"
                | "exp"
                | "pow"
                | "floor"
                | "ceil"
                | "round"
                | "split"
                | "array_get"
                // Critical string functions
                | "trim"
                | "contains"
                | "starts_with"
                | "ends_with"
                | "substring"
                | "join"
                | "to_upper"
                | "to_lower"
                | "to_string"
                // JSON functions
                | "json_parse"
                | "json_stringify"
                | "json_stringify_string"
                | "json_parse_string"
                // Additional collection functions
                | "list_insert"
                | "list_remove"
                | "list_clear"
                | "list_contains"
                | "list_index_of"
                | "list_reverse"
                | "list_sort"
                // Additional string functions
                | "string_replace"
                | "string_repeat"
                | "string_pad_left"
                | "string_pad_right"
                | "string_reverse"
                // Additional math functions
                | "clamp"
                | "sign"
                | "degrees"
                | "radians"
        )
    }

    /// Get the LLVM function name for a built-in function
    #[allow(dead_code)]
    pub fn get_llvm_name(name: &str) -> Option<&'static str> {
        match name {
            "show" => Some("umbra_show_variadic"),
            "read" => Some("umbra_read_string"),
            "write" => Some("umbra_write_file"),
            "range" => Some("umbra_range"),
            "load_dataset" => Some("umbra_load_dataset"),
            "create_model" => Some("umbra_create_model"),
            "len" => Some("umbra_len"),
            "str_len" => Some("umbra_str_len"),
            "random" => Some("umbra_random"),
            "random_int" => Some("umbra_random_int"),
            "random_float" => Some("umbra_random_float"),
            "sqrt" => Some("umbra_sqrt"),
            "sin" => Some("umbra_sin"),
            "cos" => Some("umbra_cos"),
            "tan" => Some("umbra_tan"),
            "log" => Some("umbra_log"),
            "exp" => Some("umbra_exp"),
            "pow" => Some("umbra_pow"),
            "floor" => Some("umbra_floor"),
            "ceil" => Some("umbra_ceil"),
            "round" => Some("umbra_round"),
            "abs" => Some("umbra_abs"),
            "max" => Some("umbra_max"),
            "min" => Some("umbra_min"),
            "split" => Some("umbra_split"),
            "array_get" => Some("umbra_array_get"),
            // Critical string functions
            "trim" => Some("umbra_trim"),
            "contains" => Some("umbra_contains"),
            "starts_with" => Some("umbra_starts_with"),
            "ends_with" => Some("umbra_ends_with"),
            "substring" => Some("umbra_substring"),
            "join" => Some("umbra_join"),
            "to_upper" => Some("umbra_to_upper"),
            "to_lower" => Some("umbra_to_lower"),
            "to_string" => Some("umbra_to_string"),
            _ => None,
        }
    }

    /// Get the C function signature for a built-in function
    #[allow(dead_code)]
    pub fn get_c_signature(name: &str) -> Option<&'static str> {
        match name {
            "show" => Some("void umbra_show_variadic(int argc, ...)"),
            "read" => Some("char* umbra_read_string()"),
            "write" => Some("void umbra_write_file(const char* filename, const char* content)"),
            "range" => Some("int64_t* umbra_range(int64_t start, int64_t end, size_t* length)"),
            "load_dataset" => Some("void* umbra_load_dataset(const char* path)"),
            "create_model" => Some("void* umbra_create_model(const char* model_type)"),
            "abs" => Some("int64_t umbra_abs(int64_t x)"),
            "max" => Some("int64_t umbra_max(int64_t a, int64_t b)"),
            "min" => Some("int64_t umbra_min(int64_t a, int64_t b)"),
            "len" => Some("int64_t umbra_len(void* collection)"),
            "str_len" => Some("int64_t umbra_str_len(const char* str)"),
            "random" => Some("double umbra_random()"),
            "random_int" => Some("int64_t umbra_random_int(int64_t min, int64_t max)"),
            "random_float" => Some("double umbra_random_float(double min, double max)"),
            "sqrt" => Some("double umbra_sqrt(double x)"),
            "sin" => Some("double umbra_sin(double x)"),
            "cos" => Some("double umbra_cos(double x)"),
            "tan" => Some("double umbra_tan(double x)"),
            "log" => Some("double umbra_log(double x)"),
            "exp" => Some("double umbra_exp(double x)"),
            "pow" => Some("double umbra_pow(double base, double exponent)"),
            "floor" => Some("int64_t umbra_floor(double x)"),
            "ceil" => Some("int64_t umbra_ceil(double x)"),
            "round" => Some("int64_t umbra_round(double x)"),
            _ => None,
        }
    }

    // Memory management functions
    fn create_malloc_function() -> Symbol {
        Symbol {
            name: "malloc".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::String), // Returns pointer (represented as String for now)
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![Type::Basic(BasicType::Integer)], // size
                return_type: Type::Basic(BasicType::String), // pointer
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_free_function() -> Symbol {
        Symbol {
            name: "free".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::Void),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![Type::Basic(BasicType::String)], // pointer
                return_type: Type::Basic(BasicType::Void),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    // List management functions
    fn create_list_create_function() -> Symbol {
        Symbol {
            name: "list_create".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::String), // Returns list pointer
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![
                    Type::Basic(BasicType::Integer), // element_size
                    Type::Basic(BasicType::Integer), // initial_capacity
                ],
                return_type: Type::Basic(BasicType::String), // list pointer
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_list_get_function() -> Symbol {
        Symbol {
            name: "list_get".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::String), // Returns element pointer
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![
                    Type::Basic(BasicType::String), // list pointer
                    Type::Basic(BasicType::Integer), // index
                ],
                return_type: Type::Basic(BasicType::String), // element pointer
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_list_length_function() -> Symbol {
        Symbol {
            name: "list_length".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::Integer),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![Type::Basic(BasicType::String)], // list pointer
                return_type: Type::Basic(BasicType::Integer),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_list_append_function() -> Symbol {
        Symbol {
            name: "list_append".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::Integer), // Returns success status
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![
                    Type::Basic(BasicType::String), // list pointer
                    Type::Basic(BasicType::String), // element pointer
                ],
                return_type: Type::Basic(BasicType::Integer), // success status
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    // Garbage collection functions
    fn create_gc_collect_function() -> Symbol {
        Symbol {
            name: "gc_collect".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::Void),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![],
                return_type: Type::Basic(BasicType::Void),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_gc_retain_function() -> Symbol {
        Symbol {
            name: "gc_retain".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::Void),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![Type::Basic(BasicType::String)], // object pointer
                return_type: Type::Basic(BasicType::Void),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_gc_release_function() -> Symbol {
        Symbol {
            name: "gc_release".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::Void),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![Type::Basic(BasicType::String)], // object pointer
                return_type: Type::Basic(BasicType::Void),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    // Advanced Math Functions
    fn create_sqrt_function() -> Symbol {
        Symbol {
            name: "sqrt".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::Float),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![Type::Basic(BasicType::Float)],
                return_type: Type::Basic(BasicType::Float),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_sin_function() -> Symbol {
        Symbol {
            name: "sin".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::Float),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![Type::Basic(BasicType::Float)],
                return_type: Type::Basic(BasicType::Float),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_cos_function() -> Symbol {
        Symbol {
            name: "cos".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::Float),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![Type::Basic(BasicType::Float)],
                return_type: Type::Basic(BasicType::Float),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_tan_function() -> Symbol {
        Symbol {
            name: "tan".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::Float),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![Type::Basic(BasicType::Float)],
                return_type: Type::Basic(BasicType::Float),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_log_function() -> Symbol {
        Symbol {
            name: "log".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::Float),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![Type::Basic(BasicType::Float)],
                return_type: Type::Basic(BasicType::Float),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_exp_function() -> Symbol {
        Symbol {
            name: "exp".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::Float),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![Type::Basic(BasicType::Float)],
                return_type: Type::Basic(BasicType::Float),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_pow_function() -> Symbol {
        Symbol {
            name: "pow".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::Float),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![
                    Type::Basic(BasicType::Float), // base
                    Type::Basic(BasicType::Float), // exponent
                ],
                return_type: Type::Basic(BasicType::Float),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_floor_function() -> Symbol {
        Symbol {
            name: "floor".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::Integer),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![Type::Basic(BasicType::Float)],
                return_type: Type::Basic(BasicType::Integer),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_ceil_function() -> Symbol {
        Symbol {
            name: "ceil".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::Integer),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![Type::Basic(BasicType::Float)],
                return_type: Type::Basic(BasicType::Integer),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_round_function() -> Symbol {
        Symbol {
            name: "round".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::Integer),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![Type::Basic(BasicType::Float)],
                return_type: Type::Basic(BasicType::Integer),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    // String Manipulation Functions
    fn create_substring_function() -> Symbol {
        Symbol {
            name: "substring".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::String),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![
                    Type::Basic(BasicType::String), // string
                    Type::Basic(BasicType::Integer), // start
                    Type::Basic(BasicType::Integer), // length
                ],
                return_type: Type::Basic(BasicType::String),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_split_function() -> Symbol {
        Symbol {
            name: "split".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::List(Box::new(Type::Basic(BasicType::String))),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![
                    Type::Basic(BasicType::String), // string
                    Type::Basic(BasicType::String), // delimiter
                ],
                return_type: Type::List(Box::new(Type::Basic(BasicType::String))),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_array_get_function() -> Symbol {
        Symbol {
            name: "array_get".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::String),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![
                    Type::List(Box::new(Type::Basic(BasicType::String))), // array
                    Type::Basic(BasicType::Integer), // index
                ],
                return_type: Type::Basic(BasicType::String),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_join_function() -> Symbol {
        Symbol {
            name: "join".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::String),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![
                    Type::List(Box::new(Type::Basic(BasicType::String))), // list
                    Type::Basic(BasicType::String), // separator
                ],
                return_type: Type::Basic(BasicType::String),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_to_upper_function() -> Symbol {
        Symbol {
            name: "to_upper".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::String),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![Type::Basic(BasicType::String)],
                return_type: Type::Basic(BasicType::String),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_to_lower_function() -> Symbol {
        Symbol {
            name: "to_lower".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::String),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![Type::Basic(BasicType::String)],
                return_type: Type::Basic(BasicType::String),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_to_int_function() -> Symbol {
        Symbol {
            name: "to_int".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::Integer),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![Type::Basic(BasicType::String)],
                return_type: Type::Basic(BasicType::Integer),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_to_float_function() -> Symbol {
        Symbol {
            name: "to_float".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::Float),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![Type::Basic(BasicType::String)],
                return_type: Type::Basic(BasicType::Float),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_to_string_function() -> Symbol {
        Symbol {
            name: "to_string".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::String),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![Type::Auto], // Can convert any type to string
                return_type: Type::Basic(BasicType::String),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_newline_function() -> Symbol {
        Symbol {
            name: "newline".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::Void),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![],
                return_type: Type::Basic(BasicType::Void),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_trim_function() -> Symbol {
        Symbol {
            name: "trim".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::String),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![Type::Basic(BasicType::String)],
                return_type: Type::Basic(BasicType::String),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_contains_function() -> Symbol {
        Symbol {
            name: "contains".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::Boolean),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![
                    Type::Basic(BasicType::String), // haystack
                    Type::Basic(BasicType::String), // needle
                ],
                return_type: Type::Basic(BasicType::Boolean),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_starts_with_function() -> Symbol {
        Symbol {
            name: "starts_with".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::Boolean),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![
                    Type::Basic(BasicType::String), // string
                    Type::Basic(BasicType::String), // prefix
                ],
                return_type: Type::Basic(BasicType::Boolean),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_ends_with_function() -> Symbol {
        Symbol {
            name: "ends_with".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::Boolean),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![
                    Type::Basic(BasicType::String), // string
                    Type::Basic(BasicType::String), // suffix
                ],
                return_type: Type::Basic(BasicType::Boolean),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    // File I/O Functions
    fn create_read_file_function() -> Symbol {
        Symbol {
            name: "read_file".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::String),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![Type::Basic(BasicType::String)], // path
                return_type: Type::Basic(BasicType::String),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_write_file_function() -> Symbol {
        Symbol {
            name: "write_file".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::Boolean),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![
                    Type::Basic(BasicType::String), // path
                    Type::Basic(BasicType::String), // content
                ],
                return_type: Type::Basic(BasicType::Boolean),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_file_exists_function() -> Symbol {
        Symbol {
            name: "file_exists".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::Boolean),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![Type::Basic(BasicType::String)], // path
                return_type: Type::Basic(BasicType::Boolean),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    // JSON Functions
    fn create_json_parse_function() -> Symbol {
        Symbol {
            name: "json_parse".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::String), // Simplified - would be a generic object type
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![Type::Basic(BasicType::String)], // json_string
                return_type: Type::Basic(BasicType::String),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_json_stringify_function() -> Symbol {
        Symbol {
            name: "json_stringify".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::String),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![Type::Basic(BasicType::String)], // object - simplified
                return_type: Type::Basic(BasicType::String),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_json_stringify_string_function() -> Symbol {
        Symbol {
            name: "json_stringify_string".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::String),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![Type::Basic(BasicType::String)], // string to stringify
                return_type: Type::Basic(BasicType::String),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_json_parse_string_function() -> Symbol {
        Symbol {
            name: "json_parse_string".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::String),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![Type::Basic(BasicType::String)], // json string to parse
                return_type: Type::Basic(BasicType::String),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    // Random Functions
    fn create_random_function() -> Symbol {
        Symbol {
            name: "random".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::Float),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![], // returns 0.0 to 1.0
                return_type: Type::Basic(BasicType::Float),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_random_int_function() -> Symbol {
        Symbol {
            name: "random_int".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::Integer),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![
                    Type::Basic(BasicType::Integer), // min
                    Type::Basic(BasicType::Integer), // max
                ],
                return_type: Type::Basic(BasicType::Integer),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_random_float_function() -> Symbol {
        Symbol {
            name: "random_float".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::Float),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![
                    Type::Basic(BasicType::Float), // min
                    Type::Basic(BasicType::Float), // max
                ],
                return_type: Type::Basic(BasicType::Float),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    // Statistical Functions
    fn create_mean_function() -> Symbol {
        Symbol {
            name: "mean".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::Float),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![Type::List(Box::new(Type::Basic(BasicType::Float)))], // numbers
                return_type: Type::Basic(BasicType::Float),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_std_function() -> Symbol {
        Symbol {
            name: "std".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::Float),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![Type::List(Box::new(Type::Basic(BasicType::Float)))], // numbers
                return_type: Type::Basic(BasicType::Float),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_variance_function() -> Symbol {
        Symbol {
            name: "variance".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::Float),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![Type::List(Box::new(Type::Basic(BasicType::Float)))], // numbers
                return_type: Type::Basic(BasicType::Float),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_vec_new_function() -> Symbol {
        // Vec::new() creates a new vector
        Symbol {
            name: "Vec::new".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Generic("Vec".to_string(), vec![Type::Auto]),
            is_initialized: true,
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std::collections".to_string()),
            function_signature: Some(FunctionSignature {
                parameters: vec![],
                return_type: Type::Generic("Vec".to_string(), vec![Type::Auto]),
            }),
            ..Default::default()
        }
    }

    fn create_vec_push_function() -> Symbol {
        // vec.push(item) adds an item to the vector
        Symbol {
            name: "push".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::Void),
            is_initialized: true,
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std::collections".to_string()),
            function_signature: Some(FunctionSignature {
                parameters: vec![Type::Auto], // item to push
                return_type: Type::Basic(BasicType::Void),
            }),
            ..Default::default()
        }
    }

    fn create_hashmap_new_function() -> Symbol {
        // HashMap::new() creates a new hash map
        Symbol {
            name: "HashMap::new".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Generic("HashMap".to_string(), vec![Type::Auto, Type::Auto]),
            is_initialized: true,
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std::collections".to_string()),
            function_signature: Some(FunctionSignature {
                parameters: vec![],
                return_type: Type::Generic("HashMap".to_string(), vec![Type::Auto, Type::Auto]),
            }),
            ..Default::default()
        }
    }

    // Additional collection functions
    fn create_list_insert_function() -> Symbol {
        Symbol {
            name: "list_insert".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::Void),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![
                    Type::List(Box::new(Type::Auto)),
                    Type::Basic(BasicType::Integer),
                    Type::Auto,
                ],
                return_type: Type::Basic(BasicType::Void),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_list_remove_function() -> Symbol {
        Symbol {
            name: "list_remove".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Auto,
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![
                    Type::List(Box::new(Type::Auto)),
                    Type::Basic(BasicType::Integer),
                ],
                return_type: Type::Auto,
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_list_clear_function() -> Symbol {
        Symbol {
            name: "list_clear".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::Void),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![Type::List(Box::new(Type::Auto))],
                return_type: Type::Basic(BasicType::Void),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_list_contains_function() -> Symbol {
        Symbol {
            name: "list_contains".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::Boolean),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![
                    Type::List(Box::new(Type::Auto)),
                    Type::Auto,
                ],
                return_type: Type::Basic(BasicType::Boolean),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_list_index_of_function() -> Symbol {
        Symbol {
            name: "list_index_of".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::Integer),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![
                    Type::List(Box::new(Type::Auto)),
                    Type::Auto,
                ],
                return_type: Type::Basic(BasicType::Integer),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_list_reverse_function() -> Symbol {
        Symbol {
            name: "list_reverse".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::Void),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![Type::List(Box::new(Type::Auto))],
                return_type: Type::Basic(BasicType::Void),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_list_sort_function() -> Symbol {
        Symbol {
            name: "list_sort".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::Void),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![Type::List(Box::new(Type::Auto))],
                return_type: Type::Basic(BasicType::Void),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    // Additional string functions
    fn create_string_replace_function() -> Symbol {
        Symbol {
            name: "string_replace".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::String),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![
                    Type::Basic(BasicType::String),
                    Type::Basic(BasicType::String),
                    Type::Basic(BasicType::String),
                ],
                return_type: Type::Basic(BasicType::String),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_string_repeat_function() -> Symbol {
        Symbol {
            name: "string_repeat".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::String),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![
                    Type::Basic(BasicType::String),
                    Type::Basic(BasicType::Integer),
                ],
                return_type: Type::Basic(BasicType::String),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_string_pad_left_function() -> Symbol {
        Symbol {
            name: "string_pad_left".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::String),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![
                    Type::Basic(BasicType::String),
                    Type::Basic(BasicType::Integer),
                    Type::Basic(BasicType::String),
                ],
                return_type: Type::Basic(BasicType::String),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_string_pad_right_function() -> Symbol {
        Symbol {
            name: "string_pad_right".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::String),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![
                    Type::Basic(BasicType::String),
                    Type::Basic(BasicType::Integer),
                    Type::Basic(BasicType::String),
                ],
                return_type: Type::Basic(BasicType::String),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_string_reverse_function() -> Symbol {
        Symbol {
            name: "string_reverse".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::String),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![Type::Basic(BasicType::String)],
                return_type: Type::Basic(BasicType::String),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    // Additional math functions
    fn create_clamp_function() -> Symbol {
        Symbol {
            name: "clamp".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::Float),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![
                    Type::Basic(BasicType::Float),
                    Type::Basic(BasicType::Float),
                    Type::Basic(BasicType::Float),
                ],
                return_type: Type::Basic(BasicType::Float),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_sign_function() -> Symbol {
        Symbol {
            name: "sign".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::Integer),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![Type::Basic(BasicType::Float)],
                return_type: Type::Basic(BasicType::Integer),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_degrees_function() -> Symbol {
        Symbol {
            name: "degrees".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::Float),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![Type::Basic(BasicType::Float)],
                return_type: Type::Basic(BasicType::Float),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_radians_function() -> Symbol {
        Symbol {
            name: "radians".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Basic(BasicType::Float),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![Type::Basic(BasicType::Float)],
                return_type: Type::Basic(BasicType::Float),
            }),
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    fn create_hashset_new_function() -> Symbol {
        // HashSet::new() creates a new hash set
        Symbol {
            name: "HashSet::new".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Generic("HashSet".to_string(), vec![Type::Auto]),
            is_initialized: true,
            visibility: crate::semantic::symbol_table::Visibility::Public,
            module_path: Some("std::collections".to_string()),
            function_signature: Some(FunctionSignature {
                parameters: vec![],
                return_type: Type::Generic("HashSet".to_string(), vec![Type::Auto]),
            }),
            ..Default::default()
        }
    }


}
