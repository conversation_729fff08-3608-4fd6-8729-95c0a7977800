/// Advanced mathematical utilities for Umbra standard library
/// 
/// This module provides comprehensive mathematical functions including
/// statistics, linear algebra, numerical methods, and specialized calculations.

use crate::error::{UmbraError, UmbraResult};
use std::f64::consts::{E, PI};

/// Mathematical constants
pub struct MathConstants;

impl MathConstants {
    pub const PI: f64 = PI;
    pub const E: f64 = E;
    pub const TAU: f64 = 2.0 * PI;
    pub const PHI: f64 = 1.618033988749895; // Golden ratio
    pub const SQRT_2: f64 = 1.4142135623730951;
    pub const SQRT_3: f64 = 1.7320508075688772;
    pub const LN_2: f64 = 0.6931471805599453;
    pub const LN_10: f64 = 2.302585092994046;
    pub const LOG2_E: f64 = 1.4426950408889634;
    pub const LOG10_E: f64 = 0.4342944819032518;
}

/// Advanced mathematical functions
pub struct MathUtils;

impl MathUtils {
    /// Calculate factorial of a number
    pub fn factorial(n: u64) -> UmbraResult<u64> {
        if n > 20 {
            return Err(UmbraError::Runtime("Factorial overflow for n > 20".to_string()));
        }
        
        Ok((1..=n).product())
    }
    
    /// Calculate greatest common divisor
    pub fn gcd(a: i64, b: i64) -> i64 {
        let mut a = a.abs();
        let mut b = b.abs();
        
        while b != 0 {
            let temp = b;
            b = a % b;
            a = temp;
        }
        
        a
    }
    
    /// Calculate least common multiple
    pub fn lcm(a: i64, b: i64) -> i64 {
        if a == 0 || b == 0 {
            return 0;
        }
        
        (a.abs() * b.abs()) / Self::gcd(a, b)
    }
    
    /// Check if a number is prime
    pub fn is_prime(n: u64) -> bool {
        if n < 2 {
            return false;
        }
        if n == 2 {
            return true;
        }
        if n % 2 == 0 {
            return false;
        }
        
        let sqrt_n = (n as f64).sqrt() as u64;
        for i in (3..=sqrt_n).step_by(2) {
            if n % i == 0 {
                return false;
            }
        }
        
        true
    }
    
    /// Generate prime numbers up to n using Sieve of Eratosthenes
    pub fn sieve_of_eratosthenes(n: usize) -> Vec<usize> {
        if n < 2 {
            return Vec::new();
        }
        
        let mut is_prime = vec![true; n + 1];
        is_prime[0] = false;
        is_prime[1] = false;
        
        for i in 2..=((n as f64).sqrt() as usize) {
            if is_prime[i] {
                for j in ((i * i)..=n).step_by(i) {
                    is_prime[j] = false;
                }
            }
        }
        
        (2..=n).filter(|&i| is_prime[i]).collect()
    }
    
    /// Calculate nth Fibonacci number
    pub fn fibonacci(n: u32) -> u64 {
        if n <= 1 {
            return n as u64;
        }
        
        let mut a = 0u64;
        let mut b = 1u64;
        
        for _ in 2..=n {
            let temp = a + b;
            a = b;
            b = temp;
        }
        
        b
    }
    
    /// Generate Fibonacci sequence up to n terms
    pub fn fibonacci_sequence(n: u32) -> Vec<u64> {
        let mut sequence = Vec::new();
        
        for i in 0..n {
            sequence.push(Self::fibonacci(i));
        }
        
        sequence
    }
    
    /// Calculate binomial coefficient (n choose k)
    pub fn binomial_coefficient(n: u32, k: u32) -> UmbraResult<u64> {
        if k > n {
            return Ok(0);
        }
        
        let k = std::cmp::min(k, n - k); // Take advantage of symmetry
        let mut result = 1u64;
        
        for i in 0..k {
            result = result.checked_mul(n as u64 - i as u64)
                .and_then(|r| r.checked_div(i as u64 + 1))
                .ok_or_else(|| UmbraError::Runtime("Binomial coefficient overflow".to_string()))?;
        }
        
        Ok(result)
    }
    
    /// Calculate power using fast exponentiation
    pub fn fast_power(base: f64, exp: i32) -> f64 {
        if exp == 0 {
            return 1.0;
        }
        
        let mut result = 1.0;
        let mut base = if exp < 0 { 1.0 / base } else { base };
        let mut exp = exp.abs() as u32;
        
        while exp > 0 {
            if exp % 2 == 1 {
                result *= base;
            }
            base *= base;
            exp /= 2;
        }
        
        result
    }
    
    /// Calculate square root using Newton's method
    pub fn sqrt_newton(x: f64, precision: f64) -> UmbraResult<f64> {
        if x < 0.0 {
            return Err(UmbraError::Runtime("Cannot calculate square root of negative number".to_string()));
        }
        
        if x == 0.0 {
            return Ok(0.0);
        }
        
        let mut guess = x / 2.0;
        
        loop {
            let new_guess = 0.5 * (guess + x / guess);
            if (new_guess - guess).abs() < precision {
                return Ok(new_guess);
            }
            guess = new_guess;
        }
    }
    
    /// Calculate natural logarithm using Taylor series
    pub fn ln_taylor(x: f64, terms: u32) -> UmbraResult<f64> {
        if x <= 0.0 {
            return Err(UmbraError::Runtime("Logarithm undefined for non-positive numbers".to_string()));
        }
        
        // Use the identity ln(x) = 2 * artanh((x-1)/(x+1)) for better convergence
        let y = (x - 1.0) / (x + 1.0);
        let y_squared = y * y;
        
        let mut result = 0.0;
        let mut term = y;
        
        for i in 0..terms {
            result += term / (2 * i + 1) as f64;
            term *= y_squared;
        }
        
        Ok(2.0 * result)
    }
    
    /// Calculate sine using Taylor series
    pub fn sin_taylor(x: f64, terms: u32) -> f64 {
        let mut result = 0.0;
        let mut term = x;
        let x_squared = x * x;
        
        for i in 0..terms {
            if i % 2 == 0 {
                result += term;
            } else {
                result -= term;
            }
            
            term *= x_squared / ((2 * i + 2) * (2 * i + 3)) as f64;
        }
        
        result
    }
    
    /// Calculate cosine using Taylor series
    pub fn cos_taylor(x: f64, terms: u32) -> f64 {
        let mut result = 1.0;
        let mut term = 1.0;
        let x_squared = x * x;
        
        for i in 1..terms {
            term *= -x_squared / ((2 * i - 1) * (2 * i)) as f64;
            result += term;
        }
        
        result
    }
    
    /// Calculate exponential using Taylor series
    pub fn exp_taylor(x: f64, terms: u32) -> f64 {
        let mut result = 1.0;
        let mut term = 1.0;
        
        for i in 1..terms {
            term *= x / i as f64;
            result += term;
        }
        
        result
    }
    
    /// Convert degrees to radians
    pub fn degrees_to_radians(degrees: f64) -> f64 {
        degrees * PI / 180.0
    }
    
    /// Convert radians to degrees
    pub fn radians_to_degrees(radians: f64) -> f64 {
        radians * 180.0 / PI
    }
    
    /// Clamp a value between min and max
    pub fn clamp(value: f64, min: f64, max: f64) -> f64 {
        if value < min {
            min
        } else if value > max {
            max
        } else {
            value
        }
    }
    
    /// Linear interpolation between two values
    pub fn lerp(a: f64, b: f64, t: f64) -> f64 {
        a + t * (b - a)
    }
    
    /// Map a value from one range to another
    pub fn map_range(value: f64, from_min: f64, from_max: f64, to_min: f64, to_max: f64) -> f64 {
        let normalized = (value - from_min) / (from_max - from_min);
        to_min + normalized * (to_max - to_min)
    }
    
    /// Check if two floating point numbers are approximately equal
    pub fn approx_equal(a: f64, b: f64, epsilon: f64) -> bool {
        (a - b).abs() < epsilon
    }
    
    /// Calculate the sign of a number (-1, 0, or 1)
    pub fn sign(x: f64) -> i32 {
        if x > 0.0 {
            1
        } else if x < 0.0 {
            -1
        } else {
            0
        }
    }
    
    /// Calculate the distance between two 2D points
    pub fn distance_2d(x1: f64, y1: f64, x2: f64, y2: f64) -> f64 {
        ((x2 - x1).powi(2) + (y2 - y1).powi(2)).sqrt()
    }
    
    /// Calculate the distance between two 3D points
    pub fn distance_3d(x1: f64, y1: f64, z1: f64, x2: f64, y2: f64, z2: f64) -> f64 {
        ((x2 - x1).powi(2) + (y2 - y1).powi(2) + (z2 - z1).powi(2)).sqrt()
    }
    
    /// Calculate the angle between two 2D vectors
    pub fn angle_between_vectors_2d(x1: f64, y1: f64, x2: f64, y2: f64) -> f64 {
        let dot_product = x1 * x2 + y1 * y2;
        let magnitude1 = (x1 * x1 + y1 * y1).sqrt();
        let magnitude2 = (x2 * x2 + y2 * y2).sqrt();
        
        if magnitude1 == 0.0 || magnitude2 == 0.0 {
            return 0.0;
        }
        
        (dot_product / (magnitude1 * magnitude2)).acos()
    }
    
    /// Normalize a 2D vector
    pub fn normalize_2d(x: f64, y: f64) -> (f64, f64) {
        let magnitude = (x * x + y * y).sqrt();
        if magnitude == 0.0 {
            (0.0, 0.0)
        } else {
            (x / magnitude, y / magnitude)
        }
    }
    
    /// Calculate dot product of two 2D vectors
    pub fn dot_product_2d(x1: f64, y1: f64, x2: f64, y2: f64) -> f64 {
        x1 * x2 + y1 * y2
    }
    
    /// Calculate cross product of two 2D vectors (returns scalar)
    pub fn cross_product_2d(x1: f64, y1: f64, x2: f64, y2: f64) -> f64 {
        x1 * y2 - y1 * x2
    }
}
