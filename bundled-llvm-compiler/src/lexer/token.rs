use logos::<PERSON><PERSON>;
use serde::{Deserialize, Serialize};
use std::fmt;

use crate::error::SourceLocation;

/// Token types for the Umbra programming language
#[derive(Logos, Debug, Clone, PartialEq, Serialize, Deserialize)]
#[logos(skip r"[ \t\f]+")]
pub enum TokenType {
    // Literals
    #[regex(r"\d+", |lex| lex.slice().parse::<i64>().ok())]
    Integer(i64),

    #[regex(r"\d+\.\d+", |lex| lex.slice().parse::<f64>().ok())]
    Float(f64),

    #[regex(r#""([^"\\]|\\.)*""#, |lex| {
        let s = lex.slice();
        // Remove quotes and handle escape sequences
        let content = &s[1..s.len()-1];
        Some(content.replace("\\\"", "\"").replace("\\\\", "\\"))
    })]
    String(String),

    #[token("true")]
    True,

    #[token("false")]
    False,

    // Keywords
    #[token("define")]
    Define,

    #[token("fn")]
    Fn,

    #[token("let")]
    Let,

    #[token("mut")]
    Mut,

    #[token("bring")]
    Bring,

    #[token("module")]
    Module,

    #[token("import")]
    Import,

    #[token("export")]
    Export,

    #[token("from")]
    From,

    #[token("as")]
    As,

    #[token("public")]
    Public,

    #[token("private")]
    Private,

    #[token("structure")]
    Structure,

    #[token("when")]
    When,

    #[token("otherwise")]
    Otherwise,

    #[token("repeat")]
    Repeat,

    #[token("in")]
    In,

    #[token("return")]
    Return,

    #[token("break")]
    Break,

    #[token("continue")]
    Continue,

    #[token("for")]
    For,

    #[token("while")]
    While,

    #[token("loop")]
    Loop,

    #[token("if")]
    If,

    #[token("else")]
    Else,

    #[token("impl")]
    Impl,

    #[token("trait")]
    Trait,

    #[token("struct")]
    Struct,

    #[token("enum")]
    Enum,

    #[token("type")]
    Type,

    #[token("const")]
    Const,

    #[token("static")]
    Static,

    #[token("self")]
    SelfKeyword,

    #[token("Self")]
    SelfType,

    #[token("super")]
    Super,

    #[token("crate")]
    Crate,

    #[token("mod")]
    Mod,

    #[token("use")]
    Use,

    #[token("pub")]
    Pub,

    #[token("ref")]
    Ref,

    #[token("move")]
    Move,

    #[token("async")]
    Async,

    #[token("await")]
    Await,

    #[token("unsafe")]
    Unsafe,

    #[token("extern")]
    Extern,

    #[token("dyn")]
    Dyn,

    #[token("where")]
    Where,

    #[token("macro")]
    Macro,

    // AI/ML Keywords
    #[token("train")]
    Train,

    #[token("using")]
    Using,

    #[token("evaluate")]
    Evaluate,

    #[token("on")]
    On,

    #[token("visualize")]
    Visualize,

    #[token("over")]
    Over,

    #[token("to")]
    To,

    #[token("predict")]
    Predict,

    // Database Keywords
    #[token("query")]
    Query,

    #[token("database")]
    Database,

    #[token("table")]
    Table,

    #[token("column")]
    Column,

    #[token("index")]
    Index,

    #[token("transaction")]
    Transaction,

    #[token("commit")]
    Commit,

    #[token("rollback")]
    Rollback,

    #[token("migrate")]
    Migrate,

    #[token("schema")]
    Schema,

    #[token("connection")]
    Connection,

    #[token("pool")]
    Pool,

    // Logical operators
    #[token("and")]
    And,

    #[token("or")]
    Or,

    #[token("not")]
    Not,

    // Type keywords
    #[token("Integer")]
    IntegerType,

    #[token("Float")]
    FloatType,

    #[token("String")]
    StringType,

    #[token("Boolean")]
    BooleanType,

    #[token("Void")]
    VoidType,

    #[token("List")]
    ListType,

    #[token("Dataset")]
    DatasetType,

    #[token("Model")]
    ModelType,

    #[token("Tensor")]
    TensorType,

    #[token("auto")]
    AutoType,

    #[token("Vec")]
    VecType,

    #[token("HashMap")]
    HashMapType,

    #[token("HashSet")]
    HashSetType,

    #[token("Option")]
    OptionType,

    #[token("Box")]
    BoxType,

    #[token("Rc")]
    RcType,

    #[token("Arc")]
    ArcType,

    #[token("Weak")]
    WeakType,

    #[token("RefCell")]
    RefCellType,

    #[token("Mutex")]
    MutexType,

    #[token("RwLock")]
    RwLockType,

    #[token("Cell")]
    CellType,

    #[token("i8")]
    I8Type,

    #[token("i16")]
    I16Type,

    #[token("i32")]
    I32Type,

    #[token("i64")]
    I64Type,

    #[token("u8")]
    U8Type,

    #[token("u16")]
    U16Type,

    #[token("u32")]
    U32Type,

    #[token("u64")]
    U64Type,

    #[token("f32")]
    F32Type,

    #[token("f64")]
    F64Type,

    #[token("usize")]
    UsizeType,

    #[token("isize")]
    IsizeType,

    #[token("char")]
    CharType,

    #[token("str")]
    StrType,

    #[token("bool")]
    BoolType,

    #[token("void")]
    VoidLowerType,

    // Advanced Type System Keywords
    #[token("union")]
    Union,

    #[token("Function")]
    FunctionType,

    #[token("match")]
    Match,

    #[token("case")]
    Case,

    #[token("Some")]
    Some,

    #[token("None")]
    None,

    #[token("Ok")]
    Ok,

    #[token("Err")]
    Err,

    #[token("Optional")]
    Optional,

    #[token("Result")]
    Result,

    // Error handling keywords
    #[token("try")]
    Try,

    #[token("catch")]
    Catch,

    #[token("finally")]
    Finally,

    #[token("throw")]
    Throw,

    #[token("panic")]
    Panic,

    #[token("error")]
    Error,

    // Operators
    #[token(":=")]
    Assign,

    #[token("+=")]
    PlusAssign,

    #[token("-=")]
    MinusAssign,

    #[token("*=")]
    MultiplyAssign,

    #[token("/=")]
    DivideAssign,

    #[token("->")]
    Arrow,

    #[token("==")]
    Equal,

    #[token("!=")]
    NotEqual,

    #[token("<=")]
    LessEqual,

    #[token(">=")]
    GreaterEqual,

    #[token("<")]
    Less,

    #[token(">")]
    Greater,

    #[token("+")]
    Plus,

    #[token("-")]
    Minus,

    #[token("*")]
    Multiply,

    #[token("/")]
    Divide,

    #[token("%")]
    Modulo,

    #[token("?")]
    Question,

    #[token("&")]
    Ampersand,

    #[token("&&")]
    DoubleAmpersand,

    #[token("|")]
    Pipe,

    #[token("||")]
    DoublePipe,

    #[token("^")]
    Caret,

    #[token("!")]
    Exclamation,

    #[token("~")]
    Tilde,

    #[token("<<")]
    LeftShift,

    #[token(">>")]
    RightShift,

    #[token("=")]
    Equal_,

    #[token("..")]
    DotDot,

    #[token("...")]
    DotDotDot,

    #[token("=>")]
    FatArrow,

    #[token("|>")]
    PipeRight,

    #[token("<|")]
    PipeLeft,

    // Punctuation
    #[token("(")]
    LeftParen,

    #[token(")")]
    RightParen,

    #[token("[")]
    LeftBracket,

    #[token("]")]
    RightBracket,

    #[token("{")]
    LeftBrace,

    #[token("}")]
    RightBrace,

    #[token(",")]
    Comma,

    #[token(":")]
    Colon,

    #[token("::")]
    DoubleColon,

    #[token(".")]
    Dot,

    #[token(";")]
    Semicolon,

    // Identifiers (must come after keywords)
    #[regex(r"[a-zA-Z_][a-zA-Z0-9_]*")]
    Identifier,

    // Comments
    #[regex(r"//[^\n]*")]
    Comment,

    // Newlines (significant for indentation)
    #[token("\n")]
    Newline,

    // Indentation tokens (handled specially)
    Indent,
    Dedent,

    // End of file
    Eof,
}

impl fmt::Display for TokenType {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            TokenType::Integer(n) => write!(f, "{n}"),
            TokenType::Float(n) => write!(f, "{n}"),
            TokenType::String(s) => write!(f, "\"{s}\""),
            TokenType::True => write!(f, "true"),
            TokenType::False => write!(f, "false"),
            TokenType::Identifier => write!(f, "identifier"),
            TokenType::Comment => write!(f, "comment"),
            TokenType::Newline => write!(f, "newline"),
            TokenType::Indent => write!(f, "indent"),
            TokenType::Dedent => write!(f, "dedent"),
            TokenType::Eof => write!(f, "end of file"),
            TokenType::Error => write!(f, "error"),
            _ => write!(f, "{self:?}"),
        }
    }
}

/// A token with its location in the source code
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct Token {
    pub token_type: TokenType,
    pub lexeme: String,
    pub location: SourceLocation,
}

impl Token {
    pub fn new(token_type: TokenType, lexeme: String, location: SourceLocation) -> Self {
        Self {
            token_type,
            lexeme,
            location,
        }
    }

    #[allow(dead_code)]
    pub fn eof(location: SourceLocation) -> Self {
        Self::new(TokenType::Eof, String::new(), location)
    }

    #[allow(dead_code)]
    pub fn error(lexeme: String, location: SourceLocation) -> Self {
        Self::new(TokenType::Error, lexeme, location)
    }
}

impl fmt::Display for Token {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(
            f,
            "{} '{}' at {}",
            self.token_type, self.lexeme, self.location
        )
    }
}
