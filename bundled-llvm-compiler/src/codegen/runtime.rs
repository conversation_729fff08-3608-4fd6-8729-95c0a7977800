use std::fs;
use std::path::Path;

/// Runtime library for Umbra programs
pub struct RuntimeLibrary;

impl RuntimeLibrary {
    /// Generate the C runtime library source code
    pub fn generate_c_runtime() -> String {
        r#"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <math.h>
#include <time.h>
#include <ctype.h>
#include <unistd.h>
#define _GNU_SOURCE

// Umbra Runtime Library

// Simple strdup implementation for portability
char* umbra_strdup(const char* s) {
    if (!s) return NULL;
    size_t len = strlen(s) + 1;
    char* copy = malloc(len);
    if (copy) {
        memcpy(copy, s, len);
    }
    return copy;
}

// Forward declarations
typedef struct UmbraObject UmbraObject;
typedef struct UmbraList UmbraList;
void umbra_gc_collect();

// List/Array management structure
struct UmbraList {
    void* data;
    size_t length;
    size_t capacity;
    size_t element_size;
};

// GC Object structure
struct UmbraObject {
    size_t ref_count;
    size_t size;
    void (*destructor)(struct UmbraObject*);
    struct UmbraObject* next; // For GC linked list
};

// I/O Functions
void umbra_show_int(int64_t value) {
    printf("%ld", value);
}

void umbra_show_float(double value) {
    printf("%f", value);
}

void umbra_show_string(const char* value) {
    printf("%s", value);
}

void umbra_show_bool(int value) {
    printf("%s", value ? "true" : "false");
}

void umbra_show_newline() {
    printf("\n");
}

char* umbra_read_string() {
    char* buffer = malloc(1024);
    if (fgets(buffer, 1024, stdin)) {
        // Remove newline if present
        size_t len = strlen(buffer);
        if (len > 0 && buffer[len-1] == '\n') {
            buffer[len-1] = '\0';
        }
        return buffer;
    }
    free(buffer);
    return NULL;
}

// File I/O Functions
int umbra_write_file(const char* filename, const char* content) {
    FILE* file = fopen(filename, "w");
    if (file) {
        fprintf(file, "%s", content);
        fclose(file);
        return 1; // Success
    }
    return 0; // Failure
}

int umbra_file_exists(const char* filename) {
    FILE* file = fopen(filename, "r");
    if (file) {
        fclose(file);
        return 1; // File exists
    }
    return 0; // File doesn't exist
}

char* umbra_read_file(const char* filename) {
    FILE* file = fopen(filename, "r");
    if (!file) {
        return NULL;
    }

    // Get file size
    fseek(file, 0, SEEK_END);
    long file_size = ftell(file);
    fseek(file, 0, SEEK_SET);

    // Allocate buffer
    char* buffer = malloc(file_size + 1);
    if (!buffer) {
        fclose(file);
        return NULL;
    }

    // Read file content
    size_t bytes_read = fread(buffer, 1, file_size, file);
    buffer[bytes_read] = '\0';

    fclose(file);
    return buffer;
}

// String split function
typedef struct {
    char** items;
    size_t count;
} UmbraStringArray;

UmbraStringArray* umbra_split(const char* str, const char* delimiter) {
    if (!str || !delimiter) {
        return NULL;
    }

    // Count occurrences of delimiter to estimate array size
    size_t delimiter_len = strlen(delimiter);
    size_t count = 1;
    const char* temp = str;
    while ((temp = strstr(temp, delimiter)) != NULL) {
        count++;
        temp += delimiter_len;
    }

    // Allocate array structure
    UmbraStringArray* result = malloc(sizeof(UmbraStringArray));
    result->items = malloc(count * sizeof(char*));
    result->count = 0;

    // Create a copy of the string to work with
    char* str_copy = umbra_strdup(str);
    char* token = str_copy;
    char* next_delimiter;

    while ((next_delimiter = strstr(token, delimiter)) != NULL) {
        *next_delimiter = '\0';
        result->items[result->count] = umbra_strdup(token);
        result->count++;
        token = next_delimiter + delimiter_len;
    }

    // Add the last token
    if (*token) {
        result->items[result->count] = umbra_strdup(token);
        result->count++;
    }

    free(str_copy);
    return result;
}

// Function to get an item from UmbraStringArray
char* umbra_array_get(UmbraStringArray* array, int64_t index) {
    if (!array || !array->items || index < 0 || index >= (int64_t)array->count) {
        return NULL;
    }
    return array->items[index];
}

// Function to free UmbraStringArray
void umbra_free_string_array(UmbraStringArray* array) {
    if (array) {
        if (array->items) {
            for (size_t i = 0; i < array->count; i++) {
                free(array->items[i]);
            }
            free(array->items);
        }
        free(array);
    }
}

// String join function
char* umbra_join(UmbraStringArray* array, const char* separator) {
    if (!array || !array->items || array->count == 0) {
        return umbra_strdup("");
    }

    if (!separator) separator = "";

    // Calculate total length needed
    size_t total_len = 0;
    size_t sep_len = strlen(separator);

    for (size_t i = 0; i < array->count; i++) {
        if (array->items[i]) {
            total_len += strlen(array->items[i]);
        }
        if (i < array->count - 1) {
            total_len += sep_len;
        }
    }

    char* result = malloc(total_len + 1);
    if (!result) return NULL;

    result[0] = '\0';

    for (size_t i = 0; i < array->count; i++) {
        if (array->items[i]) {
            strcat(result, array->items[i]);
        }
        if (i < array->count - 1) {
            strcat(result, separator);
        }
    }

    return result;
}

// Math Functions
int64_t* umbra_range(int64_t start, int64_t end, size_t* length) {
    if (end <= start) {
        *length = 0;
        return NULL;
    }
    
    *length = end - start;
    int64_t* array = malloc(sizeof(int64_t) * (*length));
    
    for (size_t i = 0; i < *length; i++) {
        array[i] = start + i;
    }
    
    return array;
}

// Random number generation (initialize seed once)
static int random_initialized = 0;

void umbra_init_random() {
    if (!random_initialized) {
        srand((unsigned int)time(NULL));
        random_initialized = 1;
    }
}

// Random functions
double umbra_random() {
    umbra_init_random();
    return (double)rand() / RAND_MAX;
}

int64_t umbra_random_int(int64_t min, int64_t max) {
    umbra_init_random();
    if (min >= max) return min;
    return min + (rand() % (max - min));
}

double umbra_random_float(double min, double max) {
    umbra_init_random();
    if (min >= max) return min;
    double scale = (double)rand() / RAND_MAX;
    return min + scale * (max - min);
}

// Math functions
double umbra_sqrt(double x) {
    return sqrt(x);
}

double umbra_sin(double x) {
    return sin(x);
}

double umbra_cos(double x) {
    return cos(x);
}

double umbra_tan(double x) {
    return tan(x);
}

double umbra_log(double x) {
    return log(x);
}

double umbra_exp(double x) {
    return exp(x);
}

double umbra_pow(double base, double exponent) {
    return pow(base, exponent);
}

int64_t umbra_floor(double x) {
    return (int64_t)floor(x);
}

int64_t umbra_ceil(double x) {
    return (int64_t)ceil(x);
}

int64_t umbra_round(double x) {
    return (int64_t)round(x);
}

// Basic math functions
double umbra_abs(double x) {
    return x < 0 ? -x : x;
}

int64_t umbra_abs_int(int64_t x) {
    return x < 0 ? -x : x;
}

double umbra_max(double a, double b) {
    return a > b ? a : b;
}

int64_t umbra_max_int(int64_t a, int64_t b) {
    return a > b ? a : b;
}

double umbra_min(double a, double b) {
    return a < b ? a : b;
}

int64_t umbra_min_int(int64_t a, int64_t b) {
    return a < b ? a : b;
}

// Length function for collections
int64_t umbra_len(void* collection) {
    if (!collection) return 0;

    // Try to determine if it's a UmbraStringArray or UmbraList
    // For UmbraStringArray, we can check if it has the expected structure
    UmbraStringArray* str_array = (UmbraStringArray*)collection;
    if (str_array->items && str_array->count > 0) {
        return (int64_t)str_array->count;
    }

    // Fall back to UmbraList
    UmbraList* list = (UmbraList*)collection;
    return list ? (int64_t)list->length : 0;
}

// String length function
int64_t umbra_str_len(const char* str) {
    return str ? (int64_t)strlen(str) : 0;
}

// String concatenation function
char* umbra_str_concat(const char* str1, const char* str2) {
    if (!str1 && !str2) return umbra_strdup("");
    if (!str1) return umbra_strdup(str2);
    if (!str2) return umbra_strdup(str1);

    size_t len1 = strlen(str1);
    size_t len2 = strlen(str2);
    char* result = malloc(len1 + len2 + 1);

    if (!result) return NULL;

    strcpy(result, str1);
    strcat(result, str2);

    return result;
}

// String case conversion functions
char* umbra_to_upper(const char* str) {
    if (!str) return NULL;

    size_t len = strlen(str);
    char* result = malloc(len + 1);
    if (!result) return NULL;

    for (size_t i = 0; i < len; i++) {
        result[i] = (char)toupper((unsigned char)str[i]);
    }
    result[len] = '\0';

    return result;
}

char* umbra_to_lower(const char* str) {
    if (!str) return NULL;

    size_t len = strlen(str);
    char* result = malloc(len + 1);
    if (!result) return NULL;

    for (size_t i = 0; i < len; i++) {
        result[i] = (char)tolower((unsigned char)str[i]);
    }
    result[len] = '\0';

    return result;
}

// String trimming function
char* umbra_trim(const char* str) {
    if (!str) return NULL;

    // Find start of non-whitespace
    const char* start = str;
    while (*start && isspace((unsigned char)*start)) {
        start++;
    }

    // Find end of non-whitespace
    const char* end = str + strlen(str) - 1;
    while (end > start && isspace((unsigned char)*end)) {
        end--;
    }

    // Calculate length and create result
    size_t len = end - start + 1;
    char* result = malloc(len + 1);
    if (!result) return NULL;

    strncpy(result, start, len);
    result[len] = '\0';

    return result;
}

// String contains function
int umbra_contains(const char* str, const char* substr) {
    if (!str || !substr) return 0;
    return strstr(str, substr) != NULL ? 1 : 0;
}

// String starts_with function
int umbra_starts_with(const char* str, const char* prefix) {
    if (!str || !prefix) return 0;
    size_t str_len = strlen(str);
    size_t prefix_len = strlen(prefix);
    if (prefix_len > str_len) return 0;
    return strncmp(str, prefix, prefix_len) == 0 ? 1 : 0;
}

// String ends_with function
int umbra_ends_with(const char* str, const char* suffix) {
    if (!str || !suffix) return 0;
    size_t str_len = strlen(str);
    size_t suffix_len = strlen(suffix);
    if (suffix_len > str_len) return 0;
    return strcmp(str + str_len - suffix_len, suffix) == 0 ? 1 : 0;
}

// String substring function
char* umbra_substring(const char* str, int64_t start, int64_t end) {
    if (!str) return NULL;

    size_t str_len = strlen(str);

    // Handle negative indices and bounds checking
    if (start < 0) start = 0;
    if (end < 0) end = 0;
    if (start >= (int64_t)str_len) return umbra_strdup("");
    if (end > (int64_t)str_len) end = str_len;
    if (start >= end) return umbra_strdup("");

    size_t sub_len = end - start;
    char* result = malloc(sub_len + 1);
    if (!result) return NULL;

    strncpy(result, str + start, sub_len);
    result[sub_len] = '\0';

    return result;
}

// String replace function
char* umbra_string_replace(const char* str, const char* old_substr, const char* new_substr) {
    if (!str || !old_substr || !new_substr) return NULL;

    size_t old_len = strlen(old_substr);
    size_t new_len = strlen(new_substr);
    size_t str_len = strlen(str);

    if (old_len == 0) return umbra_strdup(str);

    // Count occurrences
    size_t count = 0;
    const char* pos = str;
    while ((pos = strstr(pos, old_substr)) != NULL) {
        count++;
        pos += old_len;
    }

    if (count == 0) return umbra_strdup(str);

    // Calculate new string length
    size_t new_str_len = str_len + count * (new_len - old_len);
    char* result = malloc(new_str_len + 1);
    if (!result) return NULL;

    char* dest = result;
    const char* src = str;

    while ((pos = strstr(src, old_substr)) != NULL) {
        // Copy part before match
        size_t prefix_len = pos - src;
        memcpy(dest, src, prefix_len);
        dest += prefix_len;

        // Copy replacement
        memcpy(dest, new_substr, new_len);
        dest += new_len;

        // Move past the old substring
        src = pos + old_len;
    }

    // Copy remaining part
    strcpy(dest, src);

    return result;
}

// String repeat function
char* umbra_string_repeat(const char* str, int64_t count) {
    if (!str || count <= 0) return umbra_strdup("");

    size_t str_len = strlen(str);
    size_t total_len = str_len * count;

    char* result = malloc(total_len + 1);
    if (!result) return NULL;

    char* dest = result;
    for (int64_t i = 0; i < count; i++) {
        memcpy(dest, str, str_len);
        dest += str_len;
    }

    result[total_len] = '\0';
    return result;
}

// String pad left function
char* umbra_string_pad_left(const char* str, int64_t width, const char* pad_char) {
    if (!str || !pad_char) return NULL;

    size_t str_len = strlen(str);
    if ((int64_t)str_len >= width) return umbra_strdup(str);

    size_t pad_len = width - str_len;
    char* result = malloc(width + 1);
    if (!result) return NULL;

    // Fill with padding character
    for (size_t i = 0; i < pad_len; i++) {
        result[i] = pad_char[0];
    }

    // Copy original string
    memcpy(result + pad_len, str, str_len);
    result[width] = '\0';

    return result;
}

// String pad right function
char* umbra_string_pad_right(const char* str, int64_t width, const char* pad_char) {
    if (!str || !pad_char) return NULL;

    size_t str_len = strlen(str);
    if ((int64_t)str_len >= width) return umbra_strdup(str);

    char* result = malloc(width + 1);
    if (!result) return NULL;

    // Copy original string
    memcpy(result, str, str_len);

    // Fill with padding character
    for (size_t i = str_len; i < (size_t)width; i++) {
        result[i] = pad_char[0];
    }

    result[width] = '\0';
    return result;
}

// String reverse function
char* umbra_string_reverse(const char* str) {
    if (!str) return NULL;

    size_t len = strlen(str);
    char* result = malloc(len + 1);
    if (!result) return NULL;

    for (size_t i = 0; i < len; i++) {
        result[i] = str[len - 1 - i];
    }

    result[len] = '\0';
    return result;
}



// User input function
char* umbra_read_input() {
    char* line = NULL;
    size_t len = 0;
    long read = getline(&line, &len, stdin);

    if (read == -1) {
        if (line) free(line);
        return NULL;
    }

    // Remove trailing newline
    if (read > 0 && line[read - 1] == '\n') {
        line[read - 1] = '\0';
    }

    return line;
}

// Input function without prompt
char* umbra_input() {
    return umbra_read_input();
}

// Input function with prompt
char* umbra_input_with_prompt(const char* prompt) {
    if (prompt) {
        printf("%s", prompt);
        fflush(stdout);
    }
    return umbra_read_input();
}

// Type conversion functions
long long umbra_to_int(const char* str) {
    if (!str) return 0;
    return atoll(str);
}

double umbra_to_float(const char* str) {
    if (!str) return 0.0;
    return atof(str);
}

char* umbra_to_string_int(long long value) {
    char* result = malloc(32); // Enough for any long long
    if (!result) return NULL;
    snprintf(result, 32, "%lld", value);
    return result;
}

char* umbra_to_string_float(double value) {
    char* result = malloc(64); // Enough for any float
    if (!result) return NULL;
    snprintf(result, 64, "%.6f", value);
    return result;
}

char* umbra_to_string_bool(long long value) {
    return umbra_strdup(value ? "true" : "false");
}

// Newline function
void umbra_newline() {
    printf("\n");
    fflush(stdout);
}

// JSON functions (simplified implementations)
char* umbra_json_stringify(const char* str) {
    if (!str) return NULL;

    size_t len = strlen(str);
    char* result = malloc(len + 3); // +2 for quotes, +1 for null terminator
    if (!result) return NULL;

    result[0] = '"';
    memcpy(result + 1, str, len);
    result[len + 1] = '"';
    result[len + 2] = '\0';

    return result;
}

char* umbra_json_parse(const char* json_str) {
    if (!json_str) return NULL;

    size_t len = strlen(json_str);
    if (len < 2 || json_str[0] != '"' || json_str[len - 1] != '"') {
        return umbra_strdup(json_str); // Return as-is if not a JSON string
    }

    // Remove quotes
    char* result = malloc(len - 1);
    if (!result) return NULL;

    memcpy(result, json_str + 1, len - 2);
    result[len - 2] = '\0';

    return result;
}

// Simple JSON parsing (basic implementation)
char* umbra_json_stringify_string(const char* str) {
    if (!str) return umbra_strdup("null");

    size_t len = strlen(str);
    size_t escaped_len = len * 2 + 3; // Worst case: all chars need escaping + quotes
    char* result = malloc(escaped_len);
    if (!result) return NULL;

    char* dest = result;
    *dest++ = '"';

    for (size_t i = 0; i < len; i++) {
        char c = str[i];
        switch (c) {
            case '"':
                *dest++ = '\\';
                *dest++ = '"';
                break;
            case '\\':
                *dest++ = '\\';
                *dest++ = '\\';
                break;
            case '\n':
                *dest++ = '\\';
                *dest++ = 'n';
                break;
            case '\r':
                *dest++ = '\\';
                *dest++ = 'r';
                break;
            case '\t':
                *dest++ = '\\';
                *dest++ = 't';
                break;
            default:
                *dest++ = c;
                break;
        }
    }

    *dest++ = '"';
    *dest = '\0';

    return result;
}

// Basic JSON parsing for strings (simplified)
char* umbra_json_parse_string(const char* json) {
    if (!json) return NULL;

    // Skip whitespace
    while (*json && isspace(*json)) json++;

    if (*json != '"') return NULL;
    json++; // Skip opening quote

    size_t len = 0;
    const char* start = json;

    // Find end quote and calculate length
    while (*json && *json != '"') {
        if (*json == '\\' && *(json + 1)) {
            json += 2; // Skip escaped character
            len++;
        } else {
            json++;
            len++;
        }
    }

    if (*json != '"') return NULL;

    char* result = malloc(len + 1);
    if (!result) return NULL;

    char* dest = result;
    json = start;

    while (*json && *json != '"') {
        if (*json == '\\' && *(json + 1)) {
            json++; // Skip backslash
            switch (*json) {
                case 'n': *dest++ = '\n'; break;
                case 'r': *dest++ = '\r'; break;
                case 't': *dest++ = '\t'; break;
                case '"': *dest++ = '"'; break;
                case '\\': *dest++ = '\\'; break;
                default: *dest++ = *json; break;
            }
            json++;
        } else {
            *dest++ = *json++;
        }
    }

    *dest = '\0';
    return result;
}

// Statistical functions for List[Float]
double umbra_mean(UmbraList* list) {
    if (!list || list->length == 0) return 0.0;

    double sum = 0.0;
    double* data = (double*)list->data;

    for (size_t i = 0; i < list->length; i++) {
        sum += data[i];
    }

    return sum / (double)list->length;
}

double umbra_variance(UmbraList* list) {
    if (!list || list->length == 0) return 0.0;
    if (list->length == 1) return 0.0;

    double mean_val = umbra_mean(list);
    double sum_sq_diff = 0.0;
    double* data = (double*)list->data;

    for (size_t i = 0; i < list->length; i++) {
        double diff = data[i] - mean_val;
        sum_sq_diff += diff * diff;
    }

    return sum_sq_diff / (double)(list->length - 1); // Sample variance
}

double umbra_std(UmbraList* list) {
    return sqrt(umbra_variance(list));
}

// AI/ML Stub Functions
typedef struct {
    char* path;
    void* data; // Placeholder
} UmbraDataset;

typedef struct {
    char* model_type;
    void* weights; // Placeholder
    int trained;
} UmbraModel;

UmbraDataset* umbra_load_dataset(const char* path) {
    UmbraDataset* dataset = malloc(sizeof(UmbraDataset));
    dataset->path = umbra_strdup(path);
    dataset->data = NULL;
    printf("📊 Loading dataset from %s\n", path);
    return dataset;
}

UmbraModel* umbra_create_model(const char* model_type) {
    UmbraModel* model = malloc(sizeof(UmbraModel));
    model->model_type = umbra_strdup(model_type);
    model->weights = NULL;
    model->trained = 0;
    printf("🤖 Creating %s model\n", model_type);
    return model;
}

void umbra_train_model(UmbraModel* model, UmbraDataset* dataset) {
    printf("🚀 Training %s model on %s\n", model->model_type, dataset->path);
    model->trained = 1;
    printf("✅ Training completed!\n");
}

void umbra_evaluate_model(UmbraModel* model, UmbraDataset* dataset) {
    if (!model->trained) {
        printf("⚠️  Warning: Model %s is not trained yet\n", model->model_type);
    }
    printf("📈 Evaluating %s model on %s\n", model->model_type, dataset->path);
    printf("  Accuracy: 0.95\n");
    printf("  Precision: 0.93\n");
    printf("  Recall: 0.97\n");
    printf("✅ Evaluation completed!\n");
}

void umbra_visualize_metric(const char* metric, const char* dimension) {
    printf("📊 Visualizing %s over %s\n", metric, dimension);
    printf("📈 Visualization ready!\n");
}

void umbra_export_model(UmbraModel* model, const char* path) {
    if (!model->trained) {
        printf("⚠️  Warning: Exporting untrained model %s\n", model->model_type);
    }
    printf("💾 Exporting %s model to %s\n", model->model_type, path);
    printf("✅ Model saved to %s\n", path);
}

char* umbra_predict_sample(const char* sample, UmbraModel* model) {
    if (!model->trained) {
        printf("⚠️  Warning: Using untrained model %s for prediction\n", model->model_type);
    }
    printf("🔮 Predicting sample using %s model\n", model->model_type);
    printf("  Input: %s\n", sample);
    
    // Create a simple prediction result
    char* prediction = malloc(256);
    snprintf(prediction, 256, "prediction_for_%s", sample);
    printf("  Prediction: %s\n", prediction);
    
    return prediction;
}

// Memory management
void* umbra_malloc(size_t size) {
    return malloc(size);
}

void* umbra_calloc(size_t count, size_t size) {
    return calloc(count, size);
}

void* umbra_realloc(void* ptr, size_t size) {
    return realloc(ptr, size);
}

void umbra_free(void* ptr) {
    if (ptr) {
        free(ptr);
    }
}

// Garbage Collection Infrastructure
// (UmbraObject already defined above)

// Global GC state
static UmbraObject* gc_objects = NULL;
static size_t gc_total_allocated = 0;
static size_t gc_threshold = 1024 * 1024; // 1MB threshold for GC

// GC-managed object allocation
UmbraObject* umbra_gc_alloc(size_t size, void (*destructor)(UmbraObject*)) {
    UmbraObject* obj = malloc(sizeof(UmbraObject) + size);
    if (!obj) return NULL;

    obj->ref_count = 1;
    obj->size = size;
    obj->destructor = destructor;
    obj->next = gc_objects;
    gc_objects = obj;

    gc_total_allocated += size;

    // Trigger GC if threshold exceeded
    if (gc_total_allocated > gc_threshold) {
        umbra_gc_collect();
    }

    return obj;
}

// Get data pointer from GC object
void* umbra_gc_data(UmbraObject* obj) {
    return obj ? (char*)obj + sizeof(UmbraObject) : NULL;
}

// Reference counting
void umbra_gc_retain(UmbraObject* obj) {
    if (obj) {
        obj->ref_count++;
    }
}

void umbra_gc_release(UmbraObject* obj) {
    if (!obj) return;

    obj->ref_count--;
    if (obj->ref_count == 0) {
        // Remove from GC list
        UmbraObject** current = &gc_objects;
        while (*current) {
            if (*current == obj) {
                *current = obj->next;
                break;
            }
            current = &(*current)->next;
        }

        // Call destructor if provided
        if (obj->destructor) {
            obj->destructor(obj);
        }

        gc_total_allocated -= obj->size;
        free(obj);
    }
}

// Garbage collection
void umbra_gc_collect() {
    // For reference counting, this is mostly a no-op
    // In a mark-and-sweep collector, this would do the actual collection
    // For now, just adjust the threshold
    gc_threshold = gc_total_allocated * 2;
    if (gc_threshold < 1024 * 1024) {
        gc_threshold = 1024 * 1024;
    }
}

// List/Array management with GC
// (UmbraList already defined above)

// List destructor for GC
void umbra_list_destructor(UmbraObject* obj) {
    UmbraList* list = (UmbraList*)umbra_gc_data(obj);
    if (list && list->data) {
        free(list->data);
    }
}

UmbraList* umbra_list_create(size_t element_size, size_t initial_capacity) {
    UmbraObject* obj = umbra_gc_alloc(sizeof(UmbraList), umbra_list_destructor);
    if (!obj) return NULL;

    UmbraList* list = (UmbraList*)umbra_gc_data(obj);
    list->element_size = element_size;
    list->capacity = initial_capacity > 0 ? initial_capacity : 4;
    list->length = 0;
    list->data = malloc(list->element_size * list->capacity);

    if (!list->data) {
        umbra_gc_release(obj);
        return NULL;
    }

    return list;
}

void umbra_list_destroy(UmbraList* list) {
    if (list) {
        // Find the GC object that contains this list
        UmbraObject* current = gc_objects;
        while (current) {
            if (umbra_gc_data(current) == list) {
                umbra_gc_release(current);
                break;
            }
            current = current->next;
        }
    }
}

int umbra_list_append(UmbraList* list, const void* element) {
    if (!list || !element) return 0;

    // Resize if needed
    if (list->length >= list->capacity) {
        size_t new_capacity = list->capacity * 2;
        void* new_data = realloc(list->data, list->element_size * new_capacity);
        if (!new_data) return 0;

        list->data = new_data;
        list->capacity = new_capacity;
    }

    // Copy element to the end
    char* dest = (char*)list->data + (list->length * list->element_size);
    memcpy(dest, element, list->element_size);
    list->length++;

    return 1;
}

void* umbra_list_get(UmbraList* list, size_t index) {
    if (!list || index >= list->length) return NULL;

    return (char*)list->data + (index * list->element_size);
}

size_t umbra_list_length(UmbraList* list) {
    return list ? list->length : 0;
}

void umbra_free_dataset(UmbraDataset* dataset) {
    if (dataset) {
        free(dataset->path);
        free(dataset);
    }
}

void umbra_free_model(UmbraModel* model) {
    if (model) {
        free(model->model_type);
        free(model);
    }
}

void umbra_free_string(char* str) {
    if (str) {
        free(str);
    }
}

void umbra_free_array(void* array) {
    if (array) {
        free(array);
    }
}
"#
        .to_string()
    }

    /// Write the runtime library to a C file
    pub fn write_runtime_to_file(output_path: &Path) -> Result<(), std::io::Error> {
        let runtime_code = Self::generate_c_runtime();
        fs::write(output_path, runtime_code)?;
        Ok(())
    }

    /// Get the list of runtime functions that need to be declared in LLVM
    #[allow(dead_code)]
    pub fn get_runtime_function_declarations() -> Vec<(&'static str, &'static str)> {
        vec![
            ("umbra_show_int", "void (i64)"),
            ("umbra_show_float", "void (double)"),
            ("umbra_show_string", "void (i8*)"),
            ("umbra_show_bool", "void (i1)"),
            ("umbra_show_newline", "void ()"),
            ("umbra_read_string", "i8* ()"),
            // File I/O functions
            ("umbra_write_file", "i32 (i8*, i8*)"),
            ("umbra_file_exists", "i32 (i8*)"),
            ("umbra_read_file", "i8* (i8*)"),
            ("umbra_split", "i8* (i8*, i8*)"),
            ("umbra_array_get", "i8* (i8*, i64)"),
            ("umbra_range", "i64* (i64, i64, i64*)"),
            ("umbra_load_dataset", "i8* (i8*)"),
            ("umbra_create_model", "i8* (i8*)"),
            ("umbra_train_model", "void (i8*, i8*)"),
            ("umbra_evaluate_model", "void (i8*, i8*)"),
            ("umbra_visualize_metric", "void (i8*, i8*)"),
            ("umbra_export_model", "void (i8*, i8*)"),
            ("umbra_predict_sample", "i8* (i8*, i8*)"),
            // Random functions
            ("umbra_random", "double ()"),
            ("umbra_random_int", "i64 (i64, i64)"),
            ("umbra_random_float", "double (double, double)"),
            // Math functions
            ("umbra_sqrt", "double (double)"),
            ("umbra_sin", "double (double)"),
            ("umbra_cos", "double (double)"),
            ("umbra_tan", "double (double)"),
            ("umbra_log", "double (double)"),
            ("umbra_exp", "double (double)"),
            ("umbra_pow", "double (double, double)"),
            ("umbra_floor", "i64 (double)"),
            ("umbra_ceil", "i64 (double)"),
            ("umbra_round", "i64 (double)"),
            ("umbra_abs", "double (double)"),
            ("umbra_abs_int", "i64 (i64)"),
            ("umbra_max", "double (double, double)"),
            ("umbra_max_int", "i64 (i64, i64)"),
            ("umbra_min", "double (double, double)"),
            ("umbra_min_int", "i64 (i64, i64)"),
            // Length functions
            ("umbra_len", "i64 (i8*)"),
            ("umbra_str_len", "i64 (i8*)"),
            // String functions
            ("umbra_str_concat", "i8* (i8*, i8*)"),
            ("umbra_to_upper", "i8* (i8*)"),
            ("umbra_to_lower", "i8* (i8*)"),
            ("umbra_trim", "i8* (i8*)"),
            ("umbra_contains", "i32 (i8*, i8*)"),
            ("umbra_starts_with", "i32 (i8*, i8*)"),
            ("umbra_ends_with", "i32 (i8*, i8*)"),
            ("umbra_substring", "i8* (i8*, i64, i64)"),
            ("umbra_join", "i8* (i8*, i8*)"),
            ("umbra_string_replace", "i8* (i8*, i8*, i8*)"),
            ("umbra_string_repeat", "i8* (i8*, i64)"),
            ("umbra_string_pad_left", "i8* (i8*, i64, i8*)"),
            ("umbra_string_pad_right", "i8* (i8*, i64, i8*)"),
            ("umbra_string_reverse", "i8* (i8*)"),
            // File I/O functions
            ("umbra_read_file", "i8* (i8*)"),
            ("umbra_write_file", "i32 (i8*, i8*)"),
            ("umbra_file_exists", "i32 (i8*)"),
            ("umbra_read_input", "i8* ()"),
            ("umbra_input", "i8* ()"),
            ("umbra_input_with_prompt", "i8* (i8*)"),
            ("umbra_to_int", "i64 (i8*)"),
            ("umbra_to_float", "double (i8*)"),
            ("umbra_to_string_int", "i8* (i64)"),
            ("umbra_to_string_float", "i8* (double)"),
            ("umbra_to_string_bool", "i8* (i64)"),
            ("umbra_newline", "void ()"),
            // JSON functions
            ("umbra_json_stringify_string", "i8* (i8*)"),
            ("umbra_json_parse_string", "i8* (i8*)"),
            // Statistical functions
            ("umbra_mean", "double (i8*)"),
            ("umbra_variance", "double (i8*)"),
            ("umbra_std", "double (i8*)"),
            // Memory management functions
            ("umbra_malloc", "i8* (i64)"),
            ("umbra_calloc", "i8* (i64, i64)"),
            ("umbra_realloc", "i8* (i8*, i64)"),
            ("umbra_free", "void (i8*)"),
            // Garbage collection functions
            ("umbra_gc_alloc", "i8* (i64, i8*)"),
            ("umbra_gc_retain", "void (i8*)"),
            ("umbra_gc_release", "void (i8*)"),
            ("umbra_gc_collect", "void ()"),
            // List management functions
            ("umbra_list_create", "i8* (i64, i64)"),
            ("umbra_list_destroy", "void (i8*)"),
            ("umbra_list_append", "i32 (i8*, i8*)"),
            ("umbra_list_get", "i8* (i8*, i64)"),
            ("umbra_list_length", "i64 (i8*)"),
        ]
    }
}
