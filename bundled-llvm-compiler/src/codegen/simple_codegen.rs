// Simple C code generator for Windows builds (when LLVM is not available)
use crate::parser::ast::*;
use crate::error::{UmbraResult, UmbraError};
use std::collections::HashMap;
use std::fs;
use std::path::Path;
use std::process::Command;

pub struct SimpleCCodeGenerator {
    output: String,
    functions: HashMap<String, String>,
    variables: HashMap<String, String>,
    indent_level: usize,
}

impl SimpleCCodeGenerator {
    pub fn new() -> Self {
        Self {
            output: String::new(),
            functions: HashMap::new(),
            variables: HashMap::new(),
            indent_level: 0,
        }
    }

    pub fn generate(&mut self, program: &Program, output_path: &Path) -> UmbraResult<()> {
        // Generate C header
        self.output.push_str("#include <stdio.h>\n");
        self.output.push_str("#include <stdlib.h>\n");
        self.output.push_str("#include <string.h>\n");
        self.output.push_str("#include <stdbool.h>\n\n");

        // Generate type definitions
        self.output.push_str("typedef long long Integer;\n");
        self.output.push_str("typedef double Float;\n");
        self.output.push_str("typedef bool Boolean;\n");
        self.output.push_str("typedef char* String;\n\n");

        // Generate built-in functions
        self.generate_builtins();

        // Generate function declarations
        for statement in &program.statements {
            if let Statement::Function(func) = statement {
                self.generate_function_declaration(func)?;
            }
        }

        // Generate function implementations
        for statement in &program.statements {
            if let Statement::Function(func) = statement {
                self.generate_function(func)?;
            }
        }

        // Write to file
        let c_file = output_path.with_extension("c");
        fs::write(&c_file, &self.output)?;

        // Compile with GCC
        self.compile_c_file(&c_file, output_path)?;

        Ok(())
    }

    fn generate_builtins(&mut self) {
        self.output.push_str("// Built-in functions\n");
        self.output.push_str("void show(String str) {\n");
        self.output.push_str("    printf(\"%s\\n\", str);\n");
        self.output.push_str("}\n\n");

        self.output.push_str("String str_concat(String a, String b) {\n");
        self.output.push_str("    size_t len = strlen(a) + strlen(b) + 1;\n");
        self.output.push_str("    String result = malloc(len);\n");
        self.output.push_str("    strcpy(result, a);\n");
        self.output.push_str("    strcat(result, b);\n");
        self.output.push_str("    return result;\n");
        self.output.push_str("}\n\n");
    }

    fn generate_function_declaration(&mut self, func: &FunctionDef) -> UmbraResult<()> {
        let return_type = self.map_type(&func.return_type);
        self.output.push_str(&format!("{} {}(", return_type, func.name));

        for (i, param) in func.parameters.iter().enumerate() {
            if i > 0 {
                self.output.push_str(", ");
            }
            let param_type = self.map_type(&param.type_annotation);
            self.output.push_str(&format!("{} {}", param_type, param.name));
        }

        self.output.push_str(");\n");
        Ok(())
    }

    fn generate_function(&mut self, func: &FunctionDef) -> UmbraResult<()> {
        let return_type = self.map_type(&func.return_type);
        self.output.push_str(&format!("{} {}(", return_type, func.name));

        for (i, param) in func.parameters.iter().enumerate() {
            if i > 0 {
                self.output.push_str(", ");
            }
            let param_type = self.map_type(&param.type_annotation);
            self.output.push_str(&format!("{} {}", param_type, param.name));
        }

        self.output.push_str(") {\n");
        self.indent_level += 1;

        self.generate_statements(&func.body)?;

        self.indent_level -= 1;
        self.output.push_str("}\n\n");
        Ok(())
    }

    fn generate_statements(&mut self, statements: &[Statement]) -> UmbraResult<()> {
        for statement in statements {
            self.generate_statement(statement)?;
        }
        Ok(())
    }

    fn generate_statement(&mut self, stmt: &Statement) -> UmbraResult<()> {
        match stmt {
            Statement::Variable(var_decl) => {
                let var_type = self.map_type(&var_decl.type_annotation);
                self.add_indent();
                self.output.push_str(&format!("{} {} = ", var_type, var_decl.name));
                self.generate_expression(&var_decl.value)?;
                self.output.push_str(";\n");
            }
            Statement::Expression(expr_stmt) => {
                self.add_indent();
                self.generate_expression(&expr_stmt.expression)?;
                self.output.push_str(";\n");
            }
            Statement::Return(ret_stmt) => {
                self.add_indent();
                self.output.push_str("return ");
                if let Some(expr) = &ret_stmt.value {
                    self.generate_expression(expr)?;
                }
                self.output.push_str(";\n");
            }
            Statement::When(when_stmt) => {
                self.add_indent();
                self.output.push_str("if (");
                self.generate_expression(&when_stmt.condition)?;
                self.output.push_str(") {\n");
                self.indent_level += 1;
                self.generate_statements(&when_stmt.then_body)?;
                self.indent_level -= 1;
                self.add_indent();
                self.output.push_str("}");

                if let Some(else_body) = &when_stmt.else_body {
                    self.output.push_str(" else {\n");
                    self.indent_level += 1;
                    self.generate_statements(else_body)?;
                    self.indent_level -= 1;
                    self.add_indent();
                    self.output.push_str("}");
                }
                self.output.push_str("\n");
            }
            _ => {
                // Handle other statement types as needed
                self.add_indent();
                self.output.push_str("// Unsupported statement\n");
            }
        }
        Ok(())
    }

    fn generate_expression(&mut self, expr: &Expression) -> UmbraResult<()> {
        match expr {
            Expression::Literal(lit) => {
                match lit {
                    Literal::Integer(i) => self.output.push_str(&i.to_string()),
                    Literal::Float(f) => self.output.push_str(&f.to_string()),
                    Literal::String(s) => self.output.push_str(&format!("\"{}\"", s)),
                    Literal::Boolean(b) => self.output.push_str(if *b { "true" } else { "false" }),
                }
            }
            Expression::Identifier(id) => {
                self.output.push_str(&id.name);
            }
            Expression::Binary(bin) => {
                self.output.push_str("(");
                self.generate_expression(&bin.left)?;
                let op = match bin.operator {
                    BinaryOperator::Add => " + ",
                    BinaryOperator::Subtract => " - ",
                    BinaryOperator::Multiply => " * ",
                    BinaryOperator::Divide => " / ",
                    BinaryOperator::Modulo => " % ",
                    BinaryOperator::Equal => " == ",
                    BinaryOperator::NotEqual => " != ",
                    BinaryOperator::Less => " < ",
                    BinaryOperator::LessEqual => " <= ",
                    BinaryOperator::Greater => " > ",
                    BinaryOperator::GreaterEqual => " >= ",
                    BinaryOperator::And => " && ",
                    BinaryOperator::Or => " || ",
                };
                self.output.push_str(op);
                self.generate_expression(&bin.right)?;
                self.output.push_str(")");
            }
            Expression::Call(call) => {
                // Handle special functions
                if call.name == "show" && call.arguments.len() == 1 {
                    self.output.push_str("show(");
                    self.generate_expression(&call.arguments[0])?;
                    self.output.push_str(")");
                } else if call.name == "+" && call.arguments.len() == 2 {
                    // String concatenation
                    self.output.push_str("str_concat(");
                    self.generate_expression(&call.arguments[0])?;
                    self.output.push_str(", ");
                    self.generate_expression(&call.arguments[1])?;
                    self.output.push_str(")");
                } else {
                    // Regular function call
                    self.output.push_str(&call.name);
                    self.output.push_str("(");
                    for (i, arg) in call.arguments.iter().enumerate() {
                        if i > 0 {
                            self.output.push_str(", ");
                        }
                        self.generate_expression(arg)?;
                    }
                    self.output.push_str(")");
                }
            }
            _ => {
                self.output.push_str("/* unsupported expression */");
            }
        }
        Ok(())
    }

    fn map_type(&self, umbra_type: &Type) -> &'static str {
        match umbra_type {
            Type::Basic(basic_type) => match basic_type {
                BasicType::Integer => "Integer",
                BasicType::Float => "Float",
                BasicType::Boolean => "Boolean",
                BasicType::String => "String",
                BasicType::Void => "void",
                _ => "void*",
            },
            _ => "void*", // Generic fallback
        }
    }

    fn add_indent(&mut self) {
        for _ in 0..self.indent_level {
            self.output.push_str("    ");
        }
    }

    fn compile_c_file(&self, c_file: &Path, output_path: &Path) -> UmbraResult<()> {
        let mut cmd = Command::new("gcc");
        cmd.arg("-o")
           .arg(output_path)
           .arg(c_file)
           .arg("-std=c99");

        let output = cmd.output()
            .map_err(|e| UmbraError::CodeGen(format!("Failed to run GCC: {}", e)))?;

        if !output.status.success() {
            let stderr = String::from_utf8_lossy(&output.stderr);
            return Err(UmbraError::CodeGen(format!("GCC compilation failed: {}", stderr)));
        }

        Ok(())
    }
}
