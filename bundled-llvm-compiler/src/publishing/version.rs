/// Version Management for Umbra Packages
/// 
/// Handles semantic versioning, version comparison, and version constraints
/// for Umbra package dependencies.

use crate::error::UmbraResult;
use std::cmp::Ordering;
use std::fmt;
use serde::{Deserialize, Serialize};

/// Version manager for package versioning
pub struct VersionManager {
    /// Version parsing rules
    parser: VersionParser,
    
    /// Version constraint resolver
    resolver: ConstraintResolver,
}

/// Semantic version
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub struct Version {
    /// Major version
    pub major: u64,
    
    /// Minor version
    pub minor: u64,
    
    /// Patch version
    pub patch: u64,
    
    /// Pre-release identifier
    pub pre_release: Option<String>,
    
    /// Build metadata
    pub build: Option<String>,
}

/// Version constraint
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum VersionConstraint {
    /// Exact version match
    Exact(Version),
    
    /// Greater than version
    GreaterThan(Version),
    
    /// Greater than or equal to version
    GreaterThanOrEqual(Version),
    
    /// Less than version
    LessThan(Version),
    
    /// Less than or equal to version
    LessThanOrEqual(Version),
    
    /// Compatible version (caret constraint)
    Compatible(Version),
    
    /// Tilde constraint (patch-level changes)
    Tilde(Version),
    
    /// Version range
    Range {
        min: Version,
        max: Version,
        include_min: bool,
        include_max: bool,
    },
    
    /// Any version
    Any,
}

/// Version parser
pub struct VersionParser {
    /// Allow pre-release versions
    allow_prerelease: bool,
    
    /// Allow build metadata
    allow_build: bool,
}

/// Constraint resolver
pub struct ConstraintResolver {
    /// Prefer stable versions
    prefer_stable: bool,
    
    /// Maximum pre-release age in days
    max_prerelease_age: u32,
}

/// Version comparison result
#[derive(Debug, PartialEq)]
pub enum VersionComparison {
    /// Versions are equal
    Equal,
    
    /// First version is greater
    Greater,
    
    /// First version is less
    Less,
    
    /// Versions are incompatible
    Incompatible,
}

impl VersionManager {
    /// Create new version manager
    pub fn new() -> UmbraResult<Self> {
        Ok(Self {
            parser: VersionParser::new(),
            resolver: ConstraintResolver::new(),
        })
    }
    
    /// Parse version string
    pub fn parse_version(&self, version_str: &str) -> UmbraResult<Version> {
        self.parser.parse(version_str)
    }
    
    /// Parse version constraint
    pub fn parse_constraint(&self, constraint_str: &str) -> UmbraResult<VersionConstraint> {
        self.parser.parse_constraint(constraint_str)
    }
    
    /// Check if version satisfies constraint
    pub fn satisfies(&self, version: &Version, constraint: &VersionConstraint) -> bool {
        match constraint {
            VersionConstraint::Exact(target) => version == target,
            VersionConstraint::GreaterThan(target) => version > target,
            VersionConstraint::GreaterThanOrEqual(target) => version >= target,
            VersionConstraint::LessThan(target) => version < target,
            VersionConstraint::LessThanOrEqual(target) => version <= target,
            VersionConstraint::Compatible(target) => self.is_compatible(version, target),
            VersionConstraint::Tilde(target) => self.is_tilde_compatible(version, target),
            VersionConstraint::Range { min, max, include_min, include_max } => {
                let min_ok = if *include_min { version >= min } else { version > min };
                let max_ok = if *include_max { version <= max } else { version < max };
                min_ok && max_ok
            }
            VersionConstraint::Any => true,
        }
    }
    
    /// Find best version that satisfies constraint
    pub fn find_best_version(
        &self,
        available: &[Version],
        constraint: &VersionConstraint,
    ) -> Option<Version> {
        let mut candidates: Vec<_> = available
            .iter()
            .filter(|v| self.satisfies(v, constraint))
            .cloned()
            .collect();
        
        if candidates.is_empty() {
            return None;
        }
        
        // Sort by preference (stable versions first, then by version number)
        candidates.sort_by(|a, b| {
            // Prefer stable versions
            match (a.is_prerelease(), b.is_prerelease()) {
                (false, true) => Ordering::Less,
                (true, false) => Ordering::Greater,
                _ => b.cmp(a), // Higher version first
            }
        });
        
        candidates.into_iter().next()
    }
    
    /// Check if version string is valid
    pub fn is_valid_version(&self, version_str: &str) -> bool {
        self.parse_version(version_str).is_ok()
    }
    
    /// Get next version for increment type
    pub fn increment_version(&self, version: &Version, increment: VersionIncrement) -> Version {
        match increment {
            VersionIncrement::Major => Version {
                major: version.major + 1,
                minor: 0,
                patch: 0,
                pre_release: None,
                build: None,
            },
            VersionIncrement::Minor => Version {
                major: version.major,
                minor: version.minor + 1,
                patch: 0,
                pre_release: None,
                build: None,
            },
            VersionIncrement::Patch => Version {
                major: version.major,
                minor: version.minor,
                patch: version.patch + 1,
                pre_release: None,
                build: None,
            },
            VersionIncrement::PreRelease(identifier) => Version {
                major: version.major,
                minor: version.minor,
                patch: version.patch,
                pre_release: Some(identifier),
                build: None,
            },
        }
    }
    
    /// Check if two versions are compatible (caret constraint)
    fn is_compatible(&self, version: &Version, target: &Version) -> bool {
        if version.major != target.major {
            return false;
        }
        
        if target.major > 0 {
            // For major version > 0, allow minor and patch updates
            version >= target
        } else if target.minor > 0 {
            // For major version 0, minor version > 0, allow patch updates
            version.minor == target.minor && version >= target
        } else {
            // For major and minor version 0, only exact patch match
            version.minor == target.minor && version.patch == target.patch
        }
    }
    
    /// Check if two versions are tilde compatible
    fn is_tilde_compatible(&self, version: &Version, target: &Version) -> bool {
        version.major == target.major &&
        version.minor == target.minor &&
        version.patch >= target.patch
    }
}

/// Version increment type
#[derive(Debug, Clone)]
pub enum VersionIncrement {
    /// Increment major version
    Major,
    
    /// Increment minor version
    Minor,
    
    /// Increment patch version
    Patch,
    
    /// Add pre-release identifier
    PreRelease(String),
}

impl VersionParser {
    /// Create new version parser
    pub fn new() -> Self {
        Self {
            allow_prerelease: true,
            allow_build: true,
        }
    }
    
    /// Parse version string
    pub fn parse(&self, version_str: &str) -> UmbraResult<Version> {
        let version_str = version_str.trim();
        
        // Remove 'v' prefix if present
        let version_str = version_str.strip_prefix('v').unwrap_or(version_str);
        
        // Split by '+' to separate build metadata
        let (version_part, build) = if let Some(pos) = version_str.find('+') {
            let (v, b) = version_str.split_at(pos);
            (v, Some(b[1..].to_string()))
        } else {
            (version_str, None)
        };
        
        // Split by '-' to separate pre-release
        let (core_version, pre_release) = if let Some(pos) = version_part.find('-') {
            let (v, p) = version_part.split_at(pos);
            (v, Some(p[1..].to_string()))
        } else {
            (version_part, None)
        };
        
        // Parse core version (major.minor.patch)
        let parts: Vec<&str> = core_version.split('.').collect();
        if parts.len() != 3 {
            return Err(crate::error::UmbraError::CodeGen(
                format!("Invalid version format: {version_str}")
            ));
        }
        
        let major = parts[0].parse::<u64>()
            .map_err(|_| crate::error::UmbraError::CodeGen("Invalid major version".to_string()))?;
        
        let minor = parts[1].parse::<u64>()
            .map_err(|_| crate::error::UmbraError::CodeGen("Invalid minor version".to_string()))?;
        
        let patch = parts[2].parse::<u64>()
            .map_err(|_| crate::error::UmbraError::CodeGen("Invalid patch version".to_string()))?;
        
        Ok(Version {
            major,
            minor,
            patch,
            pre_release,
            build,
        })
    }
    
    /// Parse version constraint
    pub fn parse_constraint(&self, constraint_str: &str) -> UmbraResult<VersionConstraint> {
        let constraint_str = constraint_str.trim();
        
        if constraint_str == "*" {
            return Ok(VersionConstraint::Any);
        }
        
        if constraint_str.starts_with('^') {
            let version = self.parse(&constraint_str[1..])?;
            return Ok(VersionConstraint::Compatible(version));
        }
        
        if constraint_str.starts_with('~') {
            let version = self.parse(&constraint_str[1..])?;
            return Ok(VersionConstraint::Tilde(version));
        }
        
        if constraint_str.starts_with(">=") {
            let version = self.parse(&constraint_str[2..])?;
            return Ok(VersionConstraint::GreaterThanOrEqual(version));
        }
        
        if constraint_str.starts_with("<=") {
            let version = self.parse(&constraint_str[2..])?;
            return Ok(VersionConstraint::LessThanOrEqual(version));
        }
        
        if constraint_str.starts_with('>') {
            let version = self.parse(&constraint_str[1..])?;
            return Ok(VersionConstraint::GreaterThan(version));
        }
        
        if constraint_str.starts_with('<') {
            let version = self.parse(&constraint_str[1..])?;
            return Ok(VersionConstraint::LessThan(version));
        }
        
        // Default to exact match
        let version = self.parse(constraint_str)?;
        Ok(VersionConstraint::Exact(version))
    }
}

impl ConstraintResolver {
    /// Create new constraint resolver
    pub fn new() -> Self {
        Self {
            prefer_stable: true,
            max_prerelease_age: 30,
        }
    }
}

impl Version {
    /// Create new version
    pub fn new(major: u64, minor: u64, patch: u64) -> Self {
        Self {
            major,
            minor,
            patch,
            pre_release: None,
            build: None,
        }
    }
    
    /// Check if version is pre-release
    pub fn is_prerelease(&self) -> bool {
        self.pre_release.is_some()
    }
    
    /// Check if version is stable
    pub fn is_stable(&self) -> bool {
        !self.is_prerelease()
    }
    
    /// Get version without pre-release and build metadata
    pub fn core_version(&self) -> Version {
        Version::new(self.major, self.minor, self.patch)
    }
}

impl fmt::Display for Version {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}.{}.{}", self.major, self.minor, self.patch)?;
        
        if let Some(ref pre) = self.pre_release {
            write!(f, "-{pre}")?;
        }
        
        if let Some(ref build) = self.build {
            write!(f, "+{build}")?;
        }
        
        Ok(())
    }
}

impl PartialOrd for Version {
    fn partial_cmp(&self, other: &Self) -> Option<Ordering> {
        Some(self.cmp(other))
    }
}

impl Ord for Version {
    fn cmp(&self, other: &Self) -> Ordering {
        // Compare core version first
        match (self.major, self.minor, self.patch).cmp(&(other.major, other.minor, other.patch)) {
            Ordering::Equal => {
                // If core versions are equal, compare pre-release
                match (&self.pre_release, &other.pre_release) {
                    (None, None) => Ordering::Equal,
                    (None, Some(_)) => Ordering::Greater, // Stable > pre-release
                    (Some(_), None) => Ordering::Less,    // Pre-release < stable
                    (Some(a), Some(b)) => a.cmp(b),       // Compare pre-release strings
                }
            }
            other => other,
        }
    }
}

impl fmt::Display for VersionConstraint {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            VersionConstraint::Exact(v) => write!(f, "{v}"),
            VersionConstraint::GreaterThan(v) => write!(f, ">{v}"),
            VersionConstraint::GreaterThanOrEqual(v) => write!(f, ">={v}"),
            VersionConstraint::LessThan(v) => write!(f, "<{v}"),
            VersionConstraint::LessThanOrEqual(v) => write!(f, "<={v}"),
            VersionConstraint::Compatible(v) => write!(f, "^{v}"),
            VersionConstraint::Tilde(v) => write!(f, "~{v}"),
            VersionConstraint::Range { min, max, include_min, include_max } => {
                let min_op = if *include_min { ">=" } else { ">" };
                let max_op = if *include_max { "<=" } else { "<" };
                write!(f, "{min_op}{min}, {max_op}{max}")
            }
            VersionConstraint::Any => write!(f, "*"),
        }
    }
}

impl Default for VersionManager {
    fn default() -> Self {
        Self::new().unwrap()
    }
}
