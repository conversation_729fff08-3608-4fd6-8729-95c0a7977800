/// Package Integrity Module for Umbra
/// 
/// Provides hash verification and integrity checking for Umbra packages.

use crate::error::UmbraResult;
use serde::{Deserialize, Serialize};
use std::path::Path;
use std::fs;
use std::io::Read;
use sha2::{Sha256, Sha512, Digest};
use chrono::{DateTime, Utc};

/// Supported hash algorithms
#[derive(Debu<PERSON>, <PERSON>lone, Co<PERSON>, PartialEq, Eq, Serialize, Deserialize)]
pub enum HashAlgorithm {
    /// SHA-256 algorithm
    SHA256,
    
    /// SHA-512 algorithm
    SHA512,
}

impl std::fmt::Display for HashAlgorithm {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            HashAlgorithm::SHA256 => write!(f, "SHA-256"),
            HashAlgorithm::SHA512 => write!(f, "SHA-512"),
        }
    }
}

/// Package integrity information
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct IntegrityInfo {
    /// Hash algorithm used
    pub algorithm: HashAlgorithm,
    
    /// The hash value (hex encoded)
    pub hash: String,
    
    /// File size in bytes
    pub size: u64,
    
    /// Timestamp when hash was created
    pub timestamp: DateTime<Utc>,
}

/// Package integrity manager
pub struct IntegrityManager {
    /// Default hash algorithm
    default_algorithm: HashAlgorithm,
}

impl IntegrityManager {
    /// Create a new integrity manager
    pub fn new() -> Self {
        Self {
            default_algorithm: HashAlgorithm::SHA256,
        }
    }
    
    /// Set the default hash algorithm
    pub fn with_algorithm(mut self, algorithm: HashAlgorithm) -> Self {
        self.default_algorithm = algorithm;
        self
    }
    
    /// Calculate hash for a file
    pub fn calculate_hash(&self, file_path: &Path, algorithm: Option<HashAlgorithm>) -> UmbraResult<IntegrityInfo> {
        let algorithm = algorithm.unwrap_or(self.default_algorithm);
        
        let file = fs::File::open(file_path)?;
        let metadata = file.metadata()?;
        let size = metadata.len();
        
        let hash = match algorithm {
            HashAlgorithm::SHA256 => self.calculate_sha256(file)?,
            HashAlgorithm::SHA512 => self.calculate_sha512(file)?,
        };
        
        Ok(IntegrityInfo {
            algorithm,
            hash,
            size,
            timestamp: Utc::now(),
        })
    }
    
    /// Verify file integrity
    pub fn verify_integrity(&self, file_path: &Path, info: &IntegrityInfo) -> UmbraResult<bool> {
        // Check file size first (quick check)
        let metadata = fs::metadata(file_path)?;
        if metadata.len() != info.size {
            println!("❌ File size mismatch: expected {}, got {}", info.size, metadata.len());
            return Ok(false);
        }
        
        // Calculate hash
        let calculated_info = self.calculate_hash(file_path, Some(info.algorithm))?;
        
        // Compare hashes
        let is_valid = calculated_info.hash == info.hash;
        
        if !is_valid {
            println!("❌ Hash mismatch: expected {}, got {}", info.hash, calculated_info.hash);
        }
        
        Ok(is_valid)
    }
    
    /// Calculate SHA-256 hash
    fn calculate_sha256(&self, mut file: fs::File) -> UmbraResult<String> {
        let mut hasher = Sha256::new();
        let mut buffer = [0; 1024 * 1024]; // 1MB buffer
        
        loop {
            let bytes_read = file.read(&mut buffer)?;
            if bytes_read == 0 {
                break;
            }
            hasher.update(&buffer[..bytes_read]);
        }
        
        let hash = hasher.finalize();
        Ok(format!("{:x}", hash))
    }
    
    /// Calculate SHA-512 hash
    fn calculate_sha512(&self, mut file: fs::File) -> UmbraResult<String> {
        let mut hasher = Sha512::new();
        let mut buffer = [0; 1024 * 1024]; // 1MB buffer
        
        loop {
            let bytes_read = file.read(&mut buffer)?;
            if bytes_read == 0 {
                break;
            }
            hasher.update(&buffer[..bytes_read]);
        }
        
        let hash = hasher.finalize();
        Ok(format!("{:x}", hash))
    }
    
    /// Save integrity info to a file
    pub fn save_integrity_info(&self, info: &IntegrityInfo, output_path: &Path) -> UmbraResult<()> {
        let json = serde_json::to_string_pretty(info)?;
        fs::write(output_path, json)?;
        Ok(())
    }
    
    /// Load integrity info from a file
    pub fn load_integrity_info(&self, file_path: &Path) -> UmbraResult<IntegrityInfo> {
        let content = fs::read_to_string(file_path)?;
        let info: IntegrityInfo = serde_json::from_str(&content)?;
        Ok(info)
    }
}

/// Convenience function to calculate SHA-256 hash of a string
pub fn sha256_string(input: &str) -> String {
    let mut hasher = Sha256::new();
    hasher.update(input.as_bytes());
    let hash = hasher.finalize();
    format!("{:x}", hash)
}

/// Convenience function to calculate SHA-512 hash of a string
pub fn sha512_string(input: &str) -> String {
    let mut hasher = Sha512::new();
    hasher.update(input.as_bytes());
    let hash = hasher.finalize();
    format!("{:x}", hash)
}
