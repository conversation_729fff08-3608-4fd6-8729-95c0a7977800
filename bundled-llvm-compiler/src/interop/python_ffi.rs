/// Python Foreign Function Interface for Umbra
/// 
/// This module provides comprehensive Python interoperability including
/// automatic type conversion, error handling, and seamless integration.

use crate::error::{UmbraError, UmbraResult};
// Runtime module not available - using placeholder
// use crate::runtime::RuntimeValue;

// Placeholder for RuntimeValue
#[derive(Debug, <PERSON>lone)]
pub enum RuntimeValue {
    Integer(i64),
    Float(f64),
    String(String),
    Boolean(bool),
    List(Vec<RuntimeValue>),
    Map(std::collections::HashMap<String, RuntimeValue>),
    None,
    Null,
}

impl std::fmt::Display for RuntimeValue {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            RuntimeValue::Integer(i) => write!(f, "{}", i),
            RuntimeValue::Float(fl) => write!(f, "{}", fl),
            RuntimeValue::String(s) => write!(f, "{}", s),
            RuntimeValue::Boolean(b) => write!(f, "{}", b),
            RuntimeValue::List(l) => write!(f, "{:?}", l),
            RuntimeValue::Map(m) => write!(f, "{:?}", m),
            RuntimeValue::None | RuntimeValue::Null => write!(f, "null"),
        }
    }
}
use std::collections::HashMap;
use std::process::{Command, Stdio};
use std::io::Write;
use std::fs::{File, create_dir_all};
use serde_json::{Value, json};

/// Python FFI manager
pub struct PythonFFI {
    /// Python executable path
    python_executable: String,
    /// Working directory for temporary files
    working_dir: String,
    /// Active Python process for interactive mode
    python_process: Option<std::process::Child>,
    /// Type conversion cache
    type_cache: HashMap<String, String>,
}

impl PythonFFI {
    pub fn new(working_dir: String) -> Self {
        Self {
            python_executable: Self::find_python_executable(),
            working_dir,
            python_process: None,
            type_cache: HashMap::new(),
        }
    }

    /// Find the best available Python executable
    fn find_python_executable() -> String {
        let candidates = ["python3", "python", "python3.11", "python3.10", "python3.9"];
        
        for candidate in &candidates {
            if Command::new(candidate)
                .arg("--version")
                .output()
                .map(|output| output.status.success())
                .unwrap_or(false)
            {
                return candidate.to_string();
            }
        }
        
        "python3".to_string() // Default fallback
    }

    /// Initialize Python environment
    pub fn initialize(&mut self) -> UmbraResult<()> {
        // Create working directory
        create_dir_all(&self.working_dir)
            .map_err(|e| UmbraError::Runtime(format!("Failed to create working directory: {}", e)))?;

        // Check Python availability
        let output = Command::new(&self.python_executable)
            .arg("--version")
            .output()
            .map_err(|e| UmbraError::Runtime(format!("Python not found: {}", e)))?;

        if !output.status.success() {
            return Err(UmbraError::Runtime("Python is not available".to_string()));
        }

        println!("🐍 Python FFI initialized with {}", String::from_utf8_lossy(&output.stdout).trim());

        // Install required packages
        self.ensure_required_packages()?;

        Ok(())
    }

    /// Ensure required Python packages are installed
    fn ensure_required_packages(&self) -> UmbraResult<()> {
        let required_packages = [
            "numpy",
            "pandas", 
            "scikit-learn",
            "matplotlib",
            "seaborn",
            "joblib"
        ];

        for package in &required_packages {
            if !self.is_package_installed(package)? {
                println!("📦 Installing Python package: {}", package);
                self.install_package(package)?;
            }
        }

        Ok(())
    }

    /// Check if a Python package is installed
    fn is_package_installed(&self, package: &str) -> UmbraResult<bool> {
        let output = Command::new(&self.python_executable)
            .arg("-c")
            .arg(&format!("import {}", package))
            .output()
            .map_err(|e| UmbraError::Runtime(format!("Failed to check package {}: {}", package, e)))?;

        Ok(output.status.success())
    }

    /// Install a Python package
    fn install_package(&self, package: &str) -> UmbraResult<()> {
        let output = Command::new("pip3")
            .args(&["install", package])
            .output()
            .map_err(|e| UmbraError::Runtime(format!("Failed to install {}: {}", package, e)))?;

        if !output.status.success() {
            let error_msg = String::from_utf8_lossy(&output.stderr);
            return Err(UmbraError::Runtime(format!("Package installation failed: {}", error_msg)));
        }

        Ok(())
    }

    /// Execute Python code and return result
    pub fn execute_python(&self, code: &str) -> UmbraResult<RuntimeValue> {
        // Create temporary Python script
        let script_path = format!("{}/temp_script.py", self.working_dir);
        let mut script_file = File::create(&script_path)
            .map_err(|e| UmbraError::Runtime(format!("Failed to create Python script: {}", e)))?;

        // Wrap code to capture output as JSON
        let wrapped_code = format!(r#"
import json
import sys
import traceback

try:
    # User code
    {}
    
    # If no explicit result, capture last expression
    if 'result' not in locals():
        result = None
    
    # Convert result to JSON
    print(json.dumps({{"success": True, "result": result, "type": str(type(result).__name__)}}))
    
except Exception as e:
    print(json.dumps({{"success": False, "error": str(e), "traceback": traceback.format_exc()}}))
"#, code);

        script_file.write_all(wrapped_code.as_bytes())
            .map_err(|e| UmbraError::Runtime(format!("Failed to write Python script: {}", e)))?;

        // Execute Python script
        let output = Command::new(&self.python_executable)
            .arg(&script_path)
            .output()
            .map_err(|e| UmbraError::Runtime(format!("Failed to execute Python script: {}", e)))?;

        // Parse output
        let stdout = String::from_utf8_lossy(&output.stdout);
        let result: Value = serde_json::from_str(stdout.trim())
            .map_err(|e| UmbraError::Runtime(format!("Failed to parse Python output: {}", e)))?;

        if result["success"].as_bool().unwrap_or(false) {
            self.json_to_runtime_value(&result["result"])
        } else {
            let error_msg = result["error"].as_str().unwrap_or("Unknown Python error");
            Err(UmbraError::Runtime(format!("Python execution failed: {}", error_msg)))
        }
    }

    /// Call a Python function with arguments
    pub fn call_python_function(
        &self,
        module: &str,
        function: &str,
        args: Vec<RuntimeValue>,
    ) -> UmbraResult<RuntimeValue> {
        let args_json = self.runtime_values_to_json(&args)?;
        
        let code = format!(r#"
import {}
import json

# Parse arguments
args = json.loads('{}')

# Call function
result = {}.{}(*args)
"#, module, args_json, module, function);

        self.execute_python(&code)
    }

    /// Import a Python module and make it available
    pub fn import_module(&mut self, module: &str, alias: Option<&str>) -> UmbraResult<()> {
        let import_code = if let Some(alias) = alias {
            format!("import {} as {}", module, alias)
        } else {
            format!("import {}", module)
        };

        // Test import
        let test_result = self.execute_python(&import_code);
        match test_result {
            Ok(_) => {
                println!("📦 Successfully imported Python module: {}", module);
                Ok(())
            }
            Err(e) => Err(UmbraError::Runtime(format!("Failed to import module {}: {}", module, e)))
        }
    }

    /// Create a Python object
    pub fn create_python_object(
        &self,
        class_path: &str,
        args: Vec<RuntimeValue>,
    ) -> UmbraResult<RuntimeValue> {
        let args_json = self.runtime_values_to_json(&args)?;
        
        let code = format!(r#"
import json
from {} import {}

# Parse arguments
args = json.loads('{}')

# Create object
result = {}(*args)
"#, 
            class_path.rsplitn(2, '.').nth(1).unwrap_or(""),
            class_path.split('.').last().unwrap_or(class_path),
            args_json,
            class_path.split('.').last().unwrap_or(class_path)
        );

        self.execute_python(&code)
    }

    /// Convert Umbra RuntimeValue to Python-compatible JSON
    fn runtime_values_to_json(&self, values: &[RuntimeValue]) -> UmbraResult<String> {
        let json_values: Result<Vec<Value>, UmbraError> = values.iter()
            .map(|v| self.runtime_value_to_json(v))
            .collect();
        
        let json_array = json!(json_values?);
        Ok(json_array.to_string())
    }

    /// Convert single RuntimeValue to JSON
    fn runtime_value_to_json(&self, value: &RuntimeValue) -> UmbraResult<Value> {
        match value {
            RuntimeValue::Integer(i) => Ok(json!(*i)),
            RuntimeValue::Float(f) => Ok(json!(*f)),
            RuntimeValue::String(s) => Ok(json!(s)),
            RuntimeValue::Boolean(b) => Ok(json!(*b)),
            RuntimeValue::List(list) => {
                let json_list: Result<Vec<Value>, UmbraError> = list.iter()
                    .map(|v| self.runtime_value_to_json(v))
                    .collect();
                Ok(json!(json_list?))
            }
            RuntimeValue::Map(map) => {
                let mut json_map = serde_json::Map::new();
                for (key, value) in map {
                    json_map.insert(key.clone(), self.runtime_value_to_json(value)?);
                }
                Ok(Value::Object(json_map))
            }
            RuntimeValue::Null => Ok(Value::Null),
            _ => Ok(json!(value.to_string())),
        }
    }

    /// Convert JSON to RuntimeValue
    fn json_to_runtime_value(&self, json: &Value) -> UmbraResult<RuntimeValue> {
        match json {
            Value::Null => Ok(RuntimeValue::Null),
            Value::Bool(b) => Ok(RuntimeValue::Boolean(*b)),
            Value::Number(n) => {
                if let Some(i) = n.as_i64() {
                    Ok(RuntimeValue::Integer(i))
                } else if let Some(f) = n.as_f64() {
                    Ok(RuntimeValue::Float(f))
                } else {
                    Ok(RuntimeValue::Float(0.0))
                }
            }
            Value::String(s) => Ok(RuntimeValue::String(s.clone())),
            Value::Array(arr) => {
                let runtime_list: Result<Vec<RuntimeValue>, UmbraError> = arr.iter()
                    .map(|v| self.json_to_runtime_value(v))
                    .collect();
                Ok(RuntimeValue::List(runtime_list?))
            }
            Value::Object(obj) => {
                let mut runtime_map = HashMap::new();
                for (key, value) in obj {
                    runtime_map.insert(key.clone(), self.json_to_runtime_value(value)?);
                }
                Ok(RuntimeValue::Map(runtime_map))
            }
        }
    }

    /// Start interactive Python session
    pub fn start_interactive_session(&mut self) -> UmbraResult<()> {
        let child = Command::new(&self.python_executable)
            .arg("-i")
            .stdin(Stdio::piped())
            .stdout(Stdio::piped())
            .stderr(Stdio::piped())
            .spawn()
            .map_err(|e| UmbraError::Runtime(format!("Failed to start Python process: {}", e)))?;

        self.python_process = Some(child);
        println!("🐍 Interactive Python session started");
        Ok(())
    }

    /// Send command to interactive Python session
    pub fn send_to_interactive(&mut self, code: &str) -> UmbraResult<String> {
        if let Some(ref mut process) = self.python_process {
            if let Some(ref mut stdin) = process.stdin.as_mut() {
                writeln!(stdin, "{}", code)
                    .map_err(|e| UmbraError::Runtime(format!("Failed to send to Python: {}", e)))?;
                
                // Read response (simplified - would need proper buffering in production)
                Ok("Command sent".to_string())
            } else {
                Err(UmbraError::Runtime("Python process stdin not available".to_string()))
            }
        } else {
            Err(UmbraError::Runtime("No active Python session".to_string()))
        }
    }

    /// Stop interactive Python session
    pub fn stop_interactive_session(&mut self) -> UmbraResult<()> {
        if let Some(mut process) = self.python_process.take() {
            process.kill()
                .map_err(|e| UmbraError::Runtime(format!("Failed to kill Python process: {}", e)))?;
            println!("🐍 Interactive Python session stopped");
        }
        Ok(())
    }

    /// Execute Python file
    pub fn execute_python_file(&self, file_path: &str) -> UmbraResult<RuntimeValue> {
        let output = Command::new(&self.python_executable)
            .arg(file_path)
            .output()
            .map_err(|e| UmbraError::Runtime(format!("Failed to execute Python file: {}", e)))?;

        if !output.status.success() {
            let error_msg = String::from_utf8_lossy(&output.stderr);
            return Err(UmbraError::Runtime(format!("Python file execution failed: {}", error_msg)));
        }

        let stdout = String::from_utf8_lossy(&output.stdout);
        Ok(RuntimeValue::String(stdout.to_string()))
    }

    /// Get Python version information
    pub fn get_python_info(&self) -> UmbraResult<HashMap<String, RuntimeValue>> {
        let code = r#"
import sys
import platform

result = {
    "version": sys.version,
    "version_info": list(sys.version_info[:3]),
    "platform": platform.platform(),
    "executable": sys.executable,
    "path": sys.path[:5]  # First 5 paths
}
"#;

        match self.execute_python(code)? {
            RuntimeValue::Map(info) => Ok(info),
            _ => Err(UmbraError::Runtime("Failed to get Python info".to_string()))
        }
    }

    /// Check if Python package is available
    pub fn check_package_availability(&self, packages: &[String]) -> UmbraResult<HashMap<String, bool>> {
        let mut results = HashMap::new();
        
        for package in packages {
            let available = self.is_package_installed(package).unwrap_or(false);
            results.insert(package.clone(), available);
        }
        
        Ok(results)
    }
}

impl Drop for PythonFFI {
    fn drop(&mut self) {
        let _ = self.stop_interactive_session();
    }
}

/// Global Python FFI functions
pub fn execute_python_code(code: &str) -> UmbraResult<RuntimeValue> {
    let mut ffi = PythonFFI::new("/tmp/umbra_python".to_string());
    ffi.initialize()?;
    ffi.execute_python(code)
}

pub fn call_python_function(module: &str, function: &str, args: Vec<RuntimeValue>) -> UmbraResult<RuntimeValue> {
    let mut ffi = PythonFFI::new("/tmp/umbra_python".to_string());
    ffi.initialize()?;
    ffi.call_python_function(module, function, args)
}

pub fn import_python_module(module: &str) -> UmbraResult<()> {
    let mut ffi = PythonFFI::new("/tmp/umbra_python".to_string());
    ffi.initialize()?;
    ffi.import_module(module, None)
}

pub fn get_python_info() -> UmbraResult<HashMap<String, RuntimeValue>> {
    let mut ffi = PythonFFI::new("/tmp/umbra_python".to_string());
    ffi.initialize()?;
    ffi.get_python_info()
}
