/// Python Foreign Function Interface for Umbra
/// 
/// Provides seamless integration with Python runtime and libraries,
/// enabling Umbra to call Python functions and access Python objects.

use crate::error::{UmbraError, UmbraResult};
use crate::interop::InteropConfig;
use pyo3::prelude::*;
use pyo3::types::{PyD<PERSON>, PyList, PyTuple};
use std::collections::HashMap;

/// Python FFI manager
pub struct PythonFFI {
    /// Global Python namespace
    globals: Py<PyDict>,
    /// Imported modules cache
    modules: HashMap<String, PyObject>,
    /// Configuration
    config: InteropConfig,
}

/// Python value wrapper for Umbra
#[derive(Debug, Clone)]
pub enum PythonValue {
    None,
    Bool(bool),
    Int(i64),
    Float(f64),
    String(String),
    List(Vec<PythonValue>),
    Dict(HashMap<String, PythonValue>),
    Object(PyObject),
}

/// Python function call result
#[derive(Debug)]
pub struct PythonCallResult {
    pub value: PythonValue,
    pub success: bool,
    pub error: Option<String>,
}

impl PythonFFI {
    /// Create a new Python FFI instance
    pub fn new(config: &InteropConfig) -> UmbraResult<Self> {
        // Initialize Python interpreter
        pyo3::prepare_freethreaded_python();
        
        Python::with_gil(|py| {
            // Set up Python path if specified
            if let Some(ref python_path) = config.python_path {
                let sys = py.import("sys")?;
                let path_list = sys.getattr("path")?;
                path_list.call_method1("insert", (0, python_path.to_string_lossy().as_ref()))?;
            }

            // Add additional Python paths
            if !config.python_paths.is_empty() {
                let sys = py.import("sys")?;
                let path_list = sys.getattr("path")?;
                for path in &config.python_paths {
                    path_list.call_method1("append", (path.to_string_lossy().as_ref(),))?;
                }
            }

            // Create global namespace
            let globals = PyDict::new(py);
            
            // Import common modules
            let builtins = py.import("builtins")?;
            globals.set_item("__builtins__", builtins)?;

            Ok(Self {
                globals: globals.into(),
                modules: HashMap::new(),
                config: config.clone(),
            })
        }).map_err(|e: PyErr| UmbraError::Runtime(format!("Failed to initialize Python FFI: {}", e)))
    }

    /// Import a Python module
    pub fn import_module(&mut self, module_name: &str) -> UmbraResult<()> {
        Python::with_gil(|py| {
            let module = py.import(module_name)?;
            
            self.modules.insert(module_name.to_string(), module.into());
            
            // Add to globals for easy access
            self.globals.as_ref(py).set_item(module_name, module)?;
            
            Ok(())
        }).map_err(|e: PyErr| UmbraError::Runtime(format!("Failed to import module: {}", e)))
    }

    /// Execute Python code
    pub fn execute(&self, code: &str) -> UmbraResult<PythonValue> {
        Python::with_gil(|py| {
            let result = py.eval(code, Some(self.globals.as_ref(py)), None)
                .map_err(|e| UmbraError::Runtime(format!("Python execution error: {}", e)))?;

            self.py_to_umbra_value(result)
        })
    }

    /// Execute Python code from file
    pub fn execute_file<P: AsRef<std::path::Path>>(&self, file_path: P) -> UmbraResult<PythonValue> {
        let code = std::fs::read_to_string(file_path)
            .map_err(|e| UmbraError::Runtime(format!("Failed to read Python file: {}", e)))?;
        self.execute(&code)
    }

    /// Create Python virtual environment
    pub fn create_venv(&self, venv_path: &str) -> UmbraResult<()> {
        Python::with_gil(|py| {
            let venv_module = py.import("venv")?;
            let create_fn = venv_module.getattr("create")?;
            create_fn.call1((venv_path,))?;
            Ok(())
        }).map_err(|e: PyErr| UmbraError::Runtime(format!("Failed to create virtual environment: {}", e)))
    }

    /// Install Python package using pip
    pub fn install_package(&self, package_name: &str, version: Option<&str>) -> UmbraResult<()> {
        let package_spec = if let Some(ver) = version {
            format!("{}=={}", package_name, ver)
        } else {
            package_name.to_string()
        };

        Python::with_gil(|py| {
            let subprocess = py.import("subprocess")?;
            let run_fn = subprocess.getattr("run")?;

            let args = vec!["pip", "install", &package_spec];
            let result = run_fn.call1((args,))?;

            let returncode = result.getattr("returncode")?.extract::<i32>()?;
            if returncode != 0 {
                return Err(PyErr::new::<pyo3::exceptions::PyRuntimeError, _>(
                    format!("pip install failed with code {}", returncode)
                ));
            }

            Ok(())
        }).map_err(|e: PyErr| UmbraError::Runtime(format!("Failed to install package: {}", e)))
    }

    /// List installed Python packages
    pub fn list_packages(&self) -> UmbraResult<Vec<(String, String)>> {
        Python::with_gil(|py| {
            let pkg_resources = py.import("pkg_resources")?;
            let working_set = pkg_resources.getattr("working_set")?;

            let mut packages = Vec::new();
            for item in working_set.iter()? {
                let item = item?;
                let name = item.getattr("project_name")?.extract::<String>()?;
                let version = item.getattr("version")?.extract::<String>()?;
                packages.push((name, version));
            }

            Ok(packages)
        }).map_err(|e: PyErr| UmbraError::Runtime(format!("Failed to list packages: {}", e)))
    }

    /// Call a Python function
    pub fn call_function(&self, module_name: &str, function_name: &str, args: &[PythonValue]) -> UmbraResult<PythonCallResult> {
        Python::with_gil(|py| {
            // Get the module
            let module = self.modules.get(module_name)
                .ok_or_else(|| UmbraError::Runtime(format!("Module '{}' not imported", module_name)))?;

            // Get the function
            let function = module.getattr(py, function_name)
                .map_err(|e| UmbraError::Runtime(format!("Function '{}' not found in module '{}': {}", function_name, module_name, e)))?;

            // Convert arguments
            let py_args: Result<Vec<PyObject>, UmbraError> = args.iter()
                .map(|arg| self.umbra_to_py_value(py, arg))
                .collect();
            let py_args = py_args?;

            // Call the function
            match function.call1(py, PyTuple::new(py, py_args)) {
                Ok(result) => {
                    let value = self.py_to_umbra_value(result.as_ref(py))?;
                    Ok(PythonCallResult {
                        value,
                        success: true,
                        error: None,
                    })
                }
                Err(e) => {
                    Ok(PythonCallResult {
                        value: PythonValue::None,
                        success: false,
                        error: Some(format!("Function call failed: {}", e)),
                    })
                }
            }
        })
    }

    /// Get attribute from a Python object
    pub fn get_attribute(&self, module_name: &str, object_name: &str, attr_name: &str) -> UmbraResult<PythonValue> {
        Python::with_gil(|py| {
            let module = self.modules.get(module_name)
                .ok_or_else(|| UmbraError::Runtime(format!("Module '{}' not imported", module_name)))?;

            let object = module.getattr(py, object_name)
                .map_err(|e| UmbraError::Runtime(format!("Object '{}' not found: {}", object_name, e)))?;

            let attr = object.getattr(py, attr_name)
                .map_err(|e| UmbraError::Runtime(format!("Attribute '{}' not found: {}", attr_name, e)))?;

            self.py_to_umbra_value(attr.as_ref(py))
        })
    }

    /// Check if a module is available
    pub fn is_module_available(&self, module_name: &str) -> bool {
        Python::with_gil(|py| {
            py.import(module_name).is_ok()
        })
    }

    /// Get Python version
    pub fn python_version(&self) -> String {
        Python::with_gil(|py| {
            let sys = py.import("sys").unwrap();
            let version = sys.getattr("version").unwrap();
            version.to_string()
        })
    }

    /// List available modules
    pub fn list_modules(&self) -> Vec<String> {
        self.modules.keys().cloned().collect()
    }

    /// Convert Python value to Umbra value
    fn py_to_umbra_value(&self, py_obj: &PyAny) -> UmbraResult<PythonValue> {
        if py_obj.is_none() {
            Ok(PythonValue::None)
        } else if let Ok(val) = py_obj.extract::<bool>() {
            Ok(PythonValue::Bool(val))
        } else if let Ok(val) = py_obj.extract::<i64>() {
            Ok(PythonValue::Int(val))
        } else if let Ok(val) = py_obj.extract::<f64>() {
            Ok(PythonValue::Float(val))
        } else if let Ok(val) = py_obj.extract::<String>() {
            Ok(PythonValue::String(val))
        } else if let Ok(list) = py_obj.downcast::<PyList>() {
            let mut vec = Vec::new();
            for item in list.iter() {
                vec.push(self.py_to_umbra_value(item)?);
            }
            Ok(PythonValue::List(vec))
        } else if let Ok(dict) = py_obj.downcast::<PyDict>() {
            let mut map = HashMap::new();
            for (key, value) in dict.iter() {
                if let Ok(key_str) = key.extract::<String>() {
                    map.insert(key_str, self.py_to_umbra_value(value)?);
                }
            }
            Ok(PythonValue::Dict(map))
        } else {
            // Store as opaque Python object
            Ok(PythonValue::Object(py_obj.into()))
        }
    }

    /// Convert Umbra value to Python value
    fn umbra_to_py_value(&self, py: Python, value: &PythonValue) -> UmbraResult<PyObject> {
        match value {
            PythonValue::None => Ok(py.None()),
            PythonValue::Bool(b) => Ok(b.to_object(py)),
            PythonValue::Int(i) => Ok(i.to_object(py)),
            PythonValue::Float(f) => Ok(f.to_object(py)),
            PythonValue::String(s) => Ok(s.to_object(py)),
            PythonValue::List(list) => {
                let py_list = PyList::empty(py);
                for item in list {
                    let py_item = self.umbra_to_py_value(py, item)?;
                    py_list.append(py_item)?;
                }
                Ok(py_list.to_object(py))
            }
            PythonValue::Dict(dict) => {
                let py_dict = PyDict::new(py);
                for (key, value) in dict {
                    let py_value = self.umbra_to_py_value(py, value)?;
                    py_dict.set_item(key, py_value)?;
                }
                Ok(py_dict.to_object(py))
            }
            PythonValue::Object(obj) => Ok(obj.clone()),
        }
    }
}

/// Utility functions for Python integration
pub mod utils {
    use super::*;

    /// Check if Python is available
    pub fn is_python_available() -> bool {
        Python::with_gil(|_py| true)
    }

    /// Get installed Python packages
    pub fn get_installed_packages() -> UmbraResult<Vec<String>> {
        Python::with_gil(|py| {
            let result = py.eval(
                "import pkg_resources; [d.project_name for d in pkg_resources.working_set]",
                None,
                None,
            )?;
            
            let packages: Vec<String> = result.extract()?;
            Ok(packages)
        }).map_err(|e: PyErr| UmbraError::Runtime(format!("Failed to get packages: {}", e)))
    }

    /// Install a Python package using pip
    pub fn install_package(package_name: &str) -> UmbraResult<()> {
        Python::with_gil(|py| {
            let subprocess = py.import("subprocess")?;
            let result = subprocess.call_method1(
                "run",
                (["pip", "install", package_name],),
            )?;
            
            let returncode: i32 = result.getattr("returncode")?.extract()?;
            if returncode != 0 {
                return Err(UmbraError::Runtime(format!("Failed to install package: {}", package_name)));
            }
            
            Ok(())
        })
    }
}
