/// AI/ML Ecosystem Integration for Umbra
/// 
/// This module provides comprehensive integration with AI/ML ecosystems,
/// particularly Python-based frameworks like NumPy, PyTorch, and TensorFlow.

#[cfg(feature = "python-interop")]
pub mod python;
#[cfg(feature = "python-interop")]
pub mod numpy;
#[cfg(feature = "python-interop")]
pub mod pytorch;
#[cfg(feature = "python-interop")]
pub mod tensorflow;

pub mod python_ffi;

pub mod gpu;
pub mod data;
pub mod deployment;

use crate::error::{UmbraError, UmbraResult};
use std::collections::HashMap;
use std::path::PathBuf;
use serde::{Deserialize, Serialize};

/// Configuration for AI/ML ecosystem integration
#[derive(Debug, Clone, Serialize, Deserialize)]
#[derive(Default)]
pub struct InteropConfig {
    /// Python interpreter path
    pub python_path: Option<PathBuf>,
    /// Python virtual environment path
    pub venv_path: Option<PathBuf>,
    /// Enable GPU acceleration
    pub enable_gpu: bool,
    /// CUDA toolkit path
    pub cuda_path: Option<PathBuf>,
    /// Additional Python paths
    pub python_paths: Vec<PathBuf>,
    /// Framework-specific configurations
    pub frameworks: HashMap<String, FrameworkConfig>,
}

/// Configuration for specific AI/ML frameworks
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FrameworkConfig {
    /// Framework name (e.g., "pytorch", "tensorflow", "numpy")
    pub name: String,
    /// Framework version requirement
    pub version: Option<String>,
    /// Installation path
    pub path: Option<PathBuf>,
    /// Framework-specific options
    pub options: HashMap<String, String>,
}

/// Main interoperability manager
pub struct InteropManager {
    config: InteropConfig,
    #[cfg(feature = "python-interop")]
    python_ffi: Option<python::PythonFFI>,
    #[cfg(feature = "python-interop")]
    numpy_bridge: Option<numpy::NumPyBridge>,
    #[cfg(feature = "python-interop")]
    pytorch_bridge: Option<pytorch::PyTorchBridge>,
    #[cfg(feature = "python-interop")]
    tensorflow_bridge: Option<tensorflow::TensorFlowBridge>,
}

impl InteropManager {
    /// Create a new interoperability manager
    pub fn new(config: InteropConfig) -> UmbraResult<Self> {
        Ok(Self {
            config,
            #[cfg(feature = "python-interop")]
            python_ffi: None,
            #[cfg(feature = "python-interop")]
            numpy_bridge: None,
            #[cfg(feature = "python-interop")]
            pytorch_bridge: None,
            #[cfg(feature = "python-interop")]
            tensorflow_bridge: None,
        })
    }

    /// Initialize Python FFI
    #[cfg(feature = "python-interop")]
    pub fn init_python(&mut self) -> UmbraResult<()> {
        let python_ffi = python::PythonFFI::new(&self.config)?;
        self.python_ffi = Some(python_ffi);
        Ok(())
    }

    /// Initialize Python FFI (stub for non-Python builds)
    #[cfg(not(feature = "python-interop"))]
    pub fn init_python(&mut self) -> UmbraResult<()> {
        Err(UmbraError::Runtime("Python interoperability not enabled".to_string()))
    }

    /// Initialize NumPy integration
    #[cfg(feature = "python-interop")]
    pub fn init_numpy(&mut self) -> UmbraResult<()> {
        if self.python_ffi.is_none() {
            self.init_python()?;
        }

        let numpy_bridge = numpy::NumPyBridge::new(
            self.python_ffi.as_ref().unwrap(),
            &self.config
        )?;
        self.numpy_bridge = Some(numpy_bridge);
        Ok(())
    }

    /// Initialize NumPy integration (stub)
    #[cfg(not(feature = "python-interop"))]
    pub fn init_numpy(&mut self) -> UmbraResult<()> {
        Err(UmbraError::Runtime("Python interoperability not enabled".to_string()))
    }

    /// Initialize PyTorch integration
    #[cfg(feature = "python-interop")]
    pub fn init_pytorch(&mut self) -> UmbraResult<()> {
        if self.python_ffi.is_none() {
            self.init_python()?;
        }

        let pytorch_bridge = pytorch::PyTorchBridge::new(
            self.python_ffi.as_ref().unwrap(),
            &self.config
        )?;
        self.pytorch_bridge = Some(pytorch_bridge);
        Ok(())
    }

    /// Initialize PyTorch integration (stub)
    #[cfg(not(feature = "python-interop"))]
    pub fn init_pytorch(&mut self) -> UmbraResult<()> {
        Err(UmbraError::Runtime("Python interoperability not enabled".to_string()))
    }

    /// Initialize TensorFlow integration
    #[cfg(feature = "python-interop")]
    pub fn init_tensorflow(&mut self) -> UmbraResult<()> {
        if self.python_ffi.is_none() {
            self.init_python()?;
        }

        let tensorflow_bridge = tensorflow::TensorFlowBridge::new(
            self.python_ffi.as_ref().unwrap(),
            &self.config
        )?;
        self.tensorflow_bridge = Some(tensorflow_bridge);
        Ok(())
    }

    /// Initialize TensorFlow integration (stub)
    #[cfg(not(feature = "python-interop"))]
    pub fn init_tensorflow(&mut self) -> UmbraResult<()> {
        Err(UmbraError::Runtime("Python interoperability not enabled".to_string()))
    }

    /// Get Python FFI reference
    #[cfg(feature = "python-interop")]
    pub fn python(&self) -> Option<&python::PythonFFI> {
        self.python_ffi.as_ref()
    }

    /// Get NumPy bridge reference
    #[cfg(feature = "python-interop")]
    pub fn numpy(&self) -> Option<&numpy::NumPyBridge> {
        self.numpy_bridge.as_ref()
    }

    /// Get PyTorch bridge reference
    #[cfg(feature = "python-interop")]
    pub fn pytorch(&self) -> Option<&pytorch::PyTorchBridge> {
        self.pytorch_bridge.as_ref()
    }

    /// Get TensorFlow bridge reference
    #[cfg(feature = "python-interop")]
    pub fn tensorflow(&self) -> Option<&tensorflow::TensorFlowBridge> {
        self.tensorflow_bridge.as_ref()
    }

    /// Check if a framework is available
    pub fn is_framework_available(&self, framework: &str) -> bool {
        match framework {
            #[cfg(feature = "python-interop")]
            "python" => self.python_ffi.is_some(),
            #[cfg(feature = "python-interop")]
            "numpy" => self.numpy_bridge.is_some(),
            #[cfg(feature = "python-interop")]
            "pytorch" => self.pytorch_bridge.is_some(),
            #[cfg(feature = "python-interop")]
            "tensorflow" => self.tensorflow_bridge.is_some(),
            _ => false,
        }
    }

    /// Get available frameworks
    pub fn available_frameworks(&self) -> Vec<String> {
        let mut frameworks = Vec::new();

        #[cfg(feature = "python-interop")]
        {
            if self.python_ffi.is_some() {
                frameworks.push("python".to_string());
            }
            if self.numpy_bridge.is_some() {
                frameworks.push("numpy".to_string());
            }
            if self.pytorch_bridge.is_some() {
                frameworks.push("pytorch".to_string());
            }
            if self.tensorflow_bridge.is_some() {
                frameworks.push("tensorflow".to_string());
            }
        }

        frameworks
    }
}


/// Utility functions for interoperability
pub mod utils {
    use super::*;
    use std::process::Command;

    /// Detect Python installation
    pub fn detect_python() -> UmbraResult<PathBuf> {
        // Try common Python executables
        let python_names = ["python3", "python", "python3.11", "python3.10", "python3.9"];
        
        for name in &python_names {
            if let Ok(output) = Command::new(name).arg("--version").output() {
                if output.status.success() {
                    if let Ok(path) = which::which(name) {
                        return Ok(path);
                    }
                }
            }
        }
        
        Err(UmbraError::Runtime("Python interpreter not found".to_string()))
    }

    /// Detect virtual environment
    pub fn detect_venv() -> Option<PathBuf> {
        // Check common environment variables
        if let Ok(venv) = std::env::var("VIRTUAL_ENV") {
            return Some(PathBuf::from(venv));
        }
        
        if let Ok(conda_env) = std::env::var("CONDA_DEFAULT_ENV") {
            if conda_env != "base" {
                if let Ok(conda_prefix) = std::env::var("CONDA_PREFIX") {
                    return Some(PathBuf::from(conda_prefix));
                }
            }
        }
        
        None
    }

    /// Check if a Python package is installed
    pub fn is_package_installed(python_path: &PathBuf, package: &str) -> bool {
        Command::new(python_path)
            .args(["-c", &format!("import {package}")])
            .output()
            .map(|output| output.status.success())
            .unwrap_or(false)
    }

    /// Get package version
    pub fn get_package_version(python_path: &PathBuf, package: &str) -> UmbraResult<String> {
        let output = Command::new(python_path)
            .args(["-c", &format!("import {package}; print({package}.__version__)")])
            .output()
            .map_err(|e| UmbraError::Runtime(format!("Failed to get package version: {e}")))?;

        if output.status.success() {
            let version = String::from_utf8_lossy(&output.stdout).trim().to_string();
            Ok(version)
        } else {
            Err(UmbraError::Runtime(format!("Package {package} not found")))
        }
    }
}
