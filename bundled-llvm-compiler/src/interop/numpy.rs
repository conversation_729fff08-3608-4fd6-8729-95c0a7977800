/// NumPy Integration for Umbra
/// 
/// Provides direct integration with NumPy arrays and operations,
/// enabling efficient tensor operations and data manipulation.

use crate::error::{UmbraError, UmbraResult};
use crate::interop::{InteropConfig, python::PythonFFI};
use pyo3::prelude::*;
use pyo3::types::PyList;
use std::collections::HashMap;

/// NumPy bridge for Umbra
pub struct NumPyBridge {
    /// NumPy module
    numpy_module: PyObject,
    /// Configuration
    config: InteropConfig,
}

/// NumPy array wrapper
#[derive(Debug, Clone)]
pub struct UmbraArray {
    /// Python NumPy array object
    array: PyObject,
    /// Array shape
    shape: Vec<usize>,
    /// Data type
    dtype: String,
    /// Number of dimensions
    ndim: usize,
}

/// NumPy data types
#[derive(Debug, <PERSON><PERSON>, PartialEq)]
pub enum NumPyDType {
    Float32,
    Float64,
    Int32,
    Int64,
    Bool,
    Complex64,
    <PERSON>128,
}

impl NumPyBridge {
    /// Create a new NumPy bridge
    pub fn new(python_ffi: &PythonFFI, config: &InteropConfig) -> UmbraResult<Self> {
        Python::with_gil(|py| {
            // Import NumPy
            let numpy = py.import("numpy")
                .map_err(|e| UmbraError::Runtime(format!("Failed to import NumPy: {}. Install with: pip install numpy", e)))?;

            Ok(Self {
                numpy_module: numpy.into(),
                config: config.clone(),
            })
        })
    }

    /// Create a new array from Umbra data
    pub fn array_from_data(&self, data: &[f64], shape: &[usize]) -> UmbraResult<UmbraArray> {
        Python::with_gil(|py| {
            let numpy = self.numpy_module.as_ref(py);
            
            // Create Python list from data
            let py_data = PyList::new(py, data);
            
            // Create NumPy array
            let array = numpy.call_method1("array", (py_data,))?;
            
            // Reshape if needed
            let reshaped_array = if shape.len() > 1 {
                let shape_tuple = shape.iter().map(|&s| s as i64).collect::<Vec<_>>();
                array.call_method1("reshape", (shape_tuple,))?
            } else {
                array
            };

            // Get array properties
            let array_shape: Vec<usize> = reshaped_array.getattr("shape")?
                .extract::<Vec<i64>>()?
                .into_iter()
                .map(|x| x as usize)
                .collect();
            
            let dtype: String = reshaped_array.getattr("dtype")?
                .call_method0("__str__")?
                .extract()?;
            
            let ndim: usize = reshaped_array.getattr("ndim")?.extract::<i64>()? as usize;

            Ok(UmbraArray {
                array: reshaped_array.into(),
                shape: array_shape,
                dtype,
                ndim,
            })
        })
    }

    /// Create zeros array
    pub fn zeros(&self, shape: &[usize], dtype: NumPyDType) -> UmbraResult<UmbraArray> {
        Python::with_gil(|py| {
            let numpy = self.numpy_module.as_ref(py);
            
            let shape_tuple = shape.iter().map(|&s| s as i64).collect::<Vec<_>>();
            let dtype_str = self.dtype_to_string(&dtype);
            
            let array = numpy.call_method(
                "zeros",
                (shape_tuple,),
                None,
            )?;

            self.wrap_numpy_array(array)
        })
    }

    /// Create ones array
    pub fn ones(&self, shape: &[usize], dtype: NumPyDType) -> UmbraResult<UmbraArray> {
        Python::with_gil(|py| {
            let numpy = self.numpy_module.as_ref(py);
            
            let shape_tuple = shape.iter().map(|&s| s as i64).collect::<Vec<_>>();
            let dtype_str = self.dtype_to_string(&dtype);
            
            let array = numpy.call_method(
                "ones",
                (shape_tuple,),
                {
                    let kwargs = pyo3::types::PyDict::new(py);
                    kwargs.set_item("dtype", dtype_str)?;
                    Some(kwargs)
                },
            )?;

            self.wrap_numpy_array(array)
        })
    }

    /// Create random array
    pub fn random(&self, shape: &[usize]) -> UmbraResult<UmbraArray> {
        Python::with_gil(|py| {
            let numpy = self.numpy_module.as_ref(py);
            let random = numpy.getattr("random")?;

            let shape_tuple = shape.iter().map(|&s| s as i64).collect::<Vec<_>>();
            let array = random.call_method1("random", (shape_tuple,))?;

            self.wrap_numpy_array(array)
        })
    }

    /// Array addition
    pub fn add(&self, a: &UmbraArray, b: &UmbraArray) -> UmbraResult<UmbraArray> {
        Python::with_gil(|py| {
            let numpy = self.numpy_module.as_ref(py);
            let result = numpy.call_method1("add", (a.array.as_ref(py), b.array.as_ref(py)))?;
            self.wrap_numpy_array(result)
        })
    }

    /// Array subtraction
    pub fn subtract(&self, a: &UmbraArray, b: &UmbraArray) -> UmbraResult<UmbraArray> {
        Python::with_gil(|py| {
            let numpy = self.numpy_module.as_ref(py);
            let result = numpy.call_method1("subtract", (a.array.as_ref(py), b.array.as_ref(py)))?;
            self.wrap_numpy_array(result)
        })
    }

    /// Array multiplication
    pub fn multiply(&self, a: &UmbraArray, b: &UmbraArray) -> UmbraResult<UmbraArray> {
        Python::with_gil(|py| {
            let numpy = self.numpy_module.as_ref(py);
            let result = numpy.call_method1("multiply", (a.array.as_ref(py), b.array.as_ref(py)))?;
            self.wrap_numpy_array(result)
        })
    }

    /// Matrix multiplication (dot product)
    pub fn dot(&self, a: &UmbraArray, b: &UmbraArray) -> UmbraResult<UmbraArray> {
        Python::with_gil(|py| {
            let numpy = self.numpy_module.as_ref(py);
            let result = numpy.call_method1("dot", (a.array.as_ref(py), b.array.as_ref(py)))?;
            self.wrap_numpy_array(result)
        })
    }

    /// Array transpose
    pub fn transpose(&self, array: &UmbraArray) -> UmbraResult<UmbraArray> {
        Python::with_gil(|py| {
            let result = array.array.as_ref(py).call_method0("transpose")?;
            self.wrap_numpy_array(result)
        })
    }

    /// Array reshape
    pub fn reshape(&self, array: &UmbraArray, new_shape: &[usize]) -> UmbraResult<UmbraArray> {
        Python::with_gil(|py| {
            let shape_tuple = new_shape.iter().map(|&s| s as i64).collect::<Vec<_>>();
            let result = array.array.as_ref(py).call_method1("reshape", (shape_tuple,))?;
            self.wrap_numpy_array(result)
        })
    }

    /// Get array sum
    pub fn sum(&self, array: &UmbraArray, axis: Option<usize>) -> UmbraResult<f64> {
        Python::with_gil(|py| {
            let result = if let Some(ax) = axis {
                array.array.as_ref(py).call_method1("sum", (ax as i64,))?
            } else {
                array.array.as_ref(py).call_method0("sum")?
            };

            // Extract as f64
            let value: f64 = result.extract()?;
            Ok(value)
        })
    }

    /// Get array mean
    pub fn mean(&self, array: &UmbraArray, axis: Option<usize>) -> UmbraResult<f64> {
        Python::with_gil(|py| {
            let result = if let Some(ax) = axis {
                array.array.as_ref(py).call_method1("mean", (ax as i64,))?
            } else {
                array.array.as_ref(py).call_method0("mean")?
            };

            // Extract as f64
            let value: f64 = result.extract()?;
            Ok(value)
        })
    }

    /// Convert array to Umbra data
    pub fn to_vec(&self, array: &UmbraArray) -> UmbraResult<Vec<f64>> {
        Python::with_gil(|py| {
            let flattened = array.array.as_ref(py).call_method0("flatten")?;
            let list = flattened.call_method0("tolist")?;
            let vec: Vec<f64> = list.extract()?;
            Ok(vec)
        })
    }

    /// Check NumPy version
    pub fn version(&self) -> UmbraResult<String> {
        Python::with_gil(|py| {
            let version = self.numpy_module.as_ref(py).getattr("__version__")?;
            Ok(version.extract()?)
        })
    }

    /// Wrap a NumPy array object
    fn wrap_numpy_array(&self, array: &PyAny) -> UmbraResult<UmbraArray> {
        let shape: Vec<usize> = array.getattr("shape")?
            .extract::<Vec<i64>>()?
            .into_iter()
            .map(|x| x as usize)
            .collect();
        
        let dtype: String = array.getattr("dtype")?
            .call_method0("__str__")?
            .extract()?;
        
        let ndim: usize = array.getattr("ndim")?.extract::<i64>()? as usize;

        Ok(UmbraArray {
            array: array.into(),
            shape,
            dtype,
            ndim,
        })
    }

    /// Convert NumPy dtype enum to string
    fn dtype_to_string(&self, dtype: &NumPyDType) -> &'static str {
        match dtype {
            NumPyDType::Float32 => "float32",
            NumPyDType::Float64 => "float64",
            NumPyDType::Int32 => "int32",
            NumPyDType::Int64 => "int64",
            NumPyDType::Bool => "bool",
            NumPyDType::Complex64 => "complex64",
            NumPyDType::Complex128 => "complex128",
        }
    }
}

impl UmbraArray {
    /// Get array shape
    pub fn shape(&self) -> &[usize] {
        &self.shape
    }

    /// Get array data type
    pub fn dtype(&self) -> &str {
        &self.dtype
    }

    /// Get number of dimensions
    pub fn ndim(&self) -> usize {
        self.ndim
    }

    /// Get total number of elements
    pub fn size(&self) -> usize {
        self.shape.iter().product()
    }

    /// Check if array is empty
    pub fn is_empty(&self) -> bool {
        self.size() == 0
    }
}

/// NumPy utility functions
pub mod utils {
    use super::*;

    /// Check if NumPy is available
    pub fn is_numpy_available() -> bool {
        Python::with_gil(|py| {
            py.import("numpy").is_ok()
        })
    }

    /// Get NumPy configuration info
    pub fn get_numpy_config() -> UmbraResult<HashMap<String, String>> {
        Python::with_gil(|py| {
            let numpy = py.import("numpy")?;
            let config = numpy.call_method0("show_config")?;
            
            // This is a simplified version - in practice, you'd parse the config output
            let mut info = HashMap::new();
            info.insert("version".to_string(), numpy.getattr("__version__")?.extract()?);
            
            Ok(info)
        })
    }
}
