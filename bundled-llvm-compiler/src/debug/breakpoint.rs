/// Breakpoint management for Umbra debugger
/// 
/// Handles creation, removal, and management of breakpoints during debugging sessions.

use crate::error::UmbraResult;
use std::collections::HashMap;
use std::path::{Path, PathBuf};
use serde::{Deserialize, Serialize};

/// Breakpoint manager
pub struct BreakpointManager {
    /// Map of breakpoint ID to breakpoint
    breakpoints: HashMap<u32, Breakpoint>,
    
    /// Next available breakpoint ID
    next_id: u32,
    
    /// Breakpoints by file for quick lookup
    by_file: HashMap<PathBuf, Vec<u32>>,
}

/// Breakpoint definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Breakpoint {
    /// Unique breakpoint ID
    pub id: u32,
    
    /// Source file path
    pub file: PathBuf,
    
    /// Line number (1-based)
    pub line: u32,
    
    /// Column number (1-based, optional)
    pub column: Option<u32>,
    
    /// Breakpoint type
    pub breakpoint_type: BreakpointType,
    
    /// Whether breakpoint is enabled
    pub enabled: bool,
    
    /// Condition for conditional breakpoints
    pub condition: Option<String>,
    
    /// Hit count for breakpoint statistics
    pub hit_count: u32,
    
    /// Log message for logpoints
    pub log_message: Option<String>,
}

/// Types of breakpoints
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum BreakpointType {
    /// Standard line breakpoint
    Line,
    /// Conditional breakpoint (breaks when condition is true)
    Conditional,
    /// Function breakpoint (breaks when function is called)
    Function { function_name: String },
    /// Exception breakpoint (breaks when exception is thrown)
    Exception { exception_type: Option<String> },
    /// Logpoint (logs message without stopping)
    Logpoint,
    /// Data breakpoint (breaks when variable changes)
    Data { variable_name: String },
}

/// Breakpoint hit information
#[derive(Debug, Clone)]
pub struct BreakpointHit {
    /// Breakpoint that was hit
    pub breakpoint: Breakpoint,
    
    /// Current variable values
    pub variables: HashMap<String, String>,
    
    /// Stack trace at hit location
    pub stack_trace: Vec<String>,
}

impl BreakpointManager {
    /// Create a new breakpoint manager
    pub fn new() -> Self {
        Self {
            breakpoints: HashMap::new(),
            next_id: 1,
            by_file: HashMap::new(),
        }
    }
    
    /// Add a line breakpoint
    pub fn add(&mut self, file: &Path, line: u32) -> UmbraResult<u32> {
        self.add_with_type(file, line, None, BreakpointType::Line, None, None)
    }
    
    /// Add a conditional breakpoint
    pub fn add_conditional(&mut self, file: &Path, line: u32, condition: String) -> UmbraResult<u32> {
        self.add_with_type(
            file,
            line,
            None,
            BreakpointType::Conditional,
            Some(condition),
            None,
        )
    }
    
    /// Add a function breakpoint
    pub fn add_function(&mut self, function_name: String) -> UmbraResult<u32> {
        self.add_with_type(
            Path::new(""),
            0,
            None,
            BreakpointType::Function { function_name },
            None,
            None,
        )
    }
    
    /// Add a logpoint
    pub fn add_logpoint(&mut self, file: &Path, line: u32, message: String) -> UmbraResult<u32> {
        self.add_with_type(
            file,
            line,
            None,
            BreakpointType::Logpoint,
            None,
            Some(message),
        )
    }
    
    /// Add a data breakpoint
    pub fn add_data(&mut self, variable_name: String) -> UmbraResult<u32> {
        self.add_with_type(
            Path::new(""),
            0,
            None,
            BreakpointType::Data { variable_name },
            None,
            None,
        )
    }
    
    /// Add breakpoint with full configuration
    fn add_with_type(
        &mut self,
        file: &Path,
        line: u32,
        column: Option<u32>,
        breakpoint_type: BreakpointType,
        condition: Option<String>,
        log_message: Option<String>,
    ) -> UmbraResult<u32> {
        let id = self.next_id;
        self.next_id += 1;
        
        let breakpoint = Breakpoint {
            id,
            file: file.to_path_buf(),
            line,
            column,
            breakpoint_type,
            enabled: true,
            condition,
            hit_count: 0,
            log_message,
        };
        
        // Add to main collection
        self.breakpoints.insert(id, breakpoint.clone());
        
        // Add to file index for quick lookup
        if !file.as_os_str().is_empty() {
            self.by_file
                .entry(file.to_path_buf())
                .or_default()
                .push(id);
        }
        
        println!("🔴 Breakpoint {} added: {}", id, self.format_breakpoint(&breakpoint));
        
        Ok(id)
    }
    
    /// Remove a breakpoint
    pub fn remove(&mut self, id: u32) -> UmbraResult<bool> {
        if let Some(breakpoint) = self.breakpoints.remove(&id) {
            // Remove from file index
            if let Some(file_breakpoints) = self.by_file.get_mut(&breakpoint.file) {
                file_breakpoints.retain(|&bp_id| bp_id != id);
                if file_breakpoints.is_empty() {
                    self.by_file.remove(&breakpoint.file);
                }
            }
            
            println!("❌ Breakpoint {id} removed");
            Ok(true)
        } else {
            Ok(false)
        }
    }
    
    /// Enable or disable a breakpoint
    pub fn set_enabled(&mut self, id: u32, enabled: bool) -> UmbraResult<bool> {
        if let Some(breakpoint) = self.breakpoints.get_mut(&id) {
            breakpoint.enabled = enabled;
            let status = if enabled { "enabled" } else { "disabled" };
            println!("🔄 Breakpoint {id} {status}");
            Ok(true)
        } else {
            Ok(false)
        }
    }
    
    /// Update breakpoint condition
    pub fn set_condition(&mut self, id: u32, condition: Option<String>) -> UmbraResult<bool> {
        if let Some(breakpoint) = self.breakpoints.get_mut(&id) {
            breakpoint.condition = condition;
            println!("🔄 Breakpoint {id} condition updated");
            Ok(true)
        } else {
            Ok(false)
        }
    }
    
    /// Get breakpoint by ID
    pub fn get(&self, id: u32) -> Option<&Breakpoint> {
        self.breakpoints.get(&id)
    }
    
    /// Get mutable breakpoint by ID
    pub fn get_mut(&mut self, id: u32) -> Option<&mut Breakpoint> {
        self.breakpoints.get_mut(&id)
    }
    
    /// List all breakpoints
    pub fn list(&self) -> Vec<&Breakpoint> {
        self.breakpoints.values().collect()
    }
    
    /// Get breakpoints for a specific file
    pub fn get_for_file(&self, file: &Path) -> Vec<&Breakpoint> {
        if let Some(ids) = self.by_file.get(file) {
            ids.iter()
                .filter_map(|&id| self.breakpoints.get(&id))
                .collect()
        } else {
            Vec::new()
        }
    }
    
    /// Check if there's a breakpoint at the given location
    pub fn has_breakpoint_at(&self, file: &Path, line: u32) -> Option<&Breakpoint> {
        self.get_for_file(file)
            .into_iter()
            .find(|bp| bp.line == line && bp.enabled)
    }
    
    /// Record a breakpoint hit
    pub fn record_hit(&mut self, id: u32) -> UmbraResult<()> {
        if let Some(breakpoint) = self.breakpoints.get_mut(&id) {
            breakpoint.hit_count += 1;
            println!("🎯 Breakpoint {} hit (count: {})", id, breakpoint.hit_count);
        }
        Ok(())
    }
    
    /// Clear all breakpoints
    pub fn clear_all(&mut self) {
        let count = self.breakpoints.len();
        self.breakpoints.clear();
        self.by_file.clear();
        println!("🧹 Cleared {count} breakpoints");
    }
    
    /// Clear breakpoints for a specific file
    pub fn clear_file(&mut self, file: &Path) -> usize {
        let mut removed_count = 0;
        
        if let Some(ids) = self.by_file.remove(file) {
            for id in ids {
                if self.breakpoints.remove(&id).is_some() {
                    removed_count += 1;
                }
            }
        }
        
        if removed_count > 0 {
            println!("🧹 Cleared {} breakpoints from {}", removed_count, file.display());
        }
        
        removed_count
    }
    
    /// Get breakpoint statistics
    pub fn get_statistics(&self) -> BreakpointStatistics {
        let total = self.breakpoints.len();
        let enabled = self.breakpoints.values().filter(|bp| bp.enabled).count();
        let disabled = total - enabled;
        let total_hits = self.breakpoints.values().map(|bp| bp.hit_count).sum();
        
        let mut by_type = HashMap::new();
        for breakpoint in self.breakpoints.values() {
            let type_name = match &breakpoint.breakpoint_type {
                BreakpointType::Line => "Line",
                BreakpointType::Conditional => "Conditional",
                BreakpointType::Function { .. } => "Function",
                BreakpointType::Exception { .. } => "Exception",
                BreakpointType::Logpoint => "Logpoint",
                BreakpointType::Data { .. } => "Data",
            };
            *by_type.entry(type_name.to_string()).or_insert(0) += 1;
        }
        
        BreakpointStatistics {
            total,
            enabled,
            disabled,
            total_hits,
            by_type,
        }
    }
    
    /// Format breakpoint for display
    fn format_breakpoint(&self, breakpoint: &Breakpoint) -> String {
        match &breakpoint.breakpoint_type {
            BreakpointType::Line => {
                format!("{}:{}", breakpoint.file.display(), breakpoint.line)
            }
            BreakpointType::Conditional => {
                format!(
                    "{}:{} (condition: {})",
                    breakpoint.file.display(),
                    breakpoint.line,
                    breakpoint.condition.as_deref().unwrap_or("unknown")
                )
            }
            BreakpointType::Function { function_name } => {
                format!("function: {function_name}")
            }
            BreakpointType::Exception { exception_type } => {
                format!(
                    "exception: {}",
                    exception_type.as_deref().unwrap_or("any")
                )
            }
            BreakpointType::Logpoint => {
                format!(
                    "{}:{} (log: {})",
                    breakpoint.file.display(),
                    breakpoint.line,
                    breakpoint.log_message.as_deref().unwrap_or("no message")
                )
            }
            BreakpointType::Data { variable_name } => {
                format!("data: {variable_name}")
            }
        }
    }
}

/// Breakpoint statistics
#[derive(Debug, Clone)]
pub struct BreakpointStatistics {
    /// Total number of breakpoints
    pub total: usize,
    /// Number of enabled breakpoints
    pub enabled: usize,
    /// Number of disabled breakpoints
    pub disabled: usize,
    /// Total hit count across all breakpoints
    pub total_hits: u32,
    /// Breakpoints by type
    pub by_type: HashMap<String, usize>,
}

impl Default for BreakpointManager {
    fn default() -> Self {
        Self::new()
    }
}
