/// Stack trace analysis for Umbra debugger
/// 
/// Provides stack frame inspection, call stack analysis, and function call tracking.

use crate::error::UmbraResult;
use std::collections::HashMap;
use std::path::PathBuf;
use serde::{Deserialize, Serialize};

/// Stack trace analyzer
pub struct StackTraceAnalyzer {
    /// Current call stack
    call_stack: Vec<StackFrame>,
    
    /// Maximum stack depth to track
    max_depth: usize,
    
    /// Function call statistics
    call_stats: HashMap<String, CallStatistics>,
}

/// Stack frame information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StackFrame {
    /// Frame index (0 = current frame)
    pub index: usize,
    
    /// Function name
    pub function_name: String,
    
    /// Source file
    pub file: PathBuf,
    
    /// Line number
    pub line: u32,
    
    /// Column number
    pub column: u32,
    
    /// Local variables in this frame
    pub locals: HashMap<String, String>,
    
    /// Function parameters
    pub parameters: HashMap<String, String>,
    
    /// Return address
    pub return_address: Option<String>,
    
    /// Frame pointer
    pub frame_pointer: Option<String>,
    
    /// Stack pointer
    pub stack_pointer: Option<String>,
}

/// Function call statistics
#[derive(Debug, Clone)]
pub struct CallStatistics {
    /// Function name
    pub function_name: String,
    
    /// Total number of calls
    pub call_count: u64,
    
    /// Total time spent in function (microseconds)
    pub total_time: u64,
    
    /// Average time per call (microseconds)
    pub average_time: u64,
    
    /// Maximum time for a single call (microseconds)
    pub max_time: u64,
    
    /// Minimum time for a single call (microseconds)
    pub min_time: u64,
    
    /// Number of recursive calls
    pub recursive_calls: u64,
}

/// Stack analysis result
#[derive(Debug, Clone)]
pub struct StackAnalysis {
    /// Total stack depth
    pub depth: usize,
    
    /// Recursive function calls detected
    pub recursive_calls: Vec<String>,
    
    /// Memory usage estimate
    pub memory_usage: usize,
    
    /// Potential stack overflow risk
    pub overflow_risk: bool,
    
    /// Call chain summary
    pub call_chain: Vec<String>,
}

impl StackTraceAnalyzer {
    /// Create a new stack trace analyzer
    pub fn new() -> Self {
        Self {
            call_stack: Vec::new(),
            max_depth: 1000,
            call_stats: HashMap::new(),
        }
    }
    
    /// Create with custom maximum depth
    pub fn with_max_depth(max_depth: usize) -> Self {
        Self {
            call_stack: Vec::new(),
            max_depth,
            call_stats: HashMap::new(),
        }
    }
    
    /// Get current stack trace
    pub fn get_current_trace(&self) -> UmbraResult<Vec<StackFrame>> {
        Ok(self.call_stack.clone())
    }
    
    /// Push a new frame onto the stack
    pub fn push_frame(&mut self, frame: StackFrame) -> UmbraResult<()> {
        if self.call_stack.len() >= self.max_depth {
            return Err(crate::error::UmbraError::Runtime(
                "Stack overflow: maximum depth exceeded".to_string()
            ));
        }
        
        // Update call statistics
        self.update_call_stats(&frame.function_name);
        
        self.call_stack.push(frame);
        Ok(())
    }
    
    /// Pop the top frame from the stack
    pub fn pop_frame(&mut self) -> Option<StackFrame> {
        self.call_stack.pop()
    }
    
    /// Get the current (top) frame
    pub fn current_frame(&self) -> Option<&StackFrame> {
        self.call_stack.last()
    }
    
    /// Get frame by index (0 = current frame)
    pub fn get_frame(&self, index: usize) -> Option<&StackFrame> {
        if index < self.call_stack.len() {
            Some(&self.call_stack[self.call_stack.len() - 1 - index])
        } else {
            None
        }
    }
    
    /// Get stack depth
    pub fn depth(&self) -> usize {
        self.call_stack.len()
    }
    
    /// Analyze current stack for issues
    pub fn analyze_stack(&self) -> StackAnalysis {
        let depth = self.call_stack.len();
        let mut recursive_calls = Vec::new();
        let mut call_chain = Vec::new();
        
        // Build call chain and detect recursion
        let mut function_counts = HashMap::new();
        for frame in &self.call_stack {
            call_chain.push(frame.function_name.clone());
            
            let count = function_counts.entry(frame.function_name.clone()).or_insert(0);
            *count += 1;
            
            if *count > 1 && !recursive_calls.contains(&frame.function_name) {
                recursive_calls.push(frame.function_name.clone());
            }
        }
        
        // Estimate memory usage (rough calculation)
        let memory_usage = depth * 1024; // Assume ~1KB per frame
        
        // Check for overflow risk
        let overflow_risk = depth > (self.max_depth * 8 / 10); // 80% of max depth
        
        StackAnalysis {
            depth,
            recursive_calls,
            memory_usage,
            overflow_risk,
            call_chain,
        }
    }
    
    /// Update call statistics for a function
    fn update_call_stats(&mut self, function_name: &str) {
        let stats = self.call_stats.entry(function_name.to_string()).or_insert(CallStatistics {
            function_name: function_name.to_string(),
            call_count: 0,
            total_time: 0,
            average_time: 0,
            max_time: 0,
            min_time: u64::MAX,
            recursive_calls: 0,
        });
        
        stats.call_count += 1;
        
        // Check for recursion
        if self.call_stack.iter().any(|frame| frame.function_name == function_name) {
            stats.recursive_calls += 1;
        }
    }
    
    /// Record function execution time
    pub fn record_execution_time(&mut self, function_name: &str, time_microseconds: u64) {
        if let Some(stats) = self.call_stats.get_mut(function_name) {
            stats.total_time += time_microseconds;
            stats.average_time = stats.total_time / stats.call_count;
            stats.max_time = stats.max_time.max(time_microseconds);
            stats.min_time = stats.min_time.min(time_microseconds);
        }
    }
    
    /// Get call statistics for a function
    pub fn get_call_stats(&self, function_name: &str) -> Option<&CallStatistics> {
        self.call_stats.get(function_name)
    }
    
    /// Get all call statistics
    pub fn get_all_call_stats(&self) -> &HashMap<String, CallStatistics> {
        &self.call_stats
    }
    
    /// Format stack trace for display
    pub fn format_stack_trace(&self, max_frames: Option<usize>) -> String {
        let frames_to_show = max_frames.unwrap_or(self.call_stack.len());
        let mut result = String::new();
        
        result.push_str("📚 Stack Trace:\n");
        
        for (i, frame) in self.call_stack.iter().rev().take(frames_to_show).enumerate() {
            result.push_str(&format!(
                "  #{}: {} at {}:{}:{}\n",
                i,
                frame.function_name,
                frame.file.display(),
                frame.line,
                frame.column
            ));
            
            // Show local variables if any
            if !frame.locals.is_empty() {
                result.push_str("      Locals:\n");
                for (name, value) in &frame.locals {
                    result.push_str(&format!("        {name} = {value}\n"));
                }
            }
        }
        
        if self.call_stack.len() > frames_to_show {
            result.push_str(&format!("  ... and {} more frames\n", self.call_stack.len() - frames_to_show));
        }
        
        result
    }
    
    /// Format call statistics for display
    pub fn format_call_stats(&self) -> String {
        let mut result = String::new();
        result.push_str("📊 Function Call Statistics:\n");
        
        let mut stats_vec: Vec<_> = self.call_stats.values().collect();
        stats_vec.sort_by(|a, b| b.call_count.cmp(&a.call_count));
        
        for stats in stats_vec {
            result.push_str(&format!(
                "  {}: {} calls, avg {}μs, total {}μs",
                stats.function_name,
                stats.call_count,
                stats.average_time,
                stats.total_time
            ));
            
            if stats.recursive_calls > 0 {
                result.push_str(&format!(" (recursive: {})", stats.recursive_calls));
            }
            
            result.push('\n');
        }
        
        result
    }
    
    /// Clear all stack frames
    pub fn clear(&mut self) {
        self.call_stack.clear();
    }
    
    /// Reset call statistics
    pub fn reset_stats(&mut self) {
        self.call_stats.clear();
    }
    
    /// Set maximum stack depth
    pub fn set_max_depth(&mut self, max_depth: usize) {
        self.max_depth = max_depth;
    }
    
    /// Check if stack is empty
    pub fn is_empty(&self) -> bool {
        self.call_stack.is_empty()
    }
    
    /// Get function call hierarchy
    pub fn get_call_hierarchy(&self) -> Vec<String> {
        self.call_stack.iter().map(|frame| frame.function_name.clone()).collect()
    }
    
    /// Find frames containing a specific function
    pub fn find_frames_with_function(&self, function_name: &str) -> Vec<usize> {
        self.call_stack
            .iter()
            .enumerate()
            .filter_map(|(i, frame)| {
                if frame.function_name == function_name {
                    Some(self.call_stack.len() - 1 - i) // Convert to frame index
                } else {
                    None
                }
            })
            .collect()
    }
}

impl StackFrame {
    /// Create a new stack frame
    pub fn new(
        index: usize,
        function_name: String,
        file: PathBuf,
        line: u32,
        column: u32,
    ) -> Self {
        Self {
            index,
            function_name,
            file,
            line,
            column,
            locals: HashMap::new(),
            parameters: HashMap::new(),
            return_address: None,
            frame_pointer: None,
            stack_pointer: None,
        }
    }
    
    /// Add a local variable to the frame
    pub fn add_local(&mut self, name: String, value: String) {
        self.locals.insert(name, value);
    }
    
    /// Add a parameter to the frame
    pub fn add_parameter(&mut self, name: String, value: String) {
        self.parameters.insert(name, value);
    }
    
    /// Format frame for display
    pub fn format(&self) -> String {
        format!(
            "#{}: {} at {}:{}:{}",
            self.index,
            self.function_name,
            self.file.display(),
            self.line,
            self.column
        )
    }
}

impl Default for StackTraceAnalyzer {
    fn default() -> Self {
        Self::new()
    }
}
