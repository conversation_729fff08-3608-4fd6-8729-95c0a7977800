/// Debug Adapter Protocol (DAP) server implementation for Umbra
/// 
/// This module provides a complete DAP server that enables debugging
/// Umbra programs in VS Code and other DAP-compatible editors.

use crate::error::{UmbraError, UmbraResult};
use crate::debug::{Debugger, <PERSON>pointManager, StackFrame, Variable, DebugEvent};
use serde::{Deserialize, Serialize};
use serde_json::{Value, json};
use std::collections::HashMap;
use std::io::{BufRead, BufReader, Write};
use std::process::{Child, Command, Stdio};
use std::sync::{Arc, Mutex};
use std::thread;
use tokio::sync::mpsc;

/// DAP server for Umbra debugging
pub struct DAPServer {
    /// Underlying debugger
    debugger: Arc<Mutex<Debugger>>,
    /// Sequence number for messages
    seq: u32,
    /// Active debug session
    session: Option<DebugSession>,
    /// Message sender
    sender: Option<mpsc::UnboundedSender<DAPMessage>>,
    /// Capabilities
    capabilities: ServerCapabilities,
}

/// Debug session state
#[derive(Debug)]
pub struct DebugSession {
    /// Program being debugged
    pub program_path: String,
    /// Program arguments
    pub args: Vec<String>,
    /// Working directory
    pub cwd: String,
    /// Environment variables
    pub env: HashMap<String, String>,
    /// Whether to stop on entry
    pub stop_on_entry: bool,
    /// Running process
    pub process: Option<Child>,
}

/// Server capabilities
#[derive(Debug, Clone, Serialize)]
pub struct ServerCapabilities {
    /// Supports configuration done request
    #[serde(rename = "supportsConfigurationDoneRequest")]
    pub supports_configuration_done_request: bool,
    /// Supports function breakpoints
    #[serde(rename = "supportsFunctionBreakpoints")]
    pub supports_function_breakpoints: bool,
    /// Supports conditional breakpoints
    #[serde(rename = "supportsConditionalBreakpoints")]
    pub supports_conditional_breakpoints: bool,
    /// Supports hit conditional breakpoints
    #[serde(rename = "supportsHitConditionalBreakpoints")]
    pub supports_hit_conditional_breakpoints: bool,
    /// Supports evaluate for hovers
    #[serde(rename = "supportsEvaluateForHovers")]
    pub supports_evaluate_for_hovers: bool,
    /// Supports step back
    #[serde(rename = "supportsStepBack")]
    pub supports_step_back: bool,
    /// Supports set variable
    #[serde(rename = "supportsSetVariable")]
    pub supports_set_variable: bool,
    /// Supports restart frame
    #[serde(rename = "supportsRestartFrame")]
    pub supports_restart_frame: bool,
    /// Supports goto targets request
    #[serde(rename = "supportsGotoTargetsRequest")]
    pub supports_goto_targets_request: bool,
    /// Supports step in targets request
    #[serde(rename = "supportsStepInTargetsRequest")]
    pub supports_step_in_targets_request: bool,
    /// Supports completions request
    #[serde(rename = "supportsCompletionsRequest")]
    pub supports_completions_request: bool,
    /// Supports modules request
    #[serde(rename = "supportsModulesRequest")]
    pub supports_modules_request: bool,
    /// Supports restart request
    #[serde(rename = "supportsRestartRequest")]
    pub supports_restart_request: bool,
    /// Supports exception options
    #[serde(rename = "supportsExceptionOptions")]
    pub supports_exception_options: bool,
    /// Supports value formatting options
    #[serde(rename = "supportsValueFormattingOptions")]
    pub supports_value_formatting_options: bool,
    /// Supports exception info request
    #[serde(rename = "supportsExceptionInfoRequest")]
    pub supports_exception_info_request: bool,
    /// Supports terminate debuggee
    #[serde(rename = "supportTerminateDebuggee")]
    pub support_terminate_debuggee: bool,
    /// Supports delayed stack trace loading
    #[serde(rename = "supportsDelayedStackTraceLoading")]
    pub supports_delayed_stack_trace_loading: bool,
    /// Supports loaded sources request
    #[serde(rename = "supportsLoadedSourcesRequest")]
    pub supports_loaded_sources_request: bool,
    /// Supports log points
    #[serde(rename = "supportsLogPoints")]
    pub supports_log_points: bool,
    /// Supports terminate threads request
    #[serde(rename = "supportsTerminateThreadsRequest")]
    pub supports_terminate_threads_request: bool,
    /// Supports set expression
    #[serde(rename = "supportsSetExpression")]
    pub supports_set_expression: bool,
    /// Supports terminate request
    #[serde(rename = "supportsTerminateRequest")]
    pub supports_terminate_request: bool,
    /// Supports data breakpoints
    #[serde(rename = "supportsDataBreakpoints")]
    pub supports_data_breakpoints: bool,
    /// Supports read memory request
    #[serde(rename = "supportsReadMemoryRequest")]
    pub supports_read_memory_request: bool,
    /// Supports disassemble request
    #[serde(rename = "supportsDisassembleRequest")]
    pub supports_disassemble_request: bool,
    /// Supports cancel request
    #[serde(rename = "supportsCancelRequest")]
    pub supports_cancel_request: bool,
    /// Supports breakpoint locations request
    #[serde(rename = "supportsBreakpointLocationsRequest")]
    pub supports_breakpoint_locations_request: bool,
    /// Supports clipboard context
    #[serde(rename = "supportsClipboardContext")]
    pub supports_clipboard_context: bool,
}

/// DAP message types
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum DAPMessage {
    #[serde(rename = "request")]
    Request(DAPRequest),
    #[serde(rename = "response")]
    Response(DAPResponse),
    #[serde(rename = "event")]
    Event(DAPEvent),
}

/// DAP request
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DAPRequest {
    pub seq: u32,
    pub command: String,
    pub arguments: Option<Value>,
}

/// DAP response
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DAPResponse {
    pub request_seq: u32,
    pub seq: u32,
    pub command: String,
    pub success: bool,
    pub message: Option<String>,
    pub body: Option<Value>,
}

/// DAP event
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DAPEvent {
    pub seq: u32,
    pub event: String,
    pub body: Option<Value>,
}

impl DAPServer {
    /// Create a new DAP server
    pub fn new() -> Self {
        Self {
            debugger: Arc::new(Mutex::new(Debugger::new())),
            seq: 0,
            session: None,
            sender: None,
            capabilities: ServerCapabilities::default(),
        }
    }

    /// Start the DAP server
    pub async fn start(&mut self) -> UmbraResult<()> {
        println!("🐛 Starting Umbra Debug Adapter Protocol server");

        let (tx, mut rx) = mpsc::unbounded_channel::<DAPMessage>();
        self.sender = Some(tx);

        // Start message processing loop
        let debugger = Arc::clone(&self.debugger);
        tokio::spawn(async move {
            while let Some(message) = rx.recv().await {
                // Process DAP messages
                match message {
                    DAPMessage::Request(request) => {
                        // Handle request
                    }
                    DAPMessage::Event(event) => {
                        // Handle event
                    }
                    _ => {}
                }
            }
        });

        // Start stdin/stdout communication
        self.start_stdio_communication().await?;

        Ok(())
    }

    /// Start stdio communication with the client
    async fn start_stdio_communication(&mut self) -> UmbraResult<()> {
        let stdin = std::io::stdin();
        let mut stdout = std::io::stdout();

        // Read messages from stdin
        let reader = BufReader::new(stdin);
        for line in reader.lines() {
            let line = line.map_err(|e| UmbraError::Runtime(format!("Failed to read stdin: {}", e)))?;
            
            if line.starts_with("Content-Length:") {
                // Parse content length
                let content_length: usize = line
                    .strip_prefix("Content-Length: ")
                    .unwrap_or("0")
                    .trim()
                    .parse()
                    .unwrap_or(0);

                // Read empty line
                let _ = reader.lines().next();

                // Read message content
                let mut content = String::new();
                for _ in 0..content_length {
                    if let Some(Ok(ch)) = reader.lines().next().map(|l| l.chars().next()) {
                        content.push(ch.unwrap_or('\0'));
                    }
                }

                // Parse and handle message
                if let Ok(message) = serde_json::from_str::<DAPMessage>(&content) {
                    self.handle_message(message, &mut stdout).await?;
                }
            }
        }

        Ok(())
    }

    /// Handle a DAP message
    async fn handle_message(&mut self, message: DAPMessage, stdout: &mut std::io::Stdout) -> UmbraResult<()> {
        match message {
            DAPMessage::Request(request) => {
                let response = self.handle_request(request).await?;
                self.send_response(response, stdout)?;
            }
            DAPMessage::Event(event) => {
                self.handle_event(event).await?;
            }
            _ => {}
        }

        Ok(())
    }

    /// Handle a DAP request
    async fn handle_request(&mut self, request: DAPRequest) -> UmbraResult<DAPResponse> {
        let mut response = DAPResponse {
            request_seq: request.seq,
            seq: self.next_seq(),
            command: request.command.clone(),
            success: true,
            message: None,
            body: None,
        };

        match request.command.as_str() {
            "initialize" => {
                response.body = Some(json!(self.capabilities));
            }
            "launch" => {
                if let Some(args) = request.arguments {
                    self.handle_launch(args).await?;
                }
            }
            "attach" => {
                if let Some(args) = request.arguments {
                    self.handle_attach(args).await?;
                }
            }
            "setBreakpoints" => {
                if let Some(args) = request.arguments {
                    let breakpoints = self.handle_set_breakpoints(args).await?;
                    response.body = Some(json!({ "breakpoints": breakpoints }));
                }
            }
            "configurationDone" => {
                self.handle_configuration_done().await?;
            }
            "continue" => {
                let result = self.handle_continue().await?;
                response.body = Some(json!(result));
            }
            "next" => {
                self.handle_next().await?;
            }
            "stepIn" => {
                self.handle_step_in().await?;
            }
            "stepOut" => {
                self.handle_step_out().await?;
            }
            "pause" => {
                self.handle_pause().await?;
            }
            "stackTrace" => {
                if let Some(args) = request.arguments {
                    let stack_trace = self.handle_stack_trace(args).await?;
                    response.body = Some(json!(stack_trace));
                }
            }
            "scopes" => {
                if let Some(args) = request.arguments {
                    let scopes = self.handle_scopes(args).await?;
                    response.body = Some(json!(scopes));
                }
            }
            "variables" => {
                if let Some(args) = request.arguments {
                    let variables = self.handle_variables(args).await?;
                    response.body = Some(json!(variables));
                }
            }
            "evaluate" => {
                if let Some(args) = request.arguments {
                    let result = self.handle_evaluate(args).await?;
                    response.body = Some(json!(result));
                }
            }
            "disconnect" => {
                self.handle_disconnect().await?;
            }
            "terminate" => {
                self.handle_terminate().await?;
            }
            _ => {
                response.success = false;
                response.message = Some(format!("Unknown command: {}", request.command));
            }
        }

        Ok(response)
    }

    /// Handle launch request
    async fn handle_launch(&mut self, args: Value) -> UmbraResult<()> {
        let program = args["program"].as_str()
            .ok_or_else(|| UmbraError::Runtime("Missing program path".to_string()))?;
        
        let program_args: Vec<String> = args["args"].as_array()
            .unwrap_or(&vec![])
            .iter()
            .filter_map(|v| v.as_str().map(|s| s.to_string()))
            .collect();

        let cwd = args["cwd"].as_str().unwrap_or(".").to_string();
        let stop_on_entry = args["stopOnEntry"].as_bool().unwrap_or(false);

        let mut env = HashMap::new();
        if let Some(env_obj) = args["env"].as_object() {
            for (key, value) in env_obj {
                if let Some(value_str) = value.as_str() {
                    env.insert(key.clone(), value_str.to_string());
                }
            }
        }

        self.session = Some(DebugSession {
            program_path: program.to_string(),
            args: program_args,
            cwd,
            env,
            stop_on_entry,
            process: None,
        });

        // Start the debugged program
        self.start_debugged_program().await?;

        // Send initialized event
        self.send_event("initialized", None).await?;

        Ok(())
    }

    /// Start the debugged program
    async fn start_debugged_program(&mut self) -> UmbraResult<()> {
        if let Some(ref mut session) = self.session {
            let mut cmd = Command::new("umbra");
            cmd.arg("run")
                .arg(&session.program_path)
                .args(&session.args)
                .current_dir(&session.cwd)
                .envs(&session.env)
                .stdin(Stdio::piped())
                .stdout(Stdio::piped())
                .stderr(Stdio::piped());

            let process = cmd.spawn()
                .map_err(|e| UmbraError::Runtime(format!("Failed to start program: {}", e)))?;

            session.process = Some(process);

            // Initialize debugger with the program
            let mut debugger = self.debugger.lock().unwrap();
            debugger.attach_to_program(&session.program_path)?;

            if session.stop_on_entry {
                debugger.pause()?;
                self.send_event("stopped", Some(json!({
                    "reason": "entry",
                    "threadId": 1
                }))).await?;
            }
        }

        Ok(())
    }

    /// Handle set breakpoints request
    async fn handle_set_breakpoints(&mut self, args: Value) -> UmbraResult<Vec<Value>> {
        let source = &args["source"];
        let file_path = source["path"].as_str()
            .ok_or_else(|| UmbraError::Runtime("Missing source path".to_string()))?;

        let breakpoints = args["breakpoints"].as_array()
            .ok_or_else(|| UmbraError::Runtime("Missing breakpoints array".to_string()))?;

        let mut result_breakpoints = Vec::new();
        let mut debugger = self.debugger.lock().unwrap();

        // Clear existing breakpoints for this file
        debugger.clear_breakpoints_for_file(file_path)?;

        // Set new breakpoints
        for bp in breakpoints {
            let line = bp["line"].as_u64()
                .ok_or_else(|| UmbraError::Runtime("Missing breakpoint line".to_string()))?;
            
            let condition = bp["condition"].as_str().map(|s| s.to_string());
            let hit_condition = bp["hitCondition"].as_str().map(|s| s.to_string());
            let log_message = bp["logMessage"].as_str().map(|s| s.to_string());

            match debugger.set_breakpoint(file_path, line as usize, condition, hit_condition, log_message) {
                Ok(bp_id) => {
                    result_breakpoints.push(json!({
                        "id": bp_id,
                        "verified": true,
                        "line": line
                    }));
                }
                Err(_) => {
                    result_breakpoints.push(json!({
                        "verified": false,
                        "line": line,
                        "message": "Failed to set breakpoint"
                    }));
                }
            }
        }

        Ok(result_breakpoints)
    }

    /// Handle continue request
    async fn handle_continue(&mut self) -> UmbraResult<Value> {
        let mut debugger = self.debugger.lock().unwrap();
        debugger.continue_execution()?;

        Ok(json!({
            "allThreadsContinued": true
        }))
    }

    /// Handle next (step over) request
    async fn handle_next(&mut self) -> UmbraResult<()> {
        let mut debugger = self.debugger.lock().unwrap();
        debugger.step_over()?;

        self.send_event("stopped", Some(json!({
            "reason": "step",
            "threadId": 1
        }))).await?;

        Ok(())
    }

    /// Handle step in request
    async fn handle_step_in(&mut self) -> UmbraResult<()> {
        let mut debugger = self.debugger.lock().unwrap();
        debugger.step_into()?;

        self.send_event("stopped", Some(json!({
            "reason": "step",
            "threadId": 1
        }))).await?;

        Ok(())
    }

    /// Handle step out request
    async fn handle_step_out(&mut self) -> UmbraResult<()> {
        let mut debugger = self.debugger.lock().unwrap();
        debugger.step_out()?;

        self.send_event("stopped", Some(json!({
            "reason": "step",
            "threadId": 1
        }))).await?;

        Ok(())
    }

    /// Handle pause request
    async fn handle_pause(&mut self) -> UmbraResult<()> {
        let mut debugger = self.debugger.lock().unwrap();
        debugger.pause()?;

        self.send_event("stopped", Some(json!({
            "reason": "pause",
            "threadId": 1
        }))).await?;

        Ok(())
    }

    /// Handle stack trace request
    async fn handle_stack_trace(&mut self, args: Value) -> UmbraResult<Value> {
        let thread_id = args["threadId"].as_u64().unwrap_or(1);
        let start_frame = args["startFrame"].as_u64().unwrap_or(0) as usize;
        let levels = args["levels"].as_u64().map(|l| l as usize);

        let debugger = self.debugger.lock().unwrap();
        let stack_frames = debugger.get_stack_trace(thread_id as usize)?;

        let end_frame = if let Some(levels) = levels {
            (start_frame + levels).min(stack_frames.len())
        } else {
            stack_frames.len()
        };

        let frames: Vec<Value> = stack_frames[start_frame..end_frame]
            .iter()
            .enumerate()
            .map(|(i, frame)| json!({
                "id": start_frame + i,
                "name": frame.function_name,
                "source": {
                    "path": frame.file_path
                },
                "line": frame.line,
                "column": frame.column
            }))
            .collect();

        Ok(json!({
            "stackFrames": frames,
            "totalFrames": stack_frames.len()
        }))
    }

    /// Handle scopes request
    async fn handle_scopes(&mut self, args: Value) -> UmbraResult<Value> {
        let frame_id = args["frameId"].as_u64().unwrap_or(0) as usize;

        let scopes = vec![
            json!({
                "name": "Local",
                "variablesReference": frame_id * 1000 + 1,
                "expensive": false
            }),
            json!({
                "name": "Global",
                "variablesReference": frame_id * 1000 + 2,
                "expensive": false
            })
        ];

        Ok(json!({
            "scopes": scopes
        }))
    }

    /// Handle variables request
    async fn handle_variables(&mut self, args: Value) -> UmbraResult<Value> {
        let variables_reference = args["variablesReference"].as_u64().unwrap_or(0) as usize;
        
        let debugger = self.debugger.lock().unwrap();
        let variables = debugger.get_variables(variables_reference)?;

        let vars: Vec<Value> = variables
            .iter()
            .map(|var| json!({
                "name": var.name,
                "value": var.value,
                "type": var.var_type,
                "variablesReference": if var.has_children { var.reference } else { 0 }
            }))
            .collect();

        Ok(json!({
            "variables": vars
        }))
    }

    /// Handle evaluate request
    async fn handle_evaluate(&mut self, args: Value) -> UmbraResult<Value> {
        let expression = args["expression"].as_str()
            .ok_or_else(|| UmbraError::Runtime("Missing expression".to_string()))?;
        
        let frame_id = args["frameId"].as_u64().map(|id| id as usize);
        let context = args["context"].as_str().unwrap_or("watch");

        let mut debugger = self.debugger.lock().unwrap();
        let result = debugger.evaluate_expression(expression, frame_id)?;

        Ok(json!({
            "result": result.value,
            "type": result.var_type,
            "variablesReference": if result.has_children { result.reference } else { 0 }
        }))
    }

    /// Handle configuration done request
    async fn handle_configuration_done(&mut self) -> UmbraResult<()> {
        // Configuration is complete, start execution if not stopped on entry
        if let Some(ref session) = self.session {
            if !session.stop_on_entry {
                let mut debugger = self.debugger.lock().unwrap();
                debugger.continue_execution()?;
            }
        }

        Ok(())
    }

    /// Handle attach request
    async fn handle_attach(&mut self, _args: Value) -> UmbraResult<()> {
        // Attach to running process (not implemented for now)
        Err(UmbraError::Runtime("Attach not implemented".to_string()))
    }

    /// Handle disconnect request
    async fn handle_disconnect(&mut self) -> UmbraResult<()> {
        if let Some(ref mut session) = self.session {
            if let Some(ref mut process) = session.process {
                let _ = process.kill();
            }
        }

        self.send_event("terminated", None).await?;
        Ok(())
    }

    /// Handle terminate request
    async fn handle_terminate(&mut self) -> UmbraResult<()> {
        self.handle_disconnect().await
    }

    /// Handle DAP event
    async fn handle_event(&mut self, _event: DAPEvent) -> UmbraResult<()> {
        // Handle events from debugger
        Ok(())
    }

    /// Send a response
    fn send_response(&mut self, response: DAPResponse, stdout: &mut std::io::Stdout) -> UmbraResult<()> {
        let json = serde_json::to_string(&DAPMessage::Response(response))
            .map_err(|e| UmbraError::Runtime(format!("Failed to serialize response: {}", e)))?;

        let content_length = json.len();
        let message = format!("Content-Length: {}\r\n\r\n{}", content_length, json);

        stdout.write_all(message.as_bytes())
            .map_err(|e| UmbraError::Runtime(format!("Failed to write response: {}", e)))?;
        stdout.flush()
            .map_err(|e| UmbraError::Runtime(format!("Failed to flush stdout: {}", e)))?;

        Ok(())
    }

    /// Send an event
    async fn send_event(&mut self, event: &str, body: Option<Value>) -> UmbraResult<()> {
        let event = DAPEvent {
            seq: self.next_seq(),
            event: event.to_string(),
            body,
        };

        if let Some(ref sender) = self.sender {
            sender.send(DAPMessage::Event(event))
                .map_err(|e| UmbraError::Runtime(format!("Failed to send event: {}", e)))?;
        }

        Ok(())
    }

    /// Get next sequence number
    fn next_seq(&mut self) -> u32 {
        self.seq += 1;
        self.seq
    }
}

impl Default for ServerCapabilities {
    fn default() -> Self {
        Self {
            supports_configuration_done_request: true,
            supports_function_breakpoints: true,
            supports_conditional_breakpoints: true,
            supports_hit_conditional_breakpoints: true,
            supports_evaluate_for_hovers: true,
            supports_step_back: false,
            supports_set_variable: true,
            supports_restart_frame: false,
            supports_goto_targets_request: false,
            supports_step_in_targets_request: false,
            supports_completions_request: true,
            supports_modules_request: true,
            supports_restart_request: true,
            supports_exception_options: true,
            supports_value_formatting_options: true,
            supports_exception_info_request: true,
            support_terminate_debuggee: true,
            supports_delayed_stack_trace_loading: false,
            supports_loaded_sources_request: true,
            supports_log_points: true,
            supports_terminate_threads_request: false,
            supports_set_expression: true,
            supports_terminate_request: true,
            supports_data_breakpoints: false,
            supports_read_memory_request: false,
            supports_disassemble_request: false,
            supports_cancel_request: true,
            supports_breakpoint_locations_request: true,
            supports_clipboard_context: false,
        }
    }
}

/// Start the DAP server
pub async fn start_dap_server() -> UmbraResult<()> {
    let mut server = DAPServer::new();
    server.start().await
}
