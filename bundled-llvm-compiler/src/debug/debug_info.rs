/// Debug information generation and management for Umbra
/// 
/// <PERSON><PERSON> creation, storage, and retrieval of debug information for compiled Umbra programs.

use crate::error::UmbraResult;
use crate::parser::ast::{Program, Statement, FunctionDef};
use std::collections::HashMap;
use std::path::{Path, PathBuf};
use serde::{Deserialize, Serialize};

/// Debug information container
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DebugInfo {
    /// Source file mappings
    pub source_files: HashMap<u32, SourceFile>,
    
    /// Function debug information
    pub functions: HashMap<String, FunctionDebugInfo>,
    
    /// Variable debug information
    pub variables: HashMap<String, VariableDebugInfo>,
    
    /// Line number mappings
    pub line_mappings: Vec<LineMapping>,
    
    /// Type information
    pub types: HashMap<String, TypeDebugInfo>,
    
    /// Compilation unit information
    pub compilation_unit: CompilationUnit,
}

/// Source file information
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct SourceFile {
    /// File ID
    pub id: u32,
    
    /// File path
    pub path: PathBuf,
    
    /// File content hash
    pub hash: String,
    
    /// Line count
    pub line_count: u32,
    
    /// Character count
    pub char_count: u32,
}

/// Function debug information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FunctionDebugInfo {
    /// Function name
    pub name: String,
    
    /// Mangled name (if any)
    pub mangled_name: Option<String>,
    
    /// Source location
    pub location: SourceLocation,
    
    /// Parameter information
    pub parameters: Vec<ParameterDebugInfo>,
    
    /// Local variables
    pub locals: Vec<VariableDebugInfo>,
    
    /// Return type
    pub return_type: String,
    
    /// Function size in bytes
    pub size: u32,
    
    /// Entry point address
    pub entry_address: Option<u64>,
}

/// Variable debug information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VariableDebugInfo {
    /// Variable name
    pub name: String,
    
    /// Variable type
    pub var_type: String,
    
    /// Source location
    pub location: SourceLocation,
    
    /// Memory location
    pub memory_location: MemoryLocation,
    
    /// Scope information
    pub scope: VariableScope,
    
    /// Whether variable is mutable
    pub mutable: bool,
}

/// Parameter debug information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ParameterDebugInfo {
    /// Parameter name
    pub name: String,
    
    /// Parameter type
    pub param_type: String,
    
    /// Parameter index
    pub index: u32,
    
    /// Memory location
    pub memory_location: MemoryLocation,
}

/// Type debug information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TypeDebugInfo {
    /// Type name
    pub name: String,
    
    /// Type kind
    pub kind: TypeKind,
    
    /// Size in bytes
    pub size: u32,
    
    /// Alignment
    pub alignment: u32,
    
    /// Member information (for structures)
    pub members: Vec<MemberDebugInfo>,
}

/// Member debug information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MemberDebugInfo {
    /// Member name
    pub name: String,
    
    /// Member type
    pub member_type: String,
    
    /// Offset within structure
    pub offset: u32,
    
    /// Size in bytes
    pub size: u32,
}

/// Source location
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SourceLocation {
    /// File ID
    pub file_id: u32,
    
    /// Line number (1-based)
    pub line: u32,
    
    /// Column number (1-based)
    pub column: u32,
    
    /// Length in characters
    pub length: u32,
}

/// Memory location
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum MemoryLocation {
    /// Register location
    Register { register: String },
    
    /// Stack location (offset from frame pointer)
    Stack { offset: i32 },
    
    /// Global memory address
    Global { address: u64 },
    
    /// Constant value
    Constant { value: String },
}

/// Variable scope
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum VariableScope {
    /// Global scope
    Global,
    
    /// Function scope
    Function { function_name: String },
    
    /// Block scope
    Block { start_line: u32, end_line: u32 },
}

/// Type kind
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TypeKind {
    /// Primitive type
    Primitive,
    
    /// Structure type
    Structure,
    
    /// Array type
    Array { element_type: String, size: u32 },
    
    /// Pointer type
    Pointer { target_type: String },
    
    /// Function type
    Function { parameters: Vec<String>, return_type: String },
}

/// Line mapping for source-to-binary correspondence
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LineMapping {
    /// Source file ID
    pub file_id: u32,
    
    /// Source line number
    pub source_line: u32,
    
    /// Binary address
    pub binary_address: u64,
    
    /// Instruction length
    pub instruction_length: u32,
}

/// Compilation unit information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CompilationUnit {
    /// Unit name
    pub name: String,
    
    /// Compiler version
    pub compiler_version: String,
    
    /// Compilation timestamp
    pub timestamp: u64,
    
    /// Optimization level
    pub optimization_level: u8,
    
    /// Debug format version
    pub debug_format_version: String,
}

impl DebugInfo {
    /// Create new debug information
    pub fn new() -> Self {
        Self {
            source_files: HashMap::new(),
            functions: HashMap::new(),
            variables: HashMap::new(),
            line_mappings: Vec::new(),
            types: HashMap::new(),
            compilation_unit: CompilationUnit {
                name: "umbra_program".to_string(),
                compiler_version: env!("CARGO_PKG_VERSION").to_string(),
                timestamp: std::time::SystemTime::now()
                    .duration_since(std::time::UNIX_EPOCH)
                    .unwrap_or_default()
                    .as_secs(),
                optimization_level: 0,
                debug_format_version: "1.0".to_string(),
            },
        }
    }
    
    /// Generate debug information from AST
    pub fn generate_from_ast(program: &Program, source_file: &Path) -> UmbraResult<Self> {
        let mut debug_info = Self::new();
        
        // Add source file
        let file_id = debug_info.add_source_file(source_file)?;
        
        // Process statements
        for statement in &program.statements {
            debug_info.process_statement(statement, file_id)?;
        }
        
        Ok(debug_info)
    }
    
    /// Add source file
    pub fn add_source_file(&mut self, path: &Path) -> UmbraResult<u32> {
        let file_id = self.source_files.len() as u32;
        
        let content = std::fs::read_to_string(path)?;
        let hash = format!("{:x}", md5::compute(&content));
        let line_count = content.lines().count() as u32;
        let char_count = content.len() as u32;
        
        let source_file = SourceFile {
            id: file_id,
            path: path.to_path_buf(),
            hash,
            line_count,
            char_count,
        };
        
        self.source_files.insert(file_id, source_file);
        Ok(file_id)
    }
    
    /// Process AST statement for debug info
    fn process_statement(&mut self, statement: &Statement, file_id: u32) -> UmbraResult<()> {
        match statement {
            Statement::Function(func_def) => {
                self.process_function(func_def, file_id)?;
            }
            Statement::Variable(var_decl) => {
                self.add_variable_debug_info(&var_decl.name, &var_decl.type_annotation.to_string(), file_id, 1, 1)?;
            }
            _ => {
                // Handle other statement types as needed
            }
        }
        Ok(())
    }
    
    /// Process function definition
    fn process_function(&mut self, func_def: &FunctionDef, file_id: u32) -> UmbraResult<()> {
        let mut parameters = Vec::new();
        for (i, param) in func_def.parameters.iter().enumerate() {
            parameters.push(ParameterDebugInfo {
                name: param.name.clone(),
                param_type: param.type_annotation.to_string(),
                index: i as u32,
                memory_location: MemoryLocation::Stack { offset: -(i as i32 + 1) * 8 },
            });
        }
        
        let function_info = FunctionDebugInfo {
            name: func_def.name.clone(),
            mangled_name: None,
            location: SourceLocation {
                file_id,
                line: 1, // Would be extracted from actual source location
                column: 1,
                length: func_def.name.len() as u32,
            },
            parameters,
            locals: Vec::new(),
            return_type: func_def.return_type.to_string(),
            size: 0, // Would be filled during code generation
            entry_address: None,
        };
        
        self.functions.insert(func_def.name.clone(), function_info);
        Ok(())
    }
    
    /// Add variable debug information
    fn add_variable_debug_info(
        &mut self,
        name: &str,
        var_type: &str,
        file_id: u32,
        line: u32,
        column: u32,
    ) -> UmbraResult<()> {
        let variable_info = VariableDebugInfo {
            name: name.to_string(),
            var_type: var_type.to_string(),
            location: SourceLocation {
                file_id,
                line,
                column,
                length: name.len() as u32,
            },
            memory_location: MemoryLocation::Stack { offset: 0 }, // Placeholder
            scope: VariableScope::Global, // Placeholder
            mutable: true, // Default assumption
        };
        
        self.variables.insert(name.to_string(), variable_info);
        Ok(())
    }
    
    /// Load debug information from executable
    pub fn load_from_executable(executable_path: &Path) -> UmbraResult<Self> {
        // In a real implementation, this would parse debug sections from the executable
        // For now, return empty debug info
        Ok(Self::new())
    }
    
    /// Save debug information to file
    pub fn save_to_file(&self, path: &Path) -> UmbraResult<()> {
        let json = serde_json::to_string_pretty(self)
            .map_err(|e| crate::error::UmbraError::CodeGen(format!("Failed to serialize debug info: {e}")))?;
        
        std::fs::write(path, json)?;
        Ok(())
    }
    
    /// Load debug information from file
    pub fn load_from_file(path: &Path) -> UmbraResult<Self> {
        let content = std::fs::read_to_string(path)?;
        let debug_info = serde_json::from_str(&content)
            .map_err(|e| crate::error::UmbraError::CodeGen(format!("Failed to parse debug info: {e}")))?;
        
        Ok(debug_info)
    }
    
    /// Get function debug information
    pub fn get_function(&self, name: &str) -> Option<&FunctionDebugInfo> {
        self.functions.get(name)
    }
    
    /// Get variable debug information
    pub fn get_variable(&self, name: &str) -> Option<&VariableDebugInfo> {
        self.variables.get(name)
    }
    
    /// Get source file by ID
    pub fn get_source_file(&self, file_id: u32) -> Option<&SourceFile> {
        self.source_files.get(&file_id)
    }
    
    /// Find line mapping for address
    pub fn find_line_mapping(&self, address: u64) -> Option<&LineMapping> {
        self.line_mappings
            .iter()
            .find(|mapping| mapping.binary_address <= address && 
                  address < mapping.binary_address + mapping.instruction_length as u64)
    }
    
    /// Add line mapping
    pub fn add_line_mapping(&mut self, file_id: u32, source_line: u32, binary_address: u64, instruction_length: u32) {
        self.line_mappings.push(LineMapping {
            file_id,
            source_line,
            binary_address,
            instruction_length,
        });
    }
}

impl Default for DebugInfo {
    fn default() -> Self {
        Self::new()
    }
}
