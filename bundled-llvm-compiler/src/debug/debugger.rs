/// Main debugger implementation for Umbra
/// 
/// Provides the primary debugging interface and coordinates all debugging subsystems.

use super::{DebugSession, DebugState, DebugConfig, SourceLocation, PauseReason, StepType};
use super::breakpoint::Breakpoint;
use super::inspector::VariableValue;
use super::stack_trace::StackFrame;
use crate::error::UmbraResult;
use std::collections::HashMap;
use std::path::PathBuf;
use std::process::Child;

/// Main Umbra debugger
pub struct UmbraDebugger {
    /// Current debug session
    session: Option<DebugSession>,
    
    /// Debug configuration
    config: DebugConfig,
    
    /// Target process (when debugging external process)
    target_process: Option<Child>,
    
    /// Debug event handlers
    event_handlers: Vec<Box<dyn DebugEventHandler>>,
}

/// Debug event types
#[derive(Debug, Clone)]
pub enum DebugEvent {
    /// Session started
    SessionStarted { session_id: String },
    
    /// Execution paused
    ExecutionPaused { location: SourceLocation, reason: PauseReason },
    
    /// Execution resumed
    ExecutionResumed,
    
    /// Breakpoint hit
    BreakpointHit { breakpoint_id: u32, location: SourceLocation },
    
    /// Variable changed
    VariableChanged { name: String, old_value: String, new_value: String },
    
    /// Function entered
    FunctionEntered { function_name: String, location: SourceLocation },
    
    /// Function exited
    FunctionExited { function_name: String, return_value: Option<String> },
    
    /// Exception occurred
    ExceptionOccurred { message: String, location: SourceLocation },
    
    /// Session ended
    SessionEnded { session_id: String, exit_code: i32 },
}

/// Debug event handler trait
pub trait DebugEventHandler: Send + Sync {
    /// Handle debug event
    fn handle_event(&mut self, event: &DebugEvent);
}

/// Debug command for controlling execution
#[derive(Debug, Clone)]
pub enum DebugCommand {
    /// Start debugging
    Start,
    
    /// Pause execution
    Pause,
    
    /// Resume execution
    Resume,
    
    /// Step into function calls
    StepInto,
    
    /// Step over function calls
    StepOver,
    
    /// Step out of current function
    StepOut,
    
    /// Stop debugging
    Stop,
    
    /// Add breakpoint
    AddBreakpoint { file: PathBuf, line: u32 },
    
    /// Remove breakpoint
    RemoveBreakpoint { id: u32 },
    
    /// Evaluate expression
    Evaluate { expression: String },
    
    /// Inspect variable
    Inspect { variable: String },
    
    /// Set variable value
    SetVariable { name: String, value: String },
}

/// Debug command result
#[derive(Debug, Clone)]
pub struct DebugCommandResult {
    /// Whether command was successful
    pub success: bool,
    
    /// Result message
    pub message: String,
    
    /// Additional data (JSON-serializable)
    pub data: Option<serde_json::Value>,
}

impl UmbraDebugger {
    /// Create a new debugger
    pub fn new() -> Self {
        Self {
            session: None,
            config: DebugConfig::default(),
            target_process: None,
            event_handlers: Vec::new(),
        }
    }
    
    /// Create debugger with custom configuration
    pub fn with_config(config: DebugConfig) -> Self {
        Self {
            session: None,
            config,
            target_process: None,
            event_handlers: Vec::new(),
        }
    }
    
    /// Start debugging session
    pub fn start_session(&mut self, executable: PathBuf, source_files: HashMap<PathBuf, String>) -> UmbraResult<String> {
        if self.session.is_some() {
            return Err(crate::error::UmbraError::Runtime("Debug session already active".to_string()));
        }
        
        let mut session = DebugSession::new(executable.clone(), source_files)?;
        session.start()?;
        
        let session_id = session.id.clone();
        self.session = Some(session);
        
        // Emit session started event
        self.emit_event(DebugEvent::SessionStarted { session_id: session_id.clone() });
        
        println!("🚀 Debug session started: {session_id}");
        Ok(session_id)
    }
    
    /// Stop current debugging session
    pub fn stop_session(&mut self) -> UmbraResult<()> {
        if let Some(mut session) = self.session.take() {
            session.stop()?;
            
            // Stop target process if running
            if let Some(mut process) = self.target_process.take() {
                let _ = process.kill();
                let _ = process.wait();
            }
            
            // Emit session ended event
            self.emit_event(DebugEvent::SessionEnded { 
                session_id: session.id.clone(), 
                exit_code: 0 
            });
            
            println!("🛑 Debug session stopped");
        }
        
        Ok(())
    }
    
    /// Execute debug command
    pub fn execute_command(&mut self, command: DebugCommand) -> UmbraResult<DebugCommandResult> {
        let session = self.session.as_mut()
            .ok_or_else(|| crate::error::UmbraError::Runtime("No active debug session".to_string()))?;
        
        match command {
            DebugCommand::Start => {
                session.start()?;
                self.emit_event(DebugEvent::ExecutionResumed);
                Ok(DebugCommandResult {
                    success: true,
                    message: "Execution started".to_string(),
                    data: None,
                })
            }
            
            DebugCommand::Pause => {
                session.pause(PauseReason::UserRequested)?;
                let location = session.get_current_location()?;
                self.emit_event(DebugEvent::ExecutionPaused { 
                    location: location.clone(), 
                    reason: PauseReason::UserRequested 
                });
                Ok(DebugCommandResult {
                    success: true,
                    message: "Execution paused".to_string(),
                    data: Some(serde_json::to_value(&location).unwrap_or_default()),
                })
            }
            
            DebugCommand::Resume => {
                session.resume()?;
                self.emit_event(DebugEvent::ExecutionResumed);
                Ok(DebugCommandResult {
                    success: true,
                    message: "Execution resumed".to_string(),
                    data: None,
                })
            }
            
            DebugCommand::StepInto => {
                session.step(StepType::Into)?;
                Ok(DebugCommandResult {
                    success: true,
                    message: "Stepping into".to_string(),
                    data: None,
                })
            }
            
            DebugCommand::StepOver => {
                session.step(StepType::Over)?;
                Ok(DebugCommandResult {
                    success: true,
                    message: "Stepping over".to_string(),
                    data: None,
                })
            }
            
            DebugCommand::StepOut => {
                session.step(StepType::Out)?;
                Ok(DebugCommandResult {
                    success: true,
                    message: "Stepping out".to_string(),
                    data: None,
                })
            }
            
            DebugCommand::Stop => {
                session.stop()?;
                Ok(DebugCommandResult {
                    success: true,
                    message: "Execution stopped".to_string(),
                    data: None,
                })
            }
            
            DebugCommand::AddBreakpoint { file, line } => {
                let id = session.add_breakpoint(&file, line)?;
                Ok(DebugCommandResult {
                    success: true,
                    message: format!("Breakpoint {} added at {}:{}", id, file.display(), line),
                    data: Some(serde_json::json!({ "id": id })),
                })
            }
            
            DebugCommand::RemoveBreakpoint { id } => {
                let removed = session.remove_breakpoint(id)?;
                if removed {
                    Ok(DebugCommandResult {
                        success: true,
                        message: format!("Breakpoint {id} removed"),
                        data: None,
                    })
                } else {
                    Ok(DebugCommandResult {
                        success: false,
                        message: format!("Breakpoint {id} not found"),
                        data: None,
                    })
                }
            }
            
            DebugCommand::Evaluate { expression } => {
                match session.evaluate_expression(&expression) {
                    Ok(value) => Ok(DebugCommandResult {
                        success: true,
                        message: format!("{} = {}", expression, value.value),
                        data: Some(serde_json::to_value(&value).unwrap_or_default()),
                    }),
                    Err(error) => Ok(DebugCommandResult {
                        success: false,
                        message: format!("Evaluation failed: {error}"),
                        data: None,
                    }),
                }
            }
            
            DebugCommand::Inspect { variable } => {
                match session.inspect_variable(&variable) {
                    Ok(value) => Ok(DebugCommandResult {
                        success: true,
                        message: format!("{}: {} = {}", variable, value.var_type, value.value),
                        data: Some(serde_json::to_value(&value).unwrap_or_default()),
                    }),
                    Err(error) => Ok(DebugCommandResult {
                        success: false,
                        message: format!("Inspection failed: {error}"),
                        data: None,
                    }),
                }
            }
            
            DebugCommand::SetVariable { name, value } => {
                match session.inspector.update_variable(&name, &value) {
                    Ok(()) => {
                        self.emit_event(DebugEvent::VariableChanged {
                            name: name.clone(),
                            old_value: "unknown".to_string(), // Would track previous value
                            new_value: value.clone(),
                        });
                        Ok(DebugCommandResult {
                            success: true,
                            message: format!("Variable '{name}' set to '{value}'"),
                            data: None,
                        })
                    }
                    Err(error) => Ok(DebugCommandResult {
                        success: false,
                        message: format!("Failed to set variable: {error}"),
                        data: None,
                    }),
                }
            }
        }
    }
    
    /// Get current session state
    pub fn get_session_state(&self) -> Option<&DebugState> {
        self.session.as_ref().map(|s| s.get_state())
    }
    
    /// Get current breakpoints
    pub fn get_breakpoints(&self) -> Vec<&Breakpoint> {
        self.session.as_ref()
            .map(|s| s.list_breakpoints())
            .unwrap_or_default()
    }
    
    /// Get current stack trace
    pub fn get_stack_trace(&self) -> UmbraResult<Vec<StackFrame>> {
        self.session.as_ref()
            .ok_or_else(|| crate::error::UmbraError::Runtime("No active session".to_string()))?
            .get_stack_trace()
    }
    
    /// Get local variables
    pub fn get_local_variables(&self) -> UmbraResult<HashMap<String, VariableValue>> {
        self.session.as_ref()
            .ok_or_else(|| crate::error::UmbraError::Runtime("No active session".to_string()))?
            .get_local_variables()
    }
    
    /// Add event handler
    pub fn add_event_handler(&mut self, handler: Box<dyn DebugEventHandler>) {
        self.event_handlers.push(handler);
    }
    
    /// Emit debug event to all handlers
    fn emit_event(&mut self, event: DebugEvent) {
        for handler in &mut self.event_handlers {
            handler.handle_event(&event);
        }
    }
    
    /// Update debug configuration
    pub fn update_config(&mut self, config: DebugConfig) {
        self.config = config;
    }
    
    /// Get debug configuration
    pub fn get_config(&self) -> &DebugConfig {
        &self.config
    }
    
    /// Check if session is active
    pub fn is_session_active(&self) -> bool {
        self.session.as_ref().map(|s| s.is_active()).unwrap_or(false)
    }
}

/// Console debug event handler
pub struct ConsoleDebugEventHandler;

impl DebugEventHandler for ConsoleDebugEventHandler {
    fn handle_event(&mut self, event: &DebugEvent) {
        match event {
            DebugEvent::SessionStarted { session_id } => {
                println!("🚀 Debug session started: {session_id}");
            }
            DebugEvent::ExecutionPaused { location, reason } => {
                println!("⏸️  Execution paused at {}:{} ({:?})", 
                    location.file.display(), location.line, reason);
            }
            DebugEvent::ExecutionResumed => {
                println!("▶️  Execution resumed");
            }
            DebugEvent::BreakpointHit { breakpoint_id, location } => {
                println!("🎯 Breakpoint {} hit at {}:{}", 
                    breakpoint_id, location.file.display(), location.line);
            }
            DebugEvent::VariableChanged { name, old_value, new_value } => {
                println!("🔄 Variable '{name}' changed: {old_value} -> {new_value}");
            }
            DebugEvent::FunctionEntered { function_name, location } => {
                println!("📥 Entered function '{}' at {}:{}", 
                    function_name, location.file.display(), location.line);
            }
            DebugEvent::FunctionExited { function_name, return_value } => {
                if let Some(value) = return_value {
                    println!("📤 Exited function '{function_name}' with return value: {value}");
                } else {
                    println!("📤 Exited function '{function_name}'");
                }
            }
            DebugEvent::ExceptionOccurred { message, location } => {
                println!("💥 Exception at {}:{}: {}", 
                    location.file.display(), location.line, message);
            }
            DebugEvent::SessionEnded { session_id, exit_code } => {
                println!("🏁 Debug session ended: {session_id} (exit code: {exit_code})");
            }
        }
    }
}

impl Default for UmbraDebugger {
    fn default() -> Self {
        Self::new()
    }
}
