/// Interactive REPL (Read-Eval-Print Loop) for Umbra
/// 
/// This module provides a comprehensive interactive development environment
/// with history, completion, debugging, and advanced features.

use crate::error::{UmbraError, UmbraResult};
use crate::lexer::Lexer;
use crate::parser::Parser;
use crate::parser::ast::{Statement, Expression, FunctionDef, ImportStatement, TrainStatement, EvaluateStatement, PredictStatement, VisualizeStatement};
use crate::semantic::analyzer::SemanticAnalyzer;
use crate::runtime::{Runtime, RuntimeValue};
use crate::ai_ml::executor::AIMLExecutor;
use std::collections::HashMap;
use std::io::{self, Write};
use std::fs::{File, OpenOptions};
use std::path::PathBuf;
use crossterm::{
    event::{self, Event, KeyCode, KeyEvent, KeyModifiers},
    terminal::{self, ClearType},
    cursor,
    style::{Color, Print, ResetColor, SetForegroundColor},
    tty::IsTty,
    ExecutableCommand, QueueableCommand,
};

/// REPL configuration
#[derive(Debug, Clone)]
pub struct ReplConfig {
    /// Enable command history
    pub history_enabled: bool,
    /// History file path
    pub history_file: Option<PathBuf>,
    /// Maximum history entries
    pub max_history: usize,
    /// Enable auto-completion
    pub completion_enabled: bool,
    /// Enable syntax highlighting
    pub syntax_highlighting: bool,
    /// Enable multi-line input
    pub multiline_enabled: bool,
    /// Prompt string
    pub prompt: String,
    /// Continuation prompt
    pub continuation_prompt: String,
    /// Enable debugging features
    pub debug_mode: bool,
}

impl Default for ReplConfig {
    fn default() -> Self {
        Self {
            history_enabled: true,
            history_file: Some(PathBuf::from(".umbra_history")),
            max_history: 1000,
            completion_enabled: true,
            syntax_highlighting: true,
            multiline_enabled: true,
            prompt: "umbra> ".to_string(),
            continuation_prompt: "    | ".to_string(),
            debug_mode: false,
        }
    }
}

/// REPL command types
#[derive(Debug, Clone, PartialEq)]
pub enum ReplCommand {
    /// Regular Umbra code
    Code(String),
    /// REPL meta-command
    Meta(String, Vec<String>),
    Empty,
    Exit,
}

/// REPL state
pub struct ReplState {
    /// Runtime environment
    runtime: Runtime,
    /// AI/ML executor
    aiml_executor: AIMLExecutor,
    /// Command history
    history: Vec<String>,
    /// Current session variables
    session_vars: HashMap<String, RuntimeValue>,
    /// Multi-line input buffer
    input_buffer: String,
    /// Current line number
    line_number: usize,
    /// Debug mode state
    debug_mode: bool,
}

/// Interactive REPL
pub struct Repl {
    config: ReplConfig,
    state: ReplState,
}

impl Repl {
    /// Create a new REPL instance
    pub fn new(config: ReplConfig) -> Self {
        let state = ReplState {
            runtime: Runtime::new(),
            aiml_executor: AIMLExecutor::default(),
            history: Vec::new(),
            session_vars: HashMap::new(),
            input_buffer: String::new(),
            line_number: 1,
            debug_mode: config.debug_mode,
        };

        Self { config, state }
    }

    /// Start the REPL
    pub fn start(&mut self) -> UmbraResult<()> {
        println!("🚀 Umbra Interactive REPL");
        println!("Type :help for help, :exit to quit");
        
        // Load history
        if self.config.history_enabled {
            self.load_history()?;
        }

        // Main REPL loop
        loop {
            match self.read_input()? {
                ReplCommand::Exit => break,
                ReplCommand::Empty => continue,
                ReplCommand::Meta(cmd, args) => {
                    if let Err(e) = self.handle_meta_command(&cmd, &args) {
                        println!("Error: {}", e);
                    }
                }
                ReplCommand::Code(code) => {
                    if let Err(e) = self.evaluate_code(&code) {
                        println!("Error: {}", e);
                    }
                }
            }
        }

        // Save history
        if self.config.history_enabled {
            self.save_history()?;
        }

        println!("👋 Goodbye!");
        Ok(())
    }

    /// Read input from user with enhanced terminal support
    fn read_input(&mut self) -> UmbraResult<ReplCommand> {
        loop {
            // Print prompt
            let prompt = if self.state.input_buffer.is_empty() {
                self.config.prompt.clone()
            } else {
                self.config.continuation_prompt.clone()
            };

            let line = self.read_line_with_history(&prompt)?;

            // Handle empty input
            if line.is_empty() {
                if self.state.input_buffer.is_empty() {
                    return Ok(ReplCommand::Empty);
                } else {
                    continue; // Continue multi-line input
                }
            }

            // Handle exit commands
            if line == ":exit" || line == ":quit" || line == "exit" || line == "quit" {
                return Ok(ReplCommand::Exit);
            }

            // Handle meta commands
            if line.starts_with(':') {
                let parts: Vec<&str> = line[1..].split_whitespace().collect();
                if !parts.is_empty() {
                    let cmd = parts[0].to_string();
                    let args = parts[1..].iter().map(|s| s.to_string()).collect();
                    return Ok(ReplCommand::Meta(cmd, args));
                }
            }

            // Add to input buffer
            if !self.state.input_buffer.is_empty() {
                self.state.input_buffer.push('\n');
            }
            self.state.input_buffer.push_str(&line);

            // Check if input is complete
            if self.is_input_complete(&self.state.input_buffer)? {
                let code = self.state.input_buffer.clone();
                self.state.input_buffer.clear();
                
                // Add to history
                if self.config.history_enabled && !code.trim().is_empty() {
                    self.add_to_history(code.clone());
                }
                
                return Ok(ReplCommand::Code(code));
            }
        }
    }

    /// Read a line with history navigation and auto-completion
    fn read_line_with_history(&mut self, prompt: &str) -> UmbraResult<String> {
        // Check if we're in a terminal or if input is piped
        if !IsTty::is_tty(&io::stdin()) {
            // Fallback to simple line reading for piped input
            return self.read_line_simple(prompt);
        }

        // Enable raw mode for better terminal control
        terminal::enable_raw_mode()
            .map_err(|e| UmbraError::Runtime(format!("Failed to enable raw mode: {}", e)))?;

        let mut stdout = io::stdout();
        let mut input = String::new();
        let mut cursor_pos = 0;
        let mut history_index = self.state.history.len();

        // Print prompt
        stdout.execute(Print(prompt))
            .map_err(|e| UmbraError::Runtime(format!("Failed to print prompt: {}", e)))?;

        loop {
            // Read key event
            if let Ok(Event::Key(key_event)) = event::read() {
                match key_event {
                    KeyEvent { code: KeyCode::Enter, .. } => {
                        stdout.execute(Print("\r\n"))
                            .map_err(|e| UmbraError::Runtime(format!("Failed to print newline: {}", e)))?;
                        break;
                    }
                    KeyEvent { code: KeyCode::Char(c), modifiers: KeyModifiers::NONE, .. } => {
                        input.insert(cursor_pos, c);
                        cursor_pos += 1;
                        self.refresh_line(&mut stdout, prompt, &input, cursor_pos)?;
                    }
                    KeyEvent { code: KeyCode::Char('c'), modifiers: KeyModifiers::CONTROL, .. } => {
                        // Ctrl+C - interrupt
                        stdout.execute(Print("\r\n"))
                            .map_err(|e| UmbraError::Runtime(format!("Failed to print newline: {}", e)))?;
                        terminal::disable_raw_mode()
                            .map_err(|e| UmbraError::Runtime(format!("Failed to disable raw mode: {}", e)))?;
                        return Ok(":exit".to_string());
                    }
                    KeyEvent { code: KeyCode::Backspace, .. } => {
                        if cursor_pos > 0 {
                            input.remove(cursor_pos - 1);
                            cursor_pos -= 1;
                            self.refresh_line(&mut stdout, prompt, &input, cursor_pos)?;
                        }
                    }
                    KeyEvent { code: KeyCode::Left, .. } => {
                        if cursor_pos > 0 {
                            cursor_pos -= 1;
                            stdout.execute(cursor::MoveLeft(1))
                                .map_err(|e| UmbraError::Runtime(format!("Failed to move cursor: {}", e)))?;
                        }
                    }
                    KeyEvent { code: KeyCode::Right, .. } => {
                        if cursor_pos < input.len() {
                            cursor_pos += 1;
                            stdout.execute(cursor::MoveRight(1))
                                .map_err(|e| UmbraError::Runtime(format!("Failed to move cursor: {}", e)))?;
                        }
                    }
                    KeyEvent { code: KeyCode::Up, .. } => {
                        // Navigate history up
                        if history_index > 0 {
                            history_index -= 1;
                            if let Some(history_item) = self.state.history.get(history_index) {
                                input = history_item.clone();
                                cursor_pos = input.len();
                                self.refresh_line(&mut stdout, prompt, &input, cursor_pos)?;
                            }
                        }
                    }
                    KeyEvent { code: KeyCode::Down, .. } => {
                        // Navigate history down
                        if history_index < self.state.history.len() {
                            history_index += 1;
                            if history_index == self.state.history.len() {
                                input.clear();
                                cursor_pos = 0;
                            } else if let Some(history_item) = self.state.history.get(history_index) {
                                input = history_item.clone();
                                cursor_pos = input.len();
                            }
                            self.refresh_line(&mut stdout, prompt, &input, cursor_pos)?;
                        }
                    }
                    KeyEvent { code: KeyCode::Tab, .. } => {
                        // Auto-completion (basic implementation)
                        if let Some(completion) = self.get_completion(&input, cursor_pos) {
                            input = completion.0;
                            cursor_pos = completion.1;
                            self.refresh_line(&mut stdout, prompt, &input, cursor_pos)?;
                        }
                    }
                    _ => {}
                }
            }
        }

        // Disable raw mode
        terminal::disable_raw_mode()
            .map_err(|e| UmbraError::Runtime(format!("Failed to disable raw mode: {}", e)))?;

        Ok(input)
    }

    /// Simple line reading for non-terminal input (pipes, etc.)
    fn read_line_simple(&mut self, prompt: &str) -> UmbraResult<String> {
        print!("{}", prompt);
        io::stdout().flush().unwrap();

        let mut line = String::new();
        io::stdin().read_line(&mut line)
            .map_err(|e| UmbraError::Runtime(format!("Failed to read input: {}", e)))?;

        Ok(line.trim().to_string())
    }

    /// Refresh the current line display
    fn refresh_line(&self, stdout: &mut io::Stdout, prompt: &str, input: &str, cursor_pos: usize) -> UmbraResult<()> {
        // Clear current line and reprint
        stdout.execute(cursor::MoveToColumn(0))
            .map_err(|e| UmbraError::Runtime(format!("Failed to move cursor: {}", e)))?;
        stdout.execute(terminal::Clear(ClearType::CurrentLine))
            .map_err(|e| UmbraError::Runtime(format!("Failed to clear line: {}", e)))?;
        stdout.execute(Print(prompt))
            .map_err(|e| UmbraError::Runtime(format!("Failed to print prompt: {}", e)))?;

        if self.config.syntax_highlighting {
            self.print_with_syntax_highlighting(stdout, input)
                .map_err(|e| UmbraError::Runtime(format!("Failed to print with highlighting: {}", e)))?;
        } else {
            stdout.execute(Print(input))
                .map_err(|e| UmbraError::Runtime(format!("Failed to print input: {}", e)))?;
        }

        stdout.execute(cursor::MoveToColumn((prompt.len() + cursor_pos) as u16))
            .map_err(|e| UmbraError::Runtime(format!("Failed to move cursor: {}", e)))?;
        Ok(())
    }

    /// Print text with basic syntax highlighting
    fn print_with_syntax_highlighting(&self, stdout: &mut io::Stdout, text: &str) -> io::Result<()> {
        // Basic syntax highlighting for Umbra keywords
        let keywords = ["fn", "let", "if", "else", "when", "otherwise", "repeat", "while", "for", "in",
                       "bring", "export", "void", "Integer", "Float", "String", "Boolean", "List", "Map",
                       "train", "evaluate", "predict", "visualize", "using"];

        let mut current_word = String::new();
        let mut in_string = false;
        let mut string_char = '"';

        for ch in text.chars() {
            if in_string {
                stdout.queue(SetForegroundColor(Color::Green)).map_err(|e| io::Error::new(io::ErrorKind::Other, e))?;
                stdout.queue(Print(ch)).map_err(|e| io::Error::new(io::ErrorKind::Other, e))?;
                if ch == string_char {
                    in_string = false;
                    stdout.queue(ResetColor).map_err(|e| io::Error::new(io::ErrorKind::Other, e))?;
                }
            } else if ch == '"' || ch == '\'' {
                if !current_word.is_empty() {
                    self.print_word(stdout, &current_word, &keywords)?;
                    current_word.clear();
                }
                in_string = true;
                string_char = ch;
                stdout.queue(SetForegroundColor(Color::Green)).map_err(|e| io::Error::new(io::ErrorKind::Other, e))?;
                stdout.queue(Print(ch)).map_err(|e| io::Error::new(io::ErrorKind::Other, e))?;
            } else if ch.is_alphanumeric() || ch == '_' {
                current_word.push(ch);
            } else {
                if !current_word.is_empty() {
                    self.print_word(stdout, &current_word, &keywords)?;
                    current_word.clear();
                }
                stdout.queue(Print(ch)).map_err(|e| io::Error::new(io::ErrorKind::Other, e))?;
            }
        }

        if !current_word.is_empty() {
            self.print_word(stdout, &current_word, &keywords)?;
        }

        stdout.queue(ResetColor).map_err(|e| io::Error::new(io::ErrorKind::Other, e))?;
        Ok(())
    }

    /// Print a word with appropriate coloring
    fn print_word(&self, stdout: &mut io::Stdout, word: &str, keywords: &[&str]) -> io::Result<()> {
        if keywords.contains(&word) {
            stdout.queue(SetForegroundColor(Color::Blue)).map_err(|e| io::Error::new(io::ErrorKind::Other, e))?;
            stdout.queue(Print(word)).map_err(|e| io::Error::new(io::ErrorKind::Other, e))?;
            stdout.queue(ResetColor).map_err(|e| io::Error::new(io::ErrorKind::Other, e))?;
        } else {
            stdout.queue(Print(word)).map_err(|e| io::Error::new(io::ErrorKind::Other, e))?;
        }
        Ok(())
    }

    /// Get auto-completion suggestion
    fn get_completion(&self, input: &str, cursor_pos: usize) -> Option<(String, usize)> {
        // Find the word at cursor position
        let before_cursor = &input[..cursor_pos];
        let word_start = before_cursor.rfind(|c: char| !c.is_alphanumeric() && c != '_')
            .map(|i| i + 1)
            .unwrap_or(0);

        let partial_word = &before_cursor[word_start..];

        if partial_word.is_empty() {
            return None;
        }

        // Keywords and built-in functions for completion
        let completions = [
            "fn", "let", "if", "else", "when", "otherwise", "repeat", "while", "for", "in",
            "bring", "export", "void", "Integer", "Float", "String", "Boolean", "List", "Map",
            "train", "evaluate", "predict", "visualize", "using", "show", "print", "len", "type",
            "help", "clear", "vars", "exit", "quit"
        ];

        // Find first matching completion
        for completion in &completions {
            if completion.starts_with(partial_word) && completion.len() > partial_word.len() {
                let mut new_input = input.to_string();
                new_input.replace_range(word_start..cursor_pos, completion);
                let new_cursor_pos = word_start + completion.len();
                return Some((new_input, new_cursor_pos));
            }
        }

        // Check session variables
        for var_name in self.state.session_vars.keys() {
            if var_name.starts_with(partial_word) && var_name.len() > partial_word.len() {
                let mut new_input = input.to_string();
                new_input.replace_range(word_start..cursor_pos, var_name);
                let new_cursor_pos = word_start + var_name.len();
                return Some((new_input, new_cursor_pos));
            }
        }

        None
    }

    /// Check if input is syntactically complete
    fn is_input_complete(&self, input: &str) -> UmbraResult<bool> {
        // Simple heuristic: check for balanced braces and incomplete statements
        let mut brace_count = 0;
        let mut paren_count = 0;
        let mut bracket_count = 0;
        let mut in_string = false;
        let mut escape_next = false;

        for ch in input.chars() {
            if escape_next {
                escape_next = false;
                continue;
            }

            match ch {
                '\\' if in_string => escape_next = true,
                '"' => in_string = !in_string,
                '{' if !in_string => brace_count += 1,
                '}' if !in_string => brace_count -= 1,
                '(' if !in_string => paren_count += 1,
                ')' if !in_string => paren_count -= 1,
                '[' if !in_string => bracket_count += 1,
                ']' if !in_string => bracket_count -= 1,
                _ => {}
            }
        }

        // Input is complete if all brackets are balanced and not in a string
        Ok(brace_count == 0 && paren_count == 0 && bracket_count == 0 && !in_string)
    }

    /// Handle meta commands
    fn handle_meta_command(&mut self, cmd: &str, args: &[String]) -> UmbraResult<()> {
        match cmd {
            "help" => self.show_help(),
            "history" => self.show_history(),
            "clear" => self.clear_screen(),
            "vars" => self.show_variables(),
            "debug" => self.toggle_debug_mode(),
            "reset" => self.reset_session(),
            "load" => self.load_file(args),
            "save" => self.save_session(args),
            "type" => self.show_type(args),
            "doc" => self.show_documentation(args),
            "time" => self.toggle_timing(),
            "profile" => self.show_profiling_info(),
            "ai" => self.handle_ai_command(args),
            _ => {
                println!("Unknown command: {}. Type :help for available commands.", cmd);
                Ok(())
            }
        }
    }

    /// Show help information
    fn show_help(&self) -> UmbraResult<()> {
        println!("📚 Umbra REPL Help");
        println!("Commands:");
        println!("  :help          - Show this help");
        println!("  :exit, :quit   - Exit the REPL");
        println!("  :history       - Show command history");
        println!("  :clear         - Clear screen");
        println!("  :vars          - Show session variables");
        println!("  :debug         - Toggle debug mode");
        println!("  :reset         - Reset session");
        println!("  :load <file>   - Load and execute file");
        println!("  :save <file>   - Save session to file");
        println!("  :type <expr>   - Show type of expression");
        println!("  :doc <symbol>  - Show documentation");
        println!("  :time          - Toggle execution timing");
        println!("  :profile       - Show profiling information");
        println!("  :ai <command>  - AI/ML commands");
        println!();
        println!("Features:");
        println!("  - Multi-line input (automatic detection)");
        println!("  - Command history with arrow keys");
        println!("  - Tab completion");
        println!("  - Syntax highlighting");
        println!("  - Variable inspection");
        println!("  - AI/ML integration");
        Ok(())
    }

    /// Show command history
    fn show_history(&self) -> UmbraResult<()> {
        println!("📜 Command History:");
        for (i, cmd) in self.state.history.iter().enumerate() {
            println!("  {}: {}", i + 1, cmd);
        }
        Ok(())
    }

    /// Clear screen
    fn clear_screen(&self) -> UmbraResult<()> {
        print!("\x1B[2J\x1B[1;1H");
        io::stdout().flush().unwrap();
        Ok(())
    }

    /// Show session variables
    fn show_variables(&self) -> UmbraResult<()> {
        println!("📊 Session Variables:");
        if self.state.session_vars.is_empty() {
            println!("  No variables defined");
        } else {
            for (name, value) in &self.state.session_vars {
                println!("  {} = {} ({})", name, value.to_string(), value.type_name());
            }
        }
        Ok(())
    }

    /// Toggle debug mode
    fn toggle_debug_mode(&mut self) -> UmbraResult<()> {
        self.state.debug_mode = !self.state.debug_mode;
        println!("🐛 Debug mode: {}", if self.state.debug_mode { "ON" } else { "OFF" });
        Ok(())
    }

    /// Reset session
    fn reset_session(&mut self) -> UmbraResult<()> {
        self.state.runtime = Runtime::new();
        self.state.session_vars.clear();
        self.state.line_number = 1;
        println!("🔄 Session reset");
        Ok(())
    }

    /// Load and execute file
    fn load_file(&mut self, args: &[String]) -> UmbraResult<()> {
        if args.is_empty() {
            return Err(UmbraError::Runtime("Usage: :load <filename>".to_string()));
        }

        let filename = &args[0];
        let content = std::fs::read_to_string(filename)
            .map_err(|e| UmbraError::Runtime(format!("Failed to read file {}: {}", filename, e)))?;

        println!("📂 Loading file: {}", filename);
        self.evaluate_code(&content)?;
        println!("✅ File loaded successfully");
        Ok(())
    }

    /// Save session to file
    fn save_session(&self, args: &[String]) -> UmbraResult<()> {
        if args.is_empty() {
            return Err(UmbraError::Runtime("Usage: :save <filename>".to_string()));
        }

        let filename = &args[0];
        let mut file = File::create(filename)
            .map_err(|e| UmbraError::Runtime(format!("Failed to create file {}: {}", filename, e)))?;

        // Save history as executable Umbra code
        for cmd in &self.state.history {
            writeln!(file, "{}", cmd)
                .map_err(|e| UmbraError::Runtime(format!("Failed to write to file: {}", e)))?;
        }

        println!("💾 Session saved to: {}", filename);
        Ok(())
    }

    /// Show type of expression
    fn show_type(&mut self, args: &[String]) -> UmbraResult<()> {
        if args.is_empty() {
            return Err(UmbraError::Runtime("Usage: :type <expression>".to_string()));
        }

        let expr = args.join(" ");
        // This would use the type checker to determine the type
        println!("🔍 Type of '{}': <type analysis not implemented>", expr);
        Ok(())
    }

    /// Show documentation
    fn show_documentation(&self, args: &[String]) -> UmbraResult<()> {
        if args.is_empty() {
            return Err(UmbraError::Runtime("Usage: :doc <symbol>".to_string()));
        }

        let symbol = &args[0];
        println!("📖 Documentation for '{}': <documentation lookup not implemented>", symbol);
        Ok(())
    }

    /// Toggle execution timing
    fn toggle_timing(&mut self) -> UmbraResult<()> {
        println!("⏱️  Timing toggle not implemented");
        Ok(())
    }

    /// Show profiling information
    fn show_profiling_info(&self) -> UmbraResult<()> {
        println!("📊 Profiling information not implemented");
        Ok(())
    }

    /// Handle AI/ML commands
    fn handle_ai_command(&mut self, args: &[String]) -> UmbraResult<()> {
        if args.is_empty() {
            println!("🤖 AI/ML Commands:");
            println!("  :ai models     - List available models");
            println!("  :ai datasets   - List loaded datasets");
            println!("  :ai train      - Interactive training");
            println!("  :ai predict    - Interactive prediction");
            return Ok(());
        }

        match args[0].as_str() {
            "models" => {
                println!("🤖 Available Models: <model listing not implemented>");
            }
            "datasets" => {
                println!("📊 Loaded Datasets: <dataset listing not implemented>");
            }
            "train" => {
                println!("🏋️ Interactive Training: <training interface not implemented>");
            }
            "predict" => {
                println!("🔮 Interactive Prediction: <prediction interface not implemented>");
            }
            _ => {
                println!("Unknown AI command: {}. Use :ai for help.", args[0]);
            }
        }

        Ok(())
    }

    /// Evaluate Umbra code
    fn evaluate_code(&mut self, code: &str) -> UmbraResult<()> {
        let start_time = std::time::Instant::now();

        // Tokenize
        let mut lexer = Lexer::new(code.to_string());
        let tokens = lexer.tokenize()?;

        if self.state.debug_mode {
            println!("🔍 Tokens: {:?}", tokens);
        }

        // Parse
        let mut parser = Parser::new(tokens);
        let ast = parser.parse()?;

        if self.state.debug_mode {
            println!("🌳 AST: {:?}", ast);
        }

        // Semantic analysis
        let mut analyzer = SemanticAnalyzer::new();
        let symbols = analyzer.analyze(&ast)?;

        if self.state.debug_mode {
            println!("🔍 Symbols: {:?}", symbols);
        }

        // Execute with enhanced runtime
        let result = self.execute_with_repl_context(&ast)?;

        let execution_time = start_time.elapsed();

        // Show result if not null
        match result {
            RuntimeValue::Null => {}
            _ => {
                println!("=> {}", self.format_result(&result));
                // Store result in special variable for reuse
                self.state.session_vars.insert("_".to_string(), result);
            }
        }

        if self.state.debug_mode {
            println!("⏱️  Execution time: {:?}", execution_time);
            println!("💾 Memory usage: {} KB", self.get_memory_usage() / 1024);
        }

        self.state.line_number += 1;
        Ok(())
    }

    /// Execute code with REPL-specific context and features
    fn execute_with_repl_context(&mut self, ast: &crate::parser::ast::Program) -> UmbraResult<RuntimeValue> {
        // Set up REPL-specific functions and variables
        self.setup_repl_builtins()?;

        // Execute each statement and return the last expression result
        let mut last_result = RuntimeValue::Null;

        for statement in &ast.statements {
            match statement {
                Statement::Expression(expr) => {
                    // For expressions, evaluate and store result
                    last_result = self.evaluate_expression_in_context(&expr.expression)?;
                }
                Statement::Variable(let_stmt) => {
                    // Handle variable declarations
                    let value = self.evaluate_expression_in_context(&let_stmt.value)?;
                    self.state.session_vars.insert(let_stmt.name.clone(), value.clone());
                    self.state.runtime.set_variable(&let_stmt.name, value)?;
                }
                Statement::Function(func_stmt) => {
                    // Handle function definitions
                    self.define_repl_function(func_stmt)?;
                }
                Statement::Import(import_stmt) => {
                    // Handle imports with REPL-specific module loading
                    self.handle_repl_import(import_stmt)?;
                }
                Statement::Train(train_stmt) => {
                    // Handle AI/ML training statements
                    last_result = self.execute_train_statement(train_stmt)?;
                }
                Statement::Evaluate(eval_stmt) => {
                    // Handle AI/ML evaluation statements
                    last_result = self.execute_evaluate_statement(eval_stmt)?;
                }
                Statement::Predict(predict_stmt) => {
                    // Handle AI/ML prediction statements
                    last_result = self.execute_predict_statement(predict_stmt)?;
                }
                Statement::Visualize(viz_stmt) => {
                    // Handle visualization statements
                    last_result = self.execute_visualize_statement(viz_stmt)?;
                }
                _ => {
                    // Execute other statements normally
                    self.state.runtime.execute_statement(statement)?;
                }
            }
        }

        Ok(last_result)
    }

    /// Set up REPL-specific built-in functions
    fn setup_repl_builtins(&mut self) -> UmbraResult<()> {
        // Add REPL-specific functions

        // Add help function
        self.state.runtime.add_builtin_function("help", |args| {
            if args.is_empty() {
                println!("Available functions: print, len, type, help, clear, vars");
                println!("AI/ML functions: train, evaluate, predict, visualize");
                println!("Math functions: sqrt, sin, cos, tan, log, exp, abs, min, max");
                println!("String functions: concat, substr, split, join, trim");
                println!("Collection functions: map, filter, reduce, sort");
            } else {
                // Show help for specific function
                match args[0].to_string().as_str() {
                    "print" => println!("print(value) - Display a value"),
                    "len" => println!("len(collection) - Get length of collection"),
                    "type" => println!("type(value) - Get type of value"),
                    "train" => println!("train(model, data) - Train an AI/ML model"),
                    "evaluate" => println!("evaluate(model, test_data) - Evaluate model performance"),
                    "predict" => println!("predict(model, input) - Make predictions"),
                    _ => println!("No help available for: {:?}", args[0]),
                }
            }
            Ok(RuntimeValue::Null)
        })?;

        // Add clear function
        self.state.runtime.add_builtin_function("clear", |_| {
            print!("\x1B[2J\x1B[1;1H"); // ANSI escape codes to clear screen
            Ok(RuntimeValue::Null)
        })?;

        // Add vars function to show all variables
        let session_vars = self.state.session_vars.clone();
        self.state.runtime.add_builtin_function("vars", move |_| {
            println!("Session variables:");
            for (name, value) in &session_vars {
                println!("  {} = {:?}", name, value);
            }
            Ok(RuntimeValue::Null)
        })?;

        // Add type function
        self.state.runtime.add_builtin_function("type", |args| {
            if args.is_empty() {
                return Ok(RuntimeValue::String("Usage: type(value)".to_string()));
            }
            let type_name = match &args[0] {
                RuntimeValue::Integer(_) => "Integer",
                RuntimeValue::Float(_) => "Float",
                RuntimeValue::String(_) => "String",
                RuntimeValue::Boolean(_) => "Boolean",
                RuntimeValue::List(_) => "List",
                RuntimeValue::Map(_) => "Map",
                RuntimeValue::Set(_) => "Set",
                RuntimeValue::Struct(_) => "Struct",
                RuntimeValue::Function(_) => "Function",
                RuntimeValue::Object(obj_type, _) => obj_type,
                RuntimeValue::Null => "Null",
            };
            Ok(RuntimeValue::String(type_name.to_string()))
        })?;

        Ok(())
    }

    /// Set up AI/ML built-in functions
    fn setup_aiml_builtins(&mut self) -> UmbraResult<()> {
        // Add AI/ML convenience functions that were duplicated above
        self.state.runtime.add_builtin_function("help", |args| {
            if args.is_empty() {
                Ok(RuntimeValue::String(r#"
Umbra REPL Help:
  help()        - Show this help
  help(symbol)  - Show help for symbol
  vars()        - Show session variables
  clear()       - Clear screen
  exit()        - Exit REPL

AI/ML Functions:
  train(model, data, ...)  - Train a model
  evaluate(model, data)    - Evaluate a model
  predict(model, input)    - Make predictions
  visualize(data, type)    - Create visualizations
"#.to_string()))
            } else {
                // Show help for specific symbol
                let symbol = &args[0];
                Ok(RuntimeValue::String(format!("Help for: {}", symbol.to_string())))
            }
        })?;

        self.state.runtime.add_builtin_function("vars", |_| {
            // This would show session variables
            Ok(RuntimeValue::String("Session variables: (implementation needed)".to_string()))
        })?;

        self.state.runtime.add_builtin_function("clear", |_| {
            print!("\x1B[2J\x1B[1;1H");
            Ok(RuntimeValue::Null)
        })?;

        self.state.runtime.add_builtin_function("exit", |_| {
            std::process::exit(0);
        })?;

        Ok(())
    }

    /// Evaluate expression with REPL context
    fn evaluate_expression_in_context(&mut self, expr: &Expression) -> UmbraResult<RuntimeValue> {
        // Check if it's a session variable reference
        if let Expression::Identifier(id) = expr {
            if let Some(value) = self.state.session_vars.get(&id.name) {
                return Ok(value.clone());
            }
        }

        // Evaluate normally
        self.state.runtime.execute_expression(expr)
    }

    /// Define a function in REPL context
    fn define_repl_function(&mut self, func_stmt: &FunctionDef) -> UmbraResult<()> {
        // Store function in session for reuse
        self.state.runtime.define_function(func_stmt)?;
        println!("✅ Function '{}' defined", func_stmt.name);
        Ok(())
    }

    /// Handle imports in REPL context
    fn handle_repl_import(&mut self, import_stmt: &ImportStatement) -> UmbraResult<()> {
        // Enhanced import handling for REPL
        let module_name = match &import_stmt.import_type {
            crate::parser::ast::ImportType::Module(path) => path.to_string(),
            crate::parser::ast::ImportType::ModuleAs(path, _) => path.to_string(),
            crate::parser::ast::ImportType::Symbol(path, _) => path.to_string(),
            crate::parser::ast::ImportType::SymbolAs(path, _, _) => path.to_string(),
            crate::parser::ast::ImportType::Wildcard(path) => path.to_string(),
            crate::parser::ast::ImportType::Multiple(path, _) => path.to_string(),
        };

        // Check for special REPL modules
        match module_name.as_str() {
            "repl" => {
                // Import REPL utilities
                self.import_repl_utilities()?;
            }
            "ai" | "ml" => {
                // Import AI/ML utilities
                self.import_aiml_utilities()?;
            }
            "viz" | "plot" => {
                // Import visualization utilities
                self.import_viz_utilities()?;
            }
            _ => {
                // Regular module import
                self.state.runtime.import_module(import_stmt)?;
            }
        }

        println!("📦 Imported module: {}", module_name);
        Ok(())
    }

    /// Import REPL utilities
    fn import_repl_utilities(&mut self) -> UmbraResult<()> {
        // Add REPL-specific utilities
        self.state.runtime.add_builtin_function("history", |_| {
            Ok(RuntimeValue::String("Command history".to_string()))
        })?;

        self.state.runtime.add_builtin_function("save_session", |args| {
            if args.is_empty() {
                return Err(UmbraError::Runtime("save_session() requires filename".to_string()));
            }
            Ok(RuntimeValue::String("Session saved".to_string()))
        })?;

        Ok(())
    }

    /// Import AI/ML utilities
    fn import_aiml_utilities(&mut self) -> UmbraResult<()> {
        // Add AI/ML convenience functions
        self.state.runtime.add_builtin_function("quick_train", |args| {
            if args.len() < 2 {
                return Err(UmbraError::Runtime("quick_train() requires model and data".to_string()));
            }
            Ok(RuntimeValue::String("Quick training completed".to_string()))
        })?;

        Ok(())
    }

    /// Import visualization utilities
    fn import_viz_utilities(&mut self) -> UmbraResult<()> {
        // Add visualization functions
        self.state.runtime.add_builtin_function("plot", |args| {
            if args.is_empty() {
                return Err(UmbraError::Runtime("plot() requires data".to_string()));
            }
            Ok(RuntimeValue::String("Plot created".to_string()))
        })?;

        Ok(())
    }

    /// Execute AI/ML training statement
    fn execute_train_statement(&mut self, train_stmt: &TrainStatement) -> UmbraResult<RuntimeValue> {
        println!("🏋️ Training model: {}", train_stmt.model);

        // Use the AI/ML executor
        self.state.aiml_executor.execute_train(train_stmt)
    }

    /// Execute AI/ML evaluation statement
    fn execute_evaluate_statement(&mut self, eval_stmt: &EvaluateStatement) -> UmbraResult<RuntimeValue> {
        println!("📊 Evaluating model: {}", eval_stmt.model);

        // Use the AI/ML executor
        self.state.aiml_executor.execute_evaluate(eval_stmt)
    }

    /// Execute AI/ML prediction statement
    fn execute_predict_statement(&mut self, predict_stmt: &PredictStatement) -> UmbraResult<RuntimeValue> {
        println!("🔮 Making prediction with model: {}", predict_stmt.model);

        // Use the AI/ML executor
        self.state.aiml_executor.execute_predict(predict_stmt)
    }

    /// Execute visualization statement
    fn execute_visualize_statement(&mut self, viz_stmt: &VisualizeStatement) -> UmbraResult<RuntimeValue> {
        println!("📈 Creating {} visualization", viz_stmt.metric);

        // Use the AI/ML executor for visualization
        self.state.aiml_executor.execute_visualize(viz_stmt)
    }

    /// Format result for display
    fn format_result(&self, result: &RuntimeValue) -> String {
        match result {
            RuntimeValue::List(list) => {
                if list.len() > 10 {
                    format!("[{} items: {}, {}, ..., {}]",
                        list.len(),
                        list[0].to_string(),
                        list[1].to_string(),
                        list[list.len()-1].to_string()
                    )
                } else {
                    format!("[{}]", list.iter().map(|v| v.to_string()).collect::<Vec<_>>().join(", "))
                }
            }
            RuntimeValue::Map(map) => {
                if map.len() > 5 {
                    format!("{{{}... ({} items)}}",
                        map.iter().take(3).map(|(k, v)| format!("{}: {}", k, v.to_string())).collect::<Vec<_>>().join(", "),
                        map.len()
                    )
                } else {
                    format!("{{{}}}", map.iter().map(|(k, v)| format!("{}: {}", k, v.to_string())).collect::<Vec<_>>().join(", "))
                }
            }
            RuntimeValue::String(s) => {
                if s.len() > 100 {
                    format!("\"{}...\" ({} chars)", &s[..97], s.len())
                } else {
                    format!("\"{}\"", s)
                }
            }
            _ => result.to_string()
        }
    }

    /// Get current memory usage
    fn get_memory_usage(&self) -> usize {
        // Simplified memory usage calculation
        // In a real implementation, this would measure actual memory usage
        self.state.session_vars.len() * 64 + 1024 // Rough estimate
    }

    /// Add command to history
    fn add_to_history(&mut self, cmd: String) {
        self.state.history.push(cmd);
        
        // Limit history size
        if self.state.history.len() > self.config.max_history {
            self.state.history.remove(0);
        }
    }

    /// Load history from file
    fn load_history(&mut self) -> UmbraResult<()> {
        if let Some(ref history_file) = self.config.history_file {
            if history_file.exists() {
                let content = std::fs::read_to_string(history_file)
                    .map_err(|e| UmbraError::Runtime(format!("Failed to load history: {}", e)))?;
                
                for line in content.lines() {
                    if !line.trim().is_empty() {
                        self.state.history.push(line.to_string());
                    }
                }
            }
        }
        Ok(())
    }

    /// Save history to file
    fn save_history(&self) -> UmbraResult<()> {
        if let Some(ref history_file) = self.config.history_file {
            let mut file = OpenOptions::new()
                .create(true)
                .write(true)
                .truncate(true)
                .open(history_file)
                .map_err(|e| UmbraError::Runtime(format!("Failed to save history: {}", e)))?;

            for cmd in &self.state.history {
                writeln!(file, "{}", cmd)
                    .map_err(|e| UmbraError::Runtime(format!("Failed to write history: {}", e)))?;
            }
        }
        Ok(())
    }
}

/// Create and start REPL with default configuration
pub fn start_repl() -> UmbraResult<()> {
    let config = ReplConfig::default();
    let mut repl = Repl::new(config);
    repl.start()
}

/// Create and start REPL with custom configuration
pub fn start_repl_with_config(config: ReplConfig) -> UmbraResult<()> {
    let mut repl = Repl::new(config);
    repl.start()
}
