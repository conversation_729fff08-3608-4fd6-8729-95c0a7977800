// Platform-specific implementations for Umbra compiler
// This module provides cross-platform abstractions for system-specific functionality

#[cfg(windows)]
pub mod windows;

#[cfg(unix)]
pub mod unix;

// Re-export platform-specific implementations
#[cfg(windows)]
pub use windows::*;

#[cfg(unix)]
pub use unix::*;

// Cross-platform abstractions
pub trait PlatformFFI {
    fn call_function(&self, func_ptr: *const std::os::raw::c_void, args: &[*const std::os::raw::c_void]) -> Result<*const std::os::raw::c_void, String>;
}

pub trait PlatformDL {
    fn open(path: &str) -> Result<Self, String> where Self: Sized;
    fn symbol(&self, name: &str) -> Result<*const std::os::raw::c_void, String>;
}

pub trait PlatformRT {
    fn get_time() -> Result<u64, String>;
}

pub trait PlatformTerminal {
    fn get_size() -> Result<(u16, u16), String>;
    fn set_color(foreground: u16, background: u16) -> Result<(), String>;
}

pub trait PlatformCompression {
    fn compress(data: &[u8]) -> Result<Vec<u8>, String>;
    fn decompress(data: &[u8]) -> Result<Vec<u8>, String>;
}

pub trait PlatformXML {
    fn parse(xml_data: &str) -> Result<XMLDocument, String>;
}

// Platform-specific type aliases
#[cfg(windows)]
pub type NativeFFI = windows::WindowsFFI;
#[cfg(windows)]
pub type NativeDL = windows::WindowsDL;
#[cfg(windows)]
pub type NativeRT = windows::WindowsRT;
#[cfg(windows)]
pub type NativeTerminal = windows::WindowsTerminal;
#[cfg(windows)]
pub type NativeCompression = windows::WindowsCompression;
#[cfg(windows)]
pub type NativeXML = windows::WindowsXML;

#[cfg(unix)]
pub type NativeFFI = unix::UnixFFI;
#[cfg(unix)]
pub type NativeDL = unix::UnixDL;
#[cfg(unix)]
pub type NativeRT = unix::UnixRT;
#[cfg(unix)]
pub type NativeTerminal = unix::UnixTerminal;
#[cfg(unix)]
pub type NativeCompression = unix::UnixCompression;
#[cfg(unix)]
pub type NativeXML = unix::UnixXML;

// Initialize platform-specific support
pub fn init_platform() -> Result<(), String> {
    #[cfg(windows)]
    return windows::init_windows_platform();
    
    #[cfg(unix)]
    return unix::init_unix_platform();
}

// Cleanup platform-specific support
pub fn cleanup_platform() {
    #[cfg(windows)]
    windows::cleanup_windows_platform();
    
    #[cfg(unix)]
    unix::cleanup_unix_platform();
}

// Common XML document structure
pub struct XMLDocument {
    pub elements: Vec<String>,
    pub texts: Vec<String>,
}

impl XMLDocument {
    pub fn new() -> Self {
        XMLDocument {
            elements: Vec::new(),
            texts: Vec::new(),
        }
    }
    
    pub fn add_element(&mut self, name: String) {
        self.elements.push(name);
    }
    
    pub fn add_text(&mut self, text: String) {
        self.texts.push(text);
    }
    
    pub fn get_elements(&self) -> &[String] {
        &self.elements
    }
    
    pub fn get_texts(&self) -> &[String] {
        &self.texts
    }
}
