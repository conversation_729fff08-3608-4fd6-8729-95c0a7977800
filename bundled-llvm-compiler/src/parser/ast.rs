use crate::error::SourceLocation;
use serde::{Deserialize, Serialize};
use crate::semantic::symbol_table::StructureField;

/// Root AST node representing an entire Umbra program
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct Program {
    pub statements: Vec<Statement>,
    pub location: SourceLocation,
}

/// Base trait for all AST nodes
pub trait AstNode {
    #[allow(dead_code)]
    fn location(&self) -> SourceLocation;
}

/// Statement AST nodes
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum Statement {
    Module(ModuleDeclaration),
    Import(ImportStatement),
    Export(ExportStatement),
    Function(FunctionDef),
    Structure(StructureDef),
    Trait(TraitDef),
    Implementation(ImplDef),
    Variable(VariableDecl),
    Assignment(Assignment),
    Expression(ExpressionStatement),
    When(WhenStatement),
    Repeat(RepeatStatement),
    Return(ReturnStatement),
    Train(TrainStatement),
    Evaluate(EvaluateStatement),
    Visualize(VisualizeStatement),
    Predict(PredictStatement),
    // Database operations
    Query(QueryStatement),
    Transaction(TransactionStatement),
    Migration(MigrationStatement),
    // Error handling statements
    Try(TryStatement),
    Throw(ThrowStatement),
    Panic(PanicStatement),
    Error(ErrorDefinition),
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct ModuleDeclaration {
    pub name: ModulePath,
    pub location: SourceLocation,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct ImportStatement {
    pub import_type: ImportType,
    pub location: SourceLocation,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum ImportType {
    /// import module_path
    Module(ModulePath),
    /// import module_path as alias
    ModuleAs(ModulePath, String),
    /// from module_path import symbol
    Symbol(ModulePath, String),
    /// from module_path import symbol as alias
    SymbolAs(ModulePath, String, String),
    /// from module_path import *
    Wildcard(ModulePath),
    /// import { symbol1, symbol2 } from module_path
    Multiple(ModulePath, Vec<ImportItem>),
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct ImportItem {
    pub name: String,
    pub alias: Option<String>,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct ExportStatement {
    pub export_type: ExportType,
    pub location: SourceLocation,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum ExportType {
    /// export function_name
    Function(String),
    /// export struct_name
    Struct(String),
    /// export variable_name
    Variable(String),
    /// export { name1, name2 }
    Multiple(Vec<String>),
    /// export from module_path
    ReExport(ModulePath),
    /// export { symbol } from module_path
    ReExportSymbol(ModulePath, String),
    /// export * from module_path
    ReExportWildcard(ModulePath),
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct ModulePath {
    pub segments: Vec<String>,
}

impl ModulePath {
    pub fn new(segments: Vec<String>) -> Self {
        Self { segments }
    }

    pub fn from_string(path: &str) -> Self {
        Self {
            segments: path.split("::").map(|s| s.to_string()).collect(),
        }
    }

    pub fn to_string(&self) -> String {
        self.segments.join("::")
    }
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct FunctionDef {
    pub visibility: Visibility,
    pub name: String,
    pub type_params: Vec<TypeParameter>, // Enhanced generic type parameters with bounds
    pub parameters: Vec<Parameter>,
    pub return_type: Type,
    pub body: Vec<Statement>,
    pub constraints: Vec<WhereClause>, // Where clauses for complex constraints
    pub location: SourceLocation,
}

/// Type parameter with bounds and constraints
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct TypeParameter {
    pub name: String,
    pub bounds: Vec<TypeBound>,
    pub default: Option<Type>,
    pub variance: Option<Variance>,
    pub location: SourceLocation,
}

/// Type bound for constraining type parameters
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum TypeBound {
    /// Must implement a trait
    Trait(String),
    /// Must be a subtype
    Subtype(Type),
    /// Must have specific lifetime
    Lifetime(String),
    /// Higher-ranked trait bound
    HigherRanked(Vec<String>, Box<TypeBound>),
}

/// Variance annotation for type parameters
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum Variance {
    Covariant,     // +T
    Contravariant, // -T
    Invariant,     // T (default)
    Bivariant,     // *T (rare)
}

/// Where clause for complex type constraints
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct WhereClause {
    pub type_param: String,
    pub bounds: Vec<TypeBound>,
    pub location: SourceLocation,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct Parameter {
    pub name: String,
    pub type_annotation: Type,
    pub location: SourceLocation,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct StructureDef {
    pub visibility: Visibility,
    pub name: String,
    pub type_parameters: Vec<String>, // Generic type parameter names like <T, U>
    pub fields: Vec<Field>,
    pub annotations: Vec<Annotation>,
    pub location: SourceLocation,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct TraitDef {
    pub visibility: Visibility,
    pub name: String,
    pub type_params: Vec<TypeParameter>,
    pub supertraits: Vec<String>, // Traits this trait extends
    pub methods: Vec<TraitMethod>,
    pub associated_types: Vec<AssociatedType>,
    pub location: SourceLocation,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct TraitMethod {
    pub name: String,
    pub type_params: Vec<TypeParameter>,
    pub parameters: Vec<Parameter>,
    pub return_type: Type,
    pub default_body: Option<Vec<Statement>>, // Default implementation
    pub location: SourceLocation,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct AssociatedType {
    pub name: String,
    pub bounds: Vec<TypeBound>,
    pub default: Option<Type>,
    pub location: SourceLocation,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct ImplDef {
    pub visibility: Visibility,
    pub type_params: Vec<TypeParameter>,
    pub trait_name: Option<String>, // None for inherent impl
    pub implementing_type: Type,
    pub where_clauses: Vec<WhereClause>,
    pub methods: Vec<FunctionDef>,
    pub associated_types: Vec<TypeAssociation>,
    pub location: SourceLocation,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct TypeAssociation {
    pub name: String,
    pub type_value: Type,
    pub location: SourceLocation,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
#[derive(Default)]
pub enum Visibility {
    Public,
    #[default]
    Private,
}


#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct Field {
    pub name: String,
    pub field_type: Type,
    pub annotations: Vec<Annotation>,
    pub location: SourceLocation,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct Annotation {
    pub name: String,
    pub arguments: Vec<AnnotationArgument>,
    pub location: SourceLocation,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct AnnotationArgument {
    pub name: String,
    pub value: Expression,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct VariableDecl {
    pub name: String,
    pub type_annotation: Type,
    pub value: Expression,
    pub location: SourceLocation,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct Assignment {
    pub name: String,
    pub operator: AssignmentOperator,
    pub value: Expression,
    pub location: SourceLocation,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum AssignmentOperator {
    Assign,      // :=
    AddAssign,   // +=
    SubAssign,   // -=
    MulAssign,   // *=
    DivAssign,   // /=
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct ExpressionStatement {
    pub expression: Expression,
    pub location: SourceLocation,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct WhenStatement {
    pub condition: Expression,
    pub then_body: Vec<Statement>,
    pub else_body: Option<Vec<Statement>>,
    pub location: SourceLocation,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct RepeatStatement {
    pub variable: String,
    pub iterable: Expression,
    pub body: Vec<Statement>,
    pub location: SourceLocation,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct ReturnStatement {
    pub value: Option<Expression>,
    pub location: SourceLocation,
}

// AI/ML specific statements
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct TrainStatement {
    pub model: String,
    pub dataset: String,
    pub config: Vec<TrainParameter>,
    pub location: SourceLocation,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct TrainParameter {
    pub name: String,
    pub value: Expression,
    pub location: SourceLocation,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct EvaluateStatement {
    pub model: String,
    pub dataset: String,
    pub location: SourceLocation,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct VisualizeStatement {
    pub metric: String,
    pub dimension: String,
    pub location: SourceLocation,
}



#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct PredictStatement {
    pub sample: String,
    pub model: String,
    pub location: SourceLocation,
}

/// Expression AST nodes
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum Expression {
    Binary(BinaryOp),
    Unary(UnaryOp),
    Call(FunctionCall),
    MethodCall(MethodCall),
    FieldAccess(FieldAccess),
    IndexAccess(IndexAccess),
    Identifier(Identifier),
    QualifiedIdentifier(QualifiedIdentifier),
    Literal(Literal),
    List(ListLiteral),
    Struct(StructLiteral),
    Match(MatchExpression),
    Some(SomeExpression),
    None(NoneExpression),
    Ok(OkExpression),
    Err(ErrExpression),
    // Error handling expressions
    TryExpression(TryExpression),
    ErrorPropagation(ErrorPropagationExpression),
    // Lambda expressions
    Lambda(LambdaExpression),
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct BinaryOp {
    pub left: Box<Expression>,
    pub operator: BinaryOperator,
    pub right: Box<Expression>,
    pub location: SourceLocation,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum BinaryOperator {
    // Arithmetic
    Add,
    Subtract,
    Multiply,
    Divide,
    Modulo,
    // Comparison
    Equal,
    NotEqual,
    Less,
    LessEqual,
    Greater,
    GreaterEqual,
    // Logical
    And,
    Or,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct UnaryOp {
    pub operator: UnaryOperator,
    pub operand: Box<Expression>,
    pub location: SourceLocation,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum UnaryOperator {
    Minus,
    Not,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct FunctionCall {
    pub name: String,
    pub arguments: Vec<Expression>,
    pub location: SourceLocation,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct MethodCall {
    pub object: Box<Expression>,
    pub method_name: String,
    pub arguments: Vec<Expression>,
    pub location: SourceLocation,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct FieldAccess {
    pub object: Box<Expression>,
    pub field: String,
    pub location: SourceLocation,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct IndexAccess {
    pub object: Box<Expression>,
    pub index: Box<Expression>,
    pub location: SourceLocation,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct Identifier {
    pub name: String,
    pub location: SourceLocation,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct QualifiedIdentifier {
    pub path: ModulePath,
    pub location: SourceLocation,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct ListLiteral {
    pub elements: Vec<Expression>,
    pub location: SourceLocation,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct StructLiteral {
    pub struct_name: String,
    pub fields: Vec<StructFieldInit>,
    pub location: SourceLocation,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct StructFieldInit {
    pub name: String,
    pub value: Expression,
    pub location: SourceLocation,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct MatchExpression {
    pub expression: Box<Expression>,
    pub arms: Vec<MatchArm>,
    pub location: SourceLocation,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct SomeExpression {
    pub value: Box<Expression>,
    pub location: SourceLocation,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct NoneExpression {
    pub location: SourceLocation,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct OkExpression {
    pub value: Box<Expression>,
    pub location: SourceLocation,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct ErrExpression {
    pub error: Box<Expression>,
    pub location: SourceLocation,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct MatchArm {
    pub pattern: Pattern,
    pub guard: Option<Expression>,
    pub body: Expression,
    pub location: SourceLocation,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum Pattern {
    Literal(Literal),
    Identifier(String),
    Struct(String, Vec<FieldPattern>),
    Some(Box<Pattern>),
    None,
    Ok(Box<Pattern>),
    Err(Box<Pattern>),
    Union(Box<Pattern>, usize), // Pattern and which union variant it matches
    Wildcard,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct FieldPattern {
    pub name: String,
    pub pattern: Box<Pattern>,
}

/// Literal values
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum Literal {
    Integer(i64),
    Float(f64),
    String(String),
    Boolean(bool),
}

/// Lambda expression: |param1, param2| -> body
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct LambdaExpression {
    pub parameters: Vec<LambdaParameter>,
    pub return_type: Option<Type>,
    pub body: Box<Expression>,
    pub location: SourceLocation,
}

/// Lambda parameter with optional type annotation
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct LambdaParameter {
    pub name: String,
    pub type_annotation: Option<Type>,
    pub location: SourceLocation,
}

/// Type annotations with full generic support
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum Type {
    Basic(BasicType),
    List(Box<Type>),
    Struct(String), // User-defined struct type by name
    Optional(Box<Type>), // Optional/nullable type
    Result(Box<Type>, Box<Type>), // Result<T, E> type
    Union(Vec<Type>), // Union type
    Generic(String, Vec<Type>), // Generic type with parameters
    Auto,
    // Collection types
    HashMap(Box<Type>, Box<Type>), // HashMap<K, V>
    HashSet(Box<Type>), // HashSet<T>
    // Smart pointer types
    Box(Box<Type>), // Box<T>
    Rc(Box<Type>), // Rc<T>
    Arc(Box<Type>), // Arc<T>
    Weak(Box<Type>), // Weak<T>
    RefCell(Box<Type>), // RefCell<T>
    Mutex(Box<Type>), // Mutex<T>
    // Enhanced generic support
    TypeParameter {
        name: String,
        bounds: Vec<String>, // Trait bounds like T: Clone + Send
    },
    GenericInstance {
        base: String,
        type_args: Vec<Type>,
    },
    AssociatedType {
        trait_name: String,
        type_name: String,
    },
    HigherKinded {
        constructor: String,
        arity: usize,
    },
    // Function types
    Function(Vec<Type>, Box<Type>), // (T1, T2) -> R
    // Tuple types
    Tuple(Vec<Type>), // (T1, T2, T3)
    // Array types
    Array(Box<Type>, usize), // [T; N]
    Slice(Box<Type>), // [T]
    // Reference types
    Reference(Box<Type>), // &T
    MutableReference(Box<Type>), // &mut T
    // Pointer types
    RawPointer(Box<Type>), // *T
    MutableRawPointer(Box<Type>), // *mut T
    // Type variables for Hindley-Milner inference
    TypeVariable(u32), // Type variable with unique ID
    // Self type for trait methods
    SelfType, // Self
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum BasicType {
    Integer,
    Float,
    String,
    Boolean,
    Void,
    Dataset,
    Model,
    Tensor,
}

// Implement AstNode for all statement types
impl AstNode for Statement {
    fn location(&self) -> SourceLocation {
        match self {
            Statement::Module(s) => s.location,
            Statement::Import(s) => s.location,
            Statement::Export(s) => s.location,
            Statement::Function(s) => s.location,
            Statement::Structure(s) => s.location,
            Statement::Variable(s) => s.location,
            Statement::Assignment(s) => s.location,
            Statement::Expression(s) => s.location,
            Statement::When(s) => s.location,
            Statement::Repeat(s) => s.location,
            Statement::Return(s) => s.location,
            Statement::Train(s) => s.location,
            Statement::Evaluate(s) => s.location,
            Statement::Visualize(s) => s.location,
            Statement::Predict(s) => s.location,
            // Error handling statements
            Statement::Try(s) => s.location,
            Statement::Throw(s) => s.location,
            Statement::Panic(s) => s.location,
            Statement::Error(s) => s.location,
            Statement::Trait(s) => s.location,
            Statement::Implementation(s) => s.location,
            Statement::Query(s) => s.location,
            Statement::Transaction(s) => s.location,
            Statement::Migration(s) => s.location,
        }
    }
}

impl AstNode for Expression {
    fn location(&self) -> SourceLocation {
        match self {
            Expression::Binary(e) => e.location,
            Expression::Unary(e) => e.location,
            Expression::Call(e) => e.location,
            Expression::MethodCall(e) => e.location,
            Expression::FieldAccess(e) => e.location,
            Expression::IndexAccess(e) => e.location,
            Expression::Identifier(e) => e.location,
            Expression::QualifiedIdentifier(e) => e.location,
            Expression::Literal(_) => SourceLocation::new(0, 0), // Literals don't have meaningful locations
            Expression::List(e) => e.location,
            Expression::Struct(e) => e.location,
            Expression::Match(e) => e.location,
            Expression::Some(e) => e.location,
            Expression::None(e) => e.location,
            Expression::Ok(e) => e.location,
            Expression::Err(e) => e.location,
            Expression::TryExpression(e) => e.location,
            Expression::ErrorPropagation(e) => e.location,
            Expression::Lambda(e) => e.location,
        }
    }
}

// Error handling AST nodes

/// Try statement with catch and finally blocks
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct TryStatement {
    pub try_block: Vec<Statement>,
    pub catch_clauses: Vec<CatchClause>,
    pub finally_block: Option<Vec<Statement>>,
    pub location: SourceLocation,
}

/// Catch clause for specific error types
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct CatchClause {
    pub error_type: Option<Type>,
    pub error_variable: Option<String>,
    pub catch_block: Vec<Statement>,
    pub location: SourceLocation,
}

/// Throw statement for raising errors
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct ThrowStatement {
    pub error_expression: Expression,
    pub location: SourceLocation,
}

/// Panic statement for unrecoverable errors
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct PanicStatement {
    pub message: Option<Expression>,
    pub location: SourceLocation,
}

/// Try expression for error handling in expressions
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct TryExpression {
    pub expression: Box<Expression>,
    pub location: SourceLocation,
}

/// Error propagation expression (? operator)
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct ErrorPropagationExpression {
    pub expression: Box<Expression>,
    pub location: SourceLocation,
}

/// Error type definition statement
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct ErrorDefinition {
    pub name: String,
    pub fields: Vec<StructureField>,
    pub parent_error: Option<String>,
    pub visibility: Visibility,
    pub location: SourceLocation,
}

/// Custom error type for type system
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct ErrorType {
    pub name: String,
    pub fields: Vec<StructureField>,
    pub parent_error: Option<String>,
    pub location: SourceLocation,
}

impl std::fmt::Display for BinaryOperator {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            BinaryOperator::Add => write!(f, "+"),
            BinaryOperator::Subtract => write!(f, "-"),
            BinaryOperator::Multiply => write!(f, "*"),
            BinaryOperator::Divide => write!(f, "/"),
            BinaryOperator::Modulo => write!(f, "%"),
            BinaryOperator::Equal => write!(f, "=="),
            BinaryOperator::NotEqual => write!(f, "!="),
            BinaryOperator::Less => write!(f, "<"),
            BinaryOperator::LessEqual => write!(f, "<="),
            BinaryOperator::Greater => write!(f, ">"),
            BinaryOperator::GreaterEqual => write!(f, ">="),
            BinaryOperator::And => write!(f, "and"),
            BinaryOperator::Or => write!(f, "or"),
        }
    }
}

impl std::fmt::Display for UnaryOperator {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            UnaryOperator::Minus => write!(f, "-"),
            UnaryOperator::Not => write!(f, "not"),
        }
    }
}

impl std::fmt::Display for BasicType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            BasicType::Integer => write!(f, "Integer"),
            BasicType::Float => write!(f, "Float"),
            BasicType::String => write!(f, "String"),
            BasicType::Boolean => write!(f, "Boolean"),
            BasicType::Void => write!(f, "Void"),
            BasicType::Dataset => write!(f, "Dataset"),
            BasicType::Model => write!(f, "Model"),
            BasicType::Tensor => write!(f, "Tensor"),
        }
    }
}

impl std::fmt::Display for Type {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            Type::Basic(t) => write!(f, "{t}"),
            Type::List(t) => write!(f, "List[{t}]"),
            Type::Struct(name) => write!(f, "{name}"),
            Type::Optional(t) => write!(f, "Optional[{t}]"),
            Type::Result(ok, err) => write!(f, "Result[{ok}, {err}]"),
            Type::Union(types) => {
                write!(f, "Union[")?;
                for (i, t) in types.iter().enumerate() {
                    if i > 0 { write!(f, ", ")?; }
                    write!(f, "{t}")?;
                }
                write!(f, "]")
            },
            Type::Generic(name, params) => {
                write!(f, "{name}[")?;
                for (i, t) in params.iter().enumerate() {
                    if i > 0 { write!(f, ", ")?; }
                    write!(f, "{t}")?;
                }
                write!(f, "]")
            },
            Type::Auto => write!(f, "auto"),
            Type::HashMap(k, v) => write!(f, "HashMap<{}, {}>", k, v),
            Type::HashSet(t) => write!(f, "HashSet<{}>", t),
            Type::Box(t) => write!(f, "Box<{}>", t),
            Type::Rc(t) => write!(f, "Rc<{}>", t),
            Type::Arc(t) => write!(f, "Arc<{}>", t),
            Type::Weak(t) => write!(f, "Weak<{}>", t),
            Type::RefCell(t) => write!(f, "RefCell<{}>", t),
            Type::Mutex(t) => write!(f, "Mutex<{}>", t),
            Type::TypeParameter { name, .. } => write!(f, "{}", name),
            Type::GenericInstance { base, type_args } => {
                write!(f, "{}<{}>", base, type_args.iter().map(|t| t.to_string()).collect::<Vec<_>>().join(", "))
            },
            Type::AssociatedType { trait_name, type_name } => write!(f, "{}::{}", trait_name, type_name),
            Type::HigherKinded { constructor, .. } => write!(f, "{}", constructor),
            Type::Function(params, ret) => {
                write!(f, "({}) -> {}", params.iter().map(|t| t.to_string()).collect::<Vec<_>>().join(", "), ret)
            },
            Type::Tuple(types) => {
                write!(f, "({})", types.iter().map(|t| t.to_string()).collect::<Vec<_>>().join(", "))
            },
            Type::Array(t, size) => write!(f, "[{}; {}]", t, size),
            Type::Slice(t) => write!(f, "[{}]", t),
            Type::Reference(t) => write!(f, "&{}", t),
            Type::MutableReference(t) => write!(f, "&mut {}", t),
            Type::RawPointer(t) => write!(f, "*{}", t),
            Type::MutableRawPointer(t) => write!(f, "*mut {}", t),
            Type::TypeVariable(id) => write!(f, "'{}", id),
            Type::SelfType => write!(f, "Self"),
        }
    }
}

// Database operation AST nodes
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct QueryStatement {
    pub query_type: QueryType,
    pub sql: Option<String>,
    pub parameters: Vec<Expression>,
    pub result_binding: Option<String>,
    pub location: SourceLocation,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum QueryType {
    Raw(String),
    Select {
        fields: Vec<String>,
        from: String,
        where_clause: Option<Expression>,
        order_by: Option<Vec<OrderByClause>>,
        limit: Option<Expression>,
    },
    Insert {
        table: String,
        fields: Vec<String>,
        values: Vec<Expression>,
    },
    Update {
        table: String,
        assignments: Vec<Assignment>,
        where_clause: Option<Expression>,
    },
    Delete {
        table: String,
        where_clause: Option<Expression>,
    },
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct OrderByClause {
    pub field: String,
    pub direction: OrderDirection,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum OrderDirection {
    Asc,
    Desc,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct TransactionStatement {
    pub transaction_type: TransactionType,
    pub body: Option<Vec<Statement>>,
    pub location: SourceLocation,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum TransactionType {
    Begin {
        isolation_level: Option<IsolationLevel>,
        read_only: bool,
    },
    Commit,
    Rollback,
    Savepoint(String),
    RollbackToSavepoint(String),
    ReleaseSavepoint(String),
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum IsolationLevel {
    ReadUncommitted,
    ReadCommitted,
    RepeatableRead,
    Serializable,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct MigrationStatement {
    pub migration_type: MigrationType,
    pub location: SourceLocation,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum MigrationType {
    Up,
    Down,
    Create {
        name: String,
    },
    Status,
    Reset,
}

impl AstNode for QueryStatement {
    fn location(&self) -> SourceLocation {
        self.location
    }
}

impl AstNode for TransactionStatement {
    fn location(&self) -> SourceLocation {
        self.location
    }
}

impl AstNode for MigrationStatement {
    fn location(&self) -> SourceLocation {
        self.location
    }
}
