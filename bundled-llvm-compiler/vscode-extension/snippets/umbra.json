{"Function": {"prefix": "fn", "body": ["function ${1:name}(${2:params}) -> ${3:ReturnType} {", "\t${4:// function body}", "}"], "description": "Create a function"}, "If Statement": {"prefix": "if", "body": ["if ${1:condition} {", "\t${2:// if body}", "}"], "description": "Create an if statement"}, "When Statement": {"prefix": "when", "body": ["when ${1:expression} {", "\t${2:pattern} => ${3:result},", "\totherwise => ${4:default}", "}"], "description": "Create a when statement"}, "For Loop": {"prefix": "for", "body": ["for ${1:item} in ${2:iterable} {", "\t${3:// loop body}", "}"], "description": "Create a for loop"}, "Repeat Loop": {"prefix": "repeat", "body": ["repeat ${1:times} {", "\t${2:// loop body}", "}"], "description": "Create a repeat loop"}, "Train Model": {"prefix": "train", "body": ["train ${1:model_name} using ${2:dataset} {", "\talgorithm: ${3:LinearRegression},", "\tparameters: {", "\t\t${4:// training parameters}", "\t}", "}"], "description": "Create a train statement"}, "Evaluate Model": {"prefix": "evaluate", "body": ["evaluate ${1:model_name} on ${2:test_data} {", "\tmetrics: [${3:accuracy, precision, recall}],", "\toutput: ${4:results}", "}"], "description": "Create an evaluate statement"}, "Predict": {"prefix": "predict", "body": ["predict ${1:model_name} with ${2:input_data} {", "\toutput: ${3:predictions}", "}"], "description": "Create a predict statement"}, "Visualize": {"prefix": "visualize", "body": ["visualize ${1:data} as ${2:chart_type} {", "\ttitle: \"${3:Chart Title}\",", "\txlabel: \"${4:X Axis}\",", "\tylabel: \"${5:Y Axis}\"", "}"], "description": "Create a visualize statement"}, "Class": {"prefix": "class", "body": ["class ${1:ClassName} {", "\t${2:// class members}", "\t", "\tfunction new(${3:params}) -> ${1:ClassName} {", "\t\t${4:// constructor}", "\t}", "}"], "description": "Create a class"}, "Import": {"prefix": "bring", "body": ["bring ${1:module_name}"], "description": "Import a module"}}