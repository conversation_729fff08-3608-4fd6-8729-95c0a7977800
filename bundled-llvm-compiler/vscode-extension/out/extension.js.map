{"version": 3, "file": "extension.js", "sourceRoot": "", "sources": ["../src/extension.ts"], "names": [], "mappings": ";AAAA;;;;;GAKG;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,+CAAiC;AACjC,2CAA6B;AAC7B,6DAA+C;AAC/C,qDAKoC;AAEpC,IAAI,aAAmC,CAAC;AACxC,IAAI,aAAmC,CAAC;AACxC,IAAI,MAAsB,CAAC;AAE3B,SAAgB,QAAQ,CAAC,OAAgC;IACrD,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;IAE3D,wBAAwB;IACxB,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;IAC3D,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAE1C,yBAAyB;IACzB,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,kBAAkB,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;IACvF,aAAa,CAAC,IAAI,GAAG,eAAe,CAAC;IACrC,aAAa,CAAC,OAAO,GAAG,wBAAwB,CAAC;IACjD,aAAa,CAAC,IAAI,EAAE,CAAC;IACrB,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAE1C,6BAA6B;IAC7B,wBAAwB,CAAC,OAAO,CAAC,CAAC;IAElC,oBAAoB;IACpB,gBAAgB,CAAC,OAAO,CAAC,CAAC;IAE1B,aAAa,CAAC,UAAU,CAAC,4CAA4C,CAAC,CAAC;AAC3E,CAAC;AArBD,4BAqBC;AAED,SAAgB,UAAU;IACtB,IAAI,aAAa,EAAE;QACf,aAAa,CAAC,OAAO,EAAE,CAAC;KAC3B;IACD,IAAI,aAAa,EAAE;QACf,aAAa,CAAC,OAAO,EAAE,CAAC;KAC3B;IACD,IAAI,MAAM,EAAE;QACR,OAAO,MAAM,CAAC,IAAI,EAAE,CAAC;KACxB;IACD,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;AAC7B,CAAC;AAXD,gCAWC;AAED,SAAS,wBAAwB,CAAC,OAAgC;IAC9D,qCAAqC;IACrC,MAAM,SAAS,GAAG,mBAAmB,EAAE,CAAC;IACxC,IAAI,CAAC,SAAS,EAAE;QACZ,aAAa,CAAC,UAAU,CAAC,mEAAmE,CAAC,CAAC;QAC9F,OAAO;KACV;IAED,+BAA+B;IAC/B,MAAM,aAAa,GAAkB;QACjC,OAAO,EAAE,SAAS;QAClB,IAAI,EAAE,CAAC,KAAK,CAAC;QACb,SAAS,EAAE,oBAAa,CAAC,KAAK;KACjC,CAAC;IAEF,+BAA+B;IAC/B,MAAM,aAAa,GAA0B;QACzC,gBAAgB,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC;QACzD,WAAW,EAAE;YACT,UAAU,EAAE,MAAM,CAAC,SAAS,CAAC,uBAAuB,CAAC,YAAY,CAAC;SACrE;QACD,aAAa,EAAE,aAAa;KAC/B,CAAC;IAEF,6BAA6B;IAC7B,MAAM,GAAG,IAAI,qBAAc,CACvB,qBAAqB,EACrB,uBAAuB,EACvB,aAAa,EACb,aAAa,CAChB,CAAC;IAEF,mBAAmB;IACnB,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE;QACrB,aAAa,CAAC,UAAU,CAAC,4CAA4C,CAAC,CAAC;IAC3E,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;QACf,aAAa,CAAC,UAAU,CAAC,0CAA0C,KAAK,EAAE,CAAC,CAAC;IAChF,CAAC,CAAC,CAAC;IAEH,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACvC,CAAC;AAED,SAAS,mBAAmB;IACxB,oDAAoD;IACpD,MAAM,aAAa,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,IAAI,EAAE,CAAC;IAC/E,MAAM,aAAa,GAAG;QAClB,oBAAoB;QACpB,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,gBAAgB,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,CAAC;QACxE,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,CAAC;QACtD,cAAc;QACd,OAAO;QACP,4BAA4B;QAC5B,sBAAsB;QACtB,gBAAgB;QAChB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,IAAI,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,CAAC;KAC9D,CAAC;IAEF,KAAK,MAAM,SAAS,IAAI,aAAa,EAAE;QACnC,IAAI;YACA,aAAa,CAAC,QAAQ,CAAC,IAAI,SAAS,aAAa,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC,CAAC;YACxE,aAAa,CAAC,UAAU,CAAC,4BAA4B,SAAS,EAAE,CAAC,CAAC;YAClE,OAAO,SAAS,CAAC;SACpB;QAAC,OAAO,KAAK,EAAE;YACZ,qBAAqB;SACxB;KACJ;IAED,OAAO,IAAI,CAAC;AAChB,CAAC;AAED,SAAS,gBAAgB,CAAC,OAAgC;IACtD,kBAAkB;IAClB,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,eAAe,EAAE,KAAK,IAAI,EAAE;QACxD,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;QAC9C,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,QAAQ,CAAC,UAAU,KAAK,OAAO,EAAE;YACnD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,sBAAsB,CAAC,CAAC;YACvD,OAAO;SACV;QAED,MAAM,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;IAC3C,CAAC,CAAC,CACL,CAAC;IAEF,cAAc;IACd,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,WAAW,EAAE,KAAK,IAAI,EAAE;QACpD,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;QAC9C,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,QAAQ,CAAC,UAAU,KAAK,OAAO,EAAE;YACnD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,sBAAsB,CAAC,CAAC;YACvD,OAAO;SACV;QAED,MAAM,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;IACvC,CAAC,CAAC,CACL,CAAC;IAEF,gBAAgB;IAChB,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,aAAa,EAAE,KAAK,IAAI,EAAE;QACtD,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;QAC9C,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,QAAQ,CAAC,UAAU,KAAK,OAAO,EAAE;YACnD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,sBAAsB,CAAC,CAAC;YACvD,OAAO;SACV;QAED,MAAM,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;IACzC,CAAC,CAAC,CACL,CAAC;IAEF,eAAe;IACf,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,YAAY,EAAE,KAAK,IAAI,EAAE;QACrD,MAAM,QAAQ,EAAE,CAAC;IACrB,CAAC,CAAC,CACL,CAAC;IAEF,eAAe;IACf,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,YAAY,EAAE,KAAK,IAAI,EAAE;QACrD,MAAM,SAAS,EAAE,CAAC;IACtB,CAAC,CAAC,CACL,CAAC;IAEF,iBAAiB;IACjB,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,cAAc,EAAE,KAAK,IAAI,EAAE;QACvD,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;QAC9C,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,QAAQ,CAAC,UAAU,KAAK,OAAO,EAAE;YACnD,OAAO;SACV;QAED,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,8BAA8B,CAAC,CAAC;IACzE,CAAC,CAAC,CACL,CAAC;IAEF,sBAAsB;IACtB,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,kBAAkB,EAAE,KAAK,IAAI,EAAE;QAC3D,MAAM,gBAAgB,EAAE,CAAC;IAC7B,CAAC,CAAC,CACL,CAAC;IAEF,iBAAiB;IACjB,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,kBAAkB,EAAE,KAAK,IAAI,EAAE;QAC3D,MAAM,YAAY,EAAE,CAAC;IACzB,CAAC,CAAC,CACL,CAAC;IAEF,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,qBAAqB,EAAE,KAAK,IAAI,EAAE;QAC9D,MAAM,eAAe,EAAE,CAAC;IAC5B,CAAC,CAAC,CACL,CAAC;IAEF,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,sBAAsB,EAAE,KAAK,IAAI,EAAE;QAC/D,MAAM,mBAAmB,EAAE,CAAC;IAChC,CAAC,CAAC,CACL,CAAC;AACN,CAAC;AAED,sDAAsD;AAEtD,KAAK,UAAU,WAAW,CAAC,GAAe;IACtC,eAAe,CAAC,cAAc,CAAC,CAAC;IAEhC,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;IAC1D,MAAM,YAAY,GAAG,MAAM,CAAC,GAAG,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;IAC1D,MAAM,iBAAiB,GAAG,MAAM,CAAC,GAAG,CAAC,4BAA4B,EAAE,GAAG,CAAC,CAAC;IAExE,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;IAC/D,QAAQ,CAAC,QAAQ,CAAC,GAAG,YAAY,aAAa,GAAG,CAAC,MAAM,QAAQ,iBAAiB,EAAE,CAAC,CAAC;IACrF,QAAQ,CAAC,IAAI,EAAE,CAAC;IAEhB,eAAe,CAAC,OAAO,CAAC,CAAC;AAC7B,CAAC;AAED,KAAK,UAAU,OAAO,CAAC,GAAe;IAClC,eAAe,CAAC,YAAY,CAAC,CAAC;IAE9B,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;IAC1D,MAAM,YAAY,GAAG,MAAM,CAAC,GAAG,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;IAE1D,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;IAC3D,QAAQ,CAAC,QAAQ,CAAC,GAAG,YAAY,SAAS,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC;IACzD,QAAQ,CAAC,IAAI,EAAE,CAAC;IAEhB,eAAe,CAAC,OAAO,CAAC,CAAC;AAC7B,CAAC;AAED,KAAK,UAAU,SAAS,CAAC,GAAe;IACpC,MAAM,MAAM,GAA8B;QACtC,IAAI,EAAE,OAAO;QACb,OAAO,EAAE,QAAQ;QACjB,IAAI,EAAE,kBAAkB;QACxB,OAAO,EAAE,GAAG,CAAC,MAAM;QACnB,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC;QAC7B,WAAW,EAAE,KAAK;KACrB,CAAC;IAEF,MAAM,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;AACzD,CAAC;AAED,KAAK,UAAU,QAAQ;IACnB,eAAe,CAAC,YAAY,CAAC,CAAC;IAE9B,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;IAC1D,MAAM,YAAY,GAAG,MAAM,CAAC,GAAG,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;IAE1D,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;IAC5D,QAAQ,CAAC,QAAQ,CAAC,GAAG,YAAY,OAAO,CAAC,CAAC;IAC1C,QAAQ,CAAC,IAAI,EAAE,CAAC;IAEhB,eAAe,CAAC,OAAO,CAAC,CAAC;AAC7B,CAAC;AAED,KAAK,UAAU,SAAS;IACpB,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;IAC1D,MAAM,YAAY,GAAG,MAAM,CAAC,GAAG,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;IAE1D,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;IAC5D,QAAQ,CAAC,QAAQ,CAAC,GAAG,YAAY,OAAO,CAAC,CAAC;IAC1C,QAAQ,CAAC,IAAI,EAAE,CAAC;AACpB,CAAC;AAED,KAAK,UAAU,gBAAgB;IAC3B,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;QACjD,MAAM,EAAE,oBAAoB;QAC5B,WAAW,EAAE,kBAAkB;KAClC,CAAC,CAAC;IAEH,IAAI,CAAC,WAAW,EAAE;QACd,OAAO;KACV;IAED,MAAM,eAAe,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,CAAC;IAC/D,IAAI,CAAC,eAAe,EAAE;QAClB,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,0BAA0B,CAAC,CAAC;QAC3D,OAAO;KACV;IAED,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;IAC1D,MAAM,YAAY,GAAG,MAAM,CAAC,GAAG,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;IAE1D,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,mBAAmB,CAAC,CAAC;IACnE,QAAQ,CAAC,QAAQ,CAAC,OAAO,eAAe,CAAC,GAAG,CAAC,MAAM,QAAQ,YAAY,SAAS,WAAW,EAAE,CAAC,CAAC;IAC/F,QAAQ,CAAC,IAAI,EAAE,CAAC;AACpB,CAAC;AAED,KAAK,UAAU,YAAY;IACvB,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;IAC9C,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,QAAQ,CAAC,UAAU,KAAK,OAAO,EAAE;QACnD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,sBAAsB,CAAC,CAAC;QACvD,OAAO;KACV;IAED,kCAAkC;IAClC,MAAM,OAAO,GAAG,IAAI,MAAM,CAAC,aAAa,CAAC,iFAAiF,CAAC,CAAC;IAC5H,MAAM,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;AACxC,CAAC;AAED,KAAK,UAAU,eAAe;IAC1B,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;IAC9C,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,QAAQ,CAAC,UAAU,KAAK,OAAO,EAAE;QACnD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,sBAAsB,CAAC,CAAC;QACvD,OAAO;KACV;IAED,qCAAqC;IACrC,MAAM,OAAO,GAAG,IAAI,MAAM,CAAC,aAAa,CAAC,qFAAqF,CAAC,CAAC;IAChI,MAAM,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;AACxC,CAAC;AAED,KAAK,UAAU,mBAAmB;IAC9B,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;IAC9C,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,QAAQ,CAAC,UAAU,KAAK,OAAO,EAAE;QACnD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,sBAAsB,CAAC,CAAC;QACvD,OAAO;KACV;IAED,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC;QAC9C,WAAW;QACX,SAAS;QACT,MAAM;QACN,KAAK;QACL,SAAS;QACT,aAAa;KAChB,EAAE;QACC,WAAW,EAAE,2BAA2B;KAC3C,CAAC,CAAC;IAEH,IAAI,CAAC,OAAO,EAAE;QACV,OAAO;KACV;IAED,sCAAsC;IACtC,MAAM,OAAO,GAAG,IAAI,MAAM,CAAC,aAAa,CAAC,2BAA2B,OAAO,yCAAyC,CAAC,CAAC;IACtH,MAAM,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;AACxC,CAAC;AAED,SAAS,eAAe,CAAC,MAAc;IACnC,aAAa,CAAC,IAAI,GAAG,kBAAkB,MAAM,EAAE,CAAC;AACpD,CAAC;AAED,sBAAsB;AACtB,MAAM,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC,QAAQ,EAAE,EAAE;IAChD,IAAI,QAAQ,CAAC,UAAU,KAAK,OAAO,EAAE;QACjC,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QAC1D,IAAI,MAAM,CAAC,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC,EAAE;YACnC,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,8BAA8B,CAAC,CAAC;SAClE;KACJ;AACL,CAAC,CAAC,CAAC;AAEH,gCAAgC;AAChC,MAAM,CAAC,MAAM,CAAC,2BAA2B,CAAC,CAAC,MAAM,EAAE,EAAE;IACjD,IAAI,MAAM,IAAI,MAAM,CAAC,QAAQ,CAAC,UAAU,KAAK,OAAO,EAAE;QAClD,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QAC1D,IAAI,MAAM,CAAC,GAAG,CAAC,gBAAgB,EAAE,KAAK,CAAC,EAAE;YACrC,SAAS,EAAE,CAAC;SACf;KACJ;AACL,CAAC,CAAC,CAAC"}