#!/bin/bash

# Build script for Umbra VS Code Extension
# This script compiles the TypeScript extension and packages it for distribution

set -e

echo "🔨 Building Umbra VS Code Extension..."

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ Error: package.json not found. Please run this script from the vscode-extension directory."
    exit 1
fi

# Check for required tools
check_tool() {
    if ! command -v $1 &> /dev/null; then
        echo "❌ Error: $1 is not installed. Please install it first."
        exit 1
    fi
}

echo "🔍 Checking required tools..."
check_tool "node"
check_tool "npm"

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Install development dependencies if not present
if [ ! -d "node_modules/@types/vscode" ]; then
    echo "📦 Installing development dependencies..."
    npm install --save-dev @types/vscode @types/node typescript eslint @typescript-eslint/parser @typescript-eslint/eslint-plugin @vscode/test-electron
fi

# Install vsce if not present
if ! command -v vsce &> /dev/null; then
    echo "📦 Installing vsce (Visual Studio Code Extension manager)..."
    npm install -g vsce
fi

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p out
mkdir -p icons
mkdir -p syntaxes
mkdir -p themes
mkdir -p snippets

# Generate syntax highlighting grammar
echo "🎨 Generating syntax highlighting..."
cat > syntaxes/umbra.tmLanguage.json << 'EOF'
{
    "$schema": "https://raw.githubusercontent.com/martinring/tmlanguage/master/tmlanguage.json",
    "name": "Umbra",
    "patterns": [
        {
            "include": "#keywords"
        },
        {
            "include": "#strings"
        },
        {
            "include": "#comments"
        },
        {
            "include": "#numbers"
        },
        {
            "include": "#operators"
        },
        {
            "include": "#functions"
        },
        {
            "include": "#types"
        },
        {
            "include": "#aiml"
        }
    ],
    "repository": {
        "keywords": {
            "patterns": [
                {
                    "name": "keyword.control.umbra",
                    "match": "\\b(if|else|when|otherwise|while|for|repeat|break|continue|return|match|try|catch|finally)\\b"
                },
                {
                    "name": "keyword.declaration.umbra",
                    "match": "\\b(let|const|var|function|class|struct|enum|interface|trait|impl)\\b"
                },
                {
                    "name": "keyword.modifier.umbra",
                    "match": "\\b(public|private|protected|static|async|await|mut|ref)\\b"
                },
                {
                    "name": "keyword.other.umbra",
                    "match": "\\b(bring|export|as|from|using|namespace|module)\\b"
                },
                {
                    "name": "keyword.aiml.umbra",
                    "match": "\\b(train|evaluate|predict|visualize|dataset|model)\\b"
                }
            ]
        },
        "strings": {
            "patterns": [
                {
                    "name": "string.quoted.double.umbra",
                    "begin": "\"",
                    "end": "\"",
                    "patterns": [
                        {
                            "name": "constant.character.escape.umbra",
                            "match": "\\\\."
                        }
                    ]
                },
                {
                    "name": "string.quoted.single.umbra",
                    "begin": "'",
                    "end": "'",
                    "patterns": [
                        {
                            "name": "constant.character.escape.umbra",
                            "match": "\\\\."
                        }
                    ]
                }
            ]
        },
        "comments": {
            "patterns": [
                {
                    "name": "comment.line.double-slash.umbra",
                    "match": "//.*$"
                },
                {
                    "name": "comment.block.umbra",
                    "begin": "/\\*",
                    "end": "\\*/"
                }
            ]
        },
        "numbers": {
            "patterns": [
                {
                    "name": "constant.numeric.decimal.umbra",
                    "match": "\\b\\d+(\\.\\d+)?([eE][+-]?\\d+)?\\b"
                },
                {
                    "name": "constant.numeric.hex.umbra",
                    "match": "\\b0[xX][0-9a-fA-F]+\\b"
                },
                {
                    "name": "constant.numeric.binary.umbra",
                    "match": "\\b0[bB][01]+\\b"
                },
                {
                    "name": "constant.numeric.octal.umbra",
                    "match": "\\b0[oO][0-7]+\\b"
                }
            ]
        },
        "operators": {
            "patterns": [
                {
                    "name": "keyword.operator.arithmetic.umbra",
                    "match": "[+\\-*/%]"
                },
                {
                    "name": "keyword.operator.comparison.umbra",
                    "match": "(==|!=|<|>|<=|>=)"
                },
                {
                    "name": "keyword.operator.logical.umbra",
                    "match": "(&&|\\|\\||!|and|or|not)"
                },
                {
                    "name": "keyword.operator.assignment.umbra",
                    "match": "(=|\\+=|-=|\\*=|/=|%=)"
                },
                {
                    "name": "keyword.operator.other.umbra",
                    "match": "(\\.|::|->|=>|\\?|:)"
                }
            ]
        },
        "functions": {
            "patterns": [
                {
                    "name": "entity.name.function.umbra",
                    "match": "\\b[a-zA-Z_][a-zA-Z0-9_]*(?=\\s*\\()"
                }
            ]
        },
        "types": {
            "patterns": [
                {
                    "name": "storage.type.primitive.umbra",
                    "match": "\\b(Integer|Float|String|Boolean|List|Map|Option|Result|void)\\b"
                },
                {
                    "name": "storage.type.user.umbra",
                    "match": "\\b[A-Z][a-zA-Z0-9_]*\\b"
                }
            ]
        },
        "aiml": {
            "patterns": [
                {
                    "name": "support.class.aiml.umbra",
                    "match": "\\b(LinearRegression|RandomForest|SVM|NeuralNetwork|KMeans|PCA)\\b"
                },
                {
                    "name": "support.function.aiml.umbra",
                    "match": "\\b(fit|predict|transform|score|cross_validate|grid_search)\\b"
                }
            ]
        }
    },
    "scopeName": "source.umbra"
}
EOF

# Generate language configuration
echo "⚙️ Generating language configuration..."
cat > language-configuration.json << 'EOF'
{
    "comments": {
        "lineComment": "//",
        "blockComment": [ "/*", "*/" ]
    },
    "brackets": [
        ["{", "}"],
        ["[", "]"],
        ["(", ")"]
    ],
    "autoClosingPairs": [
        ["{", "}"],
        ["[", "]"],
        ["(", ")"],
        ["\"", "\""],
        ["'", "'"]
    ],
    "surroundingPairs": [
        ["{", "}"],
        ["[", "]"],
        ["(", ")"],
        ["\"", "\""],
        ["'", "'"]
    ],
    "indentationRules": {
        "increaseIndentPattern": "^((?!\\/\\/).)*(\\{[^}\"'`]*|\\([^)\"'`]*|\\[[^\\]\"'`]*)$",
        "decreaseIndentPattern": "^((?!.*?\\/\\*).*\\*/)?\\s*[\\}\\]\\)].*$"
    },
    "folding": {
        "markers": {
            "start": "^\\s*//\\s*#?region\\b",
            "end": "^\\s*//\\s*#?endregion\\b"
        }
    },
    "wordPattern": "(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\@\\#\\%\\^\\&\\*\\(\\)\\-\\=\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\?\\s]+)"
}
EOF

# Generate code snippets
echo "✂️ Generating code snippets..."
cat > snippets/umbra.json << 'EOF'
{
    "Function": {
        "prefix": "fn",
        "body": [
            "function ${1:name}(${2:params}) -> ${3:ReturnType} {",
            "\t${4:// function body}",
            "}"
        ],
        "description": "Create a function"
    },
    "If Statement": {
        "prefix": "if",
        "body": [
            "if ${1:condition} {",
            "\t${2:// if body}",
            "}"
        ],
        "description": "Create an if statement"
    },
    "When Statement": {
        "prefix": "when",
        "body": [
            "when ${1:expression} {",
            "\t${2:pattern} => ${3:result},",
            "\totherwise => ${4:default}",
            "}"
        ],
        "description": "Create a when statement"
    },
    "For Loop": {
        "prefix": "for",
        "body": [
            "for ${1:item} in ${2:iterable} {",
            "\t${3:// loop body}",
            "}"
        ],
        "description": "Create a for loop"
    },
    "Repeat Loop": {
        "prefix": "repeat",
        "body": [
            "repeat ${1:times} {",
            "\t${2:// loop body}",
            "}"
        ],
        "description": "Create a repeat loop"
    },
    "Train Model": {
        "prefix": "train",
        "body": [
            "train ${1:model_name} using ${2:dataset} {",
            "\talgorithm: ${3:LinearRegression},",
            "\tparameters: {",
            "\t\t${4:// training parameters}",
            "\t}",
            "}"
        ],
        "description": "Create a train statement"
    },
    "Evaluate Model": {
        "prefix": "evaluate",
        "body": [
            "evaluate ${1:model_name} on ${2:test_data} {",
            "\tmetrics: [${3:accuracy, precision, recall}],",
            "\toutput: ${4:results}",
            "}"
        ],
        "description": "Create an evaluate statement"
    },
    "Predict": {
        "prefix": "predict",
        "body": [
            "predict ${1:model_name} with ${2:input_data} {",
            "\toutput: ${3:predictions}",
            "}"
        ],
        "description": "Create a predict statement"
    },
    "Visualize": {
        "prefix": "visualize",
        "body": [
            "visualize ${1:data} as ${2:chart_type} {",
            "\ttitle: \"${3:Chart Title}\",",
            "\txlabel: \"${4:X Axis}\",",
            "\tylabel: \"${5:Y Axis}\"",
            "}"
        ],
        "description": "Create a visualize statement"
    },
    "Class": {
        "prefix": "class",
        "body": [
            "class ${1:ClassName} {",
            "\t${2:// class members}",
            "\t",
            "\tfunction new(${3:params}) -> ${1:ClassName} {",
            "\t\t${4:// constructor}",
            "\t}",
            "}"
        ],
        "description": "Create a class"
    },
    "Import": {
        "prefix": "bring",
        "body": [
            "bring ${1:module_name}"
        ],
        "description": "Import a module"
    }
}
EOF

# Generate dark theme
echo "🎨 Generating dark theme..."
cat > themes/umbra-dark.json << 'EOF'
{
    "name": "Umbra Dark",
    "type": "dark",
    "colors": {
        "editor.background": "#1e1e1e",
        "editor.foreground": "#d4d4d4",
        "editorLineNumber.foreground": "#858585",
        "editorCursor.foreground": "#aeafad",
        "editor.selectionBackground": "#264f78",
        "editor.inactiveSelectionBackground": "#3a3d41"
    },
    "tokenColors": [
        {
            "scope": "keyword.control.umbra",
            "settings": {
                "foreground": "#569cd6"
            }
        },
        {
            "scope": "keyword.declaration.umbra",
            "settings": {
                "foreground": "#569cd6"
            }
        },
        {
            "scope": "keyword.aiml.umbra",
            "settings": {
                "foreground": "#ff6b6b",
                "fontStyle": "bold"
            }
        },
        {
            "scope": "string.quoted.double.umbra",
            "settings": {
                "foreground": "#ce9178"
            }
        },
        {
            "scope": "comment.line.double-slash.umbra",
            "settings": {
                "foreground": "#6a9955"
            }
        },
        {
            "scope": "constant.numeric.decimal.umbra",
            "settings": {
                "foreground": "#b5cea8"
            }
        },
        {
            "scope": "entity.name.function.umbra",
            "settings": {
                "foreground": "#dcdcaa"
            }
        },
        {
            "scope": "storage.type.primitive.umbra",
            "settings": {
                "foreground": "#4ec9b0"
            }
        },
        {
            "scope": "support.class.aiml.umbra",
            "settings": {
                "foreground": "#4fc1ff",
                "fontStyle": "bold"
            }
        }
    ]
}
EOF

# Compile TypeScript
echo "🔧 Compiling TypeScript..."
npx tsc -p ./

# Run linting
echo "🔍 Running linter..."
npx eslint src --ext ts --fix || echo "⚠️ Linting completed with warnings"

# Run tests if they exist
if [ -d "src/test" ]; then
    echo "🧪 Running tests..."
    npm test || echo "⚠️ Some tests failed"
fi

# Package the extension
echo "📦 Packaging extension..."
vsce package --out umbra-language-support.vsix

# Verify the package
if [ -f "umbra-language-support.vsix" ]; then
    echo "✅ Extension packaged successfully: umbra-language-support.vsix"
    echo "📊 Package size: $(du -h umbra-language-support.vsix | cut -f1)"
    
    # Show package contents
    echo "📋 Package contents:"
    unzip -l umbra-language-support.vsix | head -20
    
    echo ""
    echo "🚀 To install the extension:"
    echo "   code --install-extension umbra-language-support.vsix"
    echo ""
    echo "📤 To publish to marketplace:"
    echo "   vsce publish"
else
    echo "❌ Failed to package extension"
    exit 1
fi

echo "🎉 Build completed successfully!"
