/**
 * Umbra Language Support Extension for VS Code
 *
 * This extension provides comprehensive support for the Umbra programming language
 * including syntax highlighting, IntelliSense, debugging, and AI/ML features.
 */

import * as vscode from 'vscode';
import * as path from 'path';
import * as child_process from 'child_process';
import {
    LanguageClient,
    LanguageClientOptions,
    ServerOptions,
    TransportKind
} from 'vscode-languageclient/node';

let outputChannel: vscode.OutputChannel;
let statusBarItem: vscode.StatusBarItem;
let client: LanguageClient;

export function activate(context: vscode.ExtensionContext) {
    console.log('Activating Umbra Language Support extension');

    // Create output channel
    outputChannel = vscode.window.createOutputChannel('Umbra');
    context.subscriptions.push(outputChannel);

    // Create status bar item
    statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Left, 100);
    statusBarItem.text = "$(gear) Umbra";
    statusBarItem.tooltip = "Umbra Language Support";
    statusBarItem.show();
    context.subscriptions.push(statusBarItem);

    // Initialize Language Server
    initializeLanguageServer(context);

    // Register commands
    registerCommands(context);

    outputChannel.appendLine('Umbra Language Support extension activated');
}

export function deactivate(): Thenable<void> | undefined {
    if (outputChannel) {
        outputChannel.dispose();
    }
    if (statusBarItem) {
        statusBarItem.dispose();
    }
    if (client) {
        return client.stop();
    }
    return Promise.resolve();
}

function initializeLanguageServer(context: vscode.ExtensionContext) {
    // Find the Umbra compiler executable
    const umbraPath = findUmbraExecutable();
    if (!umbraPath) {
        outputChannel.appendLine('Warning: Umbra compiler not found. LSP features will be disabled.');
        return;
    }

    // Configure the server options
    const serverOptions: ServerOptions = {
        command: umbraPath,
        args: ['lsp'],
        transport: TransportKind.stdio
    };

    // Configure the client options
    const clientOptions: LanguageClientOptions = {
        documentSelector: [{ scheme: 'file', language: 'umbra' }],
        synchronize: {
            fileEvents: vscode.workspace.createFileSystemWatcher('**/*.umbra')
        },
        outputChannel: outputChannel
    };

    // Create the language client
    client = new LanguageClient(
        'umbraLanguageServer',
        'Umbra Language Server',
        serverOptions,
        clientOptions
    );

    // Start the client
    client.start().then(() => {
        outputChannel.appendLine('Umbra Language Server started successfully');
    }).catch((error) => {
        outputChannel.appendLine(`Failed to start Umbra Language Server: ${error}`);
    });

    context.subscriptions.push(client);
}

function findUmbraExecutable(): string | null {
    // Try to find umbra executable in various locations
    const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '';
    const possiblePaths = [
        // Current workspace
        path.join(workspaceRoot, 'umbra-compiler', 'target', 'release', 'umbra'),
        path.join(workspaceRoot, 'target', 'release', 'umbra'),
        // System PATH
        'umbra',
        // Common installation paths
        '/usr/local/bin/umbra',
        '/usr/bin/umbra',
        path.join(process.env.HOME || '', '.local', 'bin', 'umbra')
    ];

    for (const umbraPath of possiblePaths) {
        try {
            child_process.execSync(`"${umbraPath}" --version`, { stdio: 'ignore' });
            outputChannel.appendLine(`Found Umbra compiler at: ${umbraPath}`);
            return umbraPath;
        } catch (error) {
            // Continue searching
        }
    }

    return null;
}

function registerCommands(context: vscode.ExtensionContext) {
    // Compile command
    context.subscriptions.push(
        vscode.commands.registerCommand('umbra.compile', async () => {
            const editor = vscode.window.activeTextEditor;
            if (!editor || editor.document.languageId !== 'umbra') {
                vscode.window.showErrorMessage('No active Umbra file');
                return;
            }

            await compileFile(editor.document.uri);
        })
    );

    // Run command
    context.subscriptions.push(
        vscode.commands.registerCommand('umbra.run', async () => {
            const editor = vscode.window.activeTextEditor;
            if (!editor || editor.document.languageId !== 'umbra') {
                vscode.window.showErrorMessage('No active Umbra file');
                return;
            }

            await runFile(editor.document.uri);
        })
    );

    // Debug command
    context.subscriptions.push(
        vscode.commands.registerCommand('umbra.debug', async () => {
            const editor = vscode.window.activeTextEditor;
            if (!editor || editor.document.languageId !== 'umbra') {
                vscode.window.showErrorMessage('No active Umbra file');
                return;
            }

            await debugFile(editor.document.uri);
        })
    );

    // Test command
    context.subscriptions.push(
        vscode.commands.registerCommand('umbra.test', async () => {
            await runTests();
        })
    );

    // REPL command
    context.subscriptions.push(
        vscode.commands.registerCommand('umbra.repl', async () => {
            await startREPL();
        })
    );

    // Format command
    context.subscriptions.push(
        vscode.commands.registerCommand('umbra.format', async () => {
            const editor = vscode.window.activeTextEditor;
            if (!editor || editor.document.languageId !== 'umbra') {
                return;
            }

            await vscode.commands.executeCommand('editor.action.formatDocument');
        })
    );

    // New project command
    context.subscriptions.push(
        vscode.commands.registerCommand('umbra.newProject', async () => {
            await createNewProject();
        })
    );

    // AI/ML commands
    context.subscriptions.push(
        vscode.commands.registerCommand('umbra.aiml.train', async () => {
            await trainMLModel();
        })
    );

    context.subscriptions.push(
        vscode.commands.registerCommand('umbra.aiml.evaluate', async () => {
            await evaluateMLModel();
        })
    );

    context.subscriptions.push(
        vscode.commands.registerCommand('umbra.aiml.visualize', async () => {
            await createVisualization();
        })
    );
}

// Simplified extension - no complex providers for now

async function compileFile(uri: vscode.Uri): Promise<void> {
    updateStatusBar('Compiling...');
    
    const config = vscode.workspace.getConfiguration('umbra');
    const compilerPath = config.get('compiler.path', 'umbra');
    const optimizationLevel = config.get('compiler.optimizationLevel', '2');
    
    const terminal = vscode.window.createTerminal('Umbra Compile');
    terminal.sendText(`${compilerPath} compile "${uri.fsPath}" -O ${optimizationLevel}`);
    terminal.show();
    
    updateStatusBar('Ready');
}

async function runFile(uri: vscode.Uri): Promise<void> {
    updateStatusBar('Running...');
    
    const config = vscode.workspace.getConfiguration('umbra');
    const compilerPath = config.get('compiler.path', 'umbra');
    
    const terminal = vscode.window.createTerminal('Umbra Run');
    terminal.sendText(`${compilerPath} run "${uri.fsPath}"`);
    terminal.show();
    
    updateStatusBar('Ready');
}

async function debugFile(uri: vscode.Uri): Promise<void> {
    const config: vscode.DebugConfiguration = {
        type: 'umbra',
        request: 'launch',
        name: 'Debug Umbra File',
        program: uri.fsPath,
        cwd: path.dirname(uri.fsPath),
        stopOnEntry: false
    };

    await vscode.debug.startDebugging(undefined, config);
}

async function runTests(): Promise<void> {
    updateStatusBar('Testing...');
    
    const config = vscode.workspace.getConfiguration('umbra');
    const compilerPath = config.get('compiler.path', 'umbra');
    
    const terminal = vscode.window.createTerminal('Umbra Test');
    terminal.sendText(`${compilerPath} test`);
    terminal.show();
    
    updateStatusBar('Ready');
}

async function startREPL(): Promise<void> {
    const config = vscode.workspace.getConfiguration('umbra');
    const compilerPath = config.get('compiler.path', 'umbra');
    
    const terminal = vscode.window.createTerminal('Umbra REPL');
    terminal.sendText(`${compilerPath} repl`);
    terminal.show();
}

async function createNewProject(): Promise<void> {
    const projectName = await vscode.window.showInputBox({
        prompt: 'Enter project name',
        placeHolder: 'my-umbra-project'
    });

    if (!projectName) {
        return;
    }

    const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
    if (!workspaceFolder) {
        vscode.window.showErrorMessage('No workspace folder open');
        return;
    }

    const config = vscode.workspace.getConfiguration('umbra');
    const compilerPath = config.get('compiler.path', 'umbra');
    
    const terminal = vscode.window.createTerminal('Umbra New Project');
    terminal.sendText(`cd "${workspaceFolder.uri.fsPath}" && ${compilerPath} init ${projectName}`);
    terminal.show();
}

async function trainMLModel(): Promise<void> {
    const editor = vscode.window.activeTextEditor;
    if (!editor || editor.document.languageId !== 'umbra') {
        vscode.window.showErrorMessage('No active Umbra file');
        return;
    }

    // Insert train statement template
    const snippet = new vscode.SnippetString('train ${1:model_name} using ${2:dataset} {\n\t${3:// Training configuration}\n}');
    await editor.insertSnippet(snippet);
}

async function evaluateMLModel(): Promise<void> {
    const editor = vscode.window.activeTextEditor;
    if (!editor || editor.document.languageId !== 'umbra') {
        vscode.window.showErrorMessage('No active Umbra file');
        return;
    }

    // Insert evaluate statement template
    const snippet = new vscode.SnippetString('evaluate ${1:model_name} on ${2:test_data} {\n\t${3:// Evaluation configuration}\n}');
    await editor.insertSnippet(snippet);
}

async function createVisualization(): Promise<void> {
    const editor = vscode.window.activeTextEditor;
    if (!editor || editor.document.languageId !== 'umbra') {
        vscode.window.showErrorMessage('No active Umbra file');
        return;
    }

    const vizType = await vscode.window.showQuickPick([
        'histogram',
        'scatter',
        'line',
        'bar',
        'heatmap',
        'correlation'
    ], {
        placeHolder: 'Select visualization type'
    });

    if (!vizType) {
        return;
    }

    // Insert visualize statement template
    const snippet = new vscode.SnippetString(`visualize \${1:data} as ${vizType} {\n\t\${2:// Visualization options}\n}`);
    await editor.insertSnippet(snippet);
}

function updateStatusBar(status: string): void {
    statusBarItem.text = `$(gear) Umbra: ${status}`;
}

// Auto-format on save
vscode.workspace.onDidSaveTextDocument((document) => {
    if (document.languageId === 'umbra') {
        const config = vscode.workspace.getConfiguration('umbra');
        if (config.get('format.onSave', true)) {
            vscode.commands.executeCommand('editor.action.formatDocument');
        }
    }
});

// Auto-start REPL if configured
vscode.window.onDidChangeActiveTextEditor((editor) => {
    if (editor && editor.document.languageId === 'umbra') {
        const config = vscode.workspace.getConfiguration('umbra');
        if (config.get('repl.autoStart', false)) {
            startREPL();
        }
    }
});
