{"$schema": "https://raw.githubusercontent.com/martinring/tmlanguage/master/tmlanguage.json", "name": "Umbra", "patterns": [{"include": "#keywords"}, {"include": "#strings"}, {"include": "#comments"}, {"include": "#numbers"}, {"include": "#operators"}, {"include": "#functions"}, {"include": "#types"}, {"include": "#aiml"}], "repository": {"keywords": {"patterns": [{"name": "keyword.control.umbra", "match": "\\b(if|else|when|otherwise|while|for|repeat|break|continue|return|match|try|catch|finally)\\b"}, {"name": "keyword.declaration.umbra", "match": "\\b(let|const|var|function|class|struct|enum|interface|trait|impl)\\b"}, {"name": "keyword.modifier.umbra", "match": "\\b(public|private|protected|static|async|await|mut|ref)\\b"}, {"name": "keyword.other.umbra", "match": "\\b(bring|export|as|from|using|namespace|module)\\b"}, {"name": "keyword.aiml.umbra", "match": "\\b(train|evaluate|predict|visualize|dataset|model)\\b"}]}, "strings": {"patterns": [{"name": "string.quoted.double.umbra", "begin": "\"", "end": "\"", "patterns": [{"name": "constant.character.escape.umbra", "match": "\\\\."}]}, {"name": "string.quoted.single.umbra", "begin": "'", "end": "'", "patterns": [{"name": "constant.character.escape.umbra", "match": "\\\\."}]}]}, "comments": {"patterns": [{"name": "comment.line.double-slash.umbra", "match": "//.*$"}, {"name": "comment.block.umbra", "begin": "/\\*", "end": "\\*/"}]}, "numbers": {"patterns": [{"name": "constant.numeric.decimal.umbra", "match": "\\b\\d+(\\.\\d+)?([eE][+-]?\\d+)?\\b"}, {"name": "constant.numeric.hex.umbra", "match": "\\b0[xX][0-9a-fA-F]+\\b"}, {"name": "constant.numeric.binary.umbra", "match": "\\b0[bB][01]+\\b"}, {"name": "constant.numeric.octal.umbra", "match": "\\b0[oO][0-7]+\\b"}]}, "operators": {"patterns": [{"name": "keyword.operator.arithmetic.umbra", "match": "[+\\-*/%]"}, {"name": "keyword.operator.comparison.umbra", "match": "(==|!=|<|>|<=|>=)"}, {"name": "keyword.operator.logical.umbra", "match": "(&&|\\|\\||!|and|or|not)"}, {"name": "keyword.operator.assignment.umbra", "match": "(=|\\+=|-=|\\*=|/=|%=)"}, {"name": "keyword.operator.other.umbra", "match": "(\\.|::|->|=>|\\?|:)"}]}, "functions": {"patterns": [{"name": "entity.name.function.umbra", "match": "\\b[a-zA-Z_][a-zA-Z0-9_]*(?=\\s*\\()"}]}, "types": {"patterns": [{"name": "storage.type.primitive.umbra", "match": "\\b(Integer|Float|String|Boolean|List|Map|Option|Result|void)\\b"}, {"name": "storage.type.user.umbra", "match": "\\b[A-Z][a-zA-Z0-9_]*\\b"}]}, "aiml": {"patterns": [{"name": "support.class.aiml.umbra", "match": "\\b(LinearRegression|RandomForest|SVM|NeuralNetwork|KMeans|PCA)\\b"}, {"name": "support.function.aiml.umbra", "match": "\\b(fit|predict|transform|score|cross_validate|grid_search)\\b"}]}}, "scopeName": "source.umbra"}