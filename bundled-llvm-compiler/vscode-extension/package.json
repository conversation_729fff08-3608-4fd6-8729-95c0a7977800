{"name": "umbra-programming-language", "displayName": "Umbra Programming Language", "description": "Complete language support for Umbra programming language with AI/ML features", "version": "1.2.7", "publisher": "EclipseSoftworks", "engines": {"vscode": "^1.74.0"}, "categories": ["Programming Languages", "Debuggers", "Formatters", "Linters", "Snippets", "Other"], "keywords": ["umbra", "programming", "language", "ai", "ml", "machine learning", "data science"], "activationEvents": ["onLanguage:umbra", "onCommand:umbra.compile", "onCommand:umbra.run", "onCommand:umbra.debug", "onCommand:umbra.test", "onCommand:umbra.repl", "onDebugResolve:umbra"], "main": "./out/extension.js", "contributes": {"languages": [{"id": "umbra", "aliases": ["Umbra", "umbra"], "extensions": [".umbra"], "configuration": "./language-configuration.json", "icon": {"light": "./icons/umbra-light.svg", "dark": "./icons/umbra-dark.svg"}}], "grammars": [{"language": "umbra", "scopeName": "source.umbra", "path": "./syntaxes/umbra.tmLanguage.json"}], "themes": [{"label": "Umbra Dark", "uiTheme": "vs-dark", "path": "./themes/umbra-dark.json"}, {"label": "Umbra Light", "uiTheme": "vs", "path": "./themes/umbra-light.json"}], "commands": [{"command": "umbra.compile", "title": "Compile Umbra File", "category": "Umbra", "icon": "$(gear)"}, {"command": "umbra.run", "title": "Run Umbra File", "category": "Umbra", "icon": "$(play)"}, {"command": "umbra.debug", "title": "Debug Umbra File", "category": "Umbra", "icon": "$(debug-alt)"}, {"command": "umbra.test", "title": "Run Tests", "category": "Umbra", "icon": "$(beaker)"}, {"command": "umbra.repl", "title": "Start REPL", "category": "Umbra", "icon": "$(terminal)"}, {"command": "umbra.format", "title": "Format Document", "category": "Umbra", "icon": "$(symbol-keyword)"}, {"command": "umbra.newProject", "title": "New Umbra Project", "category": "Umbra", "icon": "$(folder-opened)"}, {"command": "umbra.aiml.train", "title": "Train ML Model", "category": "Umbra AI/ML", "icon": "$(graph)"}, {"command": "umbra.aiml.evaluate", "title": "Evaluate ML Model", "category": "Umbra AI/ML", "icon": "$(graph-line)"}, {"command": "umbra.aiml.visualize", "title": "Create Visualization", "category": "Umbra AI/ML", "icon": "$(graph-scatter)"}], "keybindings": [{"command": "umbra.compile", "key": "ctrl+shift+b", "when": "editorLangId == umbra"}, {"command": "umbra.run", "key": "ctrl+f5", "when": "editorLangId == umbra"}, {"command": "umbra.debug", "key": "f5", "when": "editorLangId == umbra"}, {"command": "umbra.repl", "key": "ctrl+shift+r", "when": "editorLangId == umbra"}, {"command": "umbra.format", "key": "shift+alt+f", "when": "editorLangId == umbra"}], "menus": {"editor/context": [{"when": "editorLangId == umbra", "command": "umbra.run", "group": "umbra@1"}, {"when": "editorLangId == umbra", "command": "umbra.debug", "group": "umbra@2"}, {"when": "editorLangId == umbra", "command": "umbra.format", "group": "umbra@3"}], "editor/title": [{"when": "editorLangId == umbra", "command": "umbra.run", "group": "navigation@1"}, {"when": "editorLangId == umbra", "command": "umbra.debug", "group": "navigation@2"}], "explorer/context": [{"when": "resourceExtname == .umbra", "command": "umbra.run", "group": "umbra@1"}]}, "configuration": {"type": "object", "title": "Umbra Configuration", "properties": {"umbra.compiler.path": {"type": "string", "default": "umbra", "description": "Path to the Umbra compiler executable"}, "umbra.compiler.optimizationLevel": {"type": "string", "enum": ["0", "1", "2", "3", "s", "g"], "default": "2", "description": "Optimization level for compilation"}, "umbra.lsp.enabled": {"type": "boolean", "default": true, "description": "Enable Language Server Protocol support"}, "umbra.lsp.trace": {"type": "string", "enum": ["off", "messages", "verbose"], "default": "off", "description": "LSP trace level"}, "umbra.format.enabled": {"type": "boolean", "default": true, "description": "Enable automatic formatting"}, "umbra.format.onSave": {"type": "boolean", "default": true, "description": "Format on save"}, "umbra.debug.enabled": {"type": "boolean", "default": true, "description": "Enable debugging support"}, "umbra.aiml.pythonPath": {"type": "string", "default": "python3", "description": "Path to Python executable for AI/ML features"}, "umbra.aiml.autoInstallPackages": {"type": "boolean", "default": true, "description": "Automatically install required Python packages"}, "umbra.test.framework": {"type": "string", "enum": ["builtin", "external"], "default": "builtin", "description": "Test framework to use"}, "umbra.repl.autoStart": {"type": "boolean", "default": false, "description": "Automatically start REPL when opening Umbra files"}}}, "debuggers": [{"type": "umbra", "label": "Umbra Debug", "program": "./out/debugAdapter.js", "runtime": "node", "configurationAttributes": {"launch": {"required": ["program"], "properties": {"program": {"type": "string", "description": "Absolute path to the Umbra file to debug", "default": "${workspaceFolder}/main.umbra"}, "args": {"type": "array", "description": "Command line arguments", "default": []}, "cwd": {"type": "string", "description": "Working directory", "default": "${workspaceFolder}"}, "env": {"type": "object", "description": "Environment variables", "default": {}}, "stopOnEntry": {"type": "boolean", "description": "Stop on entry", "default": false}, "trace": {"type": "boolean", "description": "Enable trace output", "default": false}}}}, "initialConfigurations": [{"type": "umbra", "request": "launch", "name": "Launch Umbra Program", "program": "${workspaceFolder}/main.umbra", "cwd": "${workspaceFolder}", "stopOnEntry": false}], "configurationSnippets": [{"label": "Umbra: Launch", "description": "A new configuration for launching an Umbra program", "body": {"type": "umbra", "request": "launch", "name": "Launch Umbra Program", "program": "^\"\\${workspaceFolder}/main.umbra\"", "cwd": "^\"\\${workspaceFolder}\"", "stopOnEntry": false}}]}], "taskDefinitions": [{"type": "umbra", "required": ["task"], "properties": {"task": {"type": "string", "description": "The Umbra task to execute"}, "file": {"type": "string", "description": "The Umbra file to process"}, "args": {"type": "array", "description": "Additional arguments"}}}], "problemMatchers": [{"name": "umbra", "owner": "umbra", "fileLocation": ["relative", "${workspaceFolder}"], "pattern": {"regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error):\\s+(.*)$", "file": 1, "line": 2, "column": 3, "severity": 4, "message": 5}}], "snippets": [{"language": "umbra", "path": "./snippets/umbra.json"}]}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js", "package": "vsce package", "publish": "vsce publish"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "16.x", "@typescript-eslint/eslint-plugin": "^5.45.0", "@typescript-eslint/parser": "^5.45.0", "eslint": "^8.28.0", "typescript": "^4.9.4", "@vscode/test-electron": "^2.2.0", "vsce": "^2.15.0"}, "dependencies": {"vscode-languageclient": "^8.0.2", "vscode-debugadapter": "^1.51.0", "vscode-debugprotocol": "^1.51.0"}, "repository": {"type": "git", "url": "https://github.com/eclipse-softworks/umbra-language"}, "bugs": {"url": "https://github.com/eclipse-softworks/umbra-language/issues"}, "homepage": "https://umbra-lang.org", "license": "MIT", "icon": "icons/umbra-icon.png", "galleryBanner": {"color": "#1e1e1e", "theme": "dark"}}