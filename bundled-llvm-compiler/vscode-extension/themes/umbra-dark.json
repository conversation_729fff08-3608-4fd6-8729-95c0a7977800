{"name": "Umbra Dark", "type": "dark", "colors": {"editor.background": "#1e1e1e", "editor.foreground": "#d4d4d4", "editorLineNumber.foreground": "#858585", "editorCursor.foreground": "#aeafad", "editor.selectionBackground": "#264f78", "editor.inactiveSelectionBackground": "#3a3d41"}, "tokenColors": [{"scope": "keyword.control.umbra", "settings": {"foreground": "#569cd6"}}, {"scope": "keyword.declaration.umbra", "settings": {"foreground": "#569cd6"}}, {"scope": "keyword.aiml.umbra", "settings": {"foreground": "#ff6b6b", "fontStyle": "bold"}}, {"scope": "string.quoted.double.umbra", "settings": {"foreground": "#ce9178"}}, {"scope": "comment.line.double-slash.umbra", "settings": {"foreground": "#6a9955"}}, {"scope": "constant.numeric.decimal.umbra", "settings": {"foreground": "#b5cea8"}}, {"scope": "entity.name.function.umbra", "settings": {"foreground": "#dcdcaa"}}, {"scope": "storage.type.primitive.umbra", "settings": {"foreground": "#4ec9b0"}}, {"scope": "support.class.aiml.umbra", "settings": {"foreground": "#4fc1ff", "fontStyle": "bold"}}]}