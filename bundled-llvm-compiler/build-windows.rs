// Windows-specific build script for Umbra compiler
// This handles Windows-specific dependencies and library linking

use std::env;
use std::path::Path;

fn main() {
    let target = env::var("TARGET").unwrap_or_default();
    
    if target.contains("windows") {
        println!("cargo:rustc-cfg=windows_build");
        
        // Windows-specific build configuration
        setup_windows_build();
    } else {
        // Standard build for other platforms
        setup_standard_build();
    }
}

fn setup_windows_build() {
    println!("cargo:rustc-env=BUILD_TARGET=windows");
    
    // Link Windows system libraries
    println!("cargo:rustc-link-lib=kernel32");
    println!("cargo:rustc-link-lib=user32");
    println!("cargo:rustc-link-lib=gdi32");
    println!("cargo:rustc-link-lib=winspool");
    println!("cargo:rustc-link-lib=shell32");
    println!("cargo:rustc-link-lib=ole32");
    println!("cargo:rustc-link-lib=oleaut32");
    println!("cargo:rustc-link-lib=uuid");
    println!("cargo:rustc-link-lib=comdlg32");
    println!("cargo:rustc-link-lib=advapi32");
    println!("cargo:rustc-link-lib=ws2_32");
    println!("cargo:rustc-link-lib=winmm");
    println!("cargo:rustc-link-lib=bcrypt");
    println!("cargo:rustc-link-lib=crypt32");
    println!("cargo:rustc-link-lib=secur32");
    println!("cargo:rustc-link-lib=ntdll");
    println!("cargo:rustc-link-lib=psapi");
    println!("cargo:rustc-link-lib=iphlpapi");
    println!("cargo:rustc-link-lib=userenv");
    println!("cargo:rustc-link-lib=netapi32");
    
    // Windows threading
    println!("cargo:rustc-link-lib=winpthread");
    
    // Windows-compatible alternatives to Linux libraries
    setup_windows_alternatives();
    
    // LLVM for Windows
    setup_windows_llvm();
    
    // Python for Windows
    setup_windows_python();
}

fn setup_windows_alternatives() {
    // Instead of libffi, use Windows COM/OLE
    println!("cargo:rustc-cfg=windows_ffi");
    
    // Instead of librt/libdl, use Windows equivalents
    println!("cargo:rustc-cfg=windows_runtime");
    
    // Instead of libtinfo, use Windows console API
    println!("cargo:rustc-cfg=windows_console");
    
    // Instead of libxml2, use Windows XML APIs or pure Rust
    println!("cargo:rustc-cfg=windows_xml");
    
    // Instead of zlib, use Windows compression or pure Rust
    println!("cargo:rustc-cfg=windows_compression");
}

fn setup_windows_llvm() {
    // Try to find LLVM installation
    let llvm_paths = [
        "/usr/lib/llvm-14",
        "/usr/lib/llvm-15", 
        "/usr/lib/llvm-16",
        "/usr/lib/llvm-17",
        "/usr/local/lib/llvm-14",
        "/opt/llvm-14",
    ];
    
    for path in &llvm_paths {
        if Path::new(path).exists() {
            println!("cargo:rustc-env=LLVM_SYS_140_PREFIX={}", path);
            println!("cargo:rustc-link-search=native={}/lib", path);
            break;
        }
    }
    
    // Enable LLVM for Windows
    println!("cargo:rustc-cfg=llvm_enabled");
}

fn setup_windows_python() {
    // Python library paths for Windows cross-compilation
    let python_paths = [
        "/usr/x86_64-w64-mingw32/lib",
        "/tmp/python-windows",
    ];
    
    for path in &python_paths {
        if Path::new(path).exists() {
            println!("cargo:rustc-link-search=native={}", path);
        }
    }
    
    // Try to link Python
    println!("cargo:rustc-link-lib=python3");
    println!("cargo:rustc-link-lib=python311");
    
    // Enable Python interop for Windows
    println!("cargo:rustc-cfg=python_enabled");
}

fn setup_standard_build() {
    // Standard build date and commit info
    let build_date = chrono::Utc::now().format("%Y-%m-%d %H:%M:%S UTC").to_string();
    println!("cargo:rustc-env=BUILD_DATE={}", build_date);

    let git_commit = std::process::Command::new("git")
        .args(&["rev-parse", "--short", "HEAD"])
        .output()
        .map(|output| String::from_utf8_lossy(&output.stdout).trim().to_string())
        .unwrap_or_else(|_| "unknown".to_string());
    println!("cargo:rustc-env=BUILD_COMMIT={}", git_commit);

    let target = env::var("TARGET").unwrap_or_else(|_| "unknown".to_string());
    println!("cargo:rustc-env=BUILD_TARGET={}", target);

    println!("cargo:rerun-if-changed=.git/HEAD");
    println!("cargo:rerun-if-changed=.git/refs/heads/");
}
