// Bundled LLVM Build Script for Umbra Compiler
// This embeds LLVM tools directly into the binary

use std::env;
use std::path::Path;
use include_dir::{include_dir, Dir};

fn main() {
    let target = env::var("TARGET").unwrap_or_default();
    
    // Set build information
    setup_build_info();
    
    // Configure bundled LLVM
    setup_bundled_llvm();
    
    // Platform-specific configuration
    if target.contains("windows") {
        setup_windows_bundled();
    } else {
        setup_unix_bundled();
    }
    
    // Embed LLVM tools as resources
    embed_llvm_tools();
}

fn setup_build_info() {
    let build_date = chrono::Utc::now().format("%Y-%m-%d %H:%M:%S UTC").to_string();
    println!("cargo:rustc-env=BUILD_DATE={}", build_date);

    let git_commit = std::process::Command::new("git")
        .args(&["rev-parse", "--short", "HEAD"])
        .output()
        .map(|output| String::from_utf8_lossy(&output.stdout).trim().to_string())
        .unwrap_or_else(|_| "bundled".to_string());
    println!("cargo:rustc-env=BUILD_COMMIT={}", git_commit);

    let target = env::var("TARGET").unwrap_or_else(|_| "unknown".to_string());
    println!("cargo:rustc-env=BUILD_TARGET={}", target);
    
    println!("cargo:rustc-env=LLVM_BUNDLED=true");
}

fn setup_bundled_llvm() {
    // Configure for bundled LLVM
    let llvm_dir = "./llvm-tools";
    
    if Path::new(llvm_dir).exists() {
        println!("cargo:rustc-env=LLVM_SYS_180_PREFIX={}", llvm_dir);
        println!("cargo:rustc-link-search=native={}/lib", llvm_dir);
        println!("cargo:rustc-cfg=bundled_llvm");
        
        // Add LLVM tools to PATH during build
        let current_path = env::var("PATH").unwrap_or_default();
        let new_path = format!("{}/bin:{}", llvm_dir, current_path);
        println!("cargo:rustc-env=PATH={}", new_path);
    }
}

fn setup_windows_bundled() {
    // Windows-specific bundled configuration
    println!("cargo:rustc-cfg=windows_bundled");
    
    // Link Windows system libraries
    println!("cargo:rustc-link-lib=kernel32");
    println!("cargo:rustc-link-lib=user32");
    println!("cargo:rustc-link-lib=advapi32");
    println!("cargo:rustc-link-lib=ws2_32");
}

fn setup_unix_bundled() {
    // Unix-specific bundled configuration
    println!("cargo:rustc-cfg=unix_bundled");
    
    // Link system libraries
    println!("cargo:rustc-link-lib=dl");
    println!("cargo:rustc-link-lib=pthread");
    println!("cargo:rustc-link-lib=m");
}

fn embed_llvm_tools() {
    // This will embed the LLVM tools directory into the binary
    println!("cargo:rerun-if-changed=llvm-tools/");
    
    // The actual embedding is handled by include_dir! macro in the source
    if Path::new("llvm-tools").exists() {
        println!("cargo:rustc-cfg=has_embedded_llvm");
    }
}
