#!/bin/bash

# Build RPM package for Umbra
set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
PACKAGE_NAME="umbra"
VERSION="1.0.1"
RELEASE="1"
ARCH="x86_64"
MAINTAINER="Eclipse Softworks <<EMAIL>>"
DESCRIPTION="Umbra Programming Language Compiler"

# Create RPM build directory structure
BUILD_ROOT="$PROJECT_ROOT/packaging/linux/rpm-build"
rm -rf "$BUILD_ROOT"
mkdir -p "$BUILD_ROOT"/{BUILD,BUILDROOT,RPMS,SOURCES,SPECS,SRPMS}

# Create spec file
SPEC_FILE="$BUILD_ROOT/SPECS/$PACKAGE_NAME.spec"
cat > "$SPEC_FILE" << EOF
Name:           $PACKAGE_NAME
Version:        $VERSION
Release:        $RELEASE%{?dist}
Summary:        $DESCRIPTION
License:        MIT
URL:            https://github.com/eclipsesoftworks/umbra
Source0:        %{name}-%{version}.tar.gz
BuildArch:      $ARCH

%description
Umbra is a modern programming language designed for AI/ML applications,
systems programming, and general-purpose development. It features
advanced type safety, memory management, and built-in AI/ML primitives.

%prep
%setup -q

%build
# Binary is pre-built

%install
rm -rf %{buildroot}
mkdir -p %{buildroot}/usr/bin
mkdir -p %{buildroot}/usr/share/doc/%{name}

# Install binary
install -m 755 umbra %{buildroot}/usr/bin/umbra

# Install documentation
install -m 644 README.md %{buildroot}/usr/share/doc/%{name}/
install -m 644 LICENSE %{buildroot}/usr/share/doc/%{name}/

%files
/usr/bin/umbra
/usr/share/doc/%{name}/README.md
/usr/share/doc/%{name}/LICENSE

%post
echo "Umbra has been installed to /usr/bin/umbra"
echo "You can now use 'umbra' command from anywhere in your terminal"

# Create symbolic link if needed
if [ ! -L /usr/local/bin/umbra ]; then
    ln -sf /usr/bin/umbra /usr/local/bin/umbra 2>/dev/null || true
fi

%preun
# Remove symbolic link
if [ -L /usr/local/bin/umbra ]; then
    rm -f /usr/local/bin/umbra
fi

%changelog
* $(date '+%a %b %d %Y') Eclipse Softworks <<EMAIL>> - $VERSION-$RELEASE
- Initial RPM package for Umbra programming language
EOF

# Create source tarball
TARBALL_DIR="$BUILD_ROOT/SOURCES"
TEMP_DIR="$BUILD_ROOT/temp"
mkdir -p "$TEMP_DIR/$PACKAGE_NAME-$VERSION"

# Copy files to temp directory
cp "$PROJECT_ROOT/target/release/umbra" "$TEMP_DIR/$PACKAGE_NAME-$VERSION/"
cp "$PROJECT_ROOT/README.md" "$TEMP_DIR/$PACKAGE_NAME-$VERSION/"
cp "$PROJECT_ROOT/LICENSE" "$TEMP_DIR/$PACKAGE_NAME-$VERSION/"

# Create tarball
cd "$TEMP_DIR"
tar -czf "$TARBALL_DIR/$PACKAGE_NAME-$VERSION.tar.gz" "$PACKAGE_NAME-$VERSION"
cd "$PROJECT_ROOT"

# Build RPM
echo "Building RPM package..."
rpmbuild --define "_topdir $BUILD_ROOT" -ba "$SPEC_FILE"

# Copy RPM to distribution directory
OUTPUT_DIR="$PROJECT_ROOT/../distribution/packages"
mkdir -p "$OUTPUT_DIR"

RPM_FILE=$(find "$BUILD_ROOT/RPMS" -name "*.rpm" -type f)
if [ -n "$RPM_FILE" ]; then
    cp "$RPM_FILE" "$OUTPUT_DIR/"
    echo "RPM package created: $OUTPUT_DIR/$(basename "$RPM_FILE")"
else
    echo "Error: RPM file not found"
    exit 1
fi

# Clean up
rm -rf "$BUILD_ROOT"

echo "RPM package build completed successfully!"
