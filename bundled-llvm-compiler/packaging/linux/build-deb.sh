#!/bin/bash

# Build DEB package for Umbra
set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
PACKAGE_NAME="umbra"
VERSION="1.0.1"
ARCH="amd64"
MAINTAINER="Eclipse Softworks <<EMAIL>>"
DESCRIPTION="Umbra Programming Language Compiler"

# Create package directory structure
PACKAGE_DIR="$PROJECT_ROOT/packaging/linux/deb-build"
rm -rf "$PACKAGE_DIR"
mkdir -p "$PACKAGE_DIR/DEBIAN"
mkdir -p "$PACKAGE_DIR/usr/bin"
mkdir -p "$PACKAGE_DIR/usr/share/doc/$PACKAGE_NAME"
mkdir -p "$PACKAGE_DIR/usr/share/man/man1"

# Copy binary
cp "$PROJECT_ROOT/target/release/umbra" "$PACKAGE_DIR/usr/bin/"
chmod 755 "$PACKAGE_DIR/usr/bin/umbra"

# Copy documentation
cp "$PROJECT_ROOT/README.md" "$PACKAGE_DIR/usr/share/doc/$PACKAGE_NAME/"
cp "$PROJECT_ROOT/LICENSE" "$PACKAGE_DIR/usr/share/doc/$PACKAGE_NAME/"

# Create control file
cat > "$PACKAGE_DIR/DEBIAN/control" << EOF
Package: $PACKAGE_NAME
Version: $VERSION
Section: devel
Priority: optional
Architecture: $ARCH
Maintainer: $MAINTAINER
Description: $DESCRIPTION
 Umbra is a modern programming language designed for AI/ML applications,
 systems programming, and general-purpose development. It features
 advanced type safety, memory management, and built-in AI/ML primitives.
Homepage: https://github.com/eclipsesoftworks/umbra
EOF

# Create postinst script
cat > "$PACKAGE_DIR/DEBIAN/postinst" << 'EOF'
#!/bin/bash
set -e

# Add /usr/bin to PATH if not already there (should already be there)
echo "Umbra has been installed to /usr/bin/umbra"
echo "You can now use 'umbra' command from anywhere in your terminal"

# Create symbolic link if needed
if [ ! -L /usr/local/bin/umbra ]; then
    ln -sf /usr/bin/umbra /usr/local/bin/umbra 2>/dev/null || true
fi

exit 0
EOF

chmod 755 "$PACKAGE_DIR/DEBIAN/postinst"

# Create prerm script
cat > "$PACKAGE_DIR/DEBIAN/prerm" << 'EOF'
#!/bin/bash
set -e

# Remove symbolic link
if [ -L /usr/local/bin/umbra ]; then
    rm -f /usr/local/bin/umbra
fi

exit 0
EOF

chmod 755 "$PACKAGE_DIR/DEBIAN/prerm"

# Build the package
OUTPUT_DIR="$PROJECT_ROOT/../distribution/packages"
mkdir -p "$OUTPUT_DIR"

PACKAGE_FILE="$OUTPUT_DIR/${PACKAGE_NAME}_${VERSION}_${ARCH}.deb"

echo "Building DEB package..."
dpkg-deb --build "$PACKAGE_DIR" "$PACKAGE_FILE"

echo "DEB package created: $PACKAGE_FILE"

# Clean up
rm -rf "$PACKAGE_DIR"

echo "DEB package build completed successfully!"
