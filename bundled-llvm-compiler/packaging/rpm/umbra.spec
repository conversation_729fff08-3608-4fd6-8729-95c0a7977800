Name:           umbra
Version:        1.0.0
Release:        1%{?dist}
Summary:        Umbra Programming Language Compiler

License:        Proprietary
URL:            https://umbra-lang.org
Source0:        %{name}-%{version}.tar.gz

BuildRequires:  glibc-devel
Requires:       glibc

%description
Umbra is a modern, AI/ML-focused compiled programming language designed
for high-performance computing and machine learning applications.

Features:
- Fast compilation with sub-second build times
- High performance LLVM-based code generation
- Built-in support for AI/ML workflows
- Modern syntax inspired by Rust and Python
- Strong type system with type inference
- Memory safety without garbage collection overhead

%prep
%setup -q

%build
# Binary is pre-built

%install
rm -rf $RPM_BUILD_ROOT
mkdir -p $RPM_BUILD_ROOT/usr/bin
mkdir -p $RPM_BUILD_ROOT/usr/share/doc/umbra
mkdir -p $RPM_BUILD_ROOT/usr/share/umbra/examples

install -m 755 target/release/umbra $RPM_BUILD_ROOT/usr/bin/umbra
install -m 644 README.md $RPM_BUILD_ROOT/usr/share/doc/umbra/
install -m 644 LICENSE $RPM_BUILD_ROOT/usr/share/doc/umbra/
cp -r examples/* $RPM_BUILD_ROOT/usr/share/umbra/examples/
cp -r docs/* $RPM_BUILD_ROOT/usr/share/doc/umbra/ 2>/dev/null || true

%files
/usr/bin/umbra
/usr/share/doc/umbra/README.md
/usr/share/doc/umbra/LICENSE
/usr/share/umbra/examples/*
%doc /usr/share/doc/umbra/*

%post
echo "Umbra Programming Language installed successfully!"
echo "Run 'umbra --help' to get started."

%preun
echo "Uninstalling Umbra Programming Language..."

%changelog
* Wed Jul 16 2024 Eclipse Softworks <<EMAIL>> - 1.0.0-1
- Initial release of Umbra Programming Language
