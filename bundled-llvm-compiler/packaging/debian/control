Package: umbra
Version: 1.0.0
Section: devel
Priority: optional
Architecture: amd64
Depends: libc6 (>= 2.17)
Maintainer: Eclipse Softworks <<EMAIL>>
Description: Umbra Programming Language Compiler
 Umbra is a modern, AI/ML-focused compiled programming language designed
 for high-performance computing and machine learning applications.
 .
 Features:
  - Fast compilation with sub-second build times
  - High performance LLVM-based code generation
  - Built-in support for AI/ML workflows
  - Modern syntax inspired by Rust and Python
  - Strong type system with type inference
  - Memory safety without garbage collection overhead
Homepage: https://umbra-lang.org
