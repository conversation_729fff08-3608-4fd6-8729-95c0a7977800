#!/bin/bash
# Master build script for all Umbra distribution packages

set -e

echo "================================================================"
echo "           UMBRA DISTRIBUTION PACKAGE BUILDER"
echo "================================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if binary exists
if [ ! -f "target/release/umbra" ]; then
    echo -e "${RED}Error: umbra binary not found in target/release/${NC}"
    echo -e "${YELLOW}Please build the project first with: cargo build --release${NC}"
    exit 1
fi

echo -e "${BLUE}Found Umbra binary: target/release/umbra${NC}"

# Make build scripts executable
chmod +x packaging/build-deb.sh
chmod +x packaging/build-rpm.sh
chmod +x packaging/build-macos.sh

# Build Debian package
echo -e "${YELLOW}Building Debian package...${NC}"
if ./packaging/build-deb.sh; then
    echo -e "${GREEN}✓ Debian package built successfully${NC}"
else
    echo -e "${RED}✗ Debian package build failed${NC}"
fi

# Build RPM package (only on systems with rpmbuild)
if command -v rpmbuild &> /dev/null; then
    echo -e "${YELLOW}Building RPM package...${NC}"
    if ./packaging/build-rpm.sh; then
        echo -e "${GREEN}✓ RPM package built successfully${NC}"
    else
        echo -e "${RED}✗ RPM package build failed${NC}"
    fi
else
    echo -e "${YELLOW}Skipping RPM build (rpmbuild not available)${NC}"
fi

# Build macOS package (only on macOS)
if [[ "$OSTYPE" == "darwin"* ]]; then
    echo -e "${YELLOW}Building macOS package...${NC}"
    if ./packaging/build-macos.sh; then
        echo -e "${GREEN}✓ macOS package built successfully${NC}"
    else
        echo -e "${RED}✗ macOS package build failed${NC}"
    fi
else
    echo -e "${YELLOW}Skipping macOS build (not running on macOS)${NC}"
fi

# Windows build instructions
echo -e "${YELLOW}For Windows installer:${NC}"
echo -e "${BLUE}1. Install NSIS (Nullsoft Scriptable Install System)${NC}"
echo -e "${BLUE}2. Run: packaging\\build-windows.bat${NC}"

echo ""
echo -e "${GREEN}================================================================${NC}"
echo -e "${GREEN}           PACKAGE BUILD COMPLETED${NC}"
echo -e "${GREEN}================================================================${NC}"

# List built packages
echo -e "${BLUE}Built packages:${NC}"
ls -la *.deb *.rpm *.pkg 2>/dev/null || echo "No packages found in current directory"

echo ""
echo -e "${BLUE}Package files are ready for distribution!${NC}"
