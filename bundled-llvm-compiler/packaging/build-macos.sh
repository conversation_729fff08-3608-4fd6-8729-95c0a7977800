#!/bin/bash
# Build macOS package for Umbra Programming Language

set -e

PACKAGE_NAME="umbra"
VERSION="1.0.0"
IDENTIFIER="com.eclipse-softworks.umbra"
BUILD_DIR="build-macos"

echo "Building macOS package for Umbra..."

# Clean previous build
rm -rf "$BUILD_DIR"
mkdir -p "$BUILD_DIR/payload/usr/local/bin"
mkdir -p "$BUILD_DIR/payload/usr/local/share/doc/umbra"
mkdir -p "$BUILD_DIR/payload/usr/local/share/umbra/examples"
mkdir -p "$BUILD_DIR/scripts"

# Copy binary
cp target/release/umbra "$BUILD_DIR/payload/usr/local/bin/"
chmod 755 "$BUILD_DIR/payload/usr/local/bin/umbra"

# Copy documentation
cp README.md "$BUILD_DIR/payload/usr/local/share/doc/umbra/"
cp LICENSE "$BUILD_DIR/payload/usr/local/share/doc/umbra/"

# Copy examples
cp -r examples/* "$BUILD_DIR/payload/usr/local/share/umbra/examples/"

# Copy docs if they exist
if [ -d "docs" ]; then
    cp -r docs/* "$BUILD_DIR/payload/usr/local/share/doc/umbra/"
fi

# Create postinstall script
cat > "$BUILD_DIR/scripts/postinstall" << 'EOF'
#!/bin/bash
echo "Umbra Programming Language installed successfully!"
echo "Run 'umbra --help' to get started."
exit 0
EOF

chmod 755 "$BUILD_DIR/scripts/postinstall"

# Build component package
pkgbuild --root "$BUILD_DIR/payload" \
         --scripts "$BUILD_DIR/scripts" \
         --identifier "$IDENTIFIER" \
         --version "$VERSION" \
         --install-location "/" \
         "$BUILD_DIR/umbra.pkg"

# Build product archive with distribution
productbuild --distribution packaging/macos/Distribution.xml \
             --package-path "$BUILD_DIR" \
             --resources packaging/macos \
             "umbra-macos-x64-${VERSION}.pkg"

echo "macOS package built: umbra-macos-x64-${VERSION}.pkg"

# Clean up
rm -rf "$BUILD_DIR"

echo "macOS package build completed successfully!"
