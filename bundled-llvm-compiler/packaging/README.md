# Umbra Distribution Package Builder

This directory contains configuration files and build scripts for creating distribution packages for the Umbra Programming Language across multiple platforms.

## Supported Platforms

- **Linux Debian/Ubuntu**: `.deb` package
- **Linux Red Hat/CentOS/Fedora**: `.rpm` package  
- **macOS**: `.pkg` installer
- **Windows**: `.exe` installer (NSIS)

## Prerequisites

### All Platforms
- Umbra binary must be built first: `cargo build --release`

### Linux (Debian/Ubuntu)
- `dpkg-deb` (usually pre-installed)
- `fakeroot` (for building without root)

### Linux (Red Hat/CentOS/Fedora)
- `rpmbuild` package: `sudo yum install rpm-build` or `sudo dnf install rpm-build`

### macOS
- Xcode Command Line Tools
- `pkgbuild` and `productbuild` (included with Xcode)

### Windows
- NSIS (Nullsoft Scriptable Install System) from https://nsis.sourceforge.io/

## Building Packages

### Quick Start - Build All Available Packages
```bash
./packaging/build-all.sh
```

### Individual Platform Builds

#### Debian Package
```bash
./packaging/build-deb.sh
```
Output: `umbra_1.0.0_amd64.deb`

#### RPM Package
```bash
./packaging/build-rpm.sh
```
Output: `umbra-1.0.0-1.x86_64.rpm`

#### macOS Package
```bash
./packaging/build-macos.sh
```
Output: `umbra-macos-x64-1.0.0.pkg`

#### Windows Installer
```cmd
packaging\build-windows.bat
```
Output: `umbra-windows-x64-installer.exe`

## Package Contents

All packages include:
- **Binary**: The Umbra compiler executable
- **Documentation**: README.md and LICENSE
- **Examples**: Sample Umbra programs
- **Installation Scripts**: Platform-specific setup

## Installation Locations

### Linux (Debian/RPM)
- Binary: `/usr/bin/umbra`
- Documentation: `/usr/share/doc/umbra/`
- Examples: `/usr/share/umbra/examples/`

### macOS
- Binary: `/usr/local/bin/umbra`
- Documentation: `/usr/local/share/doc/umbra/`
- Examples: `/usr/local/share/umbra/examples/`

### Windows
- Binary: `C:\Program Files\Eclipse Softworks\Umbra Programming Language\umbra.exe`
- Documentation: `C:\Program Files\Eclipse Softworks\Umbra Programming Language\docs\`
- Examples: `C:\Program Files\Eclipse Softworks\Umbra Programming Language\examples\`

## Package Features

### Post-Installation
- Adds Umbra to system PATH
- Creates necessary symlinks
- Displays welcome message

### Uninstallation
- Removes binary and files
- Cleans up PATH entries
- Removes shortcuts (Windows/macOS)

## Customization

### Version Updates
Update version numbers in:
- `packaging/debian/control`
- `packaging/rpm/umbra.spec`
- `packaging/macos/Distribution.xml`
- `packaging/windows/umbra.nsi`

### Package Metadata
Modify package descriptions, dependencies, and metadata in the respective configuration files.

## Troubleshooting

### Common Issues

1. **Binary not found**: Ensure `cargo build --release` completed successfully
2. **Permission denied**: Make sure build scripts are executable (`chmod +x`)
3. **Missing dependencies**: Install platform-specific build tools
4. **Path issues**: Run scripts from the umbra-compiler root directory

### Build Logs
Check console output for detailed error messages during package building.

## Distribution

After building, packages are ready for distribution:
- Upload to package repositories (apt, yum, homebrew)
- Distribute via download links
- Include in CI/CD pipelines

## Support

For packaging issues or questions, contact Eclipse Softworks support.
