<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Welcome to Umbra</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, sans-serif; margin: 20px; }
        h1 { color: #1d1d1f; }
        .highlight { color: #007aff; font-weight: bold; }
    </style>
</head>
<body>
    <h1>Welcome to Umbra Programming Language</h1>
    
    <p>Thank you for choosing <span class="highlight">Umbra</span>, the modern AI/ML-focused compiled programming language.</p>
    
    <h2>What is Umbra?</h2>
    <p>Umbra is designed for high-performance computing and machine learning applications with:</p>
    <ul>
        <li><strong>Fast Compilation:</strong> Sub-second build times</li>
        <li><strong>High Performance:</strong> LLVM-based optimized code generation</li>
        <li><strong>AI/ML Focus:</strong> Built-in support for machine learning workflows</li>
        <li><strong>Modern Syntax:</strong> Clean, expressive language design</li>
        <li><strong>Memory Safety:</strong> Safe without garbage collection overhead</li>
    </ul>
    
    <h2>Installation</h2>
    <p>This installer will place the Umbra compiler in <code>/usr/local/bin/umbra</code> and include documentation and examples.</p>
    
    <p>After installation, you can start using Umbra immediately from the Terminal.</p>
    
    <p><strong>Get started:</strong> Run <code>umbra --help</code> in Terminal after installation.</p>
</body>
</html>
