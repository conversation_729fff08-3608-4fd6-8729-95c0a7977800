#!/bin/bash

# Build PKG installer for Umbra on macOS
set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
PACKAGE_NAME="umbra"
VERSION="1.0.1"
IDENTIFIER="com.eclipsesoftworks.umbra"
MAINTAINER="Eclipse Softworks"
DESCRIPTION="Umbra Programming Language Compiler"

# Create package directory structure
PACKAGE_DIR="$PROJECT_ROOT/packaging/macos/pkg-build"
PAYLOAD_DIR="$PACKAGE_DIR/payload"
SCRIPTS_DIR="$PACKAGE_DIR/scripts"

rm -rf "$PACKAGE_DIR"
mkdir -p "$PAYLOAD_DIR/usr/local/bin"
mkdir -p "$PAYLOAD_DIR/usr/local/share/doc/$PACKAGE_NAME"
mkdir -p "$SCRIPTS_DIR"

# Note: This script assumes we have a macOS binary
# For cross-compilation, we would need to build with:
# cargo build --release --target x86_64-apple-darwin
# or
# cargo build --release --target aarch64-apple-darwin

# Check if we have a macOS binary (this would be built separately)
MACOS_BINARY=""
if [ -f "$PROJECT_ROOT/target/x86_64-apple-darwin/release/umbra" ]; then
    MACOS_BINARY="$PROJECT_ROOT/target/x86_64-apple-darwin/release/umbra"
elif [ -f "$PROJECT_ROOT/target/aarch64-apple-darwin/release/umbra" ]; then
    MACOS_BINARY="$PROJECT_ROOT/target/aarch64-apple-darwin/release/umbra"
else
    echo "Warning: No macOS binary found. Using Linux binary as placeholder."
    echo "To build for macOS, run:"
    echo "  cargo build --release --target x86_64-apple-darwin"
    echo "  or"
    echo "  cargo build --release --target aarch64-apple-darwin"
    MACOS_BINARY="$PROJECT_ROOT/target/release/umbra"
fi

# Copy binary
cp "$MACOS_BINARY" "$PAYLOAD_DIR/usr/local/bin/umbra"
chmod 755 "$PAYLOAD_DIR/usr/local/bin/umbra"

# Copy documentation
cp "$PROJECT_ROOT/README.md" "$PAYLOAD_DIR/usr/local/share/doc/$PACKAGE_NAME/"
cp "$PROJECT_ROOT/LICENSE" "$PAYLOAD_DIR/usr/local/share/doc/$PACKAGE_NAME/"

# Create postinstall script
cat > "$SCRIPTS_DIR/postinstall" << 'EOF'
#!/bin/bash

# Add /usr/local/bin to PATH if not already there
SHELL_RC=""
if [ "$SHELL" = "/bin/zsh" ] || [ "$SHELL" = "/usr/bin/zsh" ]; then
    SHELL_RC="$HOME/.zshrc"
elif [ "$SHELL" = "/bin/bash" ] || [ "$SHELL" = "/usr/bin/bash" ]; then
    SHELL_RC="$HOME/.bash_profile"
fi

if [ -n "$SHELL_RC" ] && [ -f "$SHELL_RC" ]; then
    if ! grep -q "/usr/local/bin" "$SHELL_RC"; then
        echo 'export PATH="/usr/local/bin:$PATH"' >> "$SHELL_RC"
    fi
fi

echo "Umbra has been installed to /usr/local/bin/umbra"
echo "You may need to restart your terminal or run 'source ~/.zshrc' (or ~/.bash_profile)"
echo "to use the 'umbra' command."

exit 0
EOF

chmod 755 "$SCRIPTS_DIR/postinstall"

# Create preinstall script
cat > "$SCRIPTS_DIR/preinstall" << 'EOF'
#!/bin/bash

# Remove any existing installation
if [ -f /usr/local/bin/umbra ]; then
    rm -f /usr/local/bin/umbra
fi

exit 0
EOF

chmod 755 "$SCRIPTS_DIR/preinstall"

# Create component property list
cat > "$PACKAGE_DIR/component.plist" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>BundleHasStrictIdentifier</key>
    <true/>
    <key>BundleIdentifier</key>
    <string>$IDENTIFIER</string>
    <key>BundleIsRelocatable</key>
    <false/>
    <key>BundleIsVersionChecked</key>
    <true/>
    <key>BundleOverwriteAction</key>
    <string>upgrade</string>
    <key>RootRelativeBundlePath</key>
    <string>$PACKAGE_NAME.pkg</string>
</dict>
</plist>
EOF

# Create distribution XML
cat > "$PACKAGE_DIR/distribution.xml" << EOF
<?xml version="1.0" encoding="utf-8"?>
<installer-gui-script minSpecVersion="1">
    <title>$DESCRIPTION</title>
    <organization>$IDENTIFIER</organization>
    <domains enable_localSystem="true"/>
    <options customize="never" require-scripts="true" rootVolumeOnly="true" />
    <choices-outline>
        <line choice="default">
            <line choice="$IDENTIFIER"/>
        </line>
    </choices-outline>
    <choice id="default"/>
    <choice id="$IDENTIFIER" visible="false">
        <pkg-ref id="$IDENTIFIER"/>
    </choice>
    <pkg-ref id="$IDENTIFIER" version="$VERSION" onConclusion="none">$PACKAGE_NAME.pkg</pkg-ref>
</installer-gui-script>
EOF

# Build component package
echo "Building component package..."
pkgbuild --root "$PAYLOAD_DIR" \
         --scripts "$SCRIPTS_DIR" \
         --identifier "$IDENTIFIER" \
         --version "$VERSION" \
         --install-location "/" \
         "$PACKAGE_DIR/$PACKAGE_NAME.pkg"

# Build product archive
OUTPUT_DIR="$PROJECT_ROOT/../distribution/packages"
mkdir -p "$OUTPUT_DIR"

INSTALLER_FILE="$OUTPUT_DIR/umbra-$VERSION-macos.pkg"

echo "Building product archive..."
productbuild --distribution "$PACKAGE_DIR/distribution.xml" \
             --package-path "$PACKAGE_DIR" \
             "$INSTALLER_FILE"

echo "macOS PKG installer created: $INSTALLER_FILE"

# Clean up
rm -rf "$PACKAGE_DIR"

echo "macOS PKG build completed successfully!"
