<?xml version="1.0" encoding="utf-8"?>
<installer-gui-script minSpecVersion="2">
    <title>Umbra Programming Language</title>
    <organization>com.eclipse-softworks</organization>
    <domains enable_anywhere="false" enable_currentUserHome="false" enable_localSystem="true"/>
    <options customize="never" require-scripts="false" rootVolumeOnly="true"/>
    
    <welcome file="welcome.html"/>
    <license file="license.html"/>
    <readme file="readme.html"/>
    
    <pkg-ref id="com.eclipse-softworks.umbra"/>
    
    <choices-outline>
        <line choice="default">
            <line choice="com.eclipse-softworks.umbra"/>
        </line>
    </choices-outline>
    
    <choice id="default"/>
    <choice id="com.eclipse-softworks.umbra" visible="false">
        <pkg-ref id="com.eclipse-softworks.umbra"/>
    </choice>
    
    <pkg-ref id="com.eclipse-softworks.umbra" version="1.0.0" onConclusion="none">umbra.pkg</pkg-ref>
</installer-gui-script>
