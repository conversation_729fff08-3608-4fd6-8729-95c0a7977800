#!/bin/bash
# Build RPM package for Umbra Programming Language

set -e

PACKAGE_NAME="umbra"
VERSION="1.0.0"
RELEASE="1"

echo "Building RPM package for Umbra..."

# Create RPM build directories
mkdir -p ~/rpmbuild/{BUILD,<PERSON><PERSON><PERSON>ROOT,RPMS,SOURCES,SPECS,SRPMS}

# Create source tarball
TARBALL_NAME="${PACKAGE_NAME}-${VERSION}.tar.gz"
tar --exclude='target/debug' --exclude='packaging' --exclude='.git' \
    -czf ~/rpmbuild/SOURCES/"$TARBALL_NAME" \
    -C .. umbra-compiler

# Copy spec file
cp packaging/rpm/umbra.spec ~/rpmbuild/SPECS/

# Build RPM
rpmbuild -ba ~/rpmbuild/SPECS/umbra.spec

# Copy built RPM to current directory
cp ~/rpmbuild/RPMS/x86_64/${PACKAGE_NAME}-${VERSION}-${RELEASE}.*.rpm .

echo "RPM package built: ${PACKAGE_NAME}-${VERSION}-${RELEASE}.*.rpm"
echo "RPM package build completed successfully!"
