@echo off
REM Build Windows installer for Umbra Programming Language

echo Building Windows installer for Umbra...

REM Check if NSIS is installed
where makensis >nul 2>nul
if %errorlevel% neq 0 (
    echo Error: NSIS (Nullsoft Scriptable Install System) is required to build Windows installer
    echo Please install NSIS from https://nsis.sourceforge.io/
    exit /b 1
)

REM Check if binary exists
if not exist "target\release\umbra.exe" (
    echo Error: umbra.exe not found in target\release\
    echo Please build the project first with: cargo build --release
    exit /b 1
)

REM Build installer
makensis packaging\windows\umbra.nsi

if %errorlevel% equ 0 (
    echo Windows installer built successfully: umbra-windows-x64-installer.exe
) else (
    echo Error building Windows installer
    exit /b 1
)

echo Windows installer build completed!
