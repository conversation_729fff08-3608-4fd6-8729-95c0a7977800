#!/bin/bash
# Build Debian package for Umbra Programming Language

set -e

PACKAGE_NAME="umbra"
VERSION="1.0.0"
ARCH="amd64"
BUILD_DIR="build-deb"

echo "Building Debian package for Umbra..."

# Clean previous build
rm -rf "$BUILD_DIR"
mkdir -p "$BUILD_DIR/DEBIAN"
mkdir -p "$BUILD_DIR/usr/bin"
mkdir -p "$BUILD_DIR/usr/share/doc/umbra"
mkdir -p "$BUILD_DIR/usr/share/umbra/examples"

# Copy control files
cp packaging/debian/control "$BUILD_DIR/DEBIAN/"
cp packaging/debian/postinst "$BUILD_DIR/DEBIAN/"
cp packaging/debian/prerm "$BUILD_DIR/DEBIAN/"

# Make scripts executable
chmod 755 "$BUILD_DIR/DEBIAN/postinst"
chmod 755 "$BUILD_DIR/DEBIAN/prerm"

# Copy binary
cp target/release/umbra "$BUILD_DIR/usr/bin/"
chmod 755 "$BUILD_DIR/usr/bin/umbra"

# Copy documentation
cp README.md "$BUILD_DIR/usr/share/doc/umbra/"
cp LICENSE "$BUILD_DIR/usr/share/doc/umbra/"

# Copy examples
cp -r examples/* "$BUILD_DIR/usr/share/umbra/examples/"

# Copy docs if they exist
if [ -d "docs" ] && [ "$(ls -A docs)" ]; then
    cp -r docs/* "$BUILD_DIR/usr/share/doc/umbra/"
fi

# Build package
dpkg-deb --build "$BUILD_DIR" "${PACKAGE_NAME}_${VERSION}_${ARCH}.deb"

echo "Debian package built: ${PACKAGE_NAME}_${VERSION}_${ARCH}.deb"

# Clean up
rm -rf "$BUILD_DIR"

echo "Debian package build completed successfully!"
