// Test Real Metrics - Verify genuine ML computations
// Simple test to confirm real metrics are being computed

show("🧪 Test Real Metrics")
show("====================")
show("Testing genuine ML metric computation")

// Load a single dataset
show("📊 Loading customer churn dataset...")
let customer_data: Dataset := load_dataset("data/customer_churn.csv")

// Create and train a single model
show("🧠 Creating and training model...")
let test_model: Model := create_model("random_forest")

show("🚀 Training model on real data...")
train test_model using customer_data:
    n_estimators := 50
    max_depth := 5
    random_state := 42

show("📈 Evaluating model to get real metrics...")
evaluate test_model on customer_data

show("✅ Test completed!")
show("If you see real metrics above (not 0.95, 0.93, 0.97), then genuine computation is working!")
