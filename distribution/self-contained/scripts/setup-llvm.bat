@echo off
REM Setup LLVM tools for Umbra Programming Language

echo Setting up LLVM tools for Umbra...

REM Add LLVM tools to PATH
set "UMBRA_DIR=%~dp0.."
set "LLVM_TOOLS_DIR=%UMBRA_DIR%\llvm-tools\bin"

REM Add to current session PATH
set "PATH=%LLVM_TOOLS_DIR%;%PATH%"

REM Set LLVM environment variables
set "LLVM_SYS_180_PREFIX=%UMBRA_DIR%\llvm-tools"
set "LLVM_CONFIG=%LLVM_TOOLS_DIR%\llvm-config.exe"

echo LLVM tools configured successfully!
echo LLVM tools directory: %LLVM_TOOLS_DIR%
echo.

REM Test LLVM tools
if exist "%LLVM_TOOLS_DIR%\llc.exe" (
    echo ✅ LLC (LLVM Compiler) available
) else (
    echo ❌ LLC not found
)

if exist "%LLVM_TOOLS_DIR%\opt.exe" (
    echo ✅ OPT (LLVM Optimizer) available
) else (
    echo ❌ OPT not found
)

if exist "%LLVM_TOOLS_DIR%\clang.exe" (
    echo ✅ Clang (C/C++ Compiler) available
) else (
    echo ❌ Clang not found
)

echo.
echo LLVM setup complete! You can now use Umbra with full optimization.
