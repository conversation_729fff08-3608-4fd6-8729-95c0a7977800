@echo off
echo ================================================================
echo Testing Umbra Self-Contained Installation
echo ================================================================
echo.

REM Setup LLVM tools
call "%~dp0setup-llvm.bat"

echo.
echo Testing Umbra installation...
echo.

REM Test Umbra version
echo 1. Testing Umbra version:
umbra --version
echo.

REM Test LLVM integration
echo 2. Testing LLVM integration:
umbra version
echo.

REM Test basic compilation
echo 3. Testing basic compilation:
echo fn main() ^-^> Void: { show("Self-contained Umbra works!") } > test.umbra
umbra run test.umbra
del test.umbra

echo.
echo ================================================================
echo Self-contained installation test complete!
echo ================================================================
pause
