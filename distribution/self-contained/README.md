# Umbra Programming Language - Self-Contained Installation

This is a complete, self-contained installation of Umbra Programming Language with bundled LLVM tools.

## What's Included

- **Umbra Compiler**: Complete compiler with all language features
- **LLVM Tools**: Complete LLVM toolchain (llc, opt, clang, etc.)
- **No Dependencies**: Everything needed is included
- **Auto-Configuration**: LLVM tools automatically available to Umbra

## Installation

1. Run the installer as Administrator
2. Choose installation directory
3. Complete installation
4. Umbra and LLVM tools are automatically configured

## Usage

```bash
# Check installation
umbra --version

# Start interactive REPL
umbra repl

# Compile programs (with full LLVM optimization)
umbra build program.umbra
umbra run program.umbra
```

## LLVM Integration

The bundled LLVM tools are automatically available to Umbra:
- No "LLVM tools not found" warnings
- Full optimization capabilities
- Enhanced performance
- Complete development environment

## Support

For documentation and support, visit:
https://github.com/eclipse-softworks/umbra
