; Umbra Programming Language - Self-Contained Installer with LLVM Tools

!define PRODUCT_NAME "Umbra Programming Language (Self-Contained)"
!define PRODUCT_VERSION "1.2.1"
!define PRODUCT_PUBLISHER "Eclipse Softworks"
!define PRODUCT_WEB_SITE "https://github.com/eclipse-softworks/umbra"
!define PRODUCT_DIR_REGKEY "Software\Microsoft\Windows\CurrentVersion\App Paths\umbra.exe"
!define PRODUCT_UNINST_KEY "Software\Microsoft\Windows\CurrentVersion\Uninstall\${PRODUCT_NAME}"
!define PRODUCT_UNINST_ROOT_KEY "HKLM"

; Modern UI
!include "MUI2.nsh"
!include "FileFunc.nsh"
!include "LogicLib.nsh"

; Installer settings
Name "${PRODUCT_NAME} ${PRODUCT_VERSION}"
OutFile "umbra-1.2.1-self-contained-installer.exe"
InstallDir "$PROGRAMFILES64\Umbra"
InstallDirRegK<PERSON> HKLM "${PRODUCT_DIR_REGKEY}" ""
ShowInstDetails show
ShowUnInstDetails show
RequestExecutionLevel admin
SetCompressor /SOLID lzma
SetCompressorDictSize 64

; Modern UI Configuration
!define MUI_ABORTWARNING
!define MUI_ICON "${NSISDIR}\Contrib\Graphics\Icons\modern-install.ico"
!define MUI_UNICON "${NSISDIR}\Contrib\Graphics\Icons\modern-uninstall.ico"

; Welcome page
!define MUI_WELCOMEPAGE_TITLE "Welcome to Umbra Self-Contained Setup"
!define MUI_WELCOMEPAGE_TEXT "This wizard will install Umbra Programming Language with bundled LLVM tools.$\r$\n$\r$\nThis is a complete, self-contained installation that includes:$\r$\n$\r$\n• Complete Umbra compiler with all features$\r$\n• Full LLVM toolchain (llc, opt, clang, etc.)$\r$\n• No external dependencies required$\r$\n• Automatic LLVM integration$\r$\n$\r$\nClick Next to continue."
!insertmacro MUI_PAGE_WELCOME

; License page
!insertmacro MUI_PAGE_LICENSE "LICENSE"

; Directory page
!insertmacro MUI_PAGE_DIRECTORY

; Installation page
!insertmacro MUI_PAGE_INSTFILES

; Finish page
!define MUI_FINISHPAGE_RUN "$INSTDIR\scripts\test-installation.bat"
!define MUI_FINISHPAGE_RUN_TEXT "Test the installation"
!define MUI_FINISHPAGE_SHOWREADME "$INSTDIR\README.md"
!define MUI_FINISHPAGE_SHOWREADME_TEXT "View documentation"
!insertmacro MUI_PAGE_FINISH

; Uninstaller pages
!insertmacro MUI_UNPAGE_INSTFILES

; Language
!insertmacro MUI_LANGUAGE "English"

; Installation section
Section "Umbra Self-Contained (Required)" SEC01
  SectionIn RO
  SetOutPath "$INSTDIR"
  
  DetailPrint "Installing Umbra Programming Language..."
  
  ; Install Umbra binary
  File "umbra"
  
  ; Install documentation
  File "LICENSE"
  File "README.md"
  
  ; Install LLVM tools
  DetailPrint "Installing bundled LLVM tools..."
  SetOutPath "$INSTDIR\llvm-tools"
  File /r "llvm-tools\*.*"
  
  ; Install scripts
  SetOutPath "$INSTDIR\scripts"
  File /r "scripts\*.*"
  
  ; Install examples if they exist
  SetOutPath "$INSTDIR\examples"
  File /nonfatal /r "examples\*.*"
  
  ; Create shortcuts
  CreateDirectory "$SMPROGRAMS\Umbra Programming Language"
  CreateShortCut "$SMPROGRAMS\Umbra Programming Language\Umbra REPL.lnk" "$INSTDIR\umbra.exe" "repl"
  CreateShortCut "$SMPROGRAMS\Umbra Programming Language\Test Installation.lnk" "$INSTDIR\scripts\test-installation.bat"
  CreateShortCut "$SMPROGRAMS\Umbra Programming Language\Setup LLVM Tools.lnk" "$INSTDIR\scripts\setup-llvm.bat"
  CreateShortCut "$SMPROGRAMS\Umbra Programming Language\Documentation.lnk" "$INSTDIR\README.md"
  CreateShortCut "$SMPROGRAMS\Umbra Programming Language\Uninstall.lnk" "$INSTDIR\uninst.exe"
  
  ; Desktop shortcut
  CreateShortCut "$DESKTOP\Umbra Programming Language.lnk" "$INSTDIR\scripts\test-installation.bat"
  
  ; Add to PATH
  DetailPrint "Adding Umbra and LLVM tools to system PATH..."
  ReadRegStr $0 HKLM "SYSTEM\CurrentControlSet\Control\Session Manager\Environment" "PATH"
  StrCpy $1 "$0;$INSTDIR;$INSTDIR\llvm-tools\bin"
  WriteRegExpandStr HKLM "SYSTEM\CurrentControlSet\Control\Session Manager\Environment" "PATH" "$1"
  
  ; Set LLVM environment variables
  WriteRegStr HKLM "SYSTEM\CurrentControlSet\Control\Session Manager\Environment" "LLVM_SYS_180_PREFIX" "$INSTDIR\llvm-tools"
  WriteRegStr HKLM "SYSTEM\CurrentControlSet\Control\Session Manager\Environment" "LLVM_CONFIG" "$INSTDIR\llvm-tools\bin\llvm-config.exe"
  
  ; Registry entries
  WriteRegStr HKLM "${PRODUCT_DIR_REGKEY}" "" "$INSTDIR\umbra.exe"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "DisplayName" "$(^Name)"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "UninstallString" "$INSTDIR\uninst.exe"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "DisplayIcon" "$INSTDIR\umbra.exe"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "DisplayVersion" "${PRODUCT_VERSION}"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "URLInfoAbout" "${PRODUCT_WEB_SITE}"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "Publisher" "${PRODUCT_PUBLISHER}"
  
  ; Create uninstaller
  WriteUninstaller "$INSTDIR\uninst.exe"
  
  ; Calculate and store installation size
  ${GetSize} "$INSTDIR" "/S=0K" $0 $1 $2
  IntFmt $0 "0x%08X" $0
  WriteRegDWORD ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "EstimatedSize" "$0"
  
SectionEnd

; Uninstaller
Section Uninstall
  ; Remove from PATH (simplified)
  DetailPrint "Removing from system PATH..."
  
  ; Remove files
  Delete "$INSTDIR\umbra.exe"
  Delete "$INSTDIR\LICENSE"
  Delete "$INSTDIR\README.md"
  Delete "$INSTDIR\uninst.exe"
  
  ; Remove LLVM tools
  RMDir /r "$INSTDIR\llvm-tools"
  RMDir /r "$INSTDIR\scripts"
  RMDir /r "$INSTDIR\examples"
  
  ; Remove shortcuts
  Delete "$SMPROGRAMS\Umbra Programming Language\*.*"
  RMDir "$SMPROGRAMS\Umbra Programming Language"
  Delete "$DESKTOP\Umbra Programming Language.lnk"
  
  ; Remove registry entries
  DeleteRegKey ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}"
  DeleteRegKey HKLM "${PRODUCT_DIR_REGKEY}"
  DeleteRegValue HKLM "SYSTEM\CurrentControlSet\Control\Session Manager\Environment" "LLVM_SYS_180_PREFIX"
  DeleteRegValue HKLM "SYSTEM\CurrentControlSet\Control\Session Manager\Environment" "LLVM_CONFIG"
  
  ; Remove installation directory
  RMDir "$INSTDIR"
  
  SetAutoClose true
SectionEnd

; Functions
Function .onInit
  ; Check Windows version
  ReadRegStr $R0 HKLM "SOFTWARE\Microsoft\Windows NT\CurrentVersion" "CurrentVersion"
  ${If} $R0 == ""
    MessageBox MB_OK|MB_ICONSTOP "Cannot determine Windows version. Windows 10 or later required."
    Abort
  ${EndIf}
  
  ; Check if already installed
  ReadRegStr $R0 ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "UninstallString"
  StrCmp $R0 "" done
  
  MessageBox MB_OKCANCEL|MB_ICONEXCLAMATION \
    "${PRODUCT_NAME} is already installed. $\n$\nClick OK to remove the previous version or Cancel to cancel this upgrade." \
    IDOK uninst
  Abort
  
  uninst:
    ClearErrors
    ExecWait '$R0 _?=$INSTDIR'
    
    IfErrors no_remove_uninstaller done
    no_remove_uninstaller:
  
  done:
FunctionEnd

Function .onInstSuccess
  MessageBox MB_OK "Umbra Programming Language (Self-Contained) has been successfully installed!$\r$\n$\r$\nFeatures installed:$\r$\n• Complete Umbra compiler$\r$\n• Full LLVM toolchain$\r$\n• Automatic configuration$\r$\n• No external dependencies$\r$\n$\r$\nYou can now use 'umbra' commands with full LLVM optimization!"
FunctionEnd
