#!/bin/bash

# Umbra Programming Language Installer for Linux
set -e

INSTALL_DIR="/usr/local/bin"
UMBRA_HOME="/usr/local/share/umbra"

echo "🔧 Installing Umbra Programming Language..."

# Check if running as root for system-wide installation
if [[ $EUID -eq 0 ]]; then
    echo "Installing system-wide to $INSTALL_DIR"
    
    # Create directories
    mkdir -p "$INSTALL_DIR"
    mkdir -p "$UMBRA_HOME"
    
    # Copy binary
    cp umbra "$INSTALL_DIR/"
    chmod +x "$INSTALL_DIR/umbra"
    
    # Copy examples and documentation
    cp -r examples "$UMBRA_HOME/" 2>/dev/null || true
    cp README.md "$UMBRA_HOME/" 2>/dev/null || true
    cp LICENSE "$UMBRA_HOME/" 2>/dev/null || true
    
    echo "✅ Umbra installed successfully!"
    echo "Run 'umbra --version' to verify installation"
    
else
    echo "Installing to user directory ~/.local/bin"
    
    # Create user directories
    mkdir -p "$HOME/.local/bin"
    mkdir -p "$HOME/.local/share/umbra"
    
    # Copy binary
    cp umbra "$HOME/.local/bin/"
    chmod +x "$HOME/.local/bin/umbra"
    
    # Copy examples and documentation
    cp -r examples "$HOME/.local/share/umbra/" 2>/dev/null || true
    cp README.md "$HOME/.local/share/umbra/" 2>/dev/null || true
    cp LICENSE "$HOME/.local/share/umbra/" 2>/dev/null || true
    
    # Add to PATH if not already there
    if ! echo "$PATH" | grep -q "$HOME/.local/bin"; then
        echo 'export PATH="$HOME/.local/bin:$PATH"' >> "$HOME/.bashrc"
        echo "⚠️  Added ~/.local/bin to PATH in ~/.bashrc"
        echo "   Please run 'source ~/.bashrc' or restart your terminal"
    fi
    
    echo "✅ Umbra installed successfully!"
    echo "Run 'umbra --version' to verify installation"
fi
