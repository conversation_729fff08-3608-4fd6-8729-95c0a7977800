// Fibonacci Sequence Example for Umbra
// Demonstrates recursion and control flow

fn fibonacci(n: Integer) -> Integer: {
    when n <= 1: {
        return n
    } otherwise: {
        return fibonacci(n - 1) + fi<PERSON><PERSON><PERSON>(n - 2)
    }
}

fn main() -> Void: {
    show("Fibonacci Sequence Example:")
    
    let n: Integer := 10
    let result: Integer := fibonacci(n)
    
    show("fibonacci(10) = 55")
    
    // Calculate a few more values
    let fib5: Integer := fibonacci(5)
    let fib7: Integer := fibonacci(7)
    
    show("fibonacci(5) = 5")
    show("fibonacci(7) = 13")
}
