# Umbra Programming Language Examples

This directory contains example programs demonstrating various features of the Umbra programming language.

## Running Examples

To run any example, use the Umbra compiler:

```bash
# Compile and run directly
umbra run example_name.umbra

# Or compile first, then run
umbra build example_name.umbra
./example_name
```

## Available Examples

### hello_world.umbra
A simple "Hello, World!" program demonstrating basic Umbra syntax.

### basic_math.umbra
Demonstrates:
- Variable declarations
- Function definitions
- Mathematical operations
- Control flow with `when/otherwise`

### fibon<PERSON>ci.umbra
Demonstrates:
- Recursive functions
- Control flow
- Mathematical computations

## Umbra Language Features

- **Functions**: `fn name(params) -> ReturnType: { ... }`
- **Variables**: `let name: Type := value`
- **Control Flow**: `when condition: { ... } otherwise: { ... }`
- **Types**: <PERSON>teger, <PERSON>loat, <PERSON><PERSON><PERSON>, String, Void
- **Operators**: `+`, `-`, `*`, `/`, `%`, `==`, `!=`, `<`, `>`, `<=`, `>=`
- **Logical Operators**: `and`, `or`, `not`

For more information, see the main documentation.
