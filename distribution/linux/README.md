# Umbra Programming Language

Umbra is a modern, AI/ML-focused compiled programming language designed for high-performance computing and machine learning applications.

## Features

- **Fast Compilation**: Sub-second compilation times
- **High Performance**: Optimized LLVM-based code generation
- **AI/ML Focus**: Built-in support for machine learning workflows
- **Modern Syntax**: Clean, expressive syntax inspired by Rust and Python
- **Strong Type System**: Static typing with type inference
- **Memory Safety**: Safe memory management without garbage collection overhead

## Installation

### From Binary Releases

Download the latest release for your platform:
- **Windows**: `umbra-windows-x64.exe`
- **Linux**: `umbra-linux-x64.deb` or `umbra-linux-x64.rpm`
- **macOS**: `umbra-macos-x64.pkg`

### From Source

```bash
git clone https://github.com/eclipse-softworks/umbra
cd umbra/umbra-compiler
cargo build --release
```

## Quick Start

### Hello World

```umbra
fn main() -> Void: {
    show("Hello, Umbra World!")
}
```

### Compile and Run

```bash
# Compile and run directly
umbra run hello.umbra

# Or compile to binary
umbra build hello.umbra
./hello
```

## Language Syntax

### Variables
```umbra
let name: String := "Umbra"
let age: Integer := 42
let pi: Float := 3.14159
let is_awesome: Boolean := true
```

### Functions
```umbra
fn add(a: Integer, b: Integer) -> Integer: {
    return a + b
}
```

### Control Flow
```umbra
when x > 0: {
    show("Positive")
} otherwise: {
    show("Not positive")
}
```

### Pattern Matching
```umbra
when value {
    1 => show("One")
    2 => show("Two")
    otherwise => show("Other")
}
```

## CLI Commands

- `umbra build <file>` - Compile Umbra source to executable
- `umbra run <file>` - Compile and run Umbra source
- `umbra check <file>` - Check syntax and types without compilation
- `umbra repl` - Start interactive REPL
- `umbra init <name>` - Create new Umbra project
- `umbra test` - Run project tests
- `umbra package` - Package project for distribution

## Examples

See the `examples/` directory for sample programs demonstrating various language features.

## Documentation

Full documentation is available in the `docs/` directory.

## License

Copyright © 2024 Eclipse Softworks. All rights reserved.

## Contributing

This is a proprietary project by Eclipse Softworks. For support or inquiries, please contact us through our official channels.
