@echo off
echo ================================================================
echo Testing REAL Umbra Programming Language Binary
echo ================================================================
echo.
echo This demonstrates the actual 92MB Umbra binary with all features:
echo.

echo 1. Checking Umbra version (real binary):
umbra --version
echo.

echo 2. Showing all available commands (real implementation):
umbra --help
echo.

echo 3. Testing REPL availability:
echo Type 'exit' to quit if REPL starts
echo.
pause

echo Starting Umbra REPL...
umbra repl
