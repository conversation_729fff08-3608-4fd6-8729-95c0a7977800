# Core scientific computing
numpy>=1.24.0
pandas>=2.0.0
scipy>=1.11.0

# Machine learning
scikit-learn>=1.3.0
joblib>=1.3.0
xgboost>=1.7.0

# Deep learning
tensorflow>=2.13.0
torch>=2.0.0
torchvision>=0.15.0

# Visualization
matplotlib>=3.7.0
seaborn>=0.12.0
plotly>=5.15.0
bokeh>=3.2.0

# Jupyter ecosystem
jupyter>=1.0.0
notebook>=6.5.0
jupyterlab>=4.0.0
ipython>=8.0.0
ipykernel>=6.25.0

# Data processing
requests>=2.31.0
urllib3>=2.0.0
beautifulsoup4>=4.12.0
lxml>=4.9.0

# Development tools
setuptools>=68.0.0
wheel>=0.41.0
pip>=23.0.0

# Additional ML libraries
statsmodels>=0.14.0
networkx>=3.1.0
sympy>=1.12.0
