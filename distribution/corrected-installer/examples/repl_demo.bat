@echo off
echo ================================================================
echo Umbra Programming Language - Interactive REPL Demo
echo ================================================================
echo.
echo Starting Umbra REPL (Read-Eval-Print Loop)...
echo Type 'exit' to quit the REPL
echo.
echo Try these commands:
echo   show("Hello from REPL!")
echo   let x := 42
echo   show(x)
echo   let result := x * 2 + 10
echo   show("Result: " + result.to_string())
echo.
pause
umbra repl
