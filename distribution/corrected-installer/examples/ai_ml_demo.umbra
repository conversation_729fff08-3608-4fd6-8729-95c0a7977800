// AI/ML Demo in Umbra Programming Language
// Demonstrates machine learning capabilities

bring ml
bring std.io

fn main() -> void {
    show("Umbra AI/ML Demo")
    
    // Create sample dataset
    let data := [
        [1.0, 2.0, 3.0],
        [2.0, 3.0, 4.0],
        [3.0, 4.0, 5.0],
        [4.0, 5.0, 6.0]
    ]
    
    let targets := [10.0, 20.0, 30.0, 40.0]
    
    // Train a simple linear model
    show("Training linear regression model...")
    let model := train linear_regression using data, targets {
        learning_rate: 0.01,
        epochs: 1000
    }
    
    // Make predictions
    let prediction := predict model with [5.0, 6.0, 7.0]
    show("Prediction for [5.0, 6.0, 7.0]: " + prediction.to_string())
    
    show("AI/ML demo completed!")
}
