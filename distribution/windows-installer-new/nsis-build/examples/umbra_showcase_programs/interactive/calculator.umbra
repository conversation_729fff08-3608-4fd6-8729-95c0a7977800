// Interactive Calculator
// Demonstrates user input handling and mathematical operations

show("Interactive Calculator")
show("=====================")
show("Welcome to the Umbra Calculator!")
show("This calculator performs basic arithmetic operations.")

// Predefined calculations for demonstration
show("Performing sample calculations...")

// Addition example
let num1: Integer := 25
let num2: Integer := 17
let addition_result: Integer := num1 + num2

show("Addition Example:")
show("Calculate: ", num1, " + ", num2)
show("Result: ", addition_result)

// Subtraction example
let num3: Integer := 50
let num4: Integer := 23
let subtraction_result: Integer := num3 - num4

show("Subtraction Example:")
show("Calculate: ", num3, " - ", num4)
show("Result: ", subtraction_result)

// Multiplication example
let num5: Integer := 8
let num6: Integer := 12
let multiplication_result: Integer := num5 * num6

show("Multiplication Example:")
show("Calculate: ", num5, " * ", num6)
show("Result: ", multiplication_result)

// Division example
let num7: Float := 100.0
let num8: Float := 4.0
let division_result: Float := num7 / num8

show("Division Example:")
show("Calculate: ", num7, " / ", num8)
show("Result: ", division_result)

// Complex calculations
show("Complex Calculations:")

let a: Float := 15.5
let b: Float := 8.2
let c: Float := 3.7

let complex_result1: Float := (a + b) * c
show("Calculate: (", a, " + ", b, ") * ", c)
show("Result: ", complex_result1)

let complex_result2: Float := a * b - c
show("Calculate: ", a, " * ", b, " - ", c)
show("Result: ", complex_result2)

// Percentage calculations
show("Percentage Calculations:")

let total_amount: Integer := 200
let discount_percent: Integer := 15
let discount_amount: Integer := (total_amount * discount_percent) / 100
let final_amount: Integer := total_amount - discount_amount

show("Original amount: ", total_amount)
show("Discount: ", discount_percent, "%")
show("Discount amount: ", discount_amount)
show("Final amount: ", final_amount)

// Area calculations
show("Area Calculations:")

let length: Float := 12.5
let width: Float := 8.3
let rectangle_area: Float := length * width

show("Rectangle dimensions: ", length, " x ", width)
show("Area: ", rectangle_area, " square units")

let radius: Float := 7.0
let pi: Float := 3.14159
let circle_area: Float := pi * radius * radius

show("Circle radius: ", radius)
show("Area: ", circle_area, " square units")

// Financial calculations
show("Financial Calculations:")

let principal: Float := 1000.0
let rate: Float := 0.05
let time: Float := 3.0
let simple_interest: Float := principal * rate * time
let total_amount_final: Float := principal + simple_interest

show("Principal: ", principal)
show("Interest rate: ", rate * 100, "%")
show("Time: ", time, " years")
show("Simple interest: ", simple_interest)
show("Total amount: ", total_amount_final)

show("Calculator demonstration complete.")
show("All calculations performed successfully!")
