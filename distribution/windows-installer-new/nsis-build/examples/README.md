# Umbra Showcase Programs

This collection demonstrates the full capabilities of the Umbra programming language, including its native AI/ML features, standard library functions, and integration capabilities.

## Directory Structure

```
umbra_showcase_programs/
├── ai_ml/                    # AI/ML specific programs
├── standard_library/         # Standard library demonstrations
├── interactive/              # Interactive user programs
├── integration/              # AI/ML + standard library integration
├── algorithms/               # Algorithm implementations
├── data_processing/          # Data analysis and processing
├── module_system/            # Module system demonstrations
└── README.md                # This documentation file
```

## Program Categories

### 1. AI/ML Features (`ai_ml/`)

**customer_churn_prediction.umbra**

- Demonstrates real machine learning operations using native Umbra AI/ML syntax
- Uses actual customer data to predict churn probability
- Shows multiple model types: Neural Network, Random Forest, SVM
- Includes model training, evaluation, and prediction capabilities

**data_classification_system.umbra**

- Comprehensive data analysis and classification system
- Demonstrates multiple classifier types and model comparison
- Includes cross-validation and performance evaluation
- Shows real-world data science workflow

### 2. Standard Library Features (`standard_library/`)

**mathematical_operations.umbra**

- Showcases Umbra's mathematical capabilities
- Demonstrates arithmetic operations, calculations, and formulas
- Includes geometric calculations, percentages, and sequences
- Shows working with different numeric types

**string_processing.umbra**

- Comprehensive string manipulation and text processing
- Demonstrates string concatenation, formatting, and analysis
- Shows practical text processing scenarios
- Includes structured text creation and manipulation

### 3. Interactive Programs (`interactive/`)

**calculator.umbra**

- Interactive calculator with multiple operation types
- Demonstrates user input handling and mathematical operations
- Includes complex calculations, percentages, and financial computations
- Shows practical real-world calculation scenarios

**data_processor.umbra**

- Interactive data processing and analysis tool
- Demonstrates data manipulation and statistical analysis
- Includes business metrics calculation and trend analysis
- Shows practical data processing workflows

### 4. Integration Examples (`integration/`)

**business_analytics.umbra**

- Combines AI/ML capabilities with traditional business logic
- Demonstrates integration of machine learning with business metrics
- Includes ROI analysis and market opportunity assessment
- Shows practical business intelligence applications

**recommendation_system.umbra**

- Smart recommendation system using AI and business rules
- Integrates machine learning models with rule-based logic
- Demonstrates personalized recommendation generation
- Includes customer analysis and marketing campaign optimization

### 5. Algorithms (`algorithms/`)

**sorting_algorithms.umbra**

- Demonstrates various sorting algorithm implementations
- Shows algorithmic thinking and control structures
- Includes complexity analysis and performance comparison
- Demonstrates search algorithms and optimization techniques

### 6. Data Processing (`data_processing/`)

**file_analysis.umbra**

- Comprehensive file analysis and data processing system
- Demonstrates dataset loading and statistical analysis
- Includes data quality assessment and insight generation
- Shows practical data analysis workflows

### 7. Module System (`module_system/`)

**module_system_tutorial.umbra**

- Step-by-step tutorial on using the 'bring' keyword
- Teaches how to import standard library modules
- Demonstrates using imported constants (π, e) in calculations
- Shows integration with builtin functions and best practices

**mathematical_calculator.umbra**

- Advanced mathematical calculator using imported constants
- Demonstrates geometric, exponential, and trigonometric calculations
- Shows practical applications in physics and engineering
- Includes precision verification and complex mathematical modeling

**scientific_computing.umbra**

- Sophisticated scientific computing applications
- Astronomy calculations using π for orbital mechanics
- Engineering applications with precise mathematical constants
- Physics and chemistry computations with exponential functions

**multi_module_integration.umbra**

- Demonstrates integration of multiple modules in complex applications
- Business analytics combining mathematical modeling with data processing
- Shows how imported constants work with string operations
- Advanced mathematical modeling for real-world scenarios

**real_world_application.umbra**

- Complete satellite communication system design
- Orbital mechanics calculations using imported π constant
- Signal processing and power system analysis
- Demonstrates professional aerospace engineering applications

## Running the Programs

To run any of these programs, use the Umbra compiler from the main directory:

```bash
# From the umbra-compiler directory
cargo run run ../umbra_showcase_programs/ai_ml/customer_churn_prediction.umbra
cargo run run ../umbra_showcase_programs/standard_library/mathematical_operations.umbra
cargo run run ../umbra_showcase_programs/interactive/calculator.umbra
cargo run run ../umbra_showcase_programs/module_system/module_system_tutorial.umbra
cargo run run ../umbra_showcase_programs/module_system/mathematical_calculator.umbra
```

## Tested and Verified Programs

All programs have been tested and verified to work correctly:

✅ **customer_churn_prediction.umbra** - Successfully loads datasets, trains models, and makes predictions
✅ **mathematical_operations.umbra** - Performs all mathematical calculations correctly
✅ **calculator.umbra** - Executes all calculation examples successfully
✅ **data_classification_system.umbra** - Demonstrates comprehensive ML workflow
✅ **string_processing.umbra** - Shows string manipulation capabilities
✅ **data_processor.umbra** - Performs data analysis and processing
✅ **business_analytics.umbra** - Integrates AI/ML with business logic
✅ **recommendation_system.umbra** - Combines ML models with rule-based logic
✅ **sorting_algorithms.umbra** - Demonstrates algorithmic implementations
✅ **file_analysis.umbra** - Analyzes datasets and generates insights
✅ **module_system_tutorial.umbra** - Complete tutorial on module system usage
✅ **mathematical_calculator.umbra** - Advanced mathematical calculations with imports
✅ **scientific_computing.umbra** - Sophisticated scientific applications
✅ **multi_module_integration.umbra** - Complex multi-module applications
✅ **real_world_application.umbra** - Professional aerospace engineering system

## Prerequisites

1. **Data Files**: Some AI/ML programs require dataset files in the `data/` directory:
   - `data/customer_churn.csv`
   - `data/training_data.csv`
   - `data/validation_data.csv`
   - `data/test_data.csv`

2. **Python Dependencies**: AI/ML programs require Python with scikit-learn, pandas, and numpy installed.

## Key Features Demonstrated

### Native AI/ML Syntax

- `load_dataset()` - Load CSV datasets with automatic dimension detection
- `create_model()` - Create various ML model types
- `train` statements - Train models with comprehensive parameter configuration
- `evaluate` statements - Evaluate model performance with real metrics
- `predict` statements - Make predictions on new data

### Standard Library Features

- Mathematical operations and calculations
- String manipulation and text processing
- Control structures (when/otherwise statements)
- Variable declarations and type system
- Input/output operations

### Module System Features

- `bring` keyword for importing standard library modules
- Access to mathematical constants (π, e) from std.math
- Seamless integration with builtin functions (abs, str_len)
- Support for complex mathematical modeling and calculations
- Professional-grade precision for scientific computing

### Integration Capabilities

- Combining AI/ML with traditional programming
- Business logic integration with machine learning
- Data processing with predictive analytics
- Real-world application development

## Program Characteristics

- **Real Functionality**: All programs perform actual operations, no simulated results
- **Practical Applications**: Programs solve real-world problems and use cases
- **Comprehensive Coverage**: Demonstrates the full range of Umbra's capabilities
- **Production Ready**: Programs are structured for real-world deployment
- **Educational Value**: Each program teaches specific Umbra language features

## Notes

- Programs are designed to be self-contained and demonstrate specific features
- Error handling and edge cases are included where appropriate
- Comments explain the purpose and functionality of each section
- Programs can be used as templates for developing similar applications

## Module System Showcase

The `module_system/` directory contains comprehensive demonstrations of Umbra's module import system using the `bring` keyword. These programs showcase:

### Key Module System Features

- **Import Syntax**: Use `bring std.math` to import standard library modules
- **Mathematical Constants**: Access π (PI) and e (E) constants with full precision
- **Integration**: Seamless integration with builtin functions like `abs()` and `str_len()`
- **Real Applications**: Professional-grade scientific and engineering applications

### Standard Library Modules

- **std.math**: Provides mathematical constants π and e for precise calculations
- **std.string**: String manipulation functions (future expansion)
- **std.io**: Input/output operations (future expansion)

### Example Usage

```umbra
// Import the math module
bring std.math

// Use imported constants
let circle_area: Float := PI * radius * radius
let exponential_growth: Float := initial_value * E * rate * time

// Integrate with builtin functions
let absolute_value: Integer := abs(-42)
let text_length: Integer := str_len("Hello, Umbra!")
```

### Practical Applications

The module system enables:
- Scientific computing with precise mathematical constants
- Engineering calculations for real-world applications
- Aerospace and physics simulations
- Business analytics with mathematical modeling
- Educational programming with step-by-step tutorials

This showcase collection provides a comprehensive overview of Umbra's capabilities as both an AI/ML-focused language and a general-purpose programming language with a powerful module system.
