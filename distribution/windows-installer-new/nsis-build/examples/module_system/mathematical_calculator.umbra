// Mathematical Calculator with Module System
// Demonstrates the 'bring' keyword and std.math module usage
// Shows practical applications of imported constants and functions

show("🧮 Mathematical Calculator with Module System")
show("==============================================")
show("")

// Import the math module to access mathematical constants
bring std.math

show("📦 Imported std.math module successfully")
show("✅ Available constants: PI, E")
show("")

// Display the imported constants
show("📊 Mathematical Constants:")
show("   π (PI) = ", PI)
show("   e (E)  = ", E)
show("")

// Geometric Calculations
show("🔺 Geometric Calculations")
show("-------------------------")

fn calculate_circle_area(radius: Float) -> Float:
    let area: Float := PI * radius * radius
    return area

fn calculate_circle_circumference(radius: Float) -> Float:
    let circumference: Float := 2.0 * PI * radius
    return circumference

fn calculate_sphere_volume(radius: Float) -> Float:
    let volume: Float := (4.0 / 3.0) * PI * radius * radius * radius
    return volume

fn calculate_cylinder_volume(radius: Float, height: Float) -> Float:
    let base_area: Float := PI * radius * radius
    let volume: Float := base_area * height
    return volume

// Test geometric calculations
let test_radius: Float := 5.0
let circle_area: Float := calculate_circle_area(test_radius)
let circle_circumference: Float := calculate_circle_circumference(test_radius)
let sphere_volume: Float := calculate_sphere_volume(test_radius)
let cylinder_volume: Float := calculate_cylinder_volume(test_radius, 10.0)

show("For radius = ", test_radius, ":")
show("   Circle area: ", circle_area)
show("   Circle circumference: ", circle_circumference)
show("   Sphere volume: ", sphere_volume)
show("   Cylinder volume (height=10): ", cylinder_volume)
show("")

// Exponential and Logarithmic Calculations
show("📈 Exponential Calculations")
show("---------------------------")

fn calculate_compound_interest(principal: Float, rate: Float, time: Float) -> Float:
    let growth_factor: Float := E * rate * time
    let amount: Float := principal * growth_factor
    return amount

fn calculate_exponential_growth(initial: Float, growth_rate: Float, periods: Float) -> Float:
    let e_power: Float := E * growth_rate * periods
    let final_value: Float := initial * e_power
    return final_value

// Test exponential calculations
let principal: Float := 1000.0
let interest_rate: Float := 0.05
let time_years: Float := 2.0

let compound_result: Float := calculate_compound_interest(principal, interest_rate, time_years)
let growth_result: Float := calculate_exponential_growth(100.0, 0.1, 3.0)

show("Investment calculations:")
show("   Principal: $", principal)
show("   Rate: ", interest_rate * 100.0, "%")
show("   Time: ", time_years, " years")
show("   Compound interest result: $", compound_result)
show("")
show("Exponential growth (100 -> ", growth_result, ")")
show("")

// Trigonometric Approximations using PI
show("📐 Trigonometric Calculations")
show("-----------------------------")

fn degrees_to_radians(degrees: Float) -> Float:
    let radians: Float := degrees * PI / 180.0
    return radians

fn calculate_arc_length(radius: Float, angle_degrees: Float) -> Float:
    let angle_radians: Float := degrees_to_radians(angle_degrees)
    let arc_length: Float := radius * angle_radians
    return arc_length

fn calculate_sector_area(radius: Float, angle_degrees: Float) -> Float:
    let angle_radians: Float := degrees_to_radians(angle_degrees)
    let sector_area: Float := 0.5 * radius * radius * angle_radians
    return sector_area

// Test trigonometric calculations
let circle_radius: Float := 8.0
let angle_deg: Float := 60.0

let angle_rad: Float := degrees_to_radians(angle_deg)
let arc_length: Float := calculate_arc_length(circle_radius, angle_deg)
let sector_area: Float := calculate_sector_area(circle_radius, angle_deg)

show("Trigonometric calculations:")
show("   Circle radius: ", circle_radius)
show("   Angle: ", angle_deg, "° = ", angle_rad, " radians")
show("   Arc length: ", arc_length)
show("   Sector area: ", sector_area)
show("")

// Advanced Mathematical Applications
show("🔬 Advanced Applications")
show("------------------------")

fn calculate_pendulum_period(length: Float, gravity: Float) -> Float:
    let sqrt_lg: Float := length / gravity
    let period: Float := 2.0 * PI * sqrt_lg
    return period

fn calculate_wave_frequency(wavelength: Float, speed: Float) -> Float:
    let frequency: Float := speed / wavelength
    return frequency

// Physics calculations
let pendulum_length: Float := 1.0  // 1 meter
let gravity: Float := 9.81         // m/s²
let pendulum_period: Float := calculate_pendulum_period(pendulum_length, gravity)

let sound_speed: Float := 343.0    // m/s in air
let wavelength: Float := 0.5       // meters
let frequency: Float := calculate_wave_frequency(wavelength, sound_speed)

show("Physics applications:")
show("   Pendulum period (L=1m): ", pendulum_period, " seconds")
show("   Sound frequency (λ=0.5m): ", frequency, " Hz")
show("")

// Precision and Accuracy Demonstration
show("🎯 Precision Verification")
show("-------------------------")

let pi_precision: Float := PI * 1000000.0
let e_precision: Float := E * 1000000.0
let pi_squared: Float := PI * PI
let e_squared: Float := E * E

show("High precision calculations:")
show("   π × 1,000,000 = ", pi_precision)
show("   e × 1,000,000 = ", e_precision)
show("   π² = ", pi_squared)
show("   e² = ", e_squared)
show("")

// Summary
show("✅ Module System Demonstration Complete!")
show("========================================")
show("✅ Successfully imported std.math module using 'bring' keyword")
show("✅ Used PI and E constants in complex calculations")
show("✅ Demonstrated practical mathematical applications")
show("✅ Showed integration with user-defined functions")
show("✅ Verified precision and accuracy of imported constants")
show("")
show("🎉 The Umbra module system enables powerful mathematical programming!")
