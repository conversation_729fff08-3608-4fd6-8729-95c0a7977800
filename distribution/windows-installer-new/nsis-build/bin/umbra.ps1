# Umbra Programming Language PowerShell Launcher
# Provides enhanced functionality for Windows PowerShell users

param(
    [Parameter(ValueFromRemainingArguments=$true)]
    [string[]]$Arguments
)

$UmbraHome = Split-Path -Parent $MyInvocation.MyCommand.Path
$UmbraExe = Join-Path $UmbraHome "umbra.exe"

if (Test-Path $UmbraExe) {
    & $UmbraExe @Arguments
} else {
    Write-Error "Umbra executable not found at: $UmbraExe"
    exit 1
}
