; Umbra Programming Language - Professional Windows Installer
; Complete installation with PATH integration, file associations, and shortcuts

!define PRODUCT_NAME "Umbra Programming Language"
!define PRODUCT_VERSION "1.0.1"
!define PRODUCT_PUBLISHER "Eclipse Softworks"
!define PRODUCT_WEB_SITE "https://umbra-lang.org"
!define PRODUCT_DIR_REGKEY "Software\Microsoft\Windows\CurrentVersion\App Paths\umbra.exe"
!define PRODUCT_UNINST_KEY "Software\Microsoft\Windows\CurrentVersion\Uninstall\${PRODUCT_NAME}"
!define PRODUCT_UNINST_ROOT_KEY "HKLM"

; Modern UI
!include "MUI2.nsh"
!include "x64.nsh"
!include "FileAssociation.nsh"

; Installer settings
Name "${PRODUCT_NAME} ${PRODUCT_VERSION}"
OutFile "umbra-${PRODUCT_VERSION}-windows-x64-installer.exe"
InstallDir "$PROGRAMFILES64\Umbra"
InstallDirRegKey HKLM "${PRODUCT_DIR_REGKEY}" ""
ShowInstDetails show
ShowUnInstDetails show
RequestExecutionLevel admin

; Modern UI Configuration
!define MUI_ABORTWARNING
!define MUI_ICON "${NSISDIR}\Contrib\Graphics\Icons\modern-install.ico"
!define MUI_UNICON "${NSISDIR}\Contrib\Graphics\Icons\modern-uninstall.ico"
!define MUI_HEADERIMAGE
!define MUI_HEADERIMAGE_BITMAP "${NSISDIR}\Contrib\Graphics\Header\nsis3-metro.bmp"
!define MUI_WELCOMEFINISHPAGE_BITMAP "${NSISDIR}\Contrib\Graphics\Wizard\nsis3-metro.bmp"

; Pages
!insertmacro MUI_PAGE_WELCOME
!insertmacro MUI_PAGE_LICENSE "LICENSE"
!insertmacro MUI_PAGE_DIRECTORY
!insertmacro MUI_PAGE_COMPONENTS
!insertmacro MUI_PAGE_INSTFILES
!define MUI_FINISHPAGE_RUN "$INSTDIR\bin\umbra.exe"
!define MUI_FINISHPAGE_RUN_PARAMETERS "--version"
!insertmacro MUI_PAGE_FINISH

; Uninstaller pages
!insertmacro MUI_UNPAGE_INSTFILES

; Language
!insertmacro MUI_LANGUAGE "English"

; Version information
VIProductVersion "*******"
VIAddVersionKey "ProductName" "${PRODUCT_NAME}"
VIAddVersionKey "Comments" "Modern programming language with AI/ML capabilities"
VIAddVersionKey "CompanyName" "${PRODUCT_PUBLISHER}"
VIAddVersionKey "LegalCopyright" "© ${PRODUCT_PUBLISHER}"
VIAddVersionKey "FileDescription" "${PRODUCT_NAME} Installer"
VIAddVersionKey "FileVersion" "${PRODUCT_VERSION}"

; Main installer section
Section "Umbra Compiler (Required)" SEC01
  SectionIn RO
  SetOutPath "$INSTDIR"
  
  ; Install main files
  File "LICENSE"
  File "README.md"
  File "umbra.conf"
  
  ; Install binary
  SetOutPath "$INSTDIR\bin"
  File "bin\umbra.exe"
  File "bin\umbra.bat"
  File "bin\umbra.ps1"
  
  ; Create directories
  CreateDirectory "$APPDATA\Umbra"
  CreateDirectory "$APPDATA\Umbra\models"
  CreateDirectory "$APPDATA\Umbra\cache"
  
  ; Register application
  WriteRegStr HKLM "${PRODUCT_DIR_REGKEY}" "" "$INSTDIR\bin\umbra.exe"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "DisplayName" "$(^Name)"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "UninstallString" "$INSTDIR\uninst.exe"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "DisplayIcon" "$INSTDIR\bin\umbra.exe"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "DisplayVersion" "${PRODUCT_VERSION}"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "URLInfoAbout" "${PRODUCT_WEB_SITE}"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "Publisher" "${PRODUCT_PUBLISHER}"
  
  ; Add to PATH
  DetailPrint "Adding Umbra to system PATH..."
  Call AddToPath
  
  ; Create uninstaller
  WriteUninstaller "$INSTDIR\uninst.exe"
SectionEnd

Section "File Associations" SEC02
  ${RegisterExtension} "$INSTDIR\bin\umbra.exe" ".umbra" "Umbra Source File"
SectionEnd

Section "Desktop Shortcut" SEC03
  CreateShortCut "$DESKTOP\Umbra.lnk" "$INSTDIR\bin\umbra.exe" "--version"
SectionEnd

Section "Start Menu Shortcuts" SEC04
  CreateDirectory "$SMPROGRAMS\Umbra Programming Language"
  CreateShortCut "$SMPROGRAMS\Umbra Programming Language\Umbra.lnk" "$INSTDIR\bin\umbra.exe"
  CreateShortCut "$SMPROGRAMS\Umbra Programming Language\Umbra REPL.lnk" "$INSTDIR\bin\umbra.exe" "repl"
  CreateShortCut "$SMPROGRAMS\Umbra Programming Language\Command Prompt.lnk" "$SYSDIR\cmd.exe" "/k cd /d $\"$INSTDIR\bin$\""
  CreateShortCut "$SMPROGRAMS\Umbra Programming Language\Uninstall.lnk" "$INSTDIR\uninst.exe"
SectionEnd

Section "Examples and Documentation" SEC05
  SetOutPath "$INSTDIR\examples"
  File /r "examples\*.*"
  SetOutPath "$INSTDIR\docs"
  File /r "docs\*.*"
SectionEnd

; Section descriptions
!insertmacro MUI_FUNCTION_DESCRIPTION_BEGIN
  !insertmacro MUI_DESCRIPTION_TEXT ${SEC01} "Core Umbra compiler and runtime (required)"
  !insertmacro MUI_DESCRIPTION_TEXT ${SEC02} "Associate .umbra files with Umbra compiler"
  !insertmacro MUI_DESCRIPTION_TEXT ${SEC03} "Create a desktop shortcut for easy access"
  !insertmacro MUI_DESCRIPTION_TEXT ${SEC04} "Add Umbra to the Start Menu"
  !insertmacro MUI_DESCRIPTION_TEXT ${SEC05} "Install example programs and documentation"
!insertmacro MUI_FUNCTION_DESCRIPTION_END

; Add to PATH function
Function AddToPath
  Push $R0
  Push $R1
  Push $R2
  Push $R3

  ReadRegStr $R0 HKLM "SYSTEM\CurrentControlSet\Control\Session Manager\Environment" "PATH"
  StrCpy $R1 "$INSTDIR\bin"
  StrLen $R2 "$R1"
  StrCpy $R3 $R0 $R2
  StrCmp $R3 $R1 +3
    StrCmp $R0 "" AddToPath_NTPath
      StrCpy $R0 "$R0;$R1"
  Goto AddToPath_NTPath
  AddToPath_NTPath:
    WriteRegExpandStr HKLM "SYSTEM\CurrentControlSet\Control\Session Manager\Environment" "PATH" $R0
    SendMessage ${HWND_BROADCAST} ${WM_WININICHANGE} 0 "STR:Environment" /TIMEOUT=5000

  Pop $R3
  Pop $R2
  Pop $R1
  Pop $R0
FunctionEnd

; Remove from PATH function
Function un.RemoveFromPath
  Push $R0
  Push $R1
  Push $R2
  Push $R3
  Push $R4
  Push $R5
  Push $R6

  ReadRegStr $R0 HKLM "SYSTEM\CurrentControlSet\Control\Session Manager\Environment" "PATH"
  StrCpy $R1 "$INSTDIR\bin"
  StrLen $R2 "$R1"
  StrLen $R3 $R0
  StrCpy $R4 0

  loop:
    StrCpy $R5 $R0 $R2 $R4
    StrCmp $R5 $R1 found
    StrCmp $R4 $R3 done
    IntOp $R4 $R4 + 1
    Goto loop

  found:
    StrCpy $R5 $R0 $R4
    IntOp $R4 $R4 + $R2
    StrCpy $R6 $R0 "" $R4
    StrCpy $R0 "$R5$R6"
    WriteRegExpandStr HKLM "SYSTEM\CurrentControlSet\Control\Session Manager\Environment" "PATH" $R0
    SendMessage ${HWND_BROADCAST} ${WM_WININICHANGE} 0 "STR:Environment" /TIMEOUT=5000

  done:
  Pop $R6
  Pop $R5
  Pop $R4
  Pop $R3
  Pop $R2
  Pop $R1
  Pop $R0
FunctionEnd

; Uninstaller
Section Uninstall
  ; Remove from PATH
  Call un.RemoveFromPath

  ; Remove file associations
  ${UnRegisterExtension} ".umbra" "Umbra Source File"

  ; Remove files
  Delete "$INSTDIR\bin\umbra.exe"
  Delete "$INSTDIR\bin\umbra.bat"
  Delete "$INSTDIR\bin\umbra.ps1"
  Delete "$INSTDIR\LICENSE"
  Delete "$INSTDIR\README.md"
  Delete "$INSTDIR\umbra.conf"
  Delete "$INSTDIR\uninst.exe"

  ; Remove directories
  RMDir /r "$INSTDIR\examples"
  RMDir /r "$INSTDIR\docs"
  RMDir "$INSTDIR\bin"
  RMDir "$INSTDIR"

  ; Remove shortcuts
  Delete "$DESKTOP\Umbra.lnk"
  RMDir /r "$SMPROGRAMS\Umbra Programming Language"

  ; Remove registry entries
  DeleteRegKey ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}"
  DeleteRegKey HKLM "${PRODUCT_DIR_REGKEY}"

  SetAutoClose true
SectionEnd

; Constants for PATH manipulation
!ifndef HWND_BROADCAST
!define HWND_BROADCAST 0xffff
!endif
!ifndef WM_WININICHANGE
!define WM_WININICHANGE 0x001A
!endif
