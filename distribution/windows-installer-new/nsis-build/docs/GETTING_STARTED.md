# Getting Started with <PERSON>bra

Welcome to Umbra, a modern programming language designed for AI/ML applications! This guide will help you get up and running with Umbra quickly.

## Installation

### Prerequisites

- Python 3.8 or higher
- pip (Python package manager)

### Install Dependencies

```bash
# Clone the repository
git clone <repository-url>
cd Umbra

# Install required dependencies
pip install -r requirements.txt
```

### Verify Installation

```bash
# Test the compiler
python umbra_cli.py --version

# Run a simple example
python umbra_cli.py run examples/hello.umbra
```

## Your First Umbra Program

Create a file called `hello.umbra`:

```umbra
define main() -> Void:
    show "Hello, Umbra!"
    show "Welcome to AI programming!"
```

Run it:

```bash
python umbra_cli.py run hello.umbra
```

## Basic Syntax

### Variables and Types

```umbra
// Variable declarations
let name: String := "Umbra"
let version: Float := 1.0
let active: Boolean := true
let count: Integer := 42

// Lists
let numbers: List[Integer] := [1, 2, 3, 4, 5]
```

### Functions

```umbra
define greet(name: String) -> String:
    return "Hello, " + name + "!"

define add(a: Integer, b: Integer) -> Integer:
    return a + b

define main() -> Void:
    let greeting: String := greet("World")
    show greeting
    
    let sum: Integer := add(5, 3)
    show "5 + 3 =", sum
```

### Control Flow

```umbra
define check_age(age: Integer) -> Void:
    when age >= 18:
        show "You are an adult"
    otherwise:
        show "You are a minor"
```

### Loops

```umbra
define print_numbers() -> Void:
    let numbers: List[Integer] := [1, 2, 3, 4, 5]
    
    repeat num in numbers:
        show "Number:", num
```

### Structures

```umbra
structure Person:
    name: String
    age: Integer
    email: String

define create_person() -> Void:
    let alice: Person := Person("Alice", 30, "<EMAIL>")
    show "Person:", alice.name, "Age:", alice.age
```

## AI/ML Features

Umbra's special strength is in AI/ML programming:

```umbra
define train_model_example() -> Void:
    // Load data
    let dataset: Dataset := load_dataset("data/training.csv")
    let validation: Dataset := load_dataset("data/validation.csv")
    
    // Create model
    let model: Model := create_model("neural_network")
    
    // Train the model
    train model using dataset:
        epochs := 100
        learning_rate := 0.001
        batch_size := 32
    
    // Evaluate
    evaluate model on validation
    
    // Visualize results
    visualize loss over epochs
    visualize accuracy over epochs
    
    // Export trained model
    export model to "models/trained_model.pkl"
    
    // Make predictions
    let sample: String := "test_input"
    predict sample using model
```

## Command Line Usage

### Running Programs

```bash
# Run an Umbra program directly
python umbra_cli.py run program.umbra

# Compile to Python (for inspection)
python umbra_cli.py build program.umbra -o program.py

# Start interactive REPL
python umbra_cli.py repl
```

### Examples

The `examples/` directory contains various Umbra programs:

- `hello.umbra` - Basic hello world
- `variables.umbra` - Variable declarations and types
- `control_flow.umbra` - Conditional statements
- `loops.umbra` - Iteration examples
- `functions.umbra` - Function definitions
- `structures.umbra` - Custom data types
- `ai_ml.umbra` - AI/ML workflow example
- `comprehensive.umbra` - Complex program showcasing multiple features

## Development Workflow

1. **Write** your Umbra code in `.umbra` files
2. **Test** using `python umbra_cli.py run your_program.umbra`
3. **Debug** by compiling to Python: `python umbra_cli.py build your_program.umbra`
4. **Iterate** and improve your code

## Error Handling

Umbra provides helpful error messages:

```bash
# Syntax errors
Error: Line 5, Column 10: Expected ':' after function name

# Type errors  
Error: Type mismatch: cannot assign String to variable 'count' of type Integer

# Semantic errors
Error: Undefined variable: 'unknown_var'
```

## Next Steps

- Explore the `examples/` directory
- Read the [Language Reference](LANGUAGE_REFERENCE.md)
- Check out [Advanced Features](ADVANCED_FEATURES.md)
- Contribute to the project!

## Getting Help

- Check the documentation in the `docs/` folder
- Look at example programs in `examples/`
- Run tests with `python run_tests.py`
- Report issues on the project repository

Happy coding with Umbra! 🚀
