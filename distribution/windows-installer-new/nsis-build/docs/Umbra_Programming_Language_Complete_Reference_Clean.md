---
title: "Umbra Programming Language: Complete Textbook"
subtitle: "From Fundamentals to Advanced AI/ML and Database Programming"
author: "Eclipse Softworks Development Team"
date: "2025"
version: "2.0.0"
logo: "umbra-logo.png"
geometry: margin=1in
fontsize: 11pt
documentclass: book
toc: true
toc-depth: 4
numbersections: true
colorlinks: true
linkcolor: blue
urlcolor: blue
citecolor: blue
bibliography: references.bib
csl: ieee.csl
header-includes:
  - \usepackage{fancyhdr}
  - \pagestyle{fancy}
  - \fancyhead[L]{Umbra Programming Language Textbook}
  - \fancyhead[R]{Eclipse Softworks}
  - \fancyfoot[C]{\thepage}
  - \usepackage{titling}
  - \pretitle{\begin{center}\LARGE\includegraphics[width=4cm]{umbra-logo.png}\\[\bigskipamount]}
  - \posttitle{\end{center}}
  - \usepackage{listings}
  - \usepackage{xcolor}
  - \usepackage{graphicx}
  - \usepackage{amsmath}
  - \usepackage{amsfonts}
  - \usepackage{amssymb}
  - \usepackage{tikz}
  - \usepackage{pgfplots}
  - \usepackage{booktabs}
  - \usepackage{longtable}
  - \usepackage{array}
  - \usepackage{multirow}
  - \usepackage{wrapfig}
  - \usepackage{float}
  - \usepackage{colortbl}
  - \usepackage{pdflscape}
  - \usepackage{tabu}
  - \usepackage{threeparttable}
  - \usepackage{threeparttablex}
  - \usepackage[normalem]{ulem}
  - \usepackage{makecell}
  - \definecolor{codegreen}{rgb}{0,0.6,0}
  - \definecolor{codegray}{rgb}{0.5,0.5,0.5}
  - \definecolor{codepurple}{rgb}{0.58,0,0.82}
  - \definecolor{codeblue}{rgb}{0.13,0.29,0.53}
  - \definecolor{codeorange}{rgb}{0.8,0.4,0}
  - \definecolor{codedbblue}{rgb}{0.0,0.2,0.6}
  - \definecolor{backcolour}{rgb}{0.95,0.95,0.92}
  - \lstdefinelanguage{Umbra}{
      keywords={define, fn, let, mut, bring, export, from, as, public, private, when, otherwise, repeat, in, return, break, continue, for, while, loop, if, else, impl, trait, struct, enum, type, const, static, async, await, try, catch, finally, throw, match, with, using, namespace, module, and, or, not, void},
      keywordstyle=\color{codeblue}\bfseries,
      keywords=[2]{train, evaluate, predict, visualize, dataset, model, tensor, matrix, vector, neural, layer, optimizer, loss, metric, batch, epoch, learning_rate, dropout, activation, gradient, backprop, forward, inference, pipeline, transform, preprocess, postprocess, feature, target, label, supervised, unsupervised, reinforcement, classification, regression, clustering, anomaly, detection, nlp, cv, audio, time_series},
      keywordstyle=[2]\color{codeorange}\bfseries,
      keywords=[3]{show, to_string, str_len, to_upper, to_lower, split, join, trim, contains, starts_with, ends_with, substring, range, len, abs, max, min, sqrt, sin, cos, tan, log, exp, pow, floor, ceil, round, random, random_int, random_float, read, write, input, readln, json_parse, json_stringify},
      keywordstyle=[3]\color{codepurple}\bfseries,
      keywords=[4]{Integer, Float, String, Boolean, Void, List, Map, Set, Option, Result, Function, Dataset, Model, Tensor, Connection, Pool, Transaction, Migration, Schema, Query},
      keywordstyle=[4]\color{codeblue}\bfseries,
      keywords=[5]{database, table, column, index, query, transaction, commit, rollback, migrate, schema, connection, pool},
      keywordstyle=[5]\color{codedbblue}\bfseries,
      ndkeywords={true, false, null, undefined, NaN, Infinity, Some, None, Ok, Err},
      ndkeywordstyle=\color{codepurple}\bfseries,
      identifierstyle=\color{black},
      sensitive=true,
      comment=[l]{//},
      morecomment=[s]{/*}{*/},
      commentstyle=\color{codegreen}\ttfamily,
      stringstyle=\color{red}\ttfamily,
      morestring=[b]',
      morestring=[b]"
    }
  - \lstset{
      language=Umbra,
      basicstyle=\ttfamily\small,
      keywordstyle=\color{codeblue}\bfseries,
      keywordstyle=[2]\color{codeorange}\bfseries,
      keywordstyle=[3]\color{codepurple}\bfseries,
      keywordstyle=[4]\color{codeblue}\bfseries,
      keywordstyle=[5]\color{codedbblue}\bfseries,
      commentstyle=\color{codegreen}\ttfamily,
      stringstyle=\color{red}\ttfamily,
      numberstyle=\tiny\color{codegray},
      numbers=left,
      numbersep=8pt,
      frame=single,
      frameround=tttt,
      framesep=6pt,
      breaklines=true,
      breakatwhitespace=false,
      showstringspaces=false,
      showtabs=false,
      tabsize=2,
      captionpos=b,
      backgroundcolor=\color{backcolour},
      rulecolor=\color{codegray},
      xleftmargin=15pt,
      xrightmargin=5pt
    }
---

\newpage

# Preface

Welcome to the complete reference guide for the Umbra programming language! Umbra is a revolutionary, modern programming language designed specifically for the era of artificial intelligence and machine learning, while maintaining the power and flexibility needed for systems programming and general-purpose development.

*Fun fact: Umbra is named after the darkest part of a shadow, because it helps you shed light on the darkest corners of programming complexity!*

## About Umbra

Umbra represents a paradigm shift in programming language design, combining the performance of systems languages like Rust and C++ with the expressiveness of high-level languages like Python, while adding native support for AI/ML workflows. Born from the need to bridge the gap between traditional programming and modern data science, Umbra offers a unified platform for building everything from high-performance systems to sophisticated machine learning models.

The language philosophy centers around three core principles:
- **Performance without Compromise**: Native compilation with LLVM optimization
- **Safety by Design**: Memory safety and type safety without runtime overhead
- **AI/ML First**: Built-in constructs for modern data science workflows

## Target Audience

This book is designed for multiple audiences:

**Beginners**: If you're new to programming, Umbra's clear syntax and comprehensive error messages make it an excellent first language. The book assumes no prior programming experience and builds concepts gradually.

**Experienced Developers**: If you're coming from languages like Python, JavaScript, Java, C++, or Rust, you'll find familiar concepts with powerful new features. Special migration guides are included for common language transitions.

**Data Scientists and ML Engineers**: Umbra's native AI/ML support makes it ideal for researchers and practitioners who want the performance of compiled languages without sacrificing the expressiveness needed for complex algorithms.

**Systems Programmers**: Those working on performance-critical applications will appreciate Umbra's zero-cost abstractions, manual memory management options, and direct hardware access capabilities.

## Prerequisites

This book is structured to accommodate readers with varying backgrounds:

**No Programming Experience Required**: Chapters 1-6 assume no prior knowledge and introduce fundamental programming concepts alongside Umbra-specific features.

**Helpful Background Knowledge**:
- Basic computer literacy and command-line familiarity
- Mathematical concepts (algebra, basic statistics) for AI/ML chapters
- Understanding of data structures and algorithms (beneficial but not required)

**For Advanced Topics**:
- Systems programming concepts (memory management, concurrency)
- Machine learning fundamentals (for AI/ML chapters)
- Software engineering practices (testing, debugging, version control)

## Installation and Setup

Before diving into the language, ensure you have Umbra installed on your system:

### Quick Installation

**Linux/macOS**:
```bash
curl -sSf https://download.umbra-lang.eclipse-softworks.com | sh
source ~/.umbrarc
```

**Windows**:
Download the installer from [umbra-lang.eclipse-softworks.com](https://umbra-lang.eclipse-softworkks.com) or use the package manager:
```powershell
winget install EclipseSoftworks.Umbra
```

### Verification

Test your installation:
```bash
umbra --version
umbra repl
```

### Development Environment

For the best experience, install the VS Code extension:
1. Open VS Code
2. Search for "Umbra Programming Language" in extensions
3. Install the official extension by Eclipse Softworks

The extension provides syntax highlighting, IntelliSense, debugging support, and integrated compilation.

## Language Overview

Umbra combines familiar syntax with powerful new features:

```umbra
// Simple function definition
define greet(name: String) -> String:
    return "Hello, " + name + "!"

// AI/ML native syntax
define train_model() -> Void:
    let dataset := load_dataset("data.csv")
    let model := LinearRegression()
    train model using dataset
    let accuracy := evaluate model on dataset.test
    show("Model accuracy:", accuracy)

// Lambda functions and functional programming
define main() -> Void:
    let numbers: List[Integer] := [1, 2, 3, 4, 5]
    let doubled := numbers.map(|x: Integer| -> x * 2)
    show("Doubled numbers:", doubled)
```

## What Makes Umbra Special

### Native AI/ML Support
Unlike other languages that require external libraries, Umbra includes AI/ML constructs as first-class language features:
- `train`, `evaluate`, `predict` keywords
- Built-in tensor and dataset types
- Automatic GPU acceleration
- Seamless Python interoperability

### Memory Safety Without Garbage Collection
Umbra uses an advanced ownership system similar to Rust but with more intuitive syntax:
- Compile-time memory safety guarantees
- Zero-cost abstractions
- Optional manual memory management for performance-critical code

### Performance-First Design
- LLVM-based compilation for optimal machine code
- Ahead-of-time compilation with optional JIT
- Native threading and async/await support
- Direct hardware access when needed

### Developer Experience
- Comprehensive error messages with suggestions
- Built-in testing framework
- Package manager and build system
- Rich IDE support and debugging tools

## How to Use This Book

This book is carefully structured to take you from complete beginner to advanced Umbra developer through a logical progression:

### Part I: Language Fundamentals (Chapters 1-6)
- **Chapter 1**: Introduction and basic syntax
- **Chapter 2**: Data types, variables, and string operations
- **Chapter 3**: Control flow and decision making
- **Chapter 4**: Functions and lambda expressions
- **Chapter 5**: Collections and data structures
- **Chapter 6**: Error handling and debugging

### Part II: Advanced Features (Chapters 7-12)
- **Chapter 7**: Memory management and ownership
- **Chapter 8**: Object-oriented programming
- **Chapter 9**: Traits and generics
- **Chapter 10**: Pattern matching
- **Chapter 11**: Modules and project organization
- **Chapter 12**: Macros and metaprogramming

### Part III: Concurrent Programming (Chapters 13-16)
- **Chapter 13**: Async/await programming
- **Chapter 14**: Parallel processing
- **Chapter 15**: Thread safety and synchronization
- **Chapter 16**: Actor model and message passing

### Part IV: AI/ML Features (Chapters 17-24)
- **Chapter 17**: Introduction to AI/ML in Umbra
- **Chapter 18**: Data processing and tensors
- **Chapter 19**: Neural networks and deep learning
- **Chapter 20**: Training and evaluation
- **Chapter 21**: Model deployment and inference
- **Chapter 22**: Computer vision
- **Chapter 23**: Natural language processing
- **Chapter 24**: Reinforcement learning

### Part V: Standard Library and Ecosystem (Chapters 25-30)
- **Chapter 25**: Core standard library
- **Chapter 26**: Collections and algorithms
- **Chapter 27**: I/O and file system
- **Chapter 28**: Networking and web development
- **Chapter 29**: Database integration
- **Chapter 30**: Testing framework

### Part VI: Performance and Deployment (Chapters 31-39)
- **Chapter 31**: Performance profiling
- **Chapter 32**: Memory optimization
- **Chapter 33**: GPU computing
- **Chapter 34**: Compiler optimizations
- **Chapter 35**: Python integration
- **Chapter 36**: C/C++ interoperability
- **Chapter 37**: WebAssembly support
- **Chapter 38**: Package management
- **Chapter 39**: Build systems and deployment

### Learning Path Recommendations

**For Complete Beginners**: Read chapters 1-6 sequentially, practicing all examples. Focus on understanding concepts before moving to advanced topics.

**For Experienced Programmers**: Skim chapters 1-3 for syntax differences, then focus on chapters 4-12 for Umbra-specific features. Jump to relevant sections based on your interests.

**For Data Scientists**: Start with chapters 1-4 for syntax, then jump to chapters 17-24 for AI/ML features. Return to other sections as needed.

**For Systems Programmers**: Focus on chapters 7, 13-16, and 31-34 for performance and low-level features.

### Code Examples and Exercises

Every chapter includes:
- **Practical Examples**: Real-world code snippets that you can run and modify
- **Hands-on Exercises**: Progressive challenges to reinforce learning
- **Best Practices**: Industry-standard coding patterns and conventions
- **Common Pitfalls**: Typical mistakes and how to avoid them
- **Performance Tips**: Optimization techniques for each topic

All code examples have been tested with the Umbra compiler and use only verified, working syntax. Examples progress from simple concepts to complex applications.

### Conventions Used in This Book

- **Code blocks** are syntax-highlighted and include line numbers
- **File extensions** use `.umbra` for all Umbra source files
- **Function calls** use verified built-in functions like `show()` for output
- **String operations** demonstrate proper concatenation with `to_string()` or comma-separated arguments
- **Lambda syntax** follows the pattern: `|param: Type| -> expression`
- **Comments** explain complex concepts and provide context

### Online Resources

Supplement your learning with:
- **Official Documentation**: [docs.umbra-lang.org](https://docs.umbra-lang.org)
- **Community Forum**: [community.umbra-lang.org](https://community.umbra-lang.org)
- **GitHub Repository**: [github.com/eclipse-softworks/umbra-language](https://github.com/eclipse-softworks/umbra-language)
- **Package Registry**: [packages.umbra-lang.org](https://packages.umbra-lang.org)

## Acknowledgments

This book represents the collective effort of the Umbra development team and the broader community. Special thanks to:

- The core language design team at Eclipse Softworks
- Early adopters who provided invaluable feedback
- Contributors to the standard library and tooling ecosystem
- The open-source community for inspiration and collaboration

## Version Information

This reference guide covers **Umbra version 1.2.7** and is updated regularly to reflect language evolution. Check [umbra-lang.org](https://umbra-lang.org) for the latest version and updates.

---

*Ready to embark on your Umbra journey? Let's dive into the world of modern, AI-first programming!*

---

*Eclipse Softworks Development Team*  
*2025*

\newpage

# Table of Contents

## Part I: Language Fundamentals

1. [Introduction to Umbra](#introduction-to-umbra)
2. [Basic Syntax and Structure](#basic-syntax-and-structure)
3. [Data Types and Variables](#data-types-and-variables)
4. [String Operations and Manipulation](#string-operations-and-manipulation)
5. [Control Flow](#control-flow)
6. [Functions and Lambda Expressions](#functions-and-lambda-expressions)
7. [Error Handling](#error-handling)

## Part II: Advanced Language Features

8. [Memory Management](#memory-management)
9. [Object-Oriented Programming](#object-oriented-programming)
10. [Traits and Generics](#traits-and-generics)
11. [Pattern Matching](#pattern-matching)
12. [Modules and Namespaces](#modules-and-namespaces)
13. [Macros and Metaprogramming](#macros-and-metaprogramming)

## Part III: Concurrent Programming

14. [Async/Await Programming](#async-await-programming)
15. [Parallel Processing](#parallel-processing)
16. [Thread Safety and Synchronization](#thread-safety-and-synchronization)
17. [Actor Model and Message Passing](#actor-model-and-message-passing)

## Part IV: AI/ML Features

18. [Introduction to AI/ML in Umbra](#introduction-to-ai-ml-in-umbra)
19. [Data Processing and Tensors](#data-processing-and-tensors)
20. [Neural Networks and Deep Learning](#neural-networks-and-deep-learning)
21. [Training and Evaluation](#training-and-evaluation)
22. [Model Deployment and Inference](#model-deployment-and-inference)
23. [Computer Vision](#computer-vision)
24. [Natural Language Processing](#natural-language-processing)
25. [Reinforcement Learning](#reinforcement-learning)

## Part V: Database Programming

26. [Introduction to Database Programming](#introduction-to-database-programming)
27. [Database Connection Management](#database-connection-management)
28. [SQL Integration and Query Execution](#sql-integration-and-query-execution)
29. [Object-Relational Mapping (ORM)](#object-relational-mapping-orm)
30. [Database Transactions and ACID Properties](#database-transactions-and-acid-properties)
31. [Database Schema Management and Migrations](#database-schema-management-and-migrations)
32. [Connection Pooling and Performance](#connection-pooling-and-performance)
33. [Database Security and Best Practices](#database-security-and-best-practices)
34. [Multi-Database Support](#multi-database-support)
35. [Async Database Operations](#async-database-operations)

## Part VI: Standard Library and Ecosystem

36. [Core Standard Library](#core-standard-library)
37. [Collections and Data Structures](#collections-and-data-structures)
38. [I/O and File System](#io-and-file-system)
39. [Networking and Web Development](#networking-and-web-development)
40. [Testing Framework](#testing-framework)

## Part VII: Performance and Optimization

41. [Performance Profiling](#performance-profiling)
42. [Memory Optimization](#memory-optimization)
43. [GPU Computing](#gpu-computing)
44. [Compiler Optimizations](#compiler-optimizations)
45. [Database Performance Tuning](#database-performance-tuning)

## Part VIII: Interoperability and Deployment

46. [Python Integration](#python-integration)
47. [C/C++ Interoperability](#c-cpp-interoperability)
48. [WebAssembly Support](#webassembly-support)
49. [Package Management](#package-management)
50. [Build Systems and Deployment](#build-systems-and-deployment)

## Part IX: Advanced Topics

51. [Metaprogramming and Macros](#metaprogramming-and-macros)
52. [Compiler Internals](#compiler-internals)
53. [Language Design Principles](#language-design-principles)
54. [Contributing to Umbra](#contributing-to-umbra)
55. [Future Roadmap](#future-roadmap)

## Appendices

A. [Installation and Setup](#installation-and-setup)
B. [Language Reference](#language-reference)
C. [Standard Library Reference](#standard-library-reference)
D. [Tooling and IDE Support](#tooling-and-ide-support)
E. [Migration Guides](#migration-guides)
F. [Community and Resources](#community-and-resources)

\newpage

# Introduction to Umbra

Umbra is a modern systems programming language designed for the age of artificial intelligence and machine learning. Born from the need to bridge high-performance systems programming with the rapidly evolving world of AI/ML development, Umbra provides developers with a unified platform for building everything from low-level system components to sophisticated machine learning models.

## Design Philosophy

Umbra's design is guided by several core principles:

### Performance First
Every language feature is designed with performance in mind. Umbra compiles to highly optimized native code using the LLVM backend, ensuring that your programs run at maximum speed without sacrificing developer productivity.

### Memory Safety Without Compromise
Umbra provides memory safety guarantees through its advanced ownership system, eliminating entire classes of bugs like buffer overflows, use-after-free, and memory leaks, all without the overhead of garbage collection.

### AI/ML as a First-Class Citizen
Unlike other languages where AI/ML capabilities are bolted on through libraries, Umbra includes native language constructs for machine learning workflows, making it natural to express complex AI algorithms directly in the language.

### Developer Experience
Umbra prioritizes developer productivity with features like powerful type inference, comprehensive error messages, integrated tooling, and extensive IDE support.

## Language Overview

Let's start with a simple "Hello, World!" program to get a feel for Umbra's syntax:

```umbra
define main() -> Void:
    show("Hello, World!")
    show("Welcome to Umbra - the AI/ML programming language!")
```

*Yes, it's the classic "Hello, World!" - the programming equivalent of "testing, testing, 1, 2, 3" but somehow more satisfying when it actually works! Notice how Umbra doesn't need curly braces - because who has time for all that finger gymnastics?*

This simple program demonstrates several key aspects of Umbra:

- **Function Definition**: Functions are defined with `define` and explicit return types (`Void` in this case - the programming equivalent of a black hole that swallows return values)
- **Output**: The `show()` function displays text (because `print` was too mainstream and `cout` was too C++)
- **Syntax**: Clean, readable syntax with colons and indentation (Python developers will feel right at home, C++ developers might need therapy)

## Key Features

### Native AI/ML Keywords

Umbra includes built-in language constructs for machine learning (*because who doesn't want to train neural networks as easily as making a sandwich?*):

```umbra
define train_model() -> Void:
    show("Loading dataset... (please wait while we teach the computer)")
    let dataset: Dataset := load_dataset("data/training.csv")

    show("Creating model... (assembling digital brain)")
    let model: Model := create_model("neural_network")

    show("Training model... (time for coffee!)")
    train model using dataset:
        epochs := 100
        learning_rate := 0.001
        batch_size := 32
        optimizer := "adam"

    show("Model trained! (It's now smarter than my toaster)")
```

### Advanced Type System

Umbra features a sophisticated type system with generics, traits, and powerful inference:

```umbra
trait Drawable {
    void draw(self)
}

struct Circle {
    Float radius
    Point center
}

impl Drawable for Circle {
    define draw(self) -> Void:
        // Drawing implementation
}

define process_shapes<T: Drawable>(shapes: Array<T>) -> Void:
    for shape in shapes:
        shape.draw()
```

### Memory Management

Umbra uses an ownership system similar to Rust but with ergonomic improvements:

```umbra
struct Buffer {
    data: Array<Byte>
    capacity: Integer
}

define process_data(buffer: Buffer) -> Buffer:
    // Ownership is moved into the function
    let mut processed: Buffer := buffer
    processed.data.reverse()
    return processed  // Ownership is moved back to caller
```

### Concurrent Programming

First-class support for async/await and parallel processing:

```umbra
async define fetch_data(url: String) -> Result<String, Error>:
    let response: Response := await http.get(url)?
    return response.text()

async define main() -> Void:
    let urls: List[String] := ["http://api1.com", "http://api2.com", "http://api3.com"]
    let results: List[Result[String, Error]] := await parallel.map(urls, fetch_data)
    
    for result in results:
        when result:
            Ok(data) => show("Success:", data),
            Err(error) => show("Error:", error)
        }
    }
}
```

## Development Environment

Umbra provides comprehensive tooling for modern development workflows:

### Compiler and Build System
- Fast incremental compilation
- Integrated package manager
- Cross-platform build support
- WebAssembly target support

### IDE Integration
- Visual Studio Code extension with full language support
- IntelliSense with type information and documentation
- Integrated debugging and profiling
- Syntax highlighting and code formatting

### Testing Framework
- Built-in unit testing framework
- Property-based testing support
- Benchmark testing capabilities
- Code coverage analysis

## Performance Characteristics

Umbra is designed for high-performance applications:

- **Zero-cost abstractions**: High-level features compile down to efficient machine code
- **LLVM backend**: Leverages decades of compiler optimization research
- **Memory efficiency**: Predictable memory usage without garbage collection pauses
- **Parallel execution**: Built-in support for multi-core and GPU computing

## Use Cases

Umbra excels in several domains:

### Machine Learning and AI
- Neural network training and inference
- Computer vision applications
- Natural language processing
- Reinforcement learning systems

### Systems Programming
- Operating system components
- Device drivers and embedded systems
- Network services and protocols
- Database engines

### Web and Network Services
- High-performance web servers
- Microservices and APIs
- Real-time communication systems
- Distributed computing platforms

### Scientific Computing
- Numerical simulations
- Data analysis pipelines
- High-performance computing applications
- Research and prototyping

## Community and Ecosystem

Umbra benefits from a growing ecosystem:

- **Package Registry**: Centralized repository for Umbra packages
- **Community Libraries**: Growing collection of third-party libraries
- **Documentation**: Comprehensive guides and API documentation
- **Support Channels**: Active community forums and chat channels

## Getting Started

To begin your journey with Umbra:

1. **Install Umbra**: Download from [umbra-lang.org](https://umbra-lang.org) or use your system's package manager
2. **Set up your editor**: Install the Umbra extension for your preferred IDE
3. **Create your first project**: Use `umbra init` to create a new project
4. **Explore examples**: Check out the examples in the standard distribution
5. **Join the community**: Connect with other Umbra developers online

In the following chapters, we'll dive deep into each aspect of the language, building your expertise from fundamental concepts to advanced AI/ML applications.

# Basic Syntax and Structure

Understanding Umbra's syntax is essential for writing effective programs. This chapter covers the fundamental building blocks of Umbra code, from basic syntax rules to program structure and organization.

## Lexical Structure

### Comments

Umbra supports both line and block comments:

```umbra
// This is a line comment

/*
 * This is a block comment
 * that can span multiple lines
 */

/// This is a documentation comment
/// used for generating API documentation
define documented_function() -> Void:
    // Implementation here
```

### Identifiers

Identifiers in Umbra follow these rules:
- Must start with a letter (a-z, A-Z) or underscore (_)
- Can contain letters, digits (0-9), and underscores
- Are case-sensitive
- Cannot be reserved keywords

```umbra
// Valid identifiers
let user_name: String := "Alice"
let totalCount: Integer := 42
let _private_var: Boolean := true
let MyClass: Type := struct { }

// Invalid identifiers
// let 123invalid = 0    // Cannot start with digit
// let class = "test"    // 'class' is a reserved keyword
```

### Keywords

Umbra has several categories of keywords:

**Core Language Keywords:**
```
void, Integer, Float, String, Boolean, Array, Map, Set
function, if, else, when, otherwise, for, while, repeat, until
break, continue, return, bring, export, using, namespace
class, struct, enum, trait, impl, public, private, protected
static, const, mut, let, var, async, await, try, catch
finally, throw, match, with
```

**AI/ML Keywords:**
```
train, evaluate, predict, visualize, dataset, model, tensor
matrix, vector, neural, layer, optimizer, loss, metric
batch, epoch, learning_rate, dropout, activation, gradient
backprop, forward, inference, pipeline, transform
```

**Memory and Concurrency Keywords:**
```
safe, unsafe, raw, ptr, ref, box, arc, rc, weak
thread, mutex, channel, actor, message, event, stream
parallel, concurrent, spawn, join, yield, select
```

## Program Structure

### Basic Program Layout

Every Umbra program follows a consistent structure:

```umbra
// 1. Module imports
bring std.io
bring std.collections.Array
bring my_module.utilities

// 2. Type definitions
struct Point {
    x: Float
    y: Float
}

enum Color {
    Red,
    Green,
    Blue,
    RGB(Integer, Integer, Integer)
}

// 3. Function definitions
define distance(p1: Point, p2: Point) -> Float:
    let dx: Float := p1.x - p2.x
    let dy: Float := p1.y - p2.y
    return sqrt(dx * dx + dy * dy)

// 4. Main function (entry point)
define main() -> Void:
    let origin: Point := Point { x: 0.0, y: 0.0 }
    let point: Point := Point { x: 3.0, y: 4.0 }

    let dist: Float := distance(origin, point)
    show("Distance:", dist)
```

### Module System

Umbra uses a hierarchical module system:

```umbra
// Importing entire modules
bring std.io
bring std.collections

// Importing specific items
bring std.math.{sin, cos, pi}
bring std.collections.{Array, Map, Set}

// Importing with aliases
bring std.collections.HashMap as Map
bring very.long.module.name as short

// Re-exporting items
export std.math.pi
export define my_utility_function() -> Void:
```

### Namespaces

Organize related functionality using namespaces:

```umbra
namespace geometry {
    struct Point {
        x: Float
        y: Float
    }

    define distance(p1: Point, p2: Point) -> Float:
        // Implementation
}

namespace graphics {
    using geometry.Point  // Import Point into this namespace

    define draw_line(start: Point, end: Point) -> Void:
        // Implementation
}

define main() -> Void:
    let p1: geometry.Point := geometry.Point { x: 0.0, y: 0.0 }
    let p2: geometry.Point := geometry.Point { x: 1.0, y: 1.0 }
    graphics.draw_line(p1, p2)
}
```

## Statements and Expressions

### Expression vs Statement

Umbra distinguishes between expressions (which produce values) and statements (which perform actions):

```umbra
// Expressions (produce values)
let x: Integer := 5 + 3           // Arithmetic expression
let y: Integer := if x > 5: 10 else: 0  // Conditional expression
let z: Integer := {               // Block expression
    let temp: Integer := x * 2
    temp + 1            // Last expression is the value
}

// Statements (perform actions)
show("Hello")           // Function call statement
x = 15                  // Assignment statement
if x > 10:              // Control flow statement
    show("Large")
```

# Data Types and Variables

Umbra features a rich type system that combines the safety of static typing with the convenience of type inference. This chapter explores all of Umbra's built-in data types, how to declare and use variables, and the principles behind Umbra's type system.

## Primitive Types

### Numeric Types

Umbra provides several numeric types with explicit sizes:

```umbra
// Integer types
let small_int: i8 = 127                    // 8-bit signed integer (-128 to 127)
let unsigned_small: u8 = 255               // 8-bit unsigned integer (0 to 255)
let normal_int: i32 = 2_147_483_647        // 32-bit signed integer (default)
let big_int: i64 = 9_223_372_036_854_775_807  // 64-bit signed integer
let huge_int: i128 = 1_000_000_000_000_000_000  // 128-bit signed integer

// Platform-dependent sizes
let pointer_size: isize = 42               // Pointer-sized signed integer
let index_size: usize = 100                // Pointer-sized unsigned integer

// Floating-point types
let single_precision: f32 = 3.14159        // 32-bit IEEE 754 float
let double_precision: f64 = 2.718281828    // 64-bit IEEE 754 float (default)

// Type aliases for convenience
let age: Integer = 25                      // Alias for i32
let height: Float = 5.8                    // Alias for f64
```

### Boolean Type

The Boolean type represents true/false values:

```umbra
let is_valid: Boolean = true
let is_empty: Boolean = false

// Boolean operations
let result: Boolean := is_valid and not is_empty     // true
let another: Boolean := is_valid or is_empty         // true

// Comparison operations return Boolean
let is_adult: Boolean := age >= 18                   // Boolean
let is_tall: Boolean := height > 6.0                 // Boolean
```

### Character and String Types

Umbra distinguishes between individual characters and strings:

```umbra
// Character type (Unicode scalar value)
let letter: Char = 'A'
let emoji: Char = 'A'
let unicode: Char = '\u{0041}'            // Unicode escape for 'A'

// String type (UTF-8 encoded)
let name: String = "Alice"
let message: String = "Hello, World!"
let multiline: String = """
    This is a multiline string
    that preserves formatting
    and indentation.
"""

// Raw strings (no escape processing)
let regex: String = r"^\d{3}-\d{2}-\d{4}$"
let path: String = r"C:\Users\<USER>\Documents"

// String interpolation
let greeting: String := "Hello, {name}!"            // "Hello, Alice!"
let info: String := "Age: {age}, Height: {height}" // "Age: 25, Height: 5.8"
```

## Collection Types

### Arrays

Arrays are fixed-size collections of elements of the same type:

```umbra
// Array declaration and initialization
let numbers: Array<Integer> = [1, 2, 3, 4, 5]
let names = ["Alice", "Bob", "Charlie"]    // Type inferred as Array<String>
let empty: Array<Float> = []               // Empty array

// Array with repeated values
let zeros = [0; 10]                        // [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]

// Multi-dimensional arrays
let matrix: Array<Array<Integer>> = [
    [1, 2, 3],
    [4, 5, 6],
    [7, 8, 9]
]

// Array operations
let first = numbers[0]                     // Access by index: 1
let length = numbers.length()              // Get length: 5
numbers.push(6)                           // Add element (if mutable)
let last = numbers.pop()                  // Remove and return last element
```

### Dynamic Arrays (Vectors)

For resizable collections, use Vector:

```umbra
bring std.collections.Vector

let mut dynamic_list: Vector<String> = Vector.new()
dynamic_list.push("First")
dynamic_list.push("Second")
dynamic_list.push("Third")

// Vector with initial capacity
let mut optimized = Vector.with_capacity<Integer>(1000)

// Convert between Array and Vector
let array_data = [1, 2, 3, 4, 5]
let vector_data = Vector.from(array_data)
let back_to_array = vector_data.to_array()
```

### Maps (Hash Tables)

Maps store key-value pairs:

```umbra
bring std.collections.Map

// Map declaration and initialization
let mut scores: Map<String, Integer> = Map.new()
scores.insert("Alice", 95)
scores.insert("Bob", 87)
scores.insert("Charlie", 92)

// Map literal syntax
let colors = {
    "red": "#FF0000",
    "green": "#00FF00",
    "blue": "#0000FF"
}

// Map operations
let alice_score = scores.get("Alice")      // Option<Integer>
let has_bob = scores.contains_key("Bob")   // Boolean
scores.remove("Charlie")
let all_keys = scores.keys()               // Iterator over keys
let all_values = scores.values()           // Iterator over values
```

### Sets

Sets store unique values:

```umbra
bring std.collections.Set

let mut unique_numbers: Set<Integer> = Set.new()
unique_numbers.insert(1)
unique_numbers.insert(2)
unique_numbers.insert(1)  // Duplicate, won't be added

// Set literal syntax
let vowels = {"a", "e", "i", "o", "u"}

// Set operations
let contains_a = vowels.contains("a")      // true
let union_set = set1.union(set2)
let intersection = set1.intersection(set2)
let difference = set1.difference(set2)
```

## Tuples

Tuples group multiple values of potentially different types:

```umbra
// Tuple declaration
let point: (Float, Float) = (3.0, 4.0)
let person = ("Alice", 25, true)           // (String, Integer, Boolean)

// Accessing tuple elements
let x = point.0                            // 3.0
let y = point.1                            // 4.0

// Tuple destructuring
let (name, age, is_student) = person
let (x_coord, y_coord) = point

// Named tuples (similar to structs)
struct Point(Float, Float)
let origin = Point(0.0, 0.0)
```

## Optional Types

Handle potentially missing values with Option:

```umbra
bring std.option.{Option, Some, None}

// Optional values
let maybe_number: Option<Integer> = Some(42)
let empty_value: Option<String> = None

// Working with optionals
when maybe_number {
    Some(value) => show("Got value:", value),
    None => show("No value")
}

// Optional chaining and unwrapping
let result = maybe_number.unwrap_or(0)     // 42, or 0 if None
let doubled = maybe_number.map(|x| x * 2)  // Some(84) or None

// Null-safe operations
let length = optional_string?.length()     // Returns Option<usize>
```

## Result Types

Handle operations that can fail:

```umbra
bring std.result.{Result, Ok, Err}

// Function that can fail
define divide(a: Float, b: Float) -> Result<Float, String>:
    if b == 0.0:
        return Err("Division by zero")
    return Ok(a / b)

// Using Result
let calculation = divide(10.0, 2.0)
when calculation {
    Ok(value) => show("Result:", value),
    Err(error) => show("Error:", error)
}

// Error propagation with ?
define complex_calculation() -> Result<Float, String>:
    let a: Float := divide(10.0, 2.0)?                // Propagate error if any
    let b: Float := divide(a, 3.0)?
    return Ok(b * 2.0)
```

## Custom Types

### Structures

Define custom data types with structs:

```umbra
// Basic struct
struct Person {
    name: String,
    age: Integer,
    email: String
}

// Creating struct instances
let alice = Person {
    name: "Alice",
    age: 30,
    email: "<EMAIL>"
}

// Accessing struct fields
show("Name:", alice.name)
alice.age = 31  // If alice is mutable

// Struct with methods
impl Person {
    define new(name: String, age: Integer, email: String) -> Person:
        return Person { name, age, email }

    define is_adult(self) -> Boolean:
        return self.age >= 18

    define birthday(mut self) -> Void:
        self.age += 1
}

// Using struct methods
let bob = Person.new("Bob", 25, "<EMAIL>")
let adult = bob.is_adult()
bob.birthday()
```

### Enumerations

Define types with a fixed set of variants:

```umbra
// Simple enum
enum Status {
    Pending,
    InProgress,
    Completed,
    Failed
}

// Enum with associated data
enum Message {
    Text(String),
    Image(String, Integer, Integer),  // path, width, height
    Video(String, Integer),           // path, duration
    Audio(String, Float)              // path, duration
}

// Using enums
let current_status = Status.InProgress
let user_message = Message.Text("Hello, World!")

// Pattern matching with enums
when user_message {
    Message.Text(content) => show("Text:", content),
    Message.Image(path, w, h) => show("Image:", path, "(" + to_string(w) + "x" + to_string(h) + ")"),
    Message.Video(path, duration) => show("Video:", path, "(" + to_string(duration) + "s)"),
    Message.Audio(path, duration) => show("Audio:", path, "(" + to_string(duration) + "s)")
}
```

## Type Inference

Umbra's type inference reduces the need for explicit type annotations:

```umbra
// Type inference in action
let number = 42                    // Inferred as Integer
let pi = 3.14159                   // Inferred as Float
let message = "Hello"              // Inferred as String
let items = [1, 2, 3]             // Inferred as Array<Integer>

// Function return type inference
define add(a: Integer, b: Integer) -> Integer:  // Return type inferred as Integer
    return a + b

// Generic type inference
let mut list = Vector.new()        // Type unknown until first use
list.push(42)                      // Now inferred as Vector<Integer>

// Complex type inference
let data = {
    "users": [
        {"name": "Alice", "age": 30},
        {"name": "Bob", "age": 25}
    ],
    "count": 2
}
// Inferred as Map<String, Array<Map<String, Integer>>>
```

## Type Aliases

Create aliases for complex types:

```umbra
// Simple type aliases
type UserId = Integer
type EmailAddress = String
type Timestamp = Integer

// Generic type aliases
type Result<T> = Result<T, String>
type HashMap<K, V> = Map<K, V>

// Function type aliases
type BinaryOperation = (Integer, Integer) -> Integer
type Predicate<T> = (T) -> Boolean

// Using type aliases
let user_id: UserId = 12345
let email: EmailAddress = "<EMAIL>"

define is_positive(x: Integer) -> Boolean:
    return x > 0
let check: Predicate<Integer> = is_positive
```

This chapter has covered Umbra's comprehensive type system. In the next chapter, we'll explore string operations and manipulation, which are fundamental to most programming tasks.

\newpage

# Part V: Database Programming

Database programming is a critical skill in modern software development, and Umbra provides first-class support for database operations through native language constructs and a comprehensive standard library. This part of the textbook covers everything from basic database connections to advanced ORM features and performance optimization.

## Why Database Programming Matters

In today's data-driven world, most applications need to store, retrieve, and manipulate data persistently. Whether you're building a web application, a mobile app, or an enterprise system, understanding how to work with databases effectively is essential. Umbra's approach to database programming combines:

- **Type Safety**: Database operations are checked at compile time
- **Performance**: Direct compilation to optimized native code
- **Security**: Built-in protection against SQL injection and other vulnerabilities
- **Productivity**: High-level abstractions that don't sacrifice control
- **Flexibility**: Support for multiple database systems and paradigms

## Database Programming Paradigms in Umbra

Umbra supports multiple approaches to database programming:

1. **Raw SQL**: Direct SQL execution with parameter binding
2. **Query Builder**: Type-safe query construction
3. **ORM (Object-Relational Mapping)**: Automatic mapping between objects and database tables
4. **Schema-First**: Database schema drives application structure
5. **Code-First**: Application models drive database schema

## Database Systems Supported

Umbra provides native support for:

- **PostgreSQL**: Advanced relational database with JSON support
- **MySQL**: Popular open-source relational database
- **SQLite**: Embedded database for local storage
- **MongoDB**: Document-oriented NoSQL database
- **Redis**: In-memory data structure store

\newpage

# Introduction to Database Programming

Database programming in Umbra is designed around the principle of making database operations as natural and safe as any other language construct. Unlike many languages where database access is an afterthought, Umbra includes database keywords and constructs as first-class language features.

## Database Keywords in Umbra

Umbra includes the following database-specific keywords that are recognized by the compiler:

```umbra
// Core database keywords
database    // Database connection and management
table       // Database table operations
column      // Table column definitions
index       // Database indexing for performance
query       // SQL query execution
transaction // ACID transaction support
commit      // Transaction commit operations
rollback    // Transaction rollback operations
migrate     // Database schema migrations
schema      // Database schema management
connection  // Database connection handling
pool        // Connection pooling for scalability
```

These keywords enable Umbra to provide syntax highlighting, compile-time checking, and optimized code generation for database operations.

## Your First Database Program

Let's start with a simple example that demonstrates the basic concepts:

```umbra
define main() -> Void:
    show("=== Umbra Database Programming Demo ===")

    // Database keywords are recognized by the compiler
    show("Database keywords:")
    show("  database - Database management")
    show("  table - Table operations")
    show("  column - Column definitions")
    show("  query - SQL execution")
    show("  transaction - ACID transactions")
    show("  connection - Connection handling")
    show("  pool - Connection pooling")

    show("Database programming features:")
    show("  [[OK]] Type-safe database operations")
    show("  [[OK]] Compile-time SQL validation")
    show("  [[OK]] Automatic connection management")
    show("  [[OK]] Built-in security features")
    show("  [[OK]] High-performance connection pooling")

    show("Ready for database-driven applications!")
```

## Database Programming Concepts

### Connection Management

Database connections are expensive resources that need careful management. Umbra provides several approaches:

```umbra
// Simple connection (for learning/prototyping)
let connection: Connection := database.connect("postgresql://localhost:5432/mydb")

// Production connection with configuration
let config: DatabaseConfig := DatabaseConfig {
    host: "localhost",
    port: 5432,
    database: "production_db",
    username: "app_user",
    password: "secure_password",
    ssl_mode: "require",
    timeout: 30
}

let connection: Connection := database.connect_with_config(config)
```

### Query Execution

Umbra supports multiple ways to execute database queries:

```umbra
// Raw SQL with parameter binding
let user_id: Integer := 123
let result: QueryResult := query("SELECT * FROM users WHERE id = ?") {
    connection: connection,
    params: [user_id]
}

// Type-safe query builder
let users: List[User] := User.query(connection)
    .where("active", "=", true)
    .where("age", ">=", 18)
    .order_by("name", "ASC")
    .limit(10)
    .execute()
```

### Transaction Management

ACID transactions ensure data consistency:

```umbra
transaction {
    connection: connection,

    // Multiple operations in a single transaction
    let user: User := create_user("Alice", "<EMAIL>")
    let profile: Profile := create_profile(user.id, "Software Engineer")

    // Automatic commit if no errors occur
    commit()
}
```

## Database Error Handling

Umbra provides comprehensive error handling for database operations:

```umbra
// Database-specific error types
enum DatabaseError {
    ConnectionError(String),
    QueryError(String),
    TransactionError(String),
    MigrationError(String)
}

// Error handling in practice
let result: Result[User, DatabaseError] := try {
    User.find(connection, user_id)
} catch {
    DatabaseError.ConnectionError(msg) => {
        show("Connection failed:", msg)
        return Err(DatabaseError.ConnectionError(msg))
    },
    DatabaseError.QueryError(msg) => {
        show("Query failed:", msg)
        return Err(DatabaseError.QueryError(msg))
    }
}
```

## Database Schema Definition

Umbra supports defining database schemas directly in code:

```umbra
// Define a table structure
structure User:
    id: Integer
    username: String
    email: String
    created_at: DateTime
    is_active: Boolean

// Database annotations (conceptual - full implementation in progress)
// @table("users")
// @primary_key("id")
// @unique("username", "email")
// @index("created_at")
```

## Performance Considerations

Database performance is critical for application success:

```umbra
// Connection pooling for high-performance applications
let pool: ConnectionPool := database.create_pool(config) {
    max_connections: 20,
    min_connections: 5,
    connection_timeout: 30,
    idle_timeout: 300
}

// Efficient batch operations
let users: List[User] := [
    User { name: "Alice", email: "<EMAIL>" },
    User { name: "Bob", email: "<EMAIL>" },
    User { name: "Charlie", email: "<EMAIL>" }
]

User.batch_insert(connection, users)
```

## Security Best Practices

Umbra enforces security best practices by default:

```umbra
// Automatic parameter binding prevents SQL injection
let search_term: String := user_input  // Could be malicious
let results: List[User] := query("SELECT * FROM users WHERE name LIKE ?") {
    connection: connection,
    params: ["%" + search_term + "%"]  // Safely parameterized
}

// Connection encryption
let secure_config: DatabaseConfig := DatabaseConfig {
    host: "database.example.com",
    ssl_mode: "require",
    ssl_cert: "/path/to/client.crt",
    ssl_key: "/path/to/client.key",
    ssl_ca: "/path/to/ca.crt"
}
```

This introduction provides the foundation for database programming in Umbra. The following chapters will dive deep into each aspect, from connection management to advanced ORM features.

\newpage

# Database Connection Management

Effective database connection management is crucial for building scalable and reliable applications. This chapter covers everything from basic connections to advanced connection pooling strategies in Umbra.

## Understanding Database Connections

A database connection represents a communication channel between your application and the database server. Connections are:

- **Expensive to create**: Establishing a connection involves network handshakes, authentication, and resource allocation
- **Limited in number**: Database servers can only handle a finite number of concurrent connections
- **Stateful**: Connections maintain session state, transaction context, and prepared statements
- **Resource-intensive**: Each connection consumes memory and system resources

## Basic Connection Management

### Simple Connection Creation

For learning and simple applications, Umbra provides straightforward connection creation:

```umbra
define basic_connection_example() -> Void:
    show("=== Basic Database Connection ===")

    // Simple connection string
    let db_url: String := "postgresql://localhost:5432/myapp"

    // Simulate connection creation
    show("Connecting to database:", db_url)
    show("Connection established successfully!")

    // Database operations would go here
    show("Executing queries...")
    show("Processing results...")

    // Connection cleanup
    show("Closing connection...")
    show("Connection closed successfully!")
```

### Connection Configuration

For production applications, use detailed configuration:

```umbra
structure DatabaseConfig:
    host: String
    port: Integer
    database: String
    username: String
    password: String
    ssl_mode: String
    connection_timeout: Integer
    query_timeout: Integer
    max_retries: Integer

define production_connection_example() -> Void:
    show("=== Production Database Connection ===")

    let config: DatabaseConfig := DatabaseConfig {
        host: "db.production.com",
        port: 5432,
        database: "production_app",
        username: "app_user",
        password: "secure_password_123",
        ssl_mode: "require",
        connection_timeout: 30,
        query_timeout: 60,
        max_retries: 3
    }

    show("Database configuration:")
    show("  Host:", config.host)
    show("  Port:", config.port)
    show("  Database:", config.database)
    show("  SSL Mode:", config.ssl_mode)
    show("  Connection Timeout:", config.connection_timeout, "seconds")

    // Connection with configuration
    show("Establishing secure connection...")
    show("Connection established with SSL encryption!")
```

## Connection Pooling

Connection pooling is essential for high-performance applications that need to handle many concurrent database operations.

### Why Connection Pooling?

Connection pooling provides several benefits:

1. **Performance**: Reuse existing connections instead of creating new ones
2. **Resource Management**: Limit the number of concurrent connections
3. **Scalability**: Handle more concurrent users with fewer resources
4. **Reliability**: Automatic connection recovery and health checking

### Basic Connection Pool

```umbra
structure PoolConfig:
    max_connections: Integer
    min_connections: Integer
    connection_timeout: Integer
    idle_timeout: Integer
    max_lifetime: Integer

define connection_pool_example() -> Void:
    show("=== Connection Pool Management ===")

    let pool_config: PoolConfig := PoolConfig {
        max_connections: 20,
        min_connections: 5,
        connection_timeout: 30,
        idle_timeout: 300,
        max_lifetime: 3600
    }

    show("Connection pool configuration:")
    show("  Max connections:", pool_config.max_connections)
    show("  Min connections:", pool_config.min_connections)
    show("  Connection timeout:", pool_config.connection_timeout, "seconds")
    show("  Idle timeout:", pool_config.idle_timeout, "seconds")
    show("  Max lifetime:", pool_config.max_lifetime, "seconds")

    // Simulate pool operations
    show("Creating connection pool...")
    show("Pool created successfully!")

    show("Getting connection from pool...")
    show("Connection acquired (Pool size: 19 available)")

    show("Executing database operations...")
    show("Operations completed successfully!")

    show("Returning connection to pool...")
    show("Connection returned (Pool size: 20 available)")
```

### Advanced Pool Management

```umbra
define advanced_pool_example() -> Void:
    show("=== Advanced Connection Pool Features ===")

    // Pool monitoring
    show("Pool Statistics:")
    show("  Active connections: 15")
    show("  Idle connections: 5")
    show("  Total connections: 20")
    show("  Pending requests: 3")
    show("  Connection failures: 0")

    // Pool health checking
    show("Pool Health Check:")
    show("  [OK] All connections healthy")
    show("  [OK] Response time < 100ms")
    show("  [OK] No connection leaks detected")
    show("  [OK] Pool within capacity limits")

    // Pool scaling
    show("Pool Scaling Events:")
    show("  -> Scaling up: High demand detected")
    show("  -> Added 5 new connections")
    show("  -> Current pool size: 25")
    show("  -> Scaling down: Low demand detected")
    show("  -> Removed 3 idle connections")
    show("  -> Current pool size: 22")
```

## Connection Security

### SSL/TLS Encryption

Secure connections are essential for production applications:

```umbra
structure SSLConfig:
    ssl_mode: String
    ssl_cert_path: String
    ssl_key_path: String
    ssl_ca_path: String
    verify_server_cert: Boolean

define secure_connection_example() -> Void:
    show("=== Secure Database Connections ===")

    let ssl_config: SSLConfig := SSLConfig {
        ssl_mode: "require",
        ssl_cert_path: "/etc/ssl/certs/client.crt",
        ssl_key_path: "/etc/ssl/private/client.key",
        ssl_ca_path: "/etc/ssl/certs/ca.crt",
        verify_server_cert: true
    }

    show("SSL Configuration:")
    show("  SSL Mode:", ssl_config.ssl_mode)
    show("  Client Certificate:", ssl_config.ssl_cert_path)
    show("  Private Key:", ssl_config.ssl_key_path)
    show("  CA Certificate:", ssl_config.ssl_ca_path)
    show("  Verify Server:", ssl_config.verify_server_cert)

    show("Establishing secure connection...")
    show("[OK] SSL handshake completed")
    show("[OK] Certificate verification passed")
    show("[OK] Encrypted connection established")
```

### Authentication Methods

```umbra
enum AuthMethod {
    Password(String),
    Certificate(String, String),
    Kerberos(String),
    LDAP(String, String),
    OAuth(String)
}

define authentication_example() -> Void:
    show("=== Database Authentication Methods ===")

    // Password authentication
    let password_auth: AuthMethod := AuthMethod.Password("secure_password")
    show("Password Authentication: Configured")

    // Certificate authentication
    let cert_auth: AuthMethod := AuthMethod.Certificate(
        "/path/to/client.crt",
        "/path/to/client.key"
    )
    show("Certificate Authentication: Configured")

    // OAuth authentication
    let oauth_auth: AuthMethod := AuthMethod.OAuth("oauth_token_xyz")
    show("OAuth Authentication: Configured")

    show("Authentication methods available for different security requirements")
```

## Connection Error Handling

### Connection Failures

Robust applications must handle connection failures gracefully:

```umbra
enum ConnectionError {
    TimeoutError(String),
    AuthenticationError(String),
    NetworkError(String),
    SSLError(String),
    ConfigurationError(String)
}

define connection_error_handling() -> Void:
    show("=== Connection Error Handling ===")

    // Simulate different error scenarios
    show("Testing error scenarios:")

    // Timeout error
    show("1. Connection Timeout:")
    show("   Error: Connection timeout after 30 seconds")
    show("   Recovery: Retry with exponential backoff")
    show("   Status: [OK] Recovered after 2 retries")

    // Authentication error
    show("2. Authentication Failure:")
    show("   Error: Invalid username or password")
    show("   Recovery: Check credentials and retry")
    show("   Status: [FAIL] Manual intervention required")

    // Network error
    show("3. Network Connectivity:")
    show("   Error: Network unreachable")
    show("   Recovery: Switch to backup server")
    show("   Status: [OK] Connected to backup server")

    // SSL error
    show("4. SSL Certificate:")
    show("   Error: Certificate verification failed")
    show("   Recovery: Update certificate store")
    show("   Status: [OK] Certificate updated and verified")
```

### Retry Strategies

```umbra
structure RetryConfig:
    max_retries: Integer
    initial_delay: Integer
    max_delay: Integer
    backoff_multiplier: Float
    jitter: Boolean

define retry_strategy_example() -> Void:
    show("=== Connection Retry Strategies ===")

    let retry_config: RetryConfig := RetryConfig {
        max_retries: 5,
        initial_delay: 1000,
        max_delay: 30000,
        backoff_multiplier: 2.0,
        jitter: true
    }

    show("Retry Configuration:")
    show("  Max retries:", retry_config.max_retries)
    show("  Initial delay:", retry_config.initial_delay, "ms")
    show("  Max delay:", retry_config.max_delay, "ms")
    show("  Backoff multiplier:", retry_config.backoff_multiplier)
    show("  Jitter enabled:", retry_config.jitter)

    // Simulate retry sequence
    show("Retry Sequence:")
    show("  Attempt 1: Failed (delay: 1000ms)")
    show("  Attempt 2: Failed (delay: 2000ms)")
    show("  Attempt 3: Failed (delay: 4000ms)")
    show("  Attempt 4: Success!")
    show("  Total time: 7.2 seconds")
```

## Connection Monitoring and Metrics

### Performance Monitoring

```umbra
structure ConnectionMetrics:
    total_connections: Integer
    active_connections: Integer
    idle_connections: Integer
    failed_connections: Integer
    average_response_time: Float
    peak_connections: Integer
    connection_errors: Integer

define connection_monitoring_example() -> Void:
    show("=== Connection Monitoring ===")

    let metrics: ConnectionMetrics := ConnectionMetrics {
        total_connections: 1250,
        active_connections: 18,
        idle_connections: 7,
        failed_connections: 3,
        average_response_time: 45.2,
        peak_connections: 25,
        connection_errors: 12
    }

    show("Connection Metrics (Last 24 hours):")
    show("  Total connections created:", metrics.total_connections)
    show("  Currently active:", metrics.active_connections)
    show("  Currently idle:", metrics.idle_connections)
    show("  Failed connections:", metrics.failed_connections)
    show("  Average response time:", metrics.average_response_time, "ms")
    show("  Peak concurrent connections:", metrics.peak_connections)
    show("  Connection errors:", metrics.connection_errors)

    // Health assessment
    let health_score: Float := 95.2
    show("Overall connection health:", health_score, "%")

    when health_score >= 95.0:
        show("Status: [OK] Excellent")
    otherwise:
        show("Status: [WARNING] Needs attention")
```

## Best Practices for Connection Management

### Development Environment

```umbra
define development_best_practices() -> Void:
    show("=== Development Best Practices ===")

    show("1. Connection Configuration:")
    show("   [OK] Use environment variables for credentials")
    show("   [OK] Keep connection timeouts reasonable (30s)")
    show("   [OK] Enable detailed logging for debugging")
    show("   [OK] Use local database for development")

    show("2. Error Handling:")
    show("   [OK] Always handle connection failures")
    show("   [OK] Implement proper retry logic")
    show("   [OK] Log connection events for debugging")
    show("   [OK] Provide meaningful error messages")

    show("3. Resource Management:")
    show("   [OK] Always close connections when done")
    show("   [OK] Use connection pooling for multiple operations")
    show("   [OK] Monitor connection usage")
    show("   [OK] Test connection limits")
```

### Production Environment

```umbra
define production_best_practices() -> Void:
    show("=== Production Best Practices ===")

    show("1. Security:")
    show("   [OK] Always use SSL/TLS encryption")
    show("   [OK] Implement proper authentication")
    show("   [OK] Use connection string encryption")
    show("   [OK] Regular security audits")

    show("2. Performance:")
    show("   [OK] Optimize connection pool size")
    show("   [OK] Monitor connection metrics")
    show("   [OK] Implement connection health checks")
    show("   [OK] Use read replicas for scaling")

    show("3. Reliability:")
    show("   [OK] Implement circuit breaker pattern")
    show("   [OK] Set up database failover")
    show("   [OK] Monitor connection pool health")
    show("   [OK] Plan for disaster recovery")

    show("4. Monitoring:")
    show("   [OK] Track connection pool metrics")
    show("   [OK] Set up alerting for failures")
    show("   [OK] Monitor query performance")
    show("   [OK] Regular capacity planning")
```

This chapter has covered the fundamentals of database connection management in Umbra. The next chapter will explore SQL integration and query execution, building on these connection management concepts.

\newpage

# SQL Integration and Query Execution

SQL (Structured Query Language) is the standard language for relational database operations. Umbra provides comprehensive support for SQL through multiple approaches: raw SQL execution, parameterized queries, and type-safe query builders. This chapter covers all aspects of SQL integration in Umbra.

## Understanding SQL in Umbra

Umbra treats SQL as a first-class citizen in the language, providing:

- **Compile-time SQL validation**: Catch SQL errors before runtime
- **Parameter binding**: Automatic protection against SQL injection
- **Type safety**: Ensure query results match expected types
- **Performance optimization**: Prepared statement caching and query optimization
- **Multi-database support**: Write portable SQL that works across database systems

## Raw SQL Execution

### Basic Query Execution

The most direct way to execute SQL in Umbra is through raw SQL strings:

```umbra
define basic_sql_example() -> Void:
    show("=== Basic SQL Execution ===")

    // Simple SELECT query
    let sql_query: String := "SELECT id, name, email FROM users WHERE active = true"

    show("Executing SQL query:")
    show("  Query:", sql_query)
    show("  Result: 25 rows returned")
    show("  Execution time: 45ms")

    // Simulate processing results
    show("Processing query results:")
    show("  User 1: Alice (<EMAIL>)")
    show("  User 2: Bob (<EMAIL>)")
    show("  User 3: Charlie (<EMAIL>)")
    show("  ... (22 more users)")
```

### Parameterized Queries

Parameterized queries are essential for security and performance:

```umbra
define parameterized_query_example() -> Void:
    show("=== Parameterized Queries ===")

    // Query with parameters
    let user_id: Integer := 123
    let status: String := "active"

    let sql: String := "SELECT * FROM users WHERE id = ? AND status = ?"

    show("Parameterized query:")
    show("  SQL:", sql)
    show("  Parameters: [", user_id, ",", status, "]")

    // Simulate query execution
    show("Executing parameterized query...")
    show("[OK] Parameters safely bound")
    show("[OK] SQL injection protection active")
    show("[OK] Query executed successfully")
    show("Result: 1 row returned")

    // Named parameters (alternative syntax)
    let named_sql: String := "SELECT * FROM orders WHERE user_id = :user_id AND created_at > :start_date"
    show("Named parameter query:", named_sql)
    show("Parameters: user_id=123, start_date='2024-01-01'")
```

### Query Result Handling

```umbra
structure QueryResult:
    rows: List[Map[String, String]]
    affected_rows: Integer
    execution_time: Float
    columns: List[String]

define query_result_example() -> Void:
    show("=== Query Result Handling ===")

    // Simulate query result
    let result: QueryResult := QueryResult {
        rows: [
            {"id": "1", "name": "Alice", "email": "<EMAIL>"},
            {"id": "2", "name": "Bob", "email": "<EMAIL>"},
            {"id": "3", "name": "Charlie", "email": "<EMAIL>"}
        ],
        affected_rows: 3,
        execution_time: 42.5,
        columns: ["id", "name", "email"]
    }

    show("Query Result:")
    show("  Rows returned:", result.affected_rows)
    show("  Execution time:", result.execution_time, "ms")
    show("  Columns:", result.columns)

    // Process results
    show("Processing results:")
    repeat i in 0..result.affected_rows:
        let row: Map[String, String] := result.rows[i]
        show("  Row", i + 1, ":", row["name"], "(", row["email"], ")")
```

## Query Builder Pattern

The query builder provides a type-safe, fluent interface for constructing SQL queries:

### Basic Query Building

```umbra
define query_builder_example() -> Void:
    show("=== Query Builder Pattern ===")

    // Simulate query builder usage
    show("Building SELECT query:")
    show("  .select(['name', 'email', 'created_at'])")
    show("  .from('users')")
    show("  .where('active', '=', true)")
    show("  .where('age', '>=', 18)")
    show("  .order_by('name', 'ASC')")
    show("  .limit(10)")

    let generated_sql: String := "SELECT name, email, created_at FROM users WHERE active = ? AND age >= ? ORDER BY name ASC LIMIT 10"
    show("Generated SQL:", generated_sql)
    show("Parameters: [true, 18]")

    show("Query builder benefits:")
    show("  [OK] Type safety at compile time")
    show("  [OK] Automatic parameter binding")
    show("  [OK] SQL injection prevention")
    show("  [OK] Database-agnostic queries")
    show("  [OK] IDE autocompletion support")
```

### Complex Query Building

```umbra
define complex_query_builder_example() -> Void:
    show("=== Complex Query Builder ===")

    // JOIN queries
    show("Building JOIN query:")
    show("  .select(['u.name', 'p.title', 'c.name as category'])")
    show("  .from('users u')")
    show("  .join('posts p', 'u.id', '=', 'p.user_id')")
    show("  .join('categories c', 'p.category_id', '=', 'c.id')")
    show("  .where('u.active', '=', true)")
    show("  .where('p.published', '=', true)")
    show("  .group_by(['u.id', 'u.name'])")
    show("  .having('COUNT(p.id)', '>', 5)")
    show("  .order_by('u.name', 'ASC')")

    let complex_sql: String := """
        SELECT u.name, p.title, c.name as category
        FROM users u
        JOIN posts p ON u.id = p.user_id
        JOIN categories c ON p.category_id = c.id
        WHERE u.active = ? AND p.published = ?
        GROUP BY u.id, u.name
        HAVING COUNT(p.id) > ?
        ORDER BY u.name ASC
    """

    show("Generated SQL:", complex_sql)
    show("Parameters: [true, true, 5]")
```

### Subqueries and CTEs

```umbra
define advanced_query_builder_example() -> Void:
    show("=== Advanced Query Builder ===")

    // Subquery example
    show("Building subquery:")
    show("  Main query: SELECT * FROM users")
    show("  Subquery: WHERE id IN (SELECT user_id FROM orders WHERE total > 1000)")

    let subquery_sql: String := "SELECT * FROM users WHERE id IN (SELECT user_id FROM orders WHERE total > ?)"
    show("Generated SQL:", subquery_sql)
    show("Parameters: [1000]")

    // Common Table Expression (CTE)
    show("Building CTE query:")
    show("  WITH active_users AS (SELECT * FROM users WHERE active = true)")
    show("  SELECT au.name, COUNT(o.id) as order_count")
    show("  FROM active_users au")
    show("  LEFT JOIN orders o ON au.id = o.user_id")
    show("  GROUP BY au.id, au.name")

    let cte_sql: String := """
        WITH active_users AS (
            SELECT * FROM users WHERE active = ?
        )
        SELECT au.name, COUNT(o.id) as order_count
        FROM active_users au
        LEFT JOIN orders o ON au.id = o.user_id
        GROUP BY au.id, au.name
    """

    show("Generated CTE SQL:", cte_sql)
    show("Parameters: [true]")
```

## Data Manipulation Operations

### INSERT Operations

```umbra
define insert_operations_example() -> Void:
    show("=== INSERT Operations ===")

    // Single row insert
    show("Single row INSERT:")
    let insert_sql: String := "INSERT INTO users (name, email, created_at) VALUES (?, ?, ?)"
    show("  SQL:", insert_sql)
    show("  Parameters: ['Alice', '<EMAIL>', '2024-01-15 10:30:00']")
    show("  Result: 1 row inserted, ID: 123")

    // Multiple row insert
    show("Batch INSERT:")
    let batch_sql: String := "INSERT INTO users (name, email) VALUES (?, ?), (?, ?), (?, ?)"
    show("  SQL:", batch_sql)
    show("  Parameters: ['Bob', '<EMAIL>', 'Charlie', '<EMAIL>', 'David', '<EMAIL>']")
    show("  Result: 3 rows inserted")

    // INSERT with SELECT
    show("INSERT with SELECT:")
    let insert_select_sql: String := "INSERT INTO user_backup SELECT * FROM users WHERE created_at < ?"
    show("  SQL:", insert_select_sql)
    show("  Parameters: ['2023-01-01']")
    show("  Result: 150 rows copied to backup")
```

### UPDATE Operations

```umbra
define update_operations_example() -> Void:
    show("=== UPDATE Operations ===")

    // Simple update
    show("Simple UPDATE:")
    let update_sql: String := "UPDATE users SET email = ?, updated_at = ? WHERE id = ?"
    show("  SQL:", update_sql)
    show("  Parameters: ['<EMAIL>', '2024-01-15 11:00:00', 123]")
    show("  Result: 1 row updated")

    // Conditional update
    show("Conditional UPDATE:")
    let conditional_sql: String := "UPDATE users SET status = ? WHERE last_login < ? AND status = ?"
    show("  SQL:", conditional_sql)
    show("  Parameters: ['inactive', '2023-01-01', 'active']")
    show("  Result: 25 rows updated")

    // UPDATE with JOIN
    show("UPDATE with JOIN:")
    let join_update_sql: String := """
        UPDATE users u
        SET premium = true
        FROM orders o
        WHERE u.id = o.user_id
        AND o.total > ?
    """
    show("  SQL:", join_update_sql)
    show("  Parameters: [1000]")
    show("  Result: 12 users upgraded to premium")
```

### DELETE Operations

```umbra
define delete_operations_example() -> Void:
    show("=== DELETE Operations ===")

    // Simple delete
    show("Simple DELETE:")
    let delete_sql: String := "DELETE FROM users WHERE id = ?"
    show("  SQL:", delete_sql)
    show("  Parameters: [123]")
    show("  Result: 1 row deleted")

    // Conditional delete
    show("Conditional DELETE:")
    let conditional_delete_sql: String := "DELETE FROM users WHERE status = ? AND last_login < ?"
    show("  SQL:", conditional_delete_sql)
    show("  Parameters: ['inactive', '2022-01-01']")
    show("  Result: 45 rows deleted")

    // DELETE with JOIN
    show("DELETE with JOIN:")
    let join_delete_sql: String := """
        DELETE u FROM users u
        LEFT JOIN orders o ON u.id = o.user_id
        WHERE o.user_id IS NULL
        AND u.created_at < ?
    """
    show("  SQL:", join_delete_sql)
    show("  Parameters: ['2023-01-01']")
    show("  Result: 8 users without orders deleted")
```

## Query Performance and Optimization

### Query Execution Plans

```umbra
define query_performance_example() -> Void:
    show("=== Query Performance Analysis ===")

    // Execution plan analysis
    show("Query execution plan:")
    show("  1. Index Scan on users_email_idx (cost=0.42..8.44)")
    show("  2. Nested Loop (cost=0.42..16.88)")
    show("  3. Index Scan on orders_user_id_idx (cost=0.29..8.31)")
    show("  Total cost: 25.19, Rows: 150, Time: 2.3ms")

    // Performance metrics
    show("Performance metrics:")
    show("  Query execution time: 2.3ms")
    show("  Rows examined: 1,250")
    show("  Rows returned: 150")
    show("  Index usage: 2 indexes used")
    show("  Memory usage: 64KB")

    // Optimization suggestions
    show("Optimization suggestions:")
    show("  [OK] Indexes are being used effectively")
    show("  [OK] Query selectivity is good (12%)")
    show("  -> Consider adding composite index on (status, created_at)")
    show("  -> Query is well-optimized")
```

### Prepared Statements

```umbra
define prepared_statements_example() -> Void:
    show("=== Prepared Statements ===")

    // Prepared statement benefits
    show("Prepared statement benefits:")
    show("  [OK] Query parsing done once")
    show("  [OK] Execution plan cached")
    show("  [OK] Parameter binding optimized")
    show("  [OK] SQL injection prevention")
    show("  [OK] Better performance for repeated queries")

    // Prepared statement lifecycle
    show("Prepared statement lifecycle:")
    show("  1. PREPARE: Parse and optimize query")
    show("  2. BIND: Bind parameters to query")
    show("  3. EXECUTE: Execute with bound parameters")
    show("  4. DEALLOCATE: Clean up resources")

    // Performance comparison
    show("Performance comparison (1000 executions):")
    show("  Raw SQL: 2.5 seconds")
    show("  Prepared statements: 0.8 seconds")
    show("  Performance improvement: 68% faster")
```

## Error Handling and Debugging

### SQL Error Types

```umbra
enum SQLError {
    SyntaxError(String),
    ConstraintViolation(String),
    DataTypeError(String),
    PermissionError(String),
    ConnectionError(String)
}

define sql_error_handling_example() -> Void:
    show("=== SQL Error Handling ===")

    // Syntax errors
    show("1. Syntax Error:")
    show("   SQL: SELECT * FORM users")  // Typo: FORM instead of FROM
    show("   Error: Syntax error near 'FORM'")
    show("   Solution: Check SQL syntax")

    // Constraint violations
    show("2. Constraint Violation:")
    show("   SQL: INSERT INTO users (email) VALUES ('<EMAIL>')")
    show("   Error: Duplicate key violates unique constraint 'users_email_key'")
    show("   Solution: Check for existing records before insert")

    // Data type errors
    show("3. Data Type Error:")
    show("   SQL: INSERT INTO users (age) VALUES ('not_a_number')")
    show("   Error: Invalid input syntax for integer")
    show("   Solution: Validate data types before query execution")

    // Permission errors
    show("4. Permission Error:")
    show("   SQL: DROP TABLE users")
    show("   Error: Permission denied for table users")
    show("   Solution: Check user permissions and privileges")
```

### Query Debugging

```umbra
define query_debugging_example() -> Void:
    show("=== Query Debugging ===")

    // Query logging
    show("Query debugging features:")
    show("  [OK] SQL query logging")
    show("  [OK] Parameter value logging")
    show("  [OK] Execution time tracking")
    show("  [OK] Error stack traces")
    show("  [OK] Query plan analysis")

    // Debug output example
    show("Debug output:")
    show("  [DEBUG] Executing query: SELECT * FROM users WHERE id = ?")
    show("  [DEBUG] Parameters: [123]")
    show("  [DEBUG] Execution time: 15ms")
    show("  [DEBUG] Rows returned: 1")
    show("  [DEBUG] Memory used: 2KB")

    // Performance profiling
    show("Performance profiling:")
    show("  Query parsing: 2ms")
    show("  Parameter binding: 1ms")
    show("  Database execution: 10ms")
    show("  Result processing: 2ms")
    show("  Total time: 15ms")
```

This chapter has covered SQL integration and query execution in Umbra. The next chapter will explore Object-Relational Mapping (ORM) features that provide higher-level abstractions for database operations.

\newpage

# Object-Relational Mapping (ORM)

Object-Relational Mapping (ORM) bridges the gap between object-oriented programming and relational databases. Umbra's ORM provides a powerful, type-safe way to work with databases using familiar object-oriented patterns while maintaining the performance and flexibility of direct SQL access.

## Understanding ORM in Umbra

Umbra's ORM is designed with the following principles:

- **Type Safety**: All database operations are checked at compile time
- **Performance**: Zero-cost abstractions that compile to efficient SQL
- **Flexibility**: Easy fallback to raw SQL when needed
- **Simplicity**: Intuitive API that follows Umbra's design philosophy
- **Transparency**: Clear mapping between objects and database tables

## Defining Models

### Basic Model Definition

Models in Umbra are defined using structures with optional database annotations:

```umbra
// Basic user model
structure User:
    id: Integer
    username: String
    email: String
    created_at: DateTime
    is_active: Boolean

define user_model_example() -> Void:
    show("=== Basic User Model ===")

    // Create a new user instance
    let user: User := User {
        id: 0,  // Will be auto-generated
        username: "alice_smith",
        email: "<EMAIL>",
        created_at: DateTime.now(),
        is_active: true
    }

    show("User model:")
    show("  ID:", user.id)
    show("  Username:", user.username)
    show("  Email:", user.email)
    show("  Active:", user.is_active)

    show("Model represents database table 'users'")
    show("Fields map to database columns automatically")
```

### Advanced Model Features

```umbra
// Advanced model with relationships
structure Post:
    id: Integer
    title: String
    content: String
    user_id: Integer  // Foreign key
    category_id: Integer
    published_at: DateTime
    view_count: Integer
    tags: List[String]

structure Category:
    id: Integer
    name: String
    description: String
    parent_id: Integer  // Self-referencing foreign key

define advanced_model_example() -> Void:
    show("=== Advanced Model Features ===")

    // Create category
    let category: Category := Category {
        id: 1,
        name: "Technology",
        description: "Posts about technology and programming",
        parent_id: 0  // Root category
    }

    // Create post
    let post: Post := Post {
        id: 1,
        title: "Introduction to Umbra Database Programming",
        content: "This post covers the basics of database programming in Umbra...",
        user_id: 1,
        category_id: category.id,
        published_at: DateTime.now(),
        view_count: 0,
        tags: ["umbra", "database", "programming", "tutorial"]
    }

    show("Post model:")
    show("  Title:", post.title)
    show("  Category ID:", post.category_id)
    show("  Tags:", post.tags)
    show("  Published:", post.published_at)

    show("Advanced features:")
    show("  [OK] Foreign key relationships")
    show("  [OK] Complex data types (List, DateTime)")
    show("  [OK] Self-referencing relationships")
    show("  [OK] Automatic field mapping")
```

## CRUD Operations

### Create Operations

```umbra
define create_operations_example() -> Void:
    show("=== Create Operations ===")

    // Single record creation
    let new_user: User := User {
        id: 0,
        username: "john_doe",
        email: "<EMAIL>",
        created_at: DateTime.now(),
        is_active: true
    }

    show("Creating new user:")
    show("  Username:", new_user.username)
    show("  Email:", new_user.email)

    // Simulate save operation
    show("Saving to database...")
    show("[OK] User created with ID: 42")
    show("[OK] Auto-generated fields populated")
    show("[OK] Timestamps set automatically")

    // Batch creation
    let users: List[User] := [
        User { id: 0, username: "alice", email: "<EMAIL>", created_at: DateTime.now(), is_active: true },
        User { id: 0, username: "bob", email: "<EMAIL>", created_at: DateTime.now(), is_active: true },
        User { id: 0, username: "charlie", email: "<EMAIL>", created_at: DateTime.now(), is_active: true }
    ]

    show("Batch creating 3 users...")
    show("[OK] All users created successfully")
    show("[OK] IDs: [43, 44, 45]")
```

### Read Operations

```umbra
define read_operations_example() -> Void:
    show("=== Read Operations ===")

    // Find by primary key
    let user_id: Integer := 42
    show("Finding user by ID:", user_id)
    show("[OK] User found: john_doe (<EMAIL>)")

    // Find by field
    let username: String := "alice"
    show("Finding user by username:", username)
    show("[OK] User found: alice (<EMAIL>)")

    // Find multiple records
    show("Finding all active users:")
    show("[OK] Found 25 active users")
    show("  - alice (<EMAIL>)")
    show("  - bob (<EMAIL>)")
    show("  - charlie (<EMAIL>)")
    show("  ... (22 more users)")

    // Find with conditions
    show("Finding users created in last 30 days:")
    show("[OK] Found 8 recent users")

    // Find with ordering
    show("Finding users ordered by username:")
    show("[OK] Users retrieved in alphabetical order")

    // Find with pagination
    show("Finding users (page 2, 10 per page):")
    show("[OK] Retrieved users 11-20 of 25 total")
```

### Update Operations

```umbra
define update_operations_example() -> Void:
    show("=== Update Operations ===")

    // Single field update
    let user_id: Integer := 42
    show("Updating user email (ID:", user_id, ")")
    show("  Old email: <EMAIL>")
    show("  New email: <EMAIL>")
    show("[OK] Email updated successfully")

    // Multiple field update
    show("Updating user profile:")
    show("  Username: john_doe -> john_doe_senior")
    show("  Status: active -> premium")
    show("  Updated timestamp: 2024-01-15 14:30:00")
    show("[OK] Profile updated successfully")

    // Conditional update
    show("Updating inactive users:")
    show("  Condition: last_login < 2023-01-01")
    show("  Action: Set status to 'archived'")
    show("[OK] 12 users archived")

    // Bulk update
    show("Bulk updating user preferences:")
    show("  Condition: created_at > 2024-01-01")
    show("  Action: Set newsletter_enabled = true")
    show("[OK] 15 users updated")
```

### Delete Operations

```umbra
define delete_operations_example() -> Void:
    show("=== Delete Operations ===")

    // Soft delete (recommended)
    let user_id: Integer := 42
    show("Soft deleting user (ID:", user_id, ")")
    show("  Setting is_active = false")
    show("  Setting deleted_at = 2024-01-15 14:45:00")
    show("[OK] User soft deleted (data preserved)")

    // Hard delete (permanent)
    show("Hard deleting test user:")
    show("  [WARNING] Warning: This action is irreversible")
    show("  Deleting user ID: 999")
    show("[OK] User permanently deleted")

    // Conditional delete
    show("Deleting old inactive users:")
    show("  Condition: is_active = false AND deleted_at < 2023-01-01")
    show("  Found: 5 users matching criteria")
    show("[OK] 5 old inactive users deleted")

    // Cascade delete
    show("Deleting user with related data:")
    show("  User: alice (ID: 43)")
    show("  Related posts: 12")
    show("  Related comments: 45")
    show("  Action: Cascade delete enabled")
    show("[OK] User and all related data deleted")
```

## Relationships and Associations

### One-to-Many Relationships

```umbra
define one_to_many_example() -> Void:
    show("=== One-to-Many Relationships ===")

    // User has many posts
    show("User -> Posts relationship:")
    show("  User: alice (ID: 1)")
    show("  Posts: 12 posts found")
    show("    1. 'Getting Started with Umbra'")
    show("    2. 'Advanced Database Techniques'")
    show("    3. 'Performance Optimization Tips'")
    show("    ... (9 more posts)")

    // Category has many posts
    show("Category -> Posts relationship:")
    show("  Category: Technology (ID: 1)")
    show("  Posts: 45 posts in this category")

    // Accessing related data
    show("Loading user with posts:")
    show("  Query: User.find(1).with_posts()")
    show("  Result: User object with posts collection")
    show("  Performance: Single query with JOIN")
```

### Many-to-Many Relationships

```umbra
define many_to_many_example() -> Void:
    show("=== Many-to-Many Relationships ===")

    // Posts and tags (many-to-many)
    show("Posts <-> Tags relationship:")
    show("  Post: 'Introduction to Umbra'")
    show("  Tags: ['umbra', 'tutorial', 'beginner', 'programming']")

    show("  Tag: 'programming'")
    show("  Posts: 23 posts with this tag")

    // Users and roles (many-to-many)
    show("Users <-> Roles relationship:")
    show("  User: alice")
    show("  Roles: ['admin', 'moderator', 'author']")

    show("  Role: 'admin'")
    show("  Users: 3 users with admin role")

    // Junction table handling
    show("Junction table management:")
    show("  Table: post_tags")
    show("  Columns: post_id, tag_id, created_at")
    show("  Automatic management by ORM")
```

### One-to-One Relationships

```umbra
define one_to_one_example() -> Void:
    show("=== One-to-One Relationships ===")

    // User profile (one-to-one)
    show("User <-> Profile relationship:")
    show("  User: alice (ID: 1)")
    show("  Profile: UserProfile (ID: 1)")
    show("    Bio: 'Software engineer passionate about databases'")
    show("    Avatar: 'https://example.com/avatars/alice.jpg'")
    show("    Location: 'San Francisco, CA'")

    // Account settings (one-to-one)
    show("User <-> Settings relationship:")
    show("  User: alice")
    show("  Settings: UserSettings")
    show("    Theme: 'dark'")
    show("    Language: 'en'")
    show("    Notifications: true")

    show("One-to-one benefits:")
    show("  [OK] Data normalization")
    show("  [OK] Optional related data")
    show("  [OK] Performance optimization")
```

## Query Methods and Finders

### Basic Finders

```umbra
define basic_finders_example() -> Void:
    show("=== Basic Finder Methods ===")

    // Find by ID
    show("User.find(42):")
    show("  Result: User object or error if not found")

    // Find by field
    show("User.find_by_email('<EMAIL>'):")
    show("  Result: User object or None")

    // Find all
    show("User.all():")
    show("  Result: List of all users")

    // Find first
    show("User.first():")
    show("  Result: First user (ordered by ID)")

    // Find last
    show("User.last():")
    show("  Result: Last user (ordered by ID)")

    // Count
    show("User.count():")
    show("  Result: Total number of users (Integer)")

    // Exists
    show("User.exists(42):")
    show("  Result: Boolean indicating if user exists")
```

### Advanced Queries

```umbra
define advanced_queries_example() -> Void:
    show("=== Advanced Query Methods ===")

    // Where clauses
    show("User.where('active', '=', true):")
    show("  SQL: SELECT * FROM users WHERE active = ?")
    show("  Result: List of active users")

    // Multiple conditions
    show("User.where('active', '=', true).where('age', '>=', 18):")
    show("  SQL: SELECT * FROM users WHERE active = ? AND age >= ?")
    show("  Result: List of active adult users")

    // OR conditions
    show("User.where('role', '=', 'admin').or_where('role', '=', 'moderator'):")
    show("  SQL: SELECT * FROM users WHERE role = ? OR role = ?")
    show("  Result: List of admin or moderator users")

    // IN clauses
    show("User.where_in('id', [1, 2, 3, 4, 5]):")
    show("  SQL: SELECT * FROM users WHERE id IN (?, ?, ?, ?, ?)")
    show("  Result: Users with specified IDs")

    // LIKE patterns
    show("User.where_like('email', '%@gmail.com'):")
    show("  SQL: SELECT * FROM users WHERE email LIKE ?")
    show("  Result: Users with Gmail addresses")

    // Date ranges
    show("User.where_between('created_at', '2024-01-01', '2024-01-31'):")
    show("  SQL: SELECT * FROM users WHERE created_at BETWEEN ? AND ?")
    show("  Result: Users created in January 2024")
```

### Ordering and Limiting

```umbra
define ordering_limiting_example() -> Void:
    show("=== Ordering and Limiting ===")

    // Order by single field
    show("User.order_by('username', 'ASC'):")
    show("  SQL: SELECT * FROM users ORDER BY username ASC")
    show("  Result: Users ordered alphabetically")

    // Order by multiple fields
    show("User.order_by('status', 'DESC').order_by('created_at', 'ASC'):")
    show("  SQL: SELECT * FROM users ORDER BY status DESC, created_at ASC")
    show("  Result: Users ordered by status, then by creation date")

    // Limit results
    show("User.limit(10):")
    show("  SQL: SELECT * FROM users LIMIT 10")
    show("  Result: First 10 users")

    // Offset and limit (pagination)
    show("User.offset(20).limit(10):")
    show("  SQL: SELECT * FROM users LIMIT 10 OFFSET 20")
    show("  Result: Users 21-30 (page 3 of 10 per page)")

    // Take (alias for limit)
    show("User.take(5):")
    show("  SQL: SELECT * FROM users LIMIT 5")
    show("  Result: First 5 users")
```

## Aggregations and Grouping

### Basic Aggregations

```umbra
define aggregations_example() -> Void:
    show("=== Aggregation Functions ===")

    // Count
    show("User.count():")
    show("  SQL: SELECT COUNT(*) FROM users")
    show("  Result: 1,250 users")

    // Count with condition
    show("User.where('active', '=', true).count():")
    show("  SQL: SELECT COUNT(*) FROM users WHERE active = ?")
    show("  Result: 1,180 active users")

    // Average
    show("Order.average('total'):")
    show("  SQL: SELECT AVG(total) FROM orders")
    show("  Result: $156.75 average order value")

    // Sum
    show("Order.sum('total'):")
    show("  SQL: SELECT SUM(total) FROM orders")
    show("  Result: $45,230.50 total revenue")

    // Min and Max
    show("Order.minimum('total'):")
    show("  SQL: SELECT MIN(total) FROM orders")
    show("  Result: $5.99 minimum order")

    show("Order.maximum('total'):")
    show("  SQL: SELECT MAX(total) FROM orders")
    show("  Result: $1,299.99 maximum order")
```

### Grouping and Having

```umbra
define grouping_example() -> Void:
    show("=== Grouping and Having ===")

    // Group by single field
    show("Order.group_by('status').count():")
    show("  SQL: SELECT status, COUNT(*) FROM orders GROUP BY status")
    show("  Result:")
    show("    pending: 45")
    show("    completed: 1,205")
    show("    cancelled: 23")

    // Group by multiple fields
    show("Order.group_by('status', 'payment_method').sum('total'):")
    show("  SQL: SELECT status, payment_method, SUM(total) FROM orders GROUP BY status, payment_method")
    show("  Result:")
    show("    completed, credit_card: $35,420.50")
    show("    completed, paypal: $8,910.25")
    show("    pending, credit_card: $1,250.75")

    // Having clause
    show("User.group_by('status').having('COUNT(*)', '>', 100):")
    show("  SQL: SELECT status, COUNT(*) FROM users GROUP BY status HAVING COUNT(*) > ?")
    show("  Result:")
    show("    active: 1,180")
    show("    inactive: 70")

    // Complex grouping
    show("Order.group_by('DATE(created_at)').sum('total'):")
    show("  SQL: SELECT DATE(created_at), SUM(total) FROM orders GROUP BY DATE(created_at)")
    show("  Result: Daily revenue totals")
```

This chapter has covered the fundamentals of Object-Relational Mapping in Umbra. The next chapter will explore database transactions and ACID properties, which are crucial for maintaining data consistency in multi-user applications.

\newpage

# Database Transactions and ACID Properties

Database transactions are fundamental to maintaining data integrity in multi-user applications. This chapter covers transaction management in Umbra, including ACID properties, isolation levels, and advanced transaction patterns.

## Understanding Transactions

A database transaction is a sequence of operations performed as a single logical unit of work. Transactions ensure that either all operations succeed (commit) or all operations are undone (rollback), maintaining database consistency.

### ACID Properties

ACID is an acronym that describes the key properties of database transactions:

- **Atomicity**: All operations in a transaction succeed or fail together
- **Consistency**: Transactions maintain database integrity constraints
- **Isolation**: Concurrent transactions don't interfere with each other
- **Durability**: Committed changes persist even after system failures

## Basic Transaction Management

### Simple Transactions

```umbra
define basic_transaction_example() -> Void:
    show("=== Basic Transaction Management ===")

    // Simple transaction structure
    show("Starting transaction...")
    show("  BEGIN TRANSACTION")

    // Database operations
    show("Executing operations:")
    show("  1. INSERT INTO users (name, email) VALUES ('Alice', '<EMAIL>')")
    show("  2. INSERT INTO profiles (user_id, bio) VALUES (LAST_INSERT_ID(), 'Software Engineer')")
    show("  3. UPDATE counters SET user_count = user_count + 1")

    // Transaction completion
    show("Committing transaction...")
    show("  COMMIT")
    show("[OK] All operations completed successfully")
    show("[OK] Changes are now permanent")

    // Transaction benefits
    show("Transaction benefits:")
    show("  [OK] Atomicity: All operations succeed together")
    show("  [OK] Consistency: Database constraints maintained")
    show("  [OK] Isolation: No interference from other transactions")
    show("  [OK] Durability: Changes survive system restart")
```

### Transaction Rollback

```umbra
define transaction_rollback_example() -> Void:
    show("=== Transaction Rollback ===")

    show("Starting transaction...")
    show("  BEGIN TRANSACTION")

    // Successful operations
    show("Executing operations:")
    show("  1. [OK] INSERT INTO orders (user_id, total) VALUES (123, 99.99)")
    show("  2. [OK] UPDATE inventory SET quantity = quantity - 1 WHERE product_id = 456")
    show("  3. [FAIL] INSERT INTO payments (order_id, amount) VALUES (789, 99.99)")
    show("     Error: Payment processing failed")

    // Rollback due to error
    show("Rolling back transaction...")
    show("  ROLLBACK")
    show("[OK] All operations undone")
    show("[OK] Database returned to previous state")
    show("[OK] No partial changes committed")

    // Error handling benefits
    show("Rollback benefits:")
    show("  [OK] Data integrity maintained")
    show("  [OK] No orphaned records")
    show("  [OK] Consistent error recovery")
    show("  [OK] Automatic cleanup")
```

## Transaction Syntax in Umbra

### Transaction Blocks

```umbra
define transaction_syntax_example() -> Void:
    show("=== Transaction Syntax in Umbra ===")

    // Basic transaction block
    show("Basic transaction syntax:")
    show("  transaction {")
    show("      connection: db_connection,")
    show("      // database operations here")
    show("      commit()")
    show("  }")

    // Transaction with error handling
    show("Transaction with error handling:")
    show("  transaction {")
    show("      connection: db_connection,")
    show("      try {")
    show("          // risky operations")
    show("          commit()")
    show("      } catch {")
    show("          rollback('Operation failed')")
    show("      }")
    show("  }")

    // Automatic transaction management
    show("Automatic transaction (recommended):")
    show("  transaction {")
    show("      connection: db_connection,")
    show("      // operations here")
    show("      // automatic commit if no errors")
    show("      // automatic rollback on errors")
    show("  }")
```

### Manual Transaction Control

```umbra
define manual_transaction_example() -> Void:
    show("=== Manual Transaction Control ===")

    // Manual begin/commit/rollback
    show("Manual transaction control:")
    show("  let transaction_id: String := begin_transaction(connection)")
    show("  try {")
    show("      // perform operations")
    show("      commit_transaction(connection, transaction_id)")
    show("  } catch {")
    show("      rollback_transaction(connection, transaction_id)")
    show("  }")

    // Transaction state checking
    show("Transaction state management:")
    show("  let is_in_transaction: Boolean := connection.in_transaction()")
    show("  let transaction_status: String := connection.transaction_status()")
    show("  let transaction_id: String := connection.current_transaction_id()")

    show("Manual control benefits:")
    show("  [OK] Fine-grained control")
    show("  [OK] Complex transaction logic")
    show("  [OK] Custom error handling")
    show("  [OK] Transaction state inspection")
```

## Savepoints and Nested Transactions

### Understanding Savepoints

Savepoints allow you to create checkpoints within a transaction that you can rollback to without affecting the entire transaction.

```umbra
define savepoints_example() -> Void:
    show("=== Savepoints and Nested Transactions ===")

    show("Transaction with savepoints:")
    show("  BEGIN TRANSACTION")

    // First set of operations
    show("  1. INSERT INTO users (name) VALUES ('Alice')")
    show("  2. SAVEPOINT user_created")

    // Second set of operations
    show("  3. INSERT INTO profiles (user_id, bio) VALUES (1, 'Engineer')")
    show("  4. SAVEPOINT profile_created")

    // Third set of operations (fails)
    show("  5. INSERT INTO invalid_table (data) VALUES ('test')")
    show("     Error: Table does not exist")

    // Rollback to savepoint
    show("  6. ROLLBACK TO SAVEPOINT profile_created")
    show("     [OK] Profile insertion undone")
    show("     [OK] User insertion preserved")

    // Continue with transaction
    show("  7. INSERT INTO audit_log (action) VALUES ('user_created')")
    show("  8. COMMIT")

    show("Final result:")
    show("  [OK] User created")
    show("  [FAIL] Profile not created (rolled back)")
    show("  [OK] Audit log entry created")
```

### Nested Transaction Patterns

```umbra
define nested_transactions_example() -> Void:
    show("=== Nested Transaction Patterns ===")

    // Outer transaction
    show("Outer transaction:")
    show("  BEGIN TRANSACTION  -- Level 1")

    // Inner transaction (savepoint)
    show("  Inner operation block:")
    show("    SAVEPOINT inner_operation  -- Level 2")
    show("    INSERT INTO orders (user_id, total) VALUES (123, 99.99)")
    show("    INSERT INTO order_items (order_id, product_id) VALUES (456, 789)")

    // Conditional rollback
    show("    Validation check:")
    show("    IF inventory_check_fails THEN")
    show("      ROLLBACK TO SAVEPOINT inner_operation")
    show("      -- Order creation undone, continue with outer transaction")
    show("    ELSE")
    show("      RELEASE SAVEPOINT inner_operation")
    show("      -- Savepoint no longer needed")
    show("    END IF")

    show("  COMMIT  -- Level 1")

    show("Nested transaction benefits:")
    show("  [OK] Partial rollback capability")
    show("  [OK] Complex business logic support")
    show("  [OK] Error isolation")
    show("  [OK] Performance optimization")
```

## Isolation Levels

### Understanding Isolation

Transaction isolation determines how changes made by one transaction are visible to other concurrent transactions.

```umbra
enum IsolationLevel {
    ReadUncommitted,
    ReadCommitted,
    RepeatableRead,
    Serializable
}

define isolation_levels_example() -> Void:
    show("=== Transaction Isolation Levels ===")

    // Read Uncommitted
    show("1. READ UNCOMMITTED:")
    show("   - Lowest isolation level")
    show("   - Can read uncommitted changes (dirty reads)")
    show("   - Highest performance, lowest consistency")
    show("   - Use case: Analytics on non-critical data")

    // Read Committed
    show("2. READ COMMITTED:")
    show("   - Default for most databases")
    show("   - Can only read committed changes")
    show("   - Prevents dirty reads")
    show("   - Use case: Most web applications")

    // Repeatable Read
    show("3. REPEATABLE READ:")
    show("   - Same data read multiple times in transaction")
    show("   - Prevents dirty reads and non-repeatable reads")
    show("   - May still allow phantom reads")
    show("   - Use case: Financial calculations")

    // Serializable
    show("4. SERIALIZABLE:")
    show("   - Highest isolation level")
    show("   - Complete isolation from other transactions")
    show("   - Prevents all read phenomena")
    show("   - Use case: Critical financial operations")
```

### Setting Isolation Levels

```umbra
define isolation_level_usage_example() -> Void:
    show("=== Setting Isolation Levels ===")

    // Set isolation level for transaction
    show("Setting isolation level:")
    show("  SET TRANSACTION ISOLATION LEVEL READ COMMITTED")
    show("  BEGIN TRANSACTION")
    show("  -- operations with READ COMMITTED isolation")
    show("  COMMIT")

    // Different isolation levels for different operations
    show("Operation-specific isolation:")
    show("  -- Financial transaction (high isolation)")
    show("  SET TRANSACTION ISOLATION LEVEL SERIALIZABLE")
    show("  BEGIN TRANSACTION")
    show("  UPDATE accounts SET balance = balance - 100 WHERE id = 1")
    show("  UPDATE accounts SET balance = balance + 100 WHERE id = 2")
    show("  COMMIT")

    show("  -- Analytics query (low isolation)")
    show("  SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED")
    show("  SELECT COUNT(*) FROM orders WHERE created_at > '2024-01-01'")

    show("Isolation level trade-offs:")
    show("  Higher isolation -> Better consistency, lower performance")
    show("  Lower isolation -> Better performance, potential consistency issues")
```

## Concurrency and Locking

### Lock Types

```umbra
define locking_example() -> Void:
    show("=== Database Locking Mechanisms ===")

    // Shared locks (read locks)
    show("1. Shared Locks (Read Locks):")
    show("   - Multiple transactions can hold shared locks")
    show("   - Prevents data modification during read")
    show("   - Example: SELECT ... FOR SHARE")

    // Exclusive locks (write locks)
    show("2. Exclusive Locks (Write Locks):")
    show("   - Only one transaction can hold exclusive lock")
    show("   - Prevents all other access to data")
    show("   - Example: SELECT ... FOR UPDATE")

    // Row-level locking
    show("3. Row-Level Locking:")
    show("   - Locks individual rows")
    show("   - High concurrency")
    show("   - Example: UPDATE users SET email = ? WHERE id = ?")

    // Table-level locking
    show("4. Table-Level Locking:")
    show("   - Locks entire table")
    show("   - Low concurrency, simple implementation")
    show("   - Example: LOCK TABLE users IN EXCLUSIVE MODE")

    show("Locking best practices:")
    show("  [OK] Use row-level locks when possible")
    show("  [OK] Keep lock duration minimal")
    show("  [OK] Acquire locks in consistent order")
    show("  [OK] Handle deadlocks gracefully")
```

### Deadlock Detection and Prevention

```umbra
define deadlock_example() -> Void:
    show("=== Deadlock Detection and Prevention ===")

    // Deadlock scenario
    show("Deadlock scenario:")
    show("  Transaction A:")
    show("    1. LOCK row 1")
    show("    2. Wait for lock on row 2")

    show("  Transaction B:")
    show("    1. LOCK row 2")
    show("    2. Wait for lock on row 1")

    show("  Result: Deadlock! Both transactions wait forever")

    // Deadlock detection
    show("Deadlock detection:")
    show("  [OK] Database monitors lock dependencies")
    show("  [OK] Detects circular wait conditions")
    show("  [OK] Automatically rolls back one transaction")
    show("  [OK] Allows other transaction to proceed")

    // Deadlock prevention strategies
    show("Prevention strategies:")
    show("  1. Consistent lock ordering:")
    show("     - Always acquire locks in same order")
    show("     - Example: Lock by primary key order")

    show("  2. Timeout-based approach:")
    show("     - Set maximum wait time for locks")
    show("     - Rollback if timeout exceeded")

    show("  3. Optimistic locking:")
    show("     - Use version numbers or timestamps")
    show("     - Check for conflicts before commit")

    show("  4. Minimize transaction scope:")
    show("     - Keep transactions short")
    show("     - Release locks quickly")
```

## Performance Considerations

### Transaction Performance

```umbra
define transaction_performance_example() -> Void:
    show("=== Transaction Performance Optimization ===")

    // Transaction size optimization
    show("Transaction size optimization:")
    show("  Small transactions:")
    show("    [OK] Lower lock contention")
    show("    [OK] Faster rollback if needed")
    show("    [OK] Better concurrency")
    show("    [FAIL] Higher overhead per operation")

    show("  Large transactions:")
    show("    [OK] Lower per-operation overhead")
    show("    [OK] Better for bulk operations")
    show("    [FAIL] Higher lock contention")
    show("    [FAIL] Longer rollback time")

    // Batch processing
    show("Batch processing strategies:")
    show("  1. Fixed-size batches:")
    show("     - Process 1000 records per transaction")
    show("     - Predictable memory usage")
    show("     - Good for large datasets")

    show("  2. Time-based batches:")
    show("     - Process for 5 seconds per transaction")
    show("     - Adaptive to system performance")
    show("     - Good for real-time systems")

    show("  3. Hybrid approach:")
    show("     - Combine size and time limits")
    show("     - Maximum flexibility")
    show("     - Best overall performance")
```

### Connection Pool and Transactions

```umbra
define connection_pool_transactions_example() -> Void:
    show("=== Connection Pool and Transactions ===")

    // Transaction-aware pooling
    show("Transaction-aware connection pooling:")
    show("  [OK] Connections reserved during transactions")
    show("  [OK] Automatic cleanup on transaction end")
    show("  [OK] Deadlock detection across pool")
    show("  [OK] Transaction timeout handling")

    // Pool configuration for transactions
    show("Pool configuration:")
    show("  max_connections: 20")
    show("  transaction_timeout: 30 seconds")
    show("  deadlock_timeout: 5 seconds")
    show("  max_transaction_duration: 300 seconds")

    // Transaction monitoring
    show("Transaction monitoring:")
    show("  Active transactions: 5")
    show("  Average transaction duration: 150ms")
    show("  Deadlocks detected: 0")
    show("  Timeouts: 0")
    show("  Pool utilization: 25%")

    show("Best practices:")
    show("  [OK] Monitor transaction duration")
    show("  [OK] Set appropriate timeouts")
    show("  [OK] Handle connection failures")
    show("  [OK] Use connection pooling")
```

## Error Handling and Recovery

### Transaction Error Patterns

```umbra
enum TransactionError {
    DeadlockDetected(String),
    TimeoutExpired(String),
    ConstraintViolation(String),
    ConnectionLost(String),
    SerializationFailure(String)
}

define transaction_error_handling_example() -> Void:
    show("=== Transaction Error Handling ===")

    // Deadlock handling
    show("1. Deadlock Error:")
    show("   Error: Deadlock detected between transactions")
    show("   Recovery: Automatic retry with exponential backoff")
    show("   Status: [OK] Resolved after 2 retries")

    // Timeout handling
    show("2. Transaction Timeout:")
    show("   Error: Transaction exceeded 30 second limit")
    show("   Recovery: Rollback and log for investigation")
    show("   Status: [OK] Transaction rolled back safely")

    // Constraint violation
    show("3. Constraint Violation:")
    show("   Error: Unique constraint violation on email field")
    show("   Recovery: Return user-friendly error message")
    show("   Status: [OK] User notified to use different email")

    // Connection loss
    show("4. Connection Lost:")
    show("   Error: Database connection lost during transaction")
    show("   Recovery: Reconnect and check transaction status")
    show("   Status: [OK] Transaction was rolled back automatically")

    // Serialization failure
    show("5. Serialization Failure:")
    show("   Error: Concurrent modification detected")
    show("   Recovery: Retry with fresh data")
    show("   Status: [OK] Retry successful")
```

This chapter has covered database transactions and ACID properties in Umbra. The next chapter will explore database schema management and migrations, which are essential for evolving database structures over time.

# String Operations and Manipulation

Strings are one of the most commonly used data types in programming, and Umbra provides a comprehensive set of built-in functions and methods for string manipulation. This chapter covers all string operations available in Umbra, from basic concatenation to advanced text processing.

## Basic String Operations

### String Creation and Literals

Umbra supports various ways to create and work with strings:

```umbra
define main() -> Void:
    // Basic string literals
    let greeting: String := "Hello, World!"
    let name: String := "Umbra"
    let empty: String := ""

    // String with escape sequences
    let multiline: String := "Line 1\nLine 2\nLine 3"
    let quoted: String := "She said, \"Hello!\""
    let path: String := "C:\\Users\\<USER>\\file.txt"

    show("Greeting:", greeting)
    show("Name:", name)
    show("Multiline:\n" + multiline)
```

### String Concatenation

Umbra provides multiple ways to concatenate strings:

```umbra
define main() -> Void:
    let first: String := "Hello"
    let second: String := "World"

    // Basic concatenation with +
    let result1: String := first + ", " + second + "!"
    show("Concatenation:", result1)

    // Concatenating with numbers using to_string()
    let age: Integer := 25
    let message: String := "I am " + to_string(age) + " years old"
    show("With number:", message)

    // Using show() with multiple arguments (recommended)
    show("Direct output:", first, second, "- Age:", age)
```

## String Length and Properties

### Getting String Length

```umbra
define main() -> Void:
    let text: String := "Hello, Umbra!"
    let length: Integer := str_len(text)

    show("Text:", text)
    show("Length:", length)

    // Check if string is empty
    let empty_text: String := ""
    let empty_length: Integer := str_len(empty_text)
    show("Empty string length:", empty_length)

    // Unicode string length (counts characters, not bytes)
    let unicode: String := "Hello, World!"
    let unicode_length: Integer := str_len(unicode)
    show("Unicode text:", unicode)
    show("Unicode length:", unicode_length)
```

## Case Conversion

### Converting Case

```umbra
define main() -> Void:
    let original: String := "Hello, World!"

    // Convert to uppercase
    let upper: String := to_upper(original)
    show("Uppercase:", upper)

    // Convert to lowercase
    let lower: String := to_lower(original)
    show("Lowercase:", lower)

    // Mixed case example
    let mixed: String := "UmBrA PrOgRaMmInG"
    show("Original:", mixed)
    show("Upper:", to_upper(mixed))
    show("Lower:", to_lower(mixed))
```

## String Trimming and Whitespace

### Removing Whitespace

> **Note**: The `trim()` function is planned for future implementation. Currently, manual string processing is required for whitespace removal.

```umbra
define main() -> Void:
    let padded: String := "   Hello, World!   "

    // Trim whitespace from both ends (planned feature)
    // let trimmed: String := trim(padded)
    show("Original: '" + padded + "'")
    // show("Trimmed: '" + trimmed + "'")

    // For now, manual trimming would be needed
    show("Manual trimming will be implemented in future versions")
```

## String Searching and Testing

### Checking String Contents

> **Note**: String search functions like `contains()`, `starts_with()`, and `ends_with()` are planned for future implementation.

```umbra
define main() -> Void:
    let text: String := "The quick brown fox jumps over the lazy dog"

    // String search functions (planned features)
    // let has_fox: Boolean := contains(text, "fox")
    // let has_cat: Boolean := contains(text, "cat")

    show("Text:", text)
    // show("Contains 'fox':", has_fox)
    // show("Contains 'cat':", has_cat)

    // String prefix/suffix checking (planned features)
    let filename: String := "document.umbra"
    // let starts_with_doc: Boolean := starts_with(filename, "doc")
    // let ends_with_umbra: Boolean := ends_with(filename, ".umbra")

    show("Filename:", filename)
    show("String search functions will be implemented in future versions")
```

### String Comparison

```umbra
define main() -> Void:
    let str1: String := "apple"
    let str2: String := "banana"
    let str3: String := "apple"

    // String equality
    let equal1: Boolean := str1 == str3
    let equal2: Boolean := str1 == str2

    show("Comparing strings:")
    show(str1, "==", str3, ":", equal1)
    show(str1, "==", str2, ":", equal2)

    // Case-insensitive comparison
    let upper_apple: String := "APPLE"
    let case_insensitive: Boolean := to_lower(str1) == to_lower(upper_apple)
    show("Case-insensitive comparison:", case_insensitive)
```

## String Splitting and Joining

### Splitting Strings

> **Note**: The `split()` function and proper list iteration are planned for future implementation.

```umbra
define main() -> Void:
    let csv_data: String := "apple,banana,cherry,date,elderberry"
    // let fruits: List[String] := split(csv_data, ",")

    show("Original CSV:", csv_data)
    show("String splitting will be implemented in future versions")

    // Manual string processing would be needed for now
    show("For now, manual string parsing is required")
```

### Joining Strings

> **Note**: The `join()` function is planned for future implementation.

```umbra
define main() -> Void:
    // Manual string joining for now
    let word1: String := "Hello"
    let word2: String := "beautiful"
    let word3: String := "world"

    // Manual joining with space
    let sentence: String := word1 + " " + word2 + " " + word3
    show("Manually joined:", sentence)

    // Manual joining with comma
    let csv: String := word1 + "," + word2 + "," + word3
    show("CSV format:", csv)
```

## Substring Operations

### Extracting Substrings

> **Note**: The `substring()` function is planned for future implementation.

```umbra
define main() -> Void:
    let text: String := "Hello, World!"

    // Substring extraction (planned feature)
    // let hello: String := substring(text, 0, 5)
    // let world: String := substring(text, 7, 12)

    show("Original:", text)
    show("Substring extraction will be implemented in future versions")

    // For now, only basic string operations are available
    let length: Integer := str_len(text)
    show("String length:", length)
```

## Advanced String Operations

### String Replacement and Transformation

```umbra
define main() -> Void:
    let text: String := "The quick brown fox jumps over the lazy dog"

    // Note: Advanced string operations like replace() would be implemented
    // in the full standard library. For now, we show the pattern:

    show("Original text:", text)

    // Example of how replace would work (conceptual)
    // let replaced: String := replace(text, "fox", "cat")
    // show("After replacement:", replaced)

    // For now, we can demonstrate building new strings
    let words: List[String] := split(text, " ")
    let modified_words: List[String] := []

    repeat word in words:
        when word == "fox":
            // Replace "fox" with "cat"
            modified_words.push("cat")
        otherwise:
            modified_words.push(word)

    let result: String := join(modified_words, " ")
    show("Manual replacement result:", result)
```

### String Formatting and Templates

```umbra
define main() -> Void:
    let name: String := "Alice"
    let age: Integer := 30
    let city: String := "New York"

    // Manual string formatting
    let intro: String := "My name is " + name + ", I am " + to_string(age) + " years old, and I live in " + city + "."
    show("Formatted string:", intro)

    // Using show() for cleaner output
    show("Using show():", "My name is", name, ", I am", age, "years old, and I live in", city)

    // Building complex strings
    let report_lines: List[String] := [
        "=== User Report ===",
        "Name: " + name,
        "Age: " + to_string(age),
        "City: " + city,
        "=================="
    ]

    let report: String := join(report_lines, "\n")
    show("Report:\n" + report)
```

## String Validation and Utilities

### Common String Checks

```umbra
define main() -> Void:
    let empty: String := ""
    let whitespace: String := "   "
    let text: String := "Hello"

    // Check if strings are empty
    show("Empty string length:", str_len(empty))
    show("Whitespace string length:", str_len(whitespace))
    show("Text string length:", str_len(text))

    // Check if string is effectively empty after trimming
    let trimmed_whitespace: String := trim(whitespace)
    show("Trimmed whitespace length:", str_len(trimmed_whitespace))

    // Validate string content
    let email: String := "<EMAIL>"
    let has_at: Boolean := contains(email, "@")
    let has_dot: Boolean := contains(email, ".")

    show("Email:", email)
    show("Has @ symbol:", has_at)
    show("Has . symbol:", has_dot)
    show("Basic email validation:", has_at and has_dot)
```

## Best Practices for String Operations

### Performance Considerations

```umbra
define main() -> Void:
    // Efficient string building for multiple concatenations
    let parts: List[String] := ["Part", "1", "Part", "2", "Part", "3"]

    // Efficient: Use join() for multiple strings
    let efficient: String := join(parts, " - ")
    show("Efficient concatenation:", efficient)

    // Less efficient: Multiple + operations
    let less_efficient: String := parts[0] + " - " + parts[1] + " - " + parts[2] + " - " + parts[3] + " - " + parts[4] + " - " + parts[5]
    show("Less efficient result:", less_efficient)

    // For building strings in loops, collect parts first (conceptual)
    // Note: List operations and range() are not yet fully implemented
    let number_string: String := "1, 2, 3, 4, 5"  // Manual for now
    show("Numbers:", number_string)
```

### String Safety and Error Handling

```umbra
define main() -> Void:
    let text: String := "Hello, World!"
    let length: Integer := str_len(text)

    // Safe substring extraction
    when length >= 5:
        let safe_substring: String := substring(text, 0, 5)
        show("Safe substring:", safe_substring)
    otherwise:
        show("String too short for substring operation")

    // Always validate before string operations
    let user_input: String := "  user data  "
    let cleaned_input: String := trim(user_input)

    when str_len(cleaned_input) > 0:
        show("Valid input:", cleaned_input)
    otherwise:
        show("Empty input after cleaning")
```

## Summary of String Operations

### Currently Implemented Functions

The following string functions are fully implemented and tested:

- **`show()`** - Output strings and other values to console
- **`str_len(string)`** - Get the length of a string in characters
- **`to_string(value)`** - Convert any value to its string representation
- **`to_upper(string)`** - Convert string to uppercase
- **`to_lower(string)`** - Convert string to lowercase
- **String concatenation with `+`** - Join strings together
- **String literals and escape sequences** - Create strings with special characters

### Planned Functions (Future Implementation)

The following functions are designed and will be implemented in future versions:

- **`trim(string)`** - Remove whitespace from both ends
- **`contains(string, substring)`** - Check if string contains substring
- **`starts_with(string, prefix)`** - Check if string starts with prefix
- **`ends_with(string, suffix)`** - Check if string ends with suffix
- **`split(string, delimiter)`** - Split string into list of substrings
- **`join(list, separator)`** - Join list of strings with separator
- **`substring(string, start, end)`** - Extract substring by indices
- **`replace(string, old, new)`** - Replace occurrences of substring
- **Advanced string formatting and templates**

### Working with Current Implementation

For now, focus on the implemented functions and use manual string processing for advanced operations:

```umbra
define string_demo() -> Void:
    // [OK] These work now
    let name: String := "Alice"
    let age: Integer := 30
    let upper_name: String := to_upper(name)
    let message: String := "Hello, " + upper_name + "! You are " + to_string(age) + " years old."

    show("Message:", message)
    show("Length:", str_len(message))
    show("Lowercase:", to_lower(message))

    // [CONSTRUCTION] These will work in future versions
    // let trimmed: String := trim("  hello  ")
    // let words: List[String] := split(message, " ")
    // let contains_hello: Boolean := contains(message, "Hello")
```

This chapter has covered Umbra's current string manipulation capabilities and planned features. Strings are fundamental to most applications, and the implemented functions provide a solid foundation for basic text processing. Advanced string operations will be added in future releases. In the next chapter, we'll explore control flow constructs that let you direct the execution of your programs.

# Control Flow

Control flow statements determine the order in which code executes. Umbra provides a comprehensive set of control flow constructs, from traditional conditional statements to advanced pattern matching and loop constructs.

## Conditional Statements

### If Expressions

Umbra's `if` construct is an expression, meaning it returns a value:

```umbra
// Basic if expression
let age: Integer := 20
let status: String := if age >= 18: "adult" else: "minor"

// Multi-branch if
let grade: Integer := 85
let letter_grade: String := if grade >= 90:
    "A"
else if grade >= 80:
    "B"
else if grade >= 70:
    "C"
else if grade >= 60:
    "D"
else:
    "F"

// If without else (returns unit type)
if temperature > 30:
    show("It's hot today!")

// Complex conditions
if age >= 18 and has_license and not is_suspended:
    show("Can drive")
```

### When Expressions (Pattern Matching)

The `when` expression provides powerful pattern matching capabilities:

```umbra
// Basic pattern matching
enum Color {
    Red,
    Green,
    Blue,
    RGB(Integer, Integer, Integer)
}

let color = Color.RGB(255, 128, 0)
let description = when color {
    Color.Red => "Pure red",
    Color.Green => "Pure green",
    Color.Blue => "Pure blue",
    Color.RGB(r, g, b) => "RGB({}, {}, {})",
    otherwise => "Unknown color"  // Default case
}

// Matching with guards
let number = 42
let category = when number {
    x if x < 0 => "negative",
    x if x == 0 => "zero",
    x if x > 0 and x <= 10 => "small positive",
    x if x > 10 and x <= 100 => "medium positive",
    x if x > 100 => "large positive"
}

// Matching tuples
let point = (3, 4)
let quadrant = when point {
    (0, 0) => "origin",
    (x, 0) if x > 0 => "positive x-axis",
    (x, 0) if x < 0 => "negative x-axis",
    (0, y) if y > 0 => "positive y-axis",
    (0, y) if y < 0 => "negative y-axis",
    (x, y) if x > 0 and y > 0 => "first quadrant",
    (x, y) if x < 0 and y > 0 => "second quadrant",
    (x, y) if x < 0 and y < 0 => "third quadrant",
    (x, y) if x > 0 and y < 0 => "fourth quadrant"
}
```

### Matching Arrays and Collections

```umbra
// Array pattern matching
let numbers = [1, 2, 3, 4, 5]
let result = when numbers {
    [] => "empty",
    [x] => "single element: {}",
    [first, second] => "two elements: {}, {}",
    [first, ..rest] => "first: {}, rest has {} elements",
    [..init, last] => "last: {}, init has {} elements"
}

// Option pattern matching
let maybe_value: Option<Integer> = Some(42)
let unwrapped = when maybe_value {
    Some(value) => value,
    None => 0
}

// Result pattern matching
let operation_result: Result<Integer, String> = Ok(100)
when operation_result {
    Ok(value) => show("Success:", value),
    Err(error) => show("Error:", error)
}
```

## Loops

### For Loops

For loops iterate over collections and ranges:

```umbra
// Iterating over arrays
let numbers: List[Integer] := [1, 2, 3, 4, 5]
for number in numbers:
    show("Number:", number)

// Iterating over ranges
for i in 0..10:           // 0 to 9 (exclusive end)
    show("Index:", i)

for i in 0..=10:          // 0 to 10 (inclusive end)
    show("Index:", i)

// Iterating with index
for (index, value) in numbers.enumerate():
    show("Index " + to_string(index) + ":", value)

// Iterating over maps
let scores: Map[String, Integer] := {"Alice": 95, "Bob": 87, "Charlie": 92}
for (name, score) in scores:
    show(name + ":", score)

// Filtering during iteration
for number in numbers.filter(|x: Integer| -> Boolean: x % 2 == 0):
    show("Even number:", number)
```

### While Loops

While loops continue as long as a condition is true:

```umbra
// Basic while loop
let mut count: Integer := 0
while count < 10:
    show("Count:", count)
    count += 1

// While with complex condition
let mut input: String := ""
while input != "quit" and input != "exit":
    input = io.read_line("Enter command (quit to exit): ")
    process_command(input)

// Infinite loop with break
let mut running: Boolean := true
while running:
    let command: Command := get_next_command()
    when command:
        Command.Quit => running = false,
        Command.Process(data) => process_data(data),
        Command.Invalid => show("Invalid command")
```

### Repeat-Until Loops

Execute at least once, then continue while condition is false:

```umbra
// Basic repeat-until
let mut guess = 0
repeat {
    guess = input("Guess a number: ")
    if guess < target {
        show("Too low!")
    } else if guess > target {
        show("Too high!")
    }
} until guess == target

show("Correct!")
```

### Loop Control

Control loop execution with `break` and `continue`:

```umbra
// Break out of loop
for i in 0..100:
    if i * i > 50:
        break  // Exit the loop
    show("Square of " + to_string(i) + ":", i * i)

// Continue to next iteration
for i in 0..10:
    if i % 2 == 0:
        continue  // Skip even numbers
    show("Odd number:", i)

// Labeled breaks for nested loops
'outer: for i in 0..10:
    for j in 0..10:
        if i * j > 20:
            break 'outer  // Break out of outer loop
        show("i:", i, "j:", j, "product:", i * j)

// Loop expressions return values
let result: Option[Integer] := for i in 0..10:
    if i * i == 25:
        break i  // Return i from the loop
// result is Option<Integer>, Some(5) in this case
```

## Advanced Control Flow

### Exception Handling

Handle errors gracefully with try-catch:

```umbra
// Basic exception handling
try {
    let file = File.open("data.txt")?
    let content = file.read_all()?
    process_content(content)
} catch FileNotFound(path) {
    show("File not found:", path)
} catch PermissionDenied(path) {
    show("Permission denied:", path)
} catch error {
    show("Unexpected error:", error)
} finally {
    cleanup_resources()
}

// Try expressions
let result = try {
    let data = risky_operation()?
    let processed = process_data(data)?
    processed.finalize()
}
// result is Result<T, Error>
```

### Early Returns and Guards

Use guard clauses for early validation:

```umbra
define process_user(user: Option<User>) -> Result<String, Error>:
    // Guard clause - early return if user is None
    let user: User := user.ok_or("User not found")?

    // Guard clause - validate user data
    if user.age < 0:
        return Err("Invalid age")

    if user.name.is_empty():
        return Err("Name cannot be empty")

    // Main processing logic
    let processed_name: String := user.name.to_uppercase()
    return Ok("Processed user: {}".format(processed_name))
```

### Conditional Compilation

Use conditional compilation for platform-specific code:

```umbra
// Platform-specific code
#[cfg(target_os = "windows")]
define get_home_directory() -> String:
    return env.get("USERPROFILE").unwrap_or("C:\\")

#[cfg(target_os = "linux")]
define get_home_directory() -> String:
    return env.get("HOME").unwrap_or("/home")

#[cfg(target_os = "macos")]
define get_home_directory() -> String:
    return env.get("HOME").unwrap_or("/Users")

// Feature-based compilation
#[cfg(feature = "gpu")]
define accelerated_computation(data: Array<Float>) -> Array<Float>:
    return gpu.process(data)

#[cfg(not(feature = "gpu"))]
define accelerated_computation(data: Array<Float>) -> Array<Float>:
    return cpu.process(data)

// Debug vs release builds
#[cfg(debug)]
define debug_log(message: String) -> Void:
    show("[DEBUG]", message)

#[cfg(not(debug))]
define debug_log(message: String) -> Void:
    // No-op in release builds
```

This chapter covered Umbra's control flow constructs. Next, we'll explore functions and how to organize code into reusable components.

# AI/ML Programming in Umbra

One of Umbra's most distinctive features is its native support for artificial intelligence and machine learning workflows. This chapter explores how Umbra's built-in AI/ML constructs make it natural to express complex machine learning algorithms directly in the language.

## AI/ML Language Constructs

### Native Keywords

Umbra includes dedicated keywords for machine learning operations:

```umbra
bring std.ml
bring std.data

// Dataset loading and preprocessing
let dataset = load_dataset("data/iris.csv") {
    features: ["sepal_length", "sepal_width", "petal_length", "petal_width"],
    target: "species",
    split: train_test(0.8)
}

// Model definition using native syntax
let model = neural_network {
    name: "iris_classifier",
    layers: [
        dense(4, activation: none),      // Input layer
        dense(16, activation: relu),     // Hidden layer
        dropout(0.2),                    // Regularization
        dense(8, activation: relu),      // Hidden layer
        dense(3, activation: softmax)    // Output layer
    ],
    optimizer: adam(learning_rate: 0.001),
    loss: categorical_crossentropy,
    metrics: [accuracy, precision, recall]
}

// Training with native train keyword
train model using dataset.train with:
    epochs: 100
    batch_size: 32
    validation_data: dataset.test
    callbacks: [
        early_stopping(patience: 10),
        model_checkpoint("best_model.umbra"),
        reduce_lr_on_plateau(factor: 0.5)
    ]

// Evaluation using native evaluate keyword
let results = evaluate model on dataset.test
show("Test Accuracy:", results.accuracy)
show("Test Loss:", results.loss)

// Prediction using native predict keyword
let predictions = predict model on dataset.test.features
for (actual, predicted) in zip(dataset.test.targets, predictions) {
    show("Actual:", actual, "Predicted:", predicted)
}
```

### Tensor Operations

Umbra provides first-class tensor support for numerical computing:

```umbra
bring std.tensor

// Tensor creation
let matrix_a = tensor([
    [1.0, 2.0, 3.0],
    [4.0, 5.0, 6.0]
])  // Shape: [2, 3]

let matrix_b = tensor([
    [7.0, 8.0],
    [9.0, 10.0],
    [11.0, 12.0]
])  // Shape: [3, 2]

// Tensor operations
let result = matrix_a @ matrix_b        // Matrix multiplication
let element_wise = matrix_a * 2.0       // Scalar multiplication
let transposed = matrix_a.transpose()   // Transpose operation

// Advanced tensor operations
let normalized = tensor.normalize(matrix_a, axis: 1)
let reshaped = matrix_a.reshape([3, 2])
let concatenated = tensor.concat([matrix_a, matrix_b], axis: 0)

// GPU acceleration (if available)
let gpu_tensor = matrix_a.to_gpu()
let gpu_result = gpu_tensor @ matrix_b.to_gpu()
let cpu_result = gpu_result.to_cpu()
```

### Data Processing Pipeline

Umbra's pipeline syntax makes data preprocessing intuitive:

```umbra
bring std.data.pipeline

// Data preprocessing pipeline
let preprocessor = pipeline {
    load_csv("raw_data.csv") |>
    drop_columns(["id", "timestamp"]) |>
    handle_missing(strategy: mean_imputation) |>
    normalize(method: standard_scaling) |>
    encode_categorical(method: one_hot) |>
    split_features_target(target_column: "label")
}

// Apply pipeline to data
let processed_data = preprocessor.transform(raw_data)

// Feature engineering pipeline
let feature_engineer = pipeline {
    create_polynomial_features(degree: 2) |>
    select_k_best(k: 10, score_func: f_regression) |>
    pca(n_components: 5) |>
    scale(method: min_max)
}

let engineered_features = feature_engineer.fit_transform(processed_data.features)
```

## Deep Learning Models

### Convolutional Neural Networks

```umbra
bring std.ml.vision

// CNN for image classification
let cnn_model = neural_network {
    name: "image_classifier",
    input_shape: [224, 224, 3],
    layers: [
        // First convolutional block
        conv2d(32, kernel_size: 3, activation: relu),
        batch_normalization(),
        conv2d(32, kernel_size: 3, activation: relu),
        max_pooling2d(pool_size: 2),
        dropout(0.25),

        // Second convolutional block
        conv2d(64, kernel_size: 3, activation: relu),
        batch_normalization(),
        conv2d(64, kernel_size: 3, activation: relu),
        max_pooling2d(pool_size: 2),
        dropout(0.25),

        // Third convolutional block
        conv2d(128, kernel_size: 3, activation: relu),
        batch_normalization(),
        conv2d(128, kernel_size: 3, activation: relu),
        max_pooling2d(pool_size: 2),
        dropout(0.25),

        // Classifier
        flatten(),
        dense(512, activation: relu),
        dropout(0.5),
        dense(10, activation: softmax)  // 10 classes
    ],
    optimizer: adam(learning_rate: 0.001, beta1: 0.9, beta2: 0.999),
    loss: categorical_crossentropy,
    metrics: [accuracy, top_5_accuracy]
}

// Data augmentation
let augmentation = image_augmentation {
    rotation_range: 20,
    width_shift_range: 0.2,
    height_shift_range: 0.2,
    horizontal_flip: true,
    zoom_range: 0.2,
    fill_mode: nearest
}

// Training with augmented data
train cnn_model using augmented_dataset with:
    epochs: 50
    batch_size: 64
    validation_split: 0.2
    data_augmentation: augmentation
```

### Recurrent Neural Networks

```umbra
bring std.ml.sequence

// LSTM for sequence prediction
let lstm_model = neural_network {
    name: "sequence_predictor",
    layers: [
        lstm(128, return_sequences: true, dropout: 0.2),
        lstm(64, return_sequences: true, dropout: 0.2),
        lstm(32, return_sequences: false, dropout: 0.2),
        dense(16, activation: relu),
        dense(1, activation: linear)
    ],
    optimizer: rmsprop(learning_rate: 0.001),
    loss: mean_squared_error,
    metrics: [mae, mape]
}

// Time series data preparation
let time_series_data = prepare_sequences(
    data: stock_prices,
    sequence_length: 60,
    prediction_horizon: 1,
    features: ["open", "high", "low", "close", "volume"]
)

train lstm_model using time_series_data with:
    epochs: 100
    batch_size: 32
    validation_split: 0.2
    callbacks: [
        early_stopping(patience: 15, restore_best_weights: true),
        reduce_lr_on_plateau(factor: 0.5, patience: 5)
    ]
```

### Transformer Models

```umbra
bring std.ml.attention

// Transformer for natural language processing
let transformer_model = neural_network {
    name: "text_classifier",
    layers: [
        embedding(vocab_size: 10000, embedding_dim: 256),
        positional_encoding(max_length: 512),

        // Multi-head attention layers
        multi_head_attention(
            num_heads: 8,
            key_dim: 32,
            dropout: 0.1
        ),
        layer_normalization(),

        feed_forward(
            hidden_dim: 1024,
            activation: relu,
            dropout: 0.1
        ),
        layer_normalization(),

        // Classification head
        global_average_pooling1d(),
        dense(128, activation: relu),
        dropout(0.5),
        dense(2, activation: softmax)  // Binary classification
    ],
    optimizer: adam(learning_rate: 0.0001),
    loss: binary_crossentropy,
    metrics: [accuracy, f1_score]
}

// Text preprocessing
let text_processor = text_pipeline {
    tokenize(method: wordpiece, vocab_size: 10000) |>
    pad_sequences(max_length: 512, padding: post) |>
    create_attention_masks()
}

let processed_text = text_processor.transform(text_data)

train transformer_model using processed_text with:
    epochs: 10
    batch_size: 16
    validation_split: 0.2
    gradient_clipping: 1.0
```

## Reinforcement Learning

### Q-Learning Agent

```umbra
bring std.ml.reinforcement

// Q-Learning agent definition
let q_agent = reinforcement_agent {
    algorithm: q_learning,
    state_space: discrete(size: 16),
    action_space: discrete(size: 4),
    learning_rate: 0.1,
    discount_factor: 0.95,
    exploration: epsilon_greedy(
        initial_epsilon: 1.0,
        final_epsilon: 0.01,
        decay_steps: 10000
    ),
    memory: replay_buffer(capacity: 10000)
}

// Environment setup
let environment = gym_environment("FrozenLake-v1")

// Training loop
for episode in 0..1000 {
    let mut state = environment.reset()
    let mut total_reward = 0.0
    let mut done = false

    while not done:
        let action: Integer := q_agent.select_action(state)
        let (next_state, reward, done, info) := environment.step(action)

        q_agent.store_experience(state, action, reward, next_state, done)
        q_agent.learn()

        state = next_state
        total_reward += reward
    }

    if episode % 100 == 0 {
        show("Episode " + to_string(episode) + ": Total Reward =", total_reward)
    }
}
```

### Deep Q-Network (DQN)

```umbra
// DQN with neural network function approximation
let dqn_agent = reinforcement_agent {
    algorithm: deep_q_network,
    state_space: continuous(shape: [84, 84, 4]),  // Atari frames
    action_space: discrete(size: 6),

    network: neural_network {
        layers: [
            conv2d(32, kernel_size: 8, strides: 4, activation: relu),
            conv2d(64, kernel_size: 4, strides: 2, activation: relu),
            conv2d(64, kernel_size: 3, strides: 1, activation: relu),
            flatten(),
            dense(512, activation: relu),
            dense(6, activation: linear)  // Q-values for each action
        ]
    },

    target_network_update_freq: 1000,
    learning_rate: 0.00025,
    discount_factor: 0.99,
    batch_size: 32,
    memory: prioritized_replay_buffer(capacity: 100000),
    exploration: epsilon_greedy(
        initial_epsilon: 1.0,
        final_epsilon: 0.1,
        decay_steps: 100000
    )
}

// Training with experience replay
train dqn_agent in environment with:
    episodes: 10000
    max_steps_per_episode: 1000
    target_score: 200.0
    save_frequency: 1000
```

## Model Deployment and Inference

### Model Serving

```umbra
bring std.ml.serving
bring std.web

// Model serving endpoint
let model_server = web_server {
    host: "0.0.0.0",
    port: 8080,

    routes: [
        post("/predict") {
            let input_data = request.json<PredictionInput>()
            let preprocessed = preprocess(input_data)
            let prediction = predict model on preprocessed
            let response = PredictionResponse {
                prediction: prediction,
                confidence: prediction.confidence(),
                timestamp: now()
            }
            return json(response)
        },

        get("/model/info") {
            return json(ModelInfo {
                name: model.name,
                version: model.version,
                input_shape: model.input_shape,
                output_shape: model.output_shape,
                parameters: model.parameter_count()
            })
        }
    ]
}

// Start the server
model_server.start()
```

### Batch Inference

```umbra
// Batch processing for large datasets
let batch_processor = batch_inference {
    model: trained_model,
    batch_size: 1000,
    preprocessing: data_preprocessor,
    postprocessing: result_formatter,

    input_source: file_reader("large_dataset.csv"),
    output_sink: file_writer("predictions.csv"),

    parallel_workers: 4,
    gpu_acceleration: true
}

// Process the entire dataset
let results = batch_processor.process_all()
show("Processed " + to_string(results.sample_count) + " samples in " + to_string(results.duration) + "s")
```

This chapter demonstrated Umbra's powerful AI/ML capabilities. The native language support for machine learning workflows makes Umbra uniquely suited for AI development, combining the performance of systems programming with the expressiveness needed for complex ML algorithms.

# Performance Optimization

Umbra is designed for high performance, but understanding how to write efficient code and optimize performance is crucial for demanding applications. This chapter covers performance profiling, optimization techniques, and best practices.

## Performance Profiling

### Built-in Profiler

Umbra includes a comprehensive profiling system:

```umbra
bring std.profiler

// Profile a function
#[profile]
define expensive_computation(data: Array<Float>) -> Float:
    let mut result: Float := 0.0
    for value in data:
        result += value * value
    return result

// Profile a code block
let result = profile("matrix_multiplication") {
    let a = generate_matrix(1000, 1000)
    let b = generate_matrix(1000, 1000)
    return a @ b
}

// Memory profiling
let memory_stats = profile_memory {
    let large_array = Array.with_capacity<Float>(1_000_000)
    for i in 0..1_000_000 {
        large_array.push(i as Float)
    }
    return large_array
}

show("Peak memory usage:", memory_stats.peak_memory / 1024 / 1024, "MB")
```

### Performance Benchmarking

```umbra
bring std.benchmark

// Benchmark different implementations
let benchmark_results = benchmark {
    "vector_sum_loop": {
        let mut sum = 0.0
        for value in test_data {
            sum += value
        }
        return sum
    },

    "vector_sum_iterator": {
        return test_data.iter().sum()
    },

    "vector_sum_parallel": {
        return test_data.par_iter().sum()
    },

    "vector_sum_simd": {
        return simd.sum(test_data)
    }
}

for (name, result) in benchmark_results {
    show(name + ":", to_string(result.avg_time) + "ns per iteration")
}
```

## Memory Optimization

### Memory Layout Control

```umbra
// Struct layout optimization
#[repr(packed)]
struct PackedStruct {
    a: u8,
    b: u32,
    c: u16
}  // Size: 7 bytes instead of 12

#[repr(align(64))]  // Cache line alignment
struct CacheAligned {
    data: [Float; 16]
}

// Memory pool allocation
let pool = MemoryPool.new(capacity: 1024 * 1024)  // 1MB pool
let allocated = pool.allocate<MyStruct>(count: 1000)

// Stack allocation for small arrays
let stack_array = stack_array![Float; 1024]  // Allocated on stack
```

### Zero-Copy Operations

```umbra
// Zero-copy string operations
define process_substring(text: &String, start: usize, end: usize) -> &str:
    return text.slice(start, end)  // No allocation, returns view

// Memory-mapped file I/O
let mapped_file = MemoryMappedFile.open("large_data.bin")?
let data_view = mapped_file.as_slice<Float>()  // Zero-copy access

// Buffer reuse
let mut buffer = Buffer.with_capacity(4096)
loop {
    buffer.clear()  // Reuse existing capacity
    let bytes_read = socket.read_into(&mut buffer)?
    if bytes_read == 0 { break }
    process_data(&buffer[..bytes_read])
}
```

## Parallel Computing

### Data Parallelism

```umbra
bring std.parallel

// Parallel iteration
let results = data.par_iter()
    .map(|x| expensive_function(x))
    .filter(|x| x > threshold)
    .collect()

// Parallel reduction
let sum = large_array.par_iter()
    .map(|x| x * x)
    .reduce(|| 0.0, |a, b| a + b)

// Custom parallel algorithms
parallel.for_each(0..num_threads) { thread_id ->
    let start = thread_id * chunk_size
    let end = min(start + chunk_size, data.length)
    process_chunk(&data[start..end])
}
```

### GPU Computing

```umbra
bring std.gpu

// GPU kernel definition
#[gpu_kernel]
define vector_add(a: &[Float], b: &[Float], result: &mut [Float]) -> Void:
    let idx: usize := gpu.thread_id()
    if idx < result.length:
        result[idx] = a[idx] + b[idx]

// GPU memory management
let gpu_context = GpuContext.new()?
let gpu_buffer_a = gpu_context.allocate_buffer<Float>(size: 1_000_000)?
let gpu_buffer_b = gpu_context.allocate_buffer<Float>(size: 1_000_000)?
let gpu_result = gpu_context.allocate_buffer<Float>(size: 1_000_000)?

// Copy data to GPU
gpu_buffer_a.copy_from_host(&host_data_a)?
gpu_buffer_b.copy_from_host(&host_data_b)?

// Launch kernel
let grid_size = (1_000_000 + 255) / 256
let block_size = 256
gpu_context.launch_kernel(vector_add, grid_size, block_size,
                         &gpu_buffer_a, &gpu_buffer_b, &gpu_result)?

// Copy result back
let mut host_result = vec![0.0; 1_000_000]
gpu_result.copy_to_host(&mut host_result)?
```

## Compiler Optimizations

### Optimization Attributes

```umbra
// Force inlining for small, frequently called functions
#[inline(always)]
define fast_math_operation(x: Float, y: Float) -> Float:
    return x * x + y * y

// Prevent inlining for large functions
#[inline(never)]
define large_complex_function() -> Void:
    // Complex implementation

// Loop unrolling
#[unroll(4)]
for i in 0..array.length {
    process_element(array[i])
}

// Vectorization hints
#[vectorize]
for i in 0..data.length {
    result[i] = data[i] * 2.0 + 1.0
}
```

### Profile-Guided Optimization

```umbra
// Build with profiling instrumentation
// umbra build --profile-generate

// Run typical workloads to collect profile data
// ./my_program < typical_input.txt

// Rebuild with profile-guided optimization
// umbra build --profile-use

// Hot path optimization
#[hot]
define frequently_called_function() -> Void:
    // This function will be heavily optimized

#[cold]
define error_handler() -> Void:
    // This function will be optimized for size, not speed
```

# Interoperability

Umbra is designed to work seamlessly with existing codebases and libraries. This chapter covers integration with other programming languages and systems.

## Python Integration

### Calling Python from Umbra

```umbra
bring std.python

// Initialize Python interpreter
let py = Python.initialize()?

// Import Python modules
let numpy = py.import("numpy")?
let pandas = py.import("pandas")?

// Call Python functions
let array = numpy.call("array", [1, 2, 3, 4, 5])?
let result = numpy.call("sum", array)?

// Work with Python objects
let df = pandas.call("DataFrame", {
    "name": ["Alice", "Bob", "Charlie"],
    "age": [25, 30, 35],
    "city": ["New York", "London", "Tokyo"]
})?

let filtered = df.call("query", "age > 28")?
show("Filtered data:", filtered)

// Convert between Umbra and Python types
let umbra_array = [1.0, 2.0, 3.0, 4.0, 5.0]
let python_array = py.from_umbra(umbra_array)?
let processed = numpy.call("sqrt", python_array)?
let back_to_umbra = processed.to_umbra<Array<Float>>()?
```

### Exposing Umbra Functions to Python

```umbra
// Create Python module from Umbra code
#[python_module("umbra_math")]
module python_bindings {
    #[python_function]
    define fast_fibonacci(n: Integer) -> Integer:
        if n <= 1:
            return n
        let mut a: Integer := 0
        let mut b: Integer := 1
        for _ in 2..=n:
            let temp: Integer := a + b
            a = b
            b = temp
        }
        return b
    }

    #[python_class]
    struct Matrix {
        data: Array<Array<Float>>
    }

    impl Matrix {
        #[python_method]
        define new(rows: Integer, cols: Integer) -> Matrix:
            let data: Array[Array[Float]] := Array.with_capacity(rows)
            for _ in 0..rows:
                data.push(Array.with_capacity(cols))
            return Matrix { data }

        #[python_method]
        define multiply(self, other: Matrix) -> Matrix:
            // Matrix multiplication implementation
    }
}

// Build Python extension
// umbra build --target python-extension
```

## C/C++ Interoperability

### Foreign Function Interface (FFI)

```umbra
// Declare external C functions
extern "C" {
    define malloc(size: usize) -> *mut void
    define free(ptr: *mut void) -> Void
    define strlen(s: *const char) -> usize
    define printf(format: *const char, ...) -> Integer
}

// Use C functions
unsafe {
    let ptr = malloc(1024)
    if ptr.is_null() {
        panic!("Memory allocation failed")
    }

    // Use the allocated memory
    let buffer = ptr as *mut u8
    // ... work with buffer ...

    free(ptr)
}

// Wrapper for safer usage
define safe_malloc(size: usize) -> Result<*mut u8, String>:
    unsafe:
        let ptr: *mut u8 := malloc(size) as *mut u8
        if ptr.is_null():
            return Err("Memory allocation failed")
        return Ok(ptr)
```

### C++ Integration

```umbra
// Bind C++ classes
#[link(name = "mylib")]
extern "C++" {
    type CppVector;

    #[namespace = "std"]
    define vector_new() -> *mut CppVector

    #[namespace = "std"]
    define vector_push_back(vec: *mut CppVector, value: Integer) -> Void

    #[namespace = "std"]
    define vector_size(vec: *const CppVector) -> usize

    #[namespace = "std"]
    define vector_delete(vec: *mut CppVector) -> Void
}

// Safe wrapper
struct Vector {
    inner: *mut CppVector
}

impl Vector {
    define new() -> Vector:
        unsafe:
            Vector { inner: vector_new() }

    define push(mut self, value: Integer) -> Void:
        unsafe:
            vector_push_back(self.inner, value)

    define size(self) -> usize:
        unsafe:
            vector_size(self.inner)
}

impl Drop for Vector {
    define drop(mut self) -> Void:
        unsafe:
            vector_delete(self.inner)
}
```

## WebAssembly Support

### Compiling to WebAssembly

```umbra
// WebAssembly-specific attributes
#[wasm_bindgen]
define process_data(input: String) -> String:
    // Process the input data
    return input.to_uppercase()

#[wasm_bindgen]
struct Calculator {
    value: Float
}

#[wasm_bindgen]
impl Calculator {
    #[wasm_bindgen(constructor)]
    define new() -> Calculator:
        Calculator { value: 0.0 }

    #[wasm_bindgen]
    define add(mut self, x: Float) -> Float:
        self.value += x
        return self.value

    #[wasm_bindgen(getter)]
    define value(self) -> Float:
        return self.value
}

// Build for WebAssembly
// umbra build --target wasm32-unknown-unknown
```

### JavaScript Integration

```javascript
// Using Umbra WebAssembly module in JavaScript
import init, { process_data, Calculator } from './umbra_module.js';

async function main() {
    await init();

    // Use Umbra functions
    const result = process_data("hello world");
    console.log(result); // "HELLO WORLD"

    // Use Umbra classes
    const calc = new Calculator();
    calc.add(10);
    calc.add(5);
    console.log(calc.value); // 15
}

main();
```

This comprehensive guide has covered the essential aspects of Umbra programming, from basic syntax to advanced AI/ML features and performance optimization. Umbra's unique combination of systems programming capabilities with native AI/ML support makes it an ideal choice for modern high-performance applications.

\newpage

# Appendices

## Appendix A: Installation and Setup

### System Requirements

**Minimum Requirements:**
- Operating System: Windows 10+, macOS 10.12+, or Linux (kernel 3.2+)
- Architecture: x86_64 (64-bit)
- RAM: 4 GB
- Disk Space: 2 GB for compiler and standard library

**Recommended Requirements:**
- RAM: 8 GB or more
- SSD storage for faster compilation
- Multi-core processor for parallel compilation

### Installation Methods

**Package Managers:**

```bash
# Debian/Ubuntu
sudo dpkg -i umbra_1.0.1_amd64.deb

# Red Hat/CentOS/Fedora
sudo rpm -i umbra-1.0.1-1.x86_64.rpm

# Windows
# Run umbra-windows-x64-installer.exe

# macOS (when available)
# Run umbra-1.0.1-macos.pkg
```

**From Source:**

```bash
git clone https://github.com/eclipse-softworks/umbra.git
cd umbra
cargo build --release
sudo cp target/release/umbra /usr/local/bin/
```

### IDE Setup

**Visual Studio Code:**
1. Install the "Umbra Programming Language" extension (version 1.2.7+)
2. Open an Umbra project or create a new one with `umbra init`
3. The extension provides syntax highlighting, IntelliSense, and debugging support

**Other Editors:**
- Vim/Neovim: Syntax highlighting available via vim-umbra plugin
- Emacs: umbra-mode provides basic language support
- IntelliJ IDEA: Plugin in development

## Appendix B: Language Reference

### Reserved Keywords

```
async, await, break, bring, catch, class, const, continue, else, enum,
export, false, finally, for, function, if, impl, let, mut, namespace,
otherwise, private, protected, public, repeat, return, static, struct,
throw, trait, true, try, until, using, var, void, when, while
```

### AI/ML Keywords

```
activation, backprop, batch, dataset, dropout, epoch, evaluate, forward,
gradient, inference, layer, learning_rate, loss, metric, model, neural,
optimizer, pipeline, predict, tensor, train, transform, visualize
```

### Operators Precedence (Highest to Lowest)

1. `()` `[]` `.` (grouping, indexing, member access)
2. `not` `-` `~` (unary operators)
3. `**` (exponentiation)
4. `*` `/` `%` (multiplicative)
5. `+` `-` (additive)
6. `<<` `>>` (bitwise shift)
7. `&` (bitwise AND)
8. `^` (bitwise XOR)
9. `|` (bitwise OR)
10. `<` `<=` `>` `>=` `==` `!=` (comparison)
11. `and` (logical AND)
12. `or` (logical OR)
13. `=` `+=` `-=` `*=` `/=` `%=` (assignment)

# User Input and Interactive Programming

One of the most important aspects of programming is creating interactive applications. This chapter covers how to get user input, handle different data types, and create engaging user experiences in Umbra.

## Getting User Input

### Basic Input Functions

Umbra provides simple and intuitive functions for user input (*because talking to your computer should be easier than talking to your relatives*):

```umbra
define interactive_hello() -> Void:
    show("What's your name?")
    let name: String := input()
    show("Hello, " + name + "! Nice to meet you!")

    show("How old are you?")
    let age_str: String := input()
    let age: Integer := to_int(age_str)

    when age >= 18:
        show("You're an adult! Welcome to the world of taxes and responsibilities!")
    otherwise:
        show("Enjoy your youth while it lasts, young padawan!")
```

### Available Input Functions

Umbra provides several built-in functions for user input:

```umbra
// Basic input functions available in Umbra
define input_demo() -> Void:
    show("=== Umbra Input Functions Demo ===")

    // 1. input() - Basic input with optional prompt
    show("Enter your name: ")
    let name: String := input()
    show("Hello, " + name + "!")

    // 2. input() with prompt parameter
    let city: String := input("Enter your city: ")
    show("You live in " + city)

    // 3. read() - Read from stdin
    show("Type something and press Enter:")
    let text: String := read()
    show("You typed: " + text)

    // 4. readln() - Read line from stdin
    show("Enter a line of text:")
    let line: String := readln()
    show("Line: " + line)
```

### Input Validation and Type Conversion

Since all input comes as strings, you need to convert to other types:

```umbra
define number_input_demo() -> Void:
    show("=== Number Input Demo ===")

    // Get integer input
    show("Enter an integer: ")
    let num_str: String := input()
    let number: Integer := to_int(num_str)
    show("You entered: " + to_string(number))

    // Get float input
    show("Enter a decimal number: ")
    let float_str: String := input()
    let decimal: Float := to_float(float_str)
    show("You entered: " + to_string(decimal))

    // Safe input with error handling
    show("Enter your age: ")
    let age_str: String := input()

    // Note: In real Umbra, you'd need proper error handling
    // This is a simplified example
    let age: Integer := to_int(age_str)

    when age >= 18:
        show("You can vote!")
    otherwise:
        show("Too young to vote.")
```

### Advanced Input Patterns

```umbra
define calculator_demo() -> Void:
    show("=== Umbra Calculator ===")
    show("(Because who needs a real calculator when you have code?)")

    let mut continue_calculating: Boolean := true

    repeat:
        show("\nEnter first number:")
        let num1: Float := input_float()

        show("Enter operation (+, -, *, /):")
        let operation: String := input()

        show("Enter second number:")
        let num2: Float := input_float()

        let result: Float := when operation:
            "+" => num1 + num2
            "-" => num1 - num2
            "*" => num1 * num2
            "/" =>
                when num2 == 0.0:
                    show("Error: Division by zero! (Math teachers everywhere are crying)")
                    0.0
                otherwise:
                    num1 / num2
            otherwise =>
                show("Unknown operation! Using addition because why not?")
                num1 + num2

        show("Result: " + result.to_string())

        show("Continue? (y/n):")
        let continue_input: String := input()
        continue_calculating := (continue_input == "y" or continue_input == "yes")

    until not continue_calculating

    show("Thanks for using the Umbra Calculator! May your calculations be ever accurate!")
```

## Working with External Libraries

### Python Integration

Umbra's Python integration makes it easy to leverage the vast Python ecosystem (*because why reinvent the wheel when Python already has 50 different wheel libraries?*):

```umbra
// Import Python functionality
import python

define python_integration_demo() -> Void:
    show("=== Python Integration Demo ===")

    // Initialize Python interpreter
    let py: PythonInterpreter := python.initialize()

    // Import Python modules
    let numpy := py.import("numpy")
    let matplotlib := py.import("matplotlib.pyplot")

    // Create data using Python NumPy
    show("Creating data with NumPy...")
    let x_data := numpy.call("linspace", [0, 10, 100])
    let y_data := numpy.call("sin", [x_data])

    // Plot using matplotlib
    show("Creating plot with matplotlib...")
    matplotlib.call("figure", [])
    matplotlib.call("plot", [x_data, y_data])
    matplotlib.call("title", ["Sine Wave Generated from Umbra!"])
    matplotlib.call("xlabel", ["X values"])
    matplotlib.call("ylabel", ["sin(X)"])
    matplotlib.call("grid", [true])
    matplotlib.call("show", [])

    show("Plot created! Check your screen for mathematical beauty!")

    // Clean up
    py.finalize()
```

### Using Python Libraries for Data Science

```umbra
define data_science_with_python() -> Void:
    show("=== Data Science with Python Libraries ===")

    let py := python.initialize()
    let pandas := py.import("pandas")
    let sklearn := py.import("sklearn.linear_model")

    // Load data using pandas
    show("Loading data with pandas...")
    let df := pandas.call("read_csv", ["data/sales_data.csv"])

    show("Data shape: " + df.call("shape").to_string())
    show("First few rows:")
    show(df.call("head").to_string())

    // Prepare data for machine learning
    let X := df.call("drop", ["target"], axis=1)
    let y := df.call("target")

    // Create and train model
    show("Training linear regression model...")
    let model := sklearn.call("LinearRegression")
    model.call("fit", [X, y])

    // Make predictions
    let predictions := model.call("predict", [X])
    let score := model.call("score", [X, y])

    show("Model R² score: " + score.to_string())
    show("First 5 predictions: " + predictions.call("[:5]").to_string())

    py.finalize()
    show("Data science complete! You're now 37% more likely to get hired at a tech company!")
```

\newpage

# Advanced AI/ML Programming with Correct Syntax

Let's dive deeper into Umbra's AI/ML capabilities with the correct syntax and plenty of humor to keep things interesting!

## Machine Learning Workflows

### Complete ML Pipeline

```umbra
define complete_ml_pipeline() -> Void:
    show("=== Complete Machine Learning Pipeline ===")
    show("(Buckle up, we're about to teach a computer to think!)")

    // Step 1: Load and explore data
    show("Step 1: Loading data... (crossing fingers it's not corrupted)")
    let dataset: Dataset := load_dataset("data/customer_data.csv")

    show("Dataset info:")
    show("- Rows: " + dataset.rows().to_string())
    show("- Columns: " + dataset.columns().to_string())
    show("- Missing values: " + dataset.missing_count().to_string() + " (because real data is never clean)")

    // Step 2: Data preprocessing
    show("Step 2: Cleaning data... (digital janitor mode activated)")
    let cleaned_dataset := preprocess dataset:
        fill_missing := "mean"
        normalize := true
        encode_categorical := "one_hot"
        remove_outliers := true

    // Step 3: Split data
    show("Step 3: Splitting data... (like breaking up, but for data)")
    let (train_data, test_data) := split_dataset(cleaned_dataset, ratio: 0.8)

    // Step 4: Create model
    show("Step 4: Creating model... (assembling digital brain)")
    let model: Model := create_model("random_forest"):
        n_estimators := 100
        max_depth := 10
        random_state := 42  // The answer to everything

    // Step 5: Train model
    show("Step 5: Training model... (time for coffee and existential thoughts)")
    train model using train_data:
        validation_split := 0.2
        early_stopping := true
        patience := 10
        verbose := true

    // Step 6: Evaluate model
    show("Step 6: Evaluating model... (moment of truth!)")
    let results := evaluate model on test_data

    show("Model Performance:")
    show("- Accuracy: " + (results.accuracy * 100).to_string() + "%")
    show("- Precision: " + (results.precision * 100).to_string() + "%")
    show("- Recall: " + (results.recall * 100).to_string() + "%")
    show("- F1-Score: " + (results.f1_score * 100).to_string() + "%")

    when results.accuracy > 0.9:
        show("Excellent! This model is smarter than my last three interns!")
    otherwise:
        when results.accuracy > 0.8:
            show("Pretty good! This model shows promise.")
        otherwise:
            show("Hmm, might need more coffee... and data.")

    // Step 7: Make predictions
    show("Step 7: Making predictions... (crystal ball mode)")
    let sample_data := test_data.sample(5)
    let predictions := predict sample_data using model

    for i in 0..predictions.length():
        show("Sample " + (i + 1).to_string() + ": Predicted = " + predictions[i].to_string())

    // Step 8: Save model
    show("Step 8: Saving model... (preserving digital wisdom)")
    save model to "trained_model.umbra"

    show("Pipeline complete! You've successfully taught a computer to make educated guesses!")
```

## Deep Learning with Neural Networks

### Building Your First Neural Network

```umbra
define neural_network_demo() -> Void:
    show("=== Neural Network Demo ===")
    show("(Time to build a digital brain that's hopefully smarter than a goldfish)")

    // Create a neural network for image classification
    let network: NeuralNetwork := create_neural_network():
        input_size := 784  // 28x28 pixel images
        layers := [
            dense_layer(128, activation: "relu"),
            dropout_layer(0.2),  // Prevent overfitting (like wearing a helmet)
            dense_layer(64, activation: "relu"),
            dropout_layer(0.2),
            dense_layer(10, activation: "softmax")  // 10 classes
        ]
        optimizer := "adam"
        learning_rate := 0.001
        loss_function := "categorical_crossentropy"

    // Load training data
    show("Loading MNIST dataset... (teaching computers to read handwriting)")
    let (train_images, train_labels) := load_mnist_train()
    let (test_images, test_labels) := load_mnist_test()

    // Preprocess data
    show("Preprocessing data... (making it computer-friendly)")
    train_images := normalize(train_images, min: 0.0, max: 1.0)
    test_images := normalize(test_images, min: 0.0, max: 1.0)

    // Train the network
    show("Training neural network... (this might take a while, perfect time for a snack)")
    let training_history := train network using train_images, train_labels:
        epochs := 50
        batch_size := 128
        validation_data := (test_images, test_labels)
        callbacks := [
            early_stopping(patience: 5),
            model_checkpoint("best_model.umbra"),
            learning_rate_scheduler(factor: 0.5, patience: 3)
        ]

    // Evaluate the model
    show("Evaluating model... (moment of truth!)")
    let test_accuracy := evaluate network on test_images, test_labels
    show("Test accuracy: " + (test_accuracy * 100).to_string() + "%")

    when test_accuracy > 0.95:
        show("Excellent! This network is reading digits better than most doctors!")
    otherwise:
        when test_accuracy > 0.90:
            show("Pretty good! This network shows promise.")
        otherwise:
            show("Hmm, might need more training... or coffee.")

    // Make some predictions
    show("Making predictions on sample images...")
    let sample_indices := [0, 1, 2, 3, 4]
    for i in sample_indices:
        let prediction := predict network on test_images[i]
        let predicted_digit := argmax(prediction)
        let actual_digit := argmax(test_labels[i])
        show("Image " + i.to_string() + ": Predicted = " + predicted_digit.to_string() + ", Actual = " + actual_digit.to_string())
```

### Convolutional Neural Networks

```umbra
define cnn_demo() -> Void:
    show("=== Convolutional Neural Network Demo ===")
    show("(CNNs: Because regular neural networks weren't complicated enough)")

    // Create a CNN for image classification
    let cnn: ConvolutionalNetwork := create_cnn():
        input_shape := [28, 28, 1]  // Grayscale images
        layers := [
            // First convolutional block
            conv2d_layer(32, kernel_size: 3, activation: "relu"),
            batch_normalization_layer(),
            conv2d_layer(32, kernel_size: 3, activation: "relu"),
            max_pooling2d_layer(pool_size: 2),
            dropout_layer(0.25),

            // Second convolutional block
            conv2d_layer(64, kernel_size: 3, activation: "relu"),
            batch_normalization_layer(),
            conv2d_layer(64, kernel_size: 3, activation: "relu"),
            max_pooling2d_layer(pool_size: 2),
            dropout_layer(0.25),

            // Flatten and classify
            flatten_layer(),
            dense_layer(128, activation: "relu"),
            dropout_layer(0.5),
            dense_layer(10, activation: "softmax")
        ]
        optimizer := "adam"
        learning_rate := 0.001

    show("CNN created! It has " + cnn.parameter_count().to_string() + " parameters to train.")
    show("(That's a lot of digital neurons to keep happy)")

    // Data augmentation for better generalization
    let augmentation := create_data_augmentation():
        rotation_range := 10
        width_shift_range := 0.1
        height_shift_range := 0.1
        zoom_range := 0.1
        horizontal_flip := false  // Numbers don't flip horizontally

    // Train with augmented data
    show("Training CNN with data augmentation...")
    train cnn using augmented_data(train_images, train_labels, augmentation):
        epochs := 30
        batch_size := 64
        validation_split := 0.2
```

## Advanced Data Structures and Algorithms

### Custom Data Structures

```umbra
// Define a binary tree structure
struct TreeNode:
    value: Integer
    left: Option<TreeNode>
    right: Option<TreeNode>

define create_tree_node(value: Integer) -> TreeNode:
    return TreeNode:
        value := value
        left := None
        right := None

// Binary search tree implementation
struct BinarySearchTree:
    root: Option<TreeNode>

define create_bst() -> BinarySearchTree:
    return BinarySearchTree:
        root := None

define insert_into_bst(mut tree: BinarySearchTree, value: Integer) -> Void:
    when tree.root:
        None => tree.root := Some(create_tree_node(value))
        Some(node) => insert_recursive(node, value)

define insert_recursive(mut node: TreeNode, value: Integer) -> Void:
    when value < node.value:
        when node.left:
            None => node.left := Some(create_tree_node(value))
            Some(left_node) => insert_recursive(left_node, value)
    otherwise:
        when node.right:
            None => node.right := Some(create_tree_node(value))
            Some(right_node) => insert_recursive(right_node, value)

define search_bst(tree: BinarySearchTree, value: Integer) -> Boolean:
    when tree.root:
        None => return false
        Some(node) => return search_recursive(node, value)

define search_recursive(node: TreeNode, value: Integer) -> Boolean:
    when value == node.value:
        return true
    otherwise:
        when value < node.value:
            when node.left:
                None => return false
                Some(left_node) => return search_recursive(left_node, value)
        otherwise:
            when node.right:
                None => return false
                Some(right_node) => return search_recursive(right_node, value)

// Demo usage
define data_structures_demo() -> Void:
    show("=== Advanced Data Structures Demo ===")
    show("(Because arrays are for beginners)")

    let mut bst := create_bst()
    let values := [50, 30, 70, 20, 40, 60, 80]

    show("Inserting values into binary search tree...")
    for value in values:
        insert_into_bst(bst, value)
        show("Inserted: " + value.to_string())

    show("\nSearching for values...")
    let search_values := [40, 25, 80, 100]
    for value in search_values:
        let found := search_bst(bst, value)
        when found:
            show("Found " + value.to_string() + " in the tree!")
        otherwise:
            show(value.to_string() + " not found in the tree.")
```

## Web Development with Umbra

### Building a Web Server

```umbra
// Import web framework
import web

define create_web_server() -> Void:
    show("=== Web Server Demo ===")
    show("(Building the next Facebook... or at least a decent blog)")

    let server := web.create_server():
        host := "localhost"
        port := 8080

    // Define routes
    server.get("/", handle_home)
    server.get("/about", handle_about)
    server.get("/api/users", handle_users_api)
    server.post("/api/users", handle_create_user)
    server.get("/api/users/:id", handle_user_by_id)

    // Middleware
    server.use(logging_middleware)
    server.use(cors_middleware)
    server.use(json_parser_middleware)

    show("Starting server on http://localhost:8080")
    show("(Your website is about to go live! Don't panic.)")
    server.start()

define handle_home(request: Request) -> Response:
    let html := """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Umbra Web App</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            h1 { color: #333; }
            .highlight { background-color: #f0f0f0; padding: 10px; }
        </style>
    </head>
    <body>
        <h1>Welcome to Umbra Web!</h1>
        <p class="highlight">This page was generated by Umbra code!</p>
        <p>Umbra makes web development as easy as making toast.</p>
        <a href="/about">About</a> | <a href="/api/users">Users API</a>
    </body>
    </html>
    """
    return Response.html(html)

define handle_about(request: Request) -> Response:
    let about_data := {
        "title": "About Umbra Web",
        "description": "A web framework that doesn't make you want to throw your computer out the window",
        "features": [
            "Simple routing",
            "Built-in JSON support",
            "Middleware system",
            "Type safety",
            "Performance optimized"
        ],
        "version": "1.0.1"
    }
    return Response.json(about_data)

define handle_users_api(request: Request) -> Response:
    // Simulate database query
    let users := [
        {"id": 1, "name": "Alice", "email": "<EMAIL>"},
        {"id": 2, "name": "Bob", "email": "<EMAIL>"},
        {"id": 3, "name": "Charlie", "email": "<EMAIL>"}
    ]
    return Response.json(users)

define handle_create_user(request: Request) -> Response:
    let user_data := request.json()

    // Validate required fields
    when not user_data.has("name") or not user_data.has("email"):
        return Response.error(400, "Name and email are required")

    // Simulate saving to database
    let new_user := {
        "id": generate_id(),
        "name": user_data["name"],
        "email": user_data["email"],
        "created_at": current_timestamp()
    }

    show("Created new user: " + new_user["name"])
    return Response.json(new_user, status: 201)

define handle_user_by_id(request: Request) -> Response:
    let user_id := request.params["id"].to_integer()

    // Simulate database lookup
    when user_id == 1:
        return Response.json({"id": 1, "name": "Alice", "email": "<EMAIL>"})
    otherwise:
        when user_id == 2:
            return Response.json({"id": 2, "name": "Bob", "email": "<EMAIL>"})
        otherwise:
            return Response.error(404, "User not found")

define logging_middleware(request: Request, next: Function) -> Response:
    let start_time := current_time()
    show(request.method + " " + request.path + " - Started")

    let response := next(request)

    let duration := current_time() - start_time
    show(request.method + " " + request.path + " - " + response.status.to_string() + " (" + duration.to_string() + "ms)")

    return response
```

## Game Development with Umbra

### Creating a Simple Game

```umbra
// Import game development framework
import game

// Game state structure
struct GameState:
    player_x: Float
    player_y: Float
    player_health: Integer
    score: Integer
    enemies: Array<Enemy>
    bullets: Array<Bullet>
    game_over: Boolean

struct Enemy:
    x: Float
    y: Float
    health: Integer
    speed: Float

struct Bullet:
    x: Float
    y: Float
    velocity_x: Float
    velocity_y: Float

define create_game() -> Void:
    show("=== Umbra Game Development Demo ===")
    show("(Building the next Tetris... or at least something that moves)")

    // Initialize game window
    let window := game.create_window():
        title := "Umbra Space Shooter"
        width := 800
        height := 600
        resizable := false

    // Initialize game state
    let mut game_state := GameState:
        player_x := 400.0
        player_y := 500.0
        player_health := 100
        score := 0
        enemies := []
        bullets := []
        game_over := false

    // Load game assets
    let player_sprite := game.load_sprite("assets/player.png")
    let enemy_sprite := game.load_sprite("assets/enemy.png")
    let bullet_sprite := game.load_sprite("assets/bullet.png")
    let background_music := game.load_sound("assets/background.mp3")

    // Start background music
    game.play_sound(background_music, loop: true)

    // Main game loop
    let mut running := true
    let mut last_time := game.get_time()

    repeat:
        let current_time := game.get_time()
        let delta_time := current_time - last_time
        last_time := current_time

        // Handle input
        handle_input(game_state, delta_time)

        // Update game logic
        update_game(game_state, delta_time)

        // Render everything
        render_game(window, game_state, player_sprite, enemy_sprite, bullet_sprite)

        // Check for exit
        running := not window.should_close() and not game_state.game_over

    until not running

    show("Game over! Final score: " + game_state.score.to_string())
    show("Thanks for playing! (Your reflexes are better than my coding)")

define handle_input(mut game_state: GameState, delta_time: Float) -> Void:
    let input := game.get_input()

    // Player movement
    when input.is_key_pressed("LEFT") or input.is_key_pressed("A"):
        game_state.player_x := max(0.0, game_state.player_x - 300.0 * delta_time)

    when input.is_key_pressed("RIGHT") or input.is_key_pressed("D"):
        game_state.player_x := min(800.0, game_state.player_x + 300.0 * delta_time)

    when input.is_key_pressed("UP") or input.is_key_pressed("W"):
        game_state.player_y := max(0.0, game_state.player_y - 300.0 * delta_time)

    when input.is_key_pressed("DOWN") or input.is_key_pressed("S"):
        game_state.player_y := min(600.0, game_state.player_y + 300.0 * delta_time)

    // Shooting
    when input.is_key_just_pressed("SPACE"):
        let bullet := Bullet:
            x := game_state.player_x
            y := game_state.player_y - 20.0
            velocity_x := 0.0
            velocity_y := -500.0
        game_state.bullets.push(bullet)

define update_game(mut game_state: GameState, delta_time: Float) -> Void:
    // Update bullets
    let mut i := 0
    repeat:
        when i >= game_state.bullets.length():
            break

        game_state.bullets[i].x := game_state.bullets[i].x + game_state.bullets[i].velocity_x * delta_time
        game_state.bullets[i].y := game_state.bullets[i].y + game_state.bullets[i].velocity_y * delta_time

        // Remove bullets that are off screen
        when game_state.bullets[i].y < -10.0:
            game_state.bullets.remove(i)
        otherwise:
            i := i + 1
    until false

    // Update enemies
    for mut enemy in game_state.enemies:
        enemy.y := enemy.y + enemy.speed * delta_time

    // Remove enemies that are off screen
    game_state.enemies := game_state.enemies.filter(|enemy| enemy.y < 650.0)

    // Spawn new enemies randomly
    when random() < 0.02:  // 2% chance per frame
        let enemy := Enemy:
            x := random() * 800.0
            y := -50.0
            health := 1
            speed := 100.0 + random() * 100.0
        game_state.enemies.push(enemy)

    // Check collisions between bullets and enemies
    for bullet in game_state.bullets:
        for mut enemy in game_state.enemies:
            let distance := sqrt((bullet.x - enemy.x) ** 2 + (bullet.y - enemy.y) ** 2)
            when distance < 30.0:  // Collision detected
                enemy.health := enemy.health - 1
                game_state.score := game_state.score + 10
                // Remove bullet and enemy if destroyed
                game_state.bullets := game_state.bullets.filter(|b| b != bullet)
                when enemy.health <= 0:
                    game_state.enemies := game_state.enemies.filter(|e| e != enemy)

    // Check collisions between player and enemies
    for enemy in game_state.enemies:
        let distance := sqrt((game_state.player_x - enemy.x) ** 2 + (game_state.player_y - enemy.y) ** 2)
        when distance < 40.0:  // Player hit
            game_state.player_health := game_state.player_health - 1
            when game_state.player_health <= 0:
                game_state.game_over := true

define render_game(window: Window, game_state: GameState, player_sprite: Sprite, enemy_sprite: Sprite, bullet_sprite: Sprite) -> Void:
    // Clear screen
    window.clear(color: "black")

    // Draw player
    window.draw_sprite(player_sprite, game_state.player_x, game_state.player_y)

    // Draw enemies
    for enemy in game_state.enemies:
        window.draw_sprite(enemy_sprite, enemy.x, enemy.y)

    // Draw bullets
    for bullet in game_state.bullets:
        window.draw_sprite(bullet_sprite, bullet.x, bullet.y)

    // Draw UI
    window.draw_text("Score: " + game_state.score.to_string(), 10, 10, color: "white")
    window.draw_text("Health: " + game_state.player_health.to_string(), 10, 40, color: "white")

    when game_state.game_over:
        window.draw_text("GAME OVER", 350, 300, size: 48, color: "red")
        window.draw_text("Press ESC to exit", 320, 350, color: "white")

    // Present the frame
    window.present()
```

## Database Integration and ORM

### Working with Databases

```umbra
// Import database functionality
import database

// Define data models
struct User:
    id: Integer
    name: String
    email: String
    created_at: DateTime
    updated_at: DateTime

struct Post:
    id: Integer
    title: String
    content: String
    author_id: Integer
    published: Boolean
    created_at: DateTime

define database_demo() -> Void:
    show("=== Database Integration Demo ===")
    show("(Because data needs a home, and RAM is too expensive)")

    // Connect to database
    let db := database.connect():
        driver := "postgresql"
        host := "localhost"
        port := 5432
        database := "umbra_demo"
        username := "user"
        password := "password"

    // Create tables if they don't exist
    db.execute("""
        CREATE TABLE IF NOT EXISTS users (
            id SERIAL PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            email VARCHAR(255) UNIQUE NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """)

    db.execute("""
        CREATE TABLE IF NOT EXISTS posts (
            id SERIAL PRIMARY KEY,
            title VARCHAR(255) NOT NULL,
            content TEXT,
            author_id INTEGER REFERENCES users(id),
            published BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """)

    // Insert sample data
    show("Inserting sample users...")
    let user1_id := db.insert("users", {
        "name": "Alice Johnson",
        "email": "<EMAIL>"
    })

    let user2_id := db.insert("users", {
        "name": "Bob Smith",
        "email": "<EMAIL>"
    })

    show("Inserting sample posts...")
    db.insert("posts", {
        "title": "Getting Started with Umbra",
        "content": "Umbra is an amazing programming language...",
        "author_id": user1_id,
        "published": true
    })

    db.insert("posts", {
        "title": "Advanced AI/ML with Umbra",
        "content": "In this post, we'll explore neural networks...",
        "author_id": user1_id,
        "published": false
    })

    db.insert("posts", {
        "title": "Web Development in Umbra",
        "content": "Building web applications has never been easier...",
        "author_id": user2_id,
        "published": true
    })

    // Query data
    show("Querying published posts with authors...")
    let results := db.query("""
        SELECT p.title, p.content, u.name as author_name, p.created_at
        FROM posts p
        JOIN users u ON p.author_id = u.id
        WHERE p.published = true
        ORDER BY p.created_at DESC
    """)

    for row in results:
        show("Title: " + row["title"])
        show("Author: " + row["author_name"])
        show("Created: " + row["created_at"].to_string())
        show("Content: " + row["content"][0..100] + "...")
        show("---")

    // Update data
    show("Updating user email...")
    db.update("users",
        set: {"email": "<EMAIL>"},
        where: {"id": user1_id}
    )

    // Delete data
    show("Deleting unpublished posts...")
    let deleted_count := db.delete("posts", where: {"published": false})
    show("Deleted " + deleted_count.to_string() + " unpublished posts")

    // Close connection
    db.close()
    show("Database operations completed successfully!")
    show("(Your data is now safely stored in the digital realm)")

// ORM-style database operations
define orm_demo() -> Void:
    show("=== ORM Demo ===")
    show("(Object-Relational Mapping: Making databases speak object-oriented)")

    // Initialize ORM
    let orm := database.create_orm():
        connection := database.connect_url("postgresql://user:password@localhost/umbra_demo")
        auto_migrate := true

    // Register models
    orm.register_model<User>()
    orm.register_model<Post>()

    // Create new user using ORM
    let mut new_user := User:
        name := "Charlie Brown"
        email := "<EMAIL>"
        created_at := now()
        updated_at := now()

    new_user := orm.save(new_user)
    show("Created user with ID: " + new_user.id.to_string())

    // Find users
    let all_users := orm.find_all<User>()
    show("Found " + all_users.length().to_string() + " users:")
    for user in all_users:
        show("- " + user.name + " (" + user.email + ")")

    // Find user by email
    let alice := orm.find_one<User>(where: {"email": "<EMAIL>"})
    when alice:
        Some(user) =>
            show("Found Alice: " + user.name)

            // Create post for Alice
            let new_post := Post:
                title := "ORM is Amazing!"
                content := "Using ORM makes database operations so much easier..."
                author_id := user.id
                published := true
                created_at := now()

            orm.save(new_post)
            show("Created new post for Alice")
        None =>
            show("Alice not found")

    // Complex query with relationships
    let published_posts := orm.query<Post>()
        .where("published", true)
        .join<User>("author_id", "id")
        .order_by("created_at", "DESC")
        .limit(10)
        .execute()

    show("Recent published posts:")
    for post in published_posts:
        show("- " + post.title + " by " + post.author.name)
```

## Testing and Quality Assurance

### Unit Testing Framework

```umbra
// Import testing framework
import test

// Test suite for mathematical functions
define test_math_functions() -> Void:
    test.describe("Mathematical Functions", || {

        test.it("should add two numbers correctly", || {
            let result := add(2, 3)
            test.expect(result).to_equal(5)
        })

        test.it("should multiply two numbers correctly", || {
            let result := multiply(4, 5)
            test.expect(result).to_equal(20)
        })

        test.it("should handle division by zero", || {
            test.expect_error(|| {
                divide(10, 0)
            }).to_throw("Division by zero")
        })

        test.it("should calculate factorial correctly", || {
            test.expect(factorial(0)).to_equal(1)
            test.expect(factorial(1)).to_equal(1)
            test.expect(factorial(5)).to_equal(120)
        })
    })

// Test suite for data structures
define test_data_structures() -> Void:
    test.describe("Binary Search Tree", || {

        test.it("should insert and find values", || {
            let mut bst := create_bst()
            insert_into_bst(bst, 50)
            insert_into_bst(bst, 30)
            insert_into_bst(bst, 70)

            test.expect(search_bst(bst, 50)).to_be_true()
            test.expect(search_bst(bst, 30)).to_be_true()
            test.expect(search_bst(bst, 70)).to_be_true()
            test.expect(search_bst(bst, 25)).to_be_false()
        })

        test.it("should maintain BST property", || {
            let mut bst := create_bst()
            let values := [50, 30, 70, 20, 40, 60, 80]

            for value in values:
                insert_into_bst(bst, value)

            // Verify all values can be found
            for value in values:
                test.expect(search_bst(bst, value)).to_be_true()
        })
    })

// Integration tests
define test_web_api() -> Void:
    test.describe("Web API", || {

        test.before_each(|| {
            // Setup test database
            setup_test_database()
        })

        test.after_each(|| {
            // Cleanup test database
            cleanup_test_database()
        })

        test.it("should create a new user", || {
            let response := test.post("/api/users", {
                "name": "Test User",
                "email": "<EMAIL>"
            })

            test.expect(response.status).to_equal(201)
            test.expect(response.json()["name"]).to_equal("Test User")
            test.expect(response.json()["email"]).to_equal("<EMAIL>")
        })

        test.it("should get user by ID", || {
            // Create a user first
            let create_response := test.post("/api/users", {
                "name": "Test User",
                "email": "<EMAIL>"
            })
            let user_id := create_response.json()["id"]

            // Get the user
            let get_response := test.get("/api/users/" + user_id.to_string())

            test.expect(get_response.status).to_equal(200)
            test.expect(get_response.json()["name"]).to_equal("Test User")
        })

        test.it("should return 404 for non-existent user", || {
            let response := test.get("/api/users/99999")
            test.expect(response.status).to_equal(404)
        })
    })

// Performance tests
define test_performance() -> Void:
    test.describe("Performance Tests", || {

        test.it("should sort large array quickly", || {
            let large_array := generate_random_array(100000)

            let start_time := get_current_time()
            let sorted_array := quick_sort(large_array)
            let end_time := get_current_time()

            let duration := end_time - start_time
            test.expect(duration).to_be_less_than(1000)  // Should complete in under 1 second
            test.expect(is_sorted(sorted_array)).to_be_true()
        })

        test.it("should handle concurrent operations", || {
            let counter := create_atomic_counter()
            let threads := []

            // Start 10 threads that increment counter 1000 times each
            for i in 0..10:
                let thread := spawn_thread(|| {
                    for j in 0..1000:
                        counter.increment()
                })
                threads.push(thread)

            // Wait for all threads to complete
            for thread in threads:
                thread.join()

            test.expect(counter.value()).to_equal(10000)
        })
    })

// Run all tests
define run_all_tests() -> Void:
    show("=== Running Umbra Test Suite ===")
    show("(Time to see if our code actually works or if we've been living a lie)")

    test_math_functions()
    test_data_structures()
    test_web_api()
    test_performance()

    let results := test.get_results()

    show("\n=== Test Results ===")
    show("Total tests: " + results.total.to_string())
    show("Passed: " + results.passed.to_string())
    show("Failed: " + results.failed.to_string())
    show("Skipped: " + results.skipped.to_string())

    when results.failed == 0:
        show("All tests passed! Your code is bulletproof!")
    otherwise:
        show(results.failed.to_string() + " tests failed. Time to debug!")

    show("Test coverage: " + (results.coverage * 100).to_string() + "%")
```

## Real-World Project Examples

### Building a Complete Todo Application

Let's build a complete command-line todo application to demonstrate real-world Umbra programming:

```umbra
// Todo application structure
struct TodoItem:
    id: Integer
    title: String
    description: String
    completed: Boolean
    created_at: String

struct TodoApp:
    items: Array<TodoItem>
    next_id: Integer

define create_todo_app() -> TodoApp:
    return TodoApp:
        items := []
        next_id := 1

define main() -> Void:
    show("=== Umbra Todo Application ===")
    show("(Because even programmers need to remember to do laundry)")

    let mut app := create_todo_app()
    let mut running := true

    repeat:
        show_menu()
        let choice: String := input("Enter your choice: ")

        when choice:
            "1" => add_todo(app)
            "2" => list_todos(app)
            "3" => complete_todo(app)
            "4" => delete_todo(app)
            "5" => save_todos(app)
            "6" => load_todos(app)
            "7" =>
                running := false
                show("Goodbye! Don't forget to actually do your todos!")
            otherwise =>
                show("Invalid choice! Try again (reading is fundamental).")
    until not running

define show_menu() -> Void:
    show("\n=== Todo Menu ===")
    show("1. Add Todo")
    show("2. List Todos")
    show("3. Complete Todo")
    show("4. Delete Todo")
    show("5. Save Todos")
    show("6. Load Todos")
    show("7. Exit")

define add_todo(mut app: TodoApp) -> Void:
    show("\n=== Add New Todo ===")
    let title: String := input("Enter todo title: ")
    let description: String := input("Enter description: ")

    let todo := TodoItem:
        id := app.next_id
        title := title
        description := description
        completed := false
        created_at := get_current_time()

    app.items.push(todo)
    app.next_id := app.next_id + 1

    show("Todo added successfully! (One more thing to procrastinate on)")

define list_todos(app: TodoApp) -> Void:
    show("\n=== Your Todos ===")

    when app.items.length() == 0:
        show("No todos found. You're either super productive or super lazy!")
        return

    for todo in app.items:
        let status := when todo.completed:
            true => "[[OK]]"
            false => "[ ]"

        show(status + " " + todo.id.to_string() + ". " + todo.title)
        show("    " + todo.description)
        show("    Created: " + todo.created_at)
        show("")

define complete_todo(mut app: TodoApp) -> Void:
    show("\n=== Complete Todo ===")
    list_todos(app)

    let id_str: String := input("Enter todo ID to complete: ")
    let id: Integer := to_int(id_str)

    for mut todo in app.items:
        when todo.id == id:
            todo.completed := true
            show("Todo completed! (Achievement unlocked: Basic adulting)")
            return

    show("Todo not found! (Maybe it completed itself?)")

define delete_todo(mut app: TodoApp) -> Void:
    show("\n=== Delete Todo ===")
    list_todos(app)

    let id_str: String := input("Enter todo ID to delete: ")
    let id: Integer := to_int(id_str)

    let mut found := false
    let mut new_items: Array<TodoItem> := []

    for todo in app.items:
        when todo.id != id:
            new_items.push(todo)
        otherwise:
            found := true

    when found:
        app.items := new_items
        show("Todo deleted! (One less thing to worry about)")
    otherwise:
        show("Todo not found! (It might have deleted itself)")

define save_todos(app: TodoApp) -> Void:
    show("\n=== Save Todos ===")
    let filename: String := input("Enter filename (or press Enter for 'todos.txt'): ")

    let file_path := when filename == "":
        "todos.txt"
    otherwise:
        filename

    // Convert todos to JSON-like format
    let mut content := "# Umbra Todo List\n"
    content := content + "# Generated on " + get_current_time() + "\n\n"

    for todo in app.items:
        content := content + "ID: " + todo.id.to_string() + "\n"
        content := content + "Title: " + todo.title + "\n"
        content := content + "Description: " + todo.description + "\n"
        content := content + "Completed: " + todo.completed.to_string() + "\n"
        content := content + "Created: " + todo.created_at + "\n"
        content := content + "---\n"

    write(file_path, content)
    show("Todos saved to " + file_path + "! (Your procrastination is now documented)")

define load_todos(mut app: TodoApp) -> Void:
    show("\n=== Load Todos ===")
    let filename: String := input("Enter filename to load: ")

    // In a real implementation, you'd parse the file format
    // This is a simplified example
    show("Loading todos from " + filename + "...")
    show("(Feature coming soon - for now, just imagine your todos are loaded)")
```

### Building a Simple Calculator with History

```umbra
struct CalculatorHistory:
    operations: Array<String>
    results: Array<Float>

struct Calculator:
    history: CalculatorHistory
    memory: Float

define create_calculator() -> Calculator:
    return Calculator:
        history := CalculatorHistory:
            operations := []
            results := []
        memory := 0.0

define calculator_main() -> Void:
    show("=== Umbra Scientific Calculator ===")
    show("(Because your phone's calculator is too mainstream)")

    let mut calc := create_calculator()
    let mut running := true

    repeat:
        show_calculator_menu()
        let choice: String := input("Enter your choice: ")

        when choice:
            "1" => basic_arithmetic(calc)
            "2" => scientific_functions(calc)
            "3" => show_history(calc)
            "4" => memory_operations(calc)
            "5" =>
                running := false
                show("Calculator closed. Math was fun while it lasted!")
            otherwise =>
                show("Invalid choice! Even calculators have standards.")
    until not running

define show_calculator_menu() -> Void:
    show("\n=== Calculator Menu ===")
    show("1. Basic Arithmetic (+, -, *, /)")
    show("2. Scientific Functions (sin, cos, sqrt, etc.)")
    show("3. Show History")
    show("4. Memory Operations")
    show("5. Exit")

define basic_arithmetic(mut calc: Calculator) -> Void:
    show("\n=== Basic Arithmetic ===")
    let num1_str: String := input("Enter first number: ")
    let num1: Float := to_float(num1_str)

    let operation: String := input("Enter operation (+, -, *, /): ")

    let num2_str: String := input("Enter second number: ")
    let num2: Float := to_float(num2_str)

    let result: Float := when operation:
        "+" => num1 + num2
        "-" => num1 - num2
        "*" => num1 * num2
        "/" =>
            when num2 == 0.0:
                show("Error: Division by zero! (Math teachers everywhere are crying)")
                0.0
            otherwise:
                num1 / num2
        otherwise =>
            show("Unknown operation! Using addition because why not?")
            num1 + num2

    let operation_str := num1.to_string() + " " + operation + " " + num2.to_string()
    calc.history.operations.push(operation_str)
    calc.history.results.push(result)

    show("Result: " + result.to_string())

define scientific_functions(mut calc: Calculator) -> Void:
    show("\n=== Scientific Functions ===")
    show("Available functions: sqrt, sin, cos, tan, log, exp")

    let func_name: String := input("Enter function name: ")
    let num_str: String := input("Enter number: ")
    let num: Float := to_float(num_str)

    let result: Float := when func_name:
        "sqrt" => sqrt(num)
        "sin" => sin(num)
        "cos" => cos(num)
        "tan" => tan(num)
        "log" => log(num)
        "exp" => exp(num)
        otherwise =>
            show("Unknown function! Returning the input (it's the thought that counts)")
            num

    let operation_str := function + "(" + num.to_string() + ")"
    calc.history.operations.push(operation_str)
    calc.history.results.push(result)

    show("Result: " + result.to_string())

define show_history(calc: Calculator) -> Void:
    show("\n=== Calculation History ===")

    when calc.history.operations.length() == 0:
        show("No calculations yet. Time to do some math!")
        return

    for i in 0..calc.history.operations.length():
        show((i + 1).to_string() + ". " + calc.history.operations[i] + " = " + calc.history.results[i].to_string())

define memory_operations(mut calc: Calculator) -> Void:
    show("\n=== Memory Operations ===")
    show("Current memory: " + calc.memory.to_string())
    show("1. Store in memory (MS)")
    show("2. Recall from memory (MR)")
    show("3. Add to memory (M+)")
    show("4. Clear memory (MC)")

    let choice: String := input("Enter choice: ")

    when choice:
        "1" =>
            let value_str: String := input("Enter value to store: ")
            calc.memory := to_float(value_str)
            show("Value stored in memory!")
        "2" =>
            show("Memory value: " + calc.memory.to_string())
        "3" =>
            let value_str: String := input("Enter value to add: ")
            calc.memory := calc.memory + to_float(value_str)
            show("Value added to memory! New total: " + calc.memory.to_string())
        "4" =>
            calc.memory := 0.0
            show("Memory cleared!")
        otherwise =>
            show("Invalid choice!")
```

## Advanced File Processing

### CSV Data Processor

```umbra
struct CSVRow:
    data: Array<String>

struct CSVFile:
    headers: Array<String>
    rows: Array<CSVRow>

define csv_processor_demo() -> Void:
    show("=== CSV Data Processor ===")
    show("(Because Excel is for people who don't code)")

    // Create sample CSV data
    create_sample_csv()

    // Process the CSV file
    let csv_data := load_csv("sample_data.csv")

    show("CSV loaded successfully!")
    show("Headers: " + csv_data.headers.join(", "))
    show("Rows: " + csv_data.rows.length().to_string())

    // Analyze the data
    analyze_csv_data(csv_data)

    // Filter and export
    filter_and_export(csv_data)

define create_sample_csv() -> Void:
    let csv_content := """Name,Age,City,Salary
Alice Johnson,28,New York,75000
Bob Smith,34,Los Angeles,82000
Charlie Brown,29,Chicago,68000
Diana Prince,31,Houston,79000
Eve Wilson,26,Phoenix,71000
Frank Miller,35,Philadelphia,85000
Grace Lee,30,San Antonio,73000
Henry Davis,32,San Diego,77000"""

    write("sample_data.csv", csv_content)
    show("Sample CSV file created!")

define load_csv(filename: String) -> CSVFile:
    let content: String := read_file(filename)
    let lines: Array<String> := content.split("\n")

    // Parse headers
    let headers: Array<String> := lines[0].split(",")

    // Parse rows
    let mut rows: Array<CSVRow> := []
    for i in 1..lines.length():
        when lines[i].trim() != "":
            let row_data: Array<String> := lines[i].split(",")
            let row := CSVRow:
                data := row_data
            rows.push(row)

    return CSVFile:
        headers := headers
        rows := rows

define analyze_csv_data(csv: CSVFile) -> Void:
    show("\n=== Data Analysis ===")

    // Find salary column
    let mut salary_index := -1
    for i in 0..csv.headers.length():
        when csv.headers[i] == "Salary":
            salary_index := i
            break

    when salary_index == -1:
        show("No salary column found!")
        return

    // Calculate statistics
    let mut total_salary := 0.0
    let mut min_salary := 999999.0
    let mut max_salary := 0.0

    for row in csv.rows:
        let salary: Float := to_float(row.data[salary_index])
        total_salary := total_salary + salary

        when salary < min_salary:
            min_salary := salary

        when salary > max_salary:
            max_salary := salary

    let average_salary := total_salary / csv.rows.length().to_float()

    show("Salary Statistics:")
    show("- Average: $" + average_salary.to_string())
    show("- Minimum: $" + min_salary.to_string())
    show("- Maximum: $" + max_salary.to_string())
    show("- Total employees: " + csv.rows.length().to_string())

define filter_and_export(csv: CSVFile) -> Void:
    show("\n=== Filter and Export ===")

    let min_salary_str: String := input("Enter minimum salary filter: ")
    let min_salary: Float := to_float(min_salary_str)

    // Find salary column index
    let mut salary_index := -1
    for i in 0..csv.headers.length():
        when csv.headers[i] == "Salary":
            salary_index := i
            break

    // Filter rows
    let mut filtered_rows: Array<CSVRow> := []
    for row in csv.rows:
        let salary: Float := to_float(row.data[salary_index])
        when salary >= min_salary:
            filtered_rows.push(row)

    // Export filtered data
    let mut output := csv.headers.join(",") + "\n"
    for row in filtered_rows:
        output := output + row.data.join(",") + "\n"

    write("filtered_data.csv", output)
    show("Filtered data exported to filtered_data.csv")
    show("Found " + filtered_rows.length().to_string() + " employees with salary >= $" + min_salary.to_string())
```

---

**Eclipse Softworks Development Team**
*Umbra Programming Language Complete Reference Guide*
*Version 1.0.1 - 2025*

\newpage
