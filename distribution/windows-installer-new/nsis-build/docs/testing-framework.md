# Umbra Testing Framework

The Umbra Testing Framework is a comprehensive testing solution designed specifically for the Umbra programming language, with specialized support for AI/ML applications.

## Features

### Core Testing Types

1. **Unit Tests** - Test individual functions and methods in isolation
2. **Integration Tests** - Test component interactions and system behavior
3. **Property-Based Tests** - Automatically generate test cases to find edge cases
4. **Performance Tests** - Benchmark and validate performance characteristics
5. **AI/ML Tests** - Specialized testing for machine learning workflows

### Key Capabilities

- **Test Discovery** - Automatically find and parse test files
- **Parallel Execution** - Run tests concurrently for faster feedback
- **Multiple Output Formats** - Console, JUnit XML, JSON, HTML reports
- **Code Coverage** - Track test coverage across your codebase
- **Test Templates** - Generate boilerplate test code
- **Mocking Framework** - Create test doubles for isolated testing
- **AI/ML Validation** - Validate models, data quality, and ML workflows

## Quick Start

### Installation

The testing framework is built into the Umbra compiler. No additional installation required.

### Basic Usage

```bash
# Discover tests in your project
umbra test discover

# Run all tests
umbra test run

# Run tests with coverage
umbra test run --coverage

# Generate a unit test template
umbra test generate unit --name my_function --examples

# Run performance benchmarks
umbra test benchmark --iterations 1000
```

### Writing Tests

#### Unit Test Example

```umbra
// tests/calculator_test.umbra
import testing.assertions as assert

fn test_add_basic() {
    let result = add(2, 3);
    assert_eq!(result, 5, "2 + 3 should equal 5");
}

fn test_add_edge_cases() {
    let result = add(0, 0);
    assert_eq!(result, 0, "0 + 0 should equal 0");
    
    let result = add(-1, 1);
    assert_eq!(result, 0, "-1 + 1 should equal 0");
}
```

#### Property-Based Test Example

```umbra
// tests/math_properties_test.umbra
import testing.property as prop
import testing.assertions as assert

fn property_test_add_commutative(a: int, b: int) {
    let result1 = add(a, b);
    let result2 = add(b, a);
    assert_eq!(result1, result2, "Addition should be commutative");
}

fn property_test_add_associative(a: int, b: int, c: int) {
    let result1 = add(add(a, b), c);
    let result2 = add(a, add(b, c));
    assert_eq!(result1, result2, "Addition should be associative");
}
```

#### AI/ML Test Example

```umbra
// tests/model_test.umbra
import testing.ai_ml as ai
import testing.assertions as assert

fn test_model_accuracy() {
    let model = ai.load_model("models/classifier.pth");
    let test_data = ai.load_dataset("data/test.csv");
    
    let predictions = model.predict(test_data.features);
    let accuracy = ai.calculate_accuracy(predictions, test_data.labels);
    
    assert_true!(accuracy > 0.9, "Model accuracy should be > 90%");
}

fn test_data_quality() {
    let dataset = ai.load_dataset("data/training.csv");
    let quality = ai.validate_data(dataset);
    
    assert_true!(quality.completeness > 0.95, "Data should be > 95% complete");
    assert_true!(quality.consistency > 0.9, "Data should be > 90% consistent");
}
```

## Command Reference

### Test Discovery

```bash
# Discover all tests
umbra test discover

# Show test statistics
umbra test discover --stats

# Filter by test type
umbra test discover --test-type unit

# Filter by tag
umbra test discover --tag integration
```

### Running Tests

```bash
# Run all tests
umbra test run

# Run with pattern matching
umbra test run --pattern "calculator*"

# Run specific test types
umbra test run --test-type unit

# Run in parallel
umbra test run --parallel --threads 4

# Generate coverage report
umbra test run --coverage

# Output to different formats
umbra test run --format junit --output results.xml
umbra test run --format json --output results.json
umbra test run --format html --output results.html
```

### Test Generation

```bash
# Generate unit test template
umbra test generate unit --name my_function

# Generate with examples
umbra test generate unit --name my_function --examples

# Generate integration test
umbra test generate integration --name api_workflow

# Generate property-based test
umbra test generate property --name math_operations

# Generate performance test
umbra test generate performance --name sorting_algorithm

# Generate AI/ML test
umbra test generate ai-ml --name model_validation
```

### Property-Based Testing

```bash
# Run property tests with custom parameters
umbra test property --cases 1000 --seed 42

# Run with maximum shrinking iterations
umbra test property --max-shrink 200
```

### Performance Testing

```bash
# Run benchmarks
umbra test benchmark

# Custom iterations and warmup
umbra test benchmark --iterations 5000 --warmup 500

# Compare with baseline
umbra test benchmark --baseline previous_results.json

# Save new baseline
umbra test benchmark --save-baseline
```

### AI/ML Validation

```bash
# Validate a model
umbra test validate --model models/classifier.pth

# Validate data
umbra test validate --data datasets/training.csv

# Compute specific metrics
umbra test validate --model models/classifier.pth --metrics accuracy,precision,recall
```

### Coverage Reports

```bash
# Generate HTML coverage report
umbra test coverage --format html --output coverage/

# Generate XML coverage report
umbra test coverage --format xml --output coverage.xml

# Include/exclude files
umbra test coverage --include "src/**" --exclude "src/generated/**"
```

### Cleanup

```bash
# Clean all test artifacts
umbra test clean --all

# Clean specific artifacts
umbra test clean --coverage --temp
```

## Configuration

Tests can be configured using a `test.toml` file in your project root:

```toml
[test]
# Test discovery patterns
patterns = ["**/*_test.umbra", "**/test_*.umbra"]

# Parallel execution
parallel = true
max_threads = 4

# Timeouts
default_timeout = 30
integration_timeout = 300

# Coverage settings
coverage = true
coverage_threshold = 80.0

# Property testing
property_test_cases = 100
max_shrink_iterations = 100

# Performance testing
benchmark_iterations = 1000
benchmark_warmup = 100
regression_threshold = 0.1

[test.ai_ml]
# AI/ML specific settings
model_validation = true
data_validation = true
bias_detection = true
```

## Assertions

The testing framework provides a rich set of assertions:

### Basic Assertions
- `assert_true!(condition, message)`
- `assert_false!(condition, message)`
- `assert_eq!(actual, expected, message)`
- `assert_ne!(actual, expected, message)`

### Option/Result Assertions
- `assert_some!(option, message)`
- `assert_none!(option, message)`
- `assert_ok!(result, message)`
- `assert_error!(result, message)`

### Collection Assertions
- `assert_contains!(collection, item, message)`
- `assert_empty!(collection, message)`
- `assert_length!(collection, expected_length, message)`

### Numeric Assertions
- `assert_approx_eq!(actual, expected, tolerance, message)`
- `assert_greater!(actual, expected, message)`
- `assert_less!(actual, expected, message)`

### AI/ML Assertions
- `assert_accuracy!(predictions, labels, threshold, message)`
- `assert_model_valid!(model, message)`
- `assert_data_quality!(dataset, threshold, message)`

## Best Practices

1. **Test Naming** - Use descriptive names that explain what is being tested
2. **Test Organization** - Group related tests in the same file
3. **Test Independence** - Each test should be independent and not rely on others
4. **Edge Cases** - Always test boundary conditions and edge cases
5. **Error Handling** - Test both success and failure scenarios
6. **Performance** - Use performance tests for critical algorithms
7. **AI/ML Testing** - Validate models, data quality, and bias regularly

## Integration with CI/CD

The testing framework integrates seamlessly with CI/CD pipelines:

```yaml
# GitHub Actions example
- name: Run Tests
  run: |
    umbra test run --format junit --output test-results.xml
    umbra test coverage --format xml --output coverage.xml

- name: Upload Test Results
  uses: actions/upload-artifact@v2
  with:
    name: test-results
    path: test-results.xml

- name: Upload Coverage
  uses: codecov/codecov-action@v1
  with:
    file: coverage.xml
```

## Advanced Features

### Custom Test Runners
You can extend the framework with custom test runners for specialized testing needs.

### Test Fixtures
Set up and tear down test data using setup and teardown functions.

### Test Parameterization
Run the same test with different input parameters.

### Snapshot Testing
Compare outputs against saved snapshots for regression testing.

### Mutation Testing
Verify test quality by introducing code mutations.

For more detailed documentation, see the individual module documentation in the source code.
