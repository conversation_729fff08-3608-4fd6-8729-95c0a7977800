# Umbra Language Reference

This document provides a comprehensive reference for the Umbra programming language syntax and features.

## Table of Contents

1. [Lexical Elements](#lexical-elements)
2. [Types](#types)
3. [Variables](#variables)
4. [Functions](#functions)
5. [Control Flow](#control-flow)
6. [Structures](#structures)
7. [AI/ML Features](#aiml-features)
8. [Built-in Functions](#built-in-functions)

## Lexical Elements

### Comments

```umbra
// Single-line comment
```

### Keywords

**Control Flow:** `when`, `otherwise`, `repeat`, `in`, `return`

**Declarations:** `define`, `let`, `structure`, `bring`

**AI/ML:** `train`, `using`, `evaluate`, `on`, `visualize`, `over`, `export`, `to`, `predict`

**Logical:** `and`, `or`, `not`

**Literals:** `true`, `false`

### Operators

**Assignment:** `:=`

**Arithmetic:** `+`, `-`, `*`, `/`, `%`

**Comparison:** `==`, `!=`, `<`, `<=`, `>`, `>=`

**Logical:** `and`, `or`, `not`

### Punctuation

`(`, `)`, `[`, `]`, `,`, `:`, `->`

## Types

### Basic Types

- `Integer` - Whole numbers (e.g., `42`, `-10`)
- `Float` - Decimal numbers (e.g., `3.14`, `-2.5`)
- `String` - Text (e.g., `"hello"`, `"world"`)
- `Boolean` - True/false values (`true`, `false`)
- `Void` - No return value (for functions)

### AI/ML Types

- `Dataset` - Machine learning dataset
- `Model` - Machine learning model
- `Tensor` - Multi-dimensional array (future feature)

### Collection Types

- `List[T]` - Ordered collection of type T (e.g., `List[Integer]`)

### Type Inference

- `auto` - Automatic type inference (future feature)

## Variables

### Declaration

```umbra
let variable_name: Type := value
```

### Examples

```umbra
let name: String := "Umbra"
let age: Integer := 25
let height: Float := 5.9
let active: Boolean := true
let numbers: List[Integer] := [1, 2, 3]
```

### Assignment

```umbra
variable_name := new_value
```

## Functions

### Definition

```umbra
define function_name(param1: Type1, param2: Type2) -> ReturnType:
    // function body
    return value
```

### Examples

```umbra
define greet(name: String) -> String:
    return "Hello, " + name

define add(a: Integer, b: Integer) -> Integer:
    return a + b

define print_info(name: String, age: Integer) -> Void:
    show "Name:", name
    show "Age:", age
```

### Main Function

Every Umbra program should have a main function:

```umbra
define main() -> Void:
    // program entry point
```

## Control Flow

### When Statement

```umbra
when condition:
    // statements
otherwise:
    // statements
```

### Examples

```umbra
when age >= 18:
    show "Adult"
otherwise:
    show "Minor"

// Nested when statements
when score >= 90:
    show "A"
otherwise:
    when score >= 80:
        show "B"
    otherwise:
        show "C"
```

### Repeat Statement

```umbra
repeat variable in iterable:
    // statements
```

### Examples

```umbra
let numbers: List[Integer] := [1, 2, 3, 4, 5]
repeat num in numbers:
    show num

let range_nums: List[Integer] := range(0, 10)
repeat i in range_nums:
    show "Index:", i
```

## Structures

### Definition

```umbra
structure StructureName:
    field1: Type1
    field2: Type2
    // ...
```

### Usage

```umbra
structure Person:
    name: String
    age: Integer
    email: String

define main() -> Void:
    let alice: Person := Person("Alice", 30, "<EMAIL>")
    show alice.name
    show alice.age
```

## AI/ML Features

### Dataset Operations

```umbra
let dataset: Dataset := load_dataset("path/to/data.csv")
```

### Model Operations

```umbra
let model: Model := create_model("model_type")
```

### Training

```umbra
train model using dataset:
    epochs := 100
    learning_rate := 0.001
    batch_size := 32
    optimizer := "adam"
```

### Evaluation

```umbra
evaluate model on validation_dataset
```

### Visualization

```umbra
visualize loss over epochs
visualize accuracy over time
```

### Export/Import

```umbra
export model to "path/to/model.pkl"
```

### Prediction

```umbra
predict sample using model
```

## Built-in Functions

### I/O Functions

- `show(args...)` - Print values to console
- `read() -> String` - Read input from user
- `write(filename: String, content: String)` - Write to file

### Utility Functions

- `range(start: Integer, end: Integer) -> List[Integer]` - Generate number range

### AI/ML Functions

- `load_dataset(path: String) -> Dataset` - Load dataset from file
- `create_model(type: String) -> Model` - Create ML model

## Expressions

### Literals

```umbra
42              // Integer
3.14            // Float
"hello"         // String
true            // Boolean
[1, 2, 3]       // List
```

### Binary Operations

```umbra
a + b           // Addition
a - b           // Subtraction
a * b           // Multiplication
a / b           // Division
a % b           // Modulo
a == b          // Equality
a != b          // Inequality
a < b           // Less than
a <= b          // Less than or equal
a > b           // Greater than
a >= b          // Greater than or equal
a and b         // Logical AND
a or b          // Logical OR
```

### Unary Operations

```umbra
-a              // Negation
not a           // Logical NOT
```

### Function Calls

```umbra
function_name(arg1, arg2, ...)
```

## Operator Precedence

From highest to lowest:

1. Unary operators (`-`, `not`)
2. Multiplicative (`*`, `/`, `%`)
3. Additive (`+`, `-`)
4. Comparison (`<`, `<=`, `>`, `>=`)
5. Equality (`==`, `!=`)
6. Logical AND (`and`)
7. Logical OR (`or`)

## Indentation

Umbra uses indentation to define code blocks, similar to Python:

```umbra
define example() -> Void:
    when condition:
        show "nested"
        when another_condition:
            show "deeply nested"
    show "back to function level"
```

## Import System

```umbra
bring module_name
```

Note: The import system is currently basic and will be expanded in future versions.

## Error Handling

Umbra provides clear error messages for:

- **Lexical errors** - Invalid characters or tokens
- **Parse errors** - Syntax violations
- **Semantic errors** - Type mismatches, undefined variables
- **Runtime errors** - Execution failures

## Future Features

The following features are planned for future versions:

- Exception handling (`try`/`catch`)
- Generics and templates
- Module system improvements
- Advanced tensor operations
- Async/await for concurrent programming
- Package manager integration
