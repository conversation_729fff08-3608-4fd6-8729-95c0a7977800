# Umbra Programming Language Configuration
# Windows-specific settings

[compiler]
optimization_level = 2
debug_symbols = false
target_cpu = native

[runtime]
memory_limit = 2GB
gc_enabled = true
thread_pool_size = auto

[ai_ml]
python_integration = true
gpu_acceleration = auto
model_cache_dir = %APPDATA%\Umbra\models

[lsp]
enabled = true
port = 9257
log_level = info

[windows]
console_colors = true
file_associations = true
path_integration = true
