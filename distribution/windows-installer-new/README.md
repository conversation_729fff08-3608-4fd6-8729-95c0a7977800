# 🧠 Umbra Programming Language

**A proprietary, enterprise-grade programming language for AI/ML development**

**Created by Eclipse Softworks** | Version 1.0 | **Private Release**

---

## 🎯 Overview

Umbra is an advanced, proprietary programming language engineered exclusively for artificial intelligence and machine learning development. As a closed-source solution from Eclipse Softworks, Umbra delivers enterprise-grade performance, security, and support for mission-critical AI/ML applications.

## ✨ Key Features

- **🚀 Native AI/ML Support** - Built-in keywords: `train`, `evaluate`, `predict`, `visualize`, `export`
- **⚡ High Performance** - LLVM-based compilation to optimized native machine code
- **🔒 Type Safety** - Advanced static typing with compile-time error detection
- **🧮 Specialized Types** - `Dataset`, `Model`, `Tensor` for ML workflows
- **🛠️ Professional Tooling** - Enterprise VSCode extension with IntelliSense and debugging
- **📚 Rich Ecosystem** - Comprehensive standard library and professional documentation
- **🏢 Enterprise Support** - Professional support and licensing from Eclipse Softworks
- **🔐 Secure & Private** - Closed-source with proprietary optimizations

## 🏢 Licensing and Distribution

**⚠️ IMPORTANT**: Umbra is proprietary software owned by Eclipse Softworks.

### License Requirements
- ✅ **Licensed Use Only** - Valid license required from Eclipse Softworks
- ❌ **No Source Code Access** - Source code is proprietary and not available
- ❌ **No Redistribution** - Cannot be redistributed without explicit permission
- ❌ **No Reverse Engineering** - Decompilation and reverse engineering prohibited
- ✅ **Enterprise Support** - Professional support included with license

### Contact for Licensing
- **Email**: <EMAIL>
- **Website**: https://eclipse-softworks.com/umbra
- **Phone**: +27(0) 82 079 1642 

## 📦 Installation

### Professional Installers Available

**Windows (x64)**
```powershell
# Download and run the Windows installer
UmbraInstaller-1.0.0-Windows-x64.exe
```

**macOS (Universal)**
```bash
# Download and install the macOS package
UmbraInstaller-1.0.0-macOS-Universal.pkg
```

**Linux (Multiple Formats)**
```bash
# Universal installer (recommended)
sudo ./umbra-installer-1.0.0-linux-x86_64.sh

# DEB package (Debian/Ubuntu)
sudo dpkg -i umbra-1.0.0-linux-amd64.deb

# RPM package (RHEL/CentOS/Fedora)
sudo rpm -ivh umbra-1.0.0-linux-x86_64.rpm
```

### Using Umbra

```bash
# Run an Umbra program directly
umbra run working_demo.umbra

# Check syntax and types
umbra check examples/basic/hello_world.umbra

# Compile to native binary
umbra build examples/basic/math_only.umbra -o math_demo

# Run the compiled program
./math_demo

# Start Language Server
umbra lsp

# Interactive REPL
umbra repl
```

## 📖 Language Syntax

### Variables and Types
```umbra
let name: String := "Umbra"
let age: Integer := 25
let pi: Float := 3.14159
let active: Boolean := true
```

### Functions
```umbra
define greet(name: String) -> String:
    return "Hello, " + name + "!"

define calculate_accuracy(correct: Integer, total: Integer) -> Integer:
    when total == 0:
        return 0
    otherwise:
        return correct * 100 / total
```

### Control Flow
```umbra
when age >= 18:
    show("Adult")
otherwise:
    show("Minor")

repeat i in range(1, 10):
    show(i)
```

### AI/ML Features
```umbra
// Load and process data
let dataset: Dataset := load_dataset("data.csv")
let model: Model := create_model("neural_network")

// Train the model
train model using dataset:
    epochs := 100
    learning_rate := 0.001
    batch_size := 32
    optimizer := "adam"

// Evaluate and predict
evaluate model on dataset
predict "new_sample_data" using model
visualize "accuracy" by "epoch"
export model to "trained_model.umbra"
```

## 📁 Project Structure

```
Umbra/
├── umbra-compiler/           # Rust-based compiler
│   ├── src/
│   │   ├── lexer.rs         # Tokenization
│   │   ├── parser.rs        # AST generation
│   │   ├── semantic.rs      # Type checking and analysis
│   │   ├── codegen.rs       # LLVM code generation
│   │   ├── lsp.rs           # Language Server Protocol
│   │   └── main.rs          # CLI interface
│   └── Cargo.toml           # Rust dependencies
├── umbra-vscode-extension/   # VSCode extension
│   ├── src/extension.ts     # Extension logic
│   ├── syntaxes/           # Syntax highlighting
│   ├── icons/              # Professional file icons
│   └── package.json        # Extension manifest
├── examples/               # Example Umbra programs
│   ├── basic/             # Basic language features
│   ├── ai_ml/             # AI/ML demonstrations
│   ├── advanced/          # Advanced features
│   └── stdlib/            # Standard library examples
├── distribution/          # Ready-to-distribute files
├── working_demo.umbra     # Main demonstration program
└── README.md             # This file
```

## 🎯 Development Status

🎉 **PRODUCTION READY!** - Umbra is a fully working programming language with complete implementation!

### ✅ Completed Features

**Core Language:**
- ✅ Complete lexer, parser, and semantic analyzer
- ✅ LLVM-based code generation for native binaries
- ✅ Static type system with comprehensive checking
- ✅ Variables, functions, control flow, loops, and structures
- ✅ Error handling with detailed diagnostics

**AI/ML Specialization:**
- ✅ Domain-specific keywords: `train`, `evaluate`, `predict`, `visualize`, `export`
- ✅ AI/ML data types: `Dataset`, `Model`, `Tensor`
- ✅ Specialized syntax for machine learning workflows
- ✅ Working AI/ML demonstration programs

**Development Tools:**
- ✅ Complete CLI with `run`, `build`, `check`, `lsp`, `repl` commands
- ✅ Language Server Protocol (LSP) support
- ✅ Professional VSCode extension with syntax highlighting
- ✅ Custom file icons (no white background)
- ✅ IntelliSense and error detection
- ✅ Comprehensive example programs

**Production Quality:**
- ✅ Memory-efficient design for low-RAM systems
- ✅ Professional Eclipse Softworks branding
- ✅ Ready-to-distribute packages
- ✅ Complete documentation and examples

### 🚀 Try Umbra Now

```bash
# Run the main AI/ML demonstration
umbra run working_demo.umbra

# Try the comprehensive AI demo
umbra run umbra_ai_demo.umbra

# Explore examples
ls examples/
umbra run examples/ai_ml/basic_ml_workflow.umbra
umbra run examples/basic/hello_world.umbra
umbra run examples/advanced/fibonacci.umbra

# Test keyword highlighting precision
code test_highlighting.umbra
```

## 📚 Example Programs

**Working Demonstrations:**
- `working_demo.umbra` - Complete AI/ML calculator with model evaluation
- `umbra_ai_demo.umbra` - Comprehensive AI workflow demonstration
- `test_highlighting.umbra` - Keyword highlighting precision test

**Basic Examples:**
- `examples/basic/hello_world.umbra` - Simple "Hello, World!" program
- `examples/basic/math_only.umbra` - Mathematical operations
- `examples/basic/control_flow.umbra` - Conditional logic and loops

**AI/ML Examples:**
- `examples/ai_ml/basic_ml_workflow.umbra` - Complete ML pipeline
- `examples/ai_ml/advanced_ai.umbra` - Advanced AI features
- `examples/ai_ml/eclipse_softworks_demo.umbra` - Professional demo

**Advanced Examples:**
- `examples/advanced/fibonacci.umbra` - Recursive and iterative algorithms
- `examples/advanced/performance_test.umbra` - Performance benchmarks
- `examples/stdlib/comprehensive_demo.umbra` - Standard library features

## 🛠️ VSCode Extension

Install the professional Umbra language support:

```bash
# Install from VSIX
code --install-extension distribution/umbra-language-support-1.0.0.vsix

# Features included:
# - Syntax highlighting with precise keyword detection
# - Professional file icons (transparent background)
# - Language Server Protocol support
# - IntelliSense and error detection
# - Eclipse Softworks branding
```

## 📄 License & Legal

**⚠️ PROPRIETARY SOFTWARE** - This software is proprietary to Eclipse Softworks.

### License Requirements
- ✅ **Licensed Use Only** - Valid license required from Eclipse Softworks
- ❌ **No Source Code Access** - Source code is proprietary and not available
- ❌ **No Redistribution** - Cannot be redistributed without explicit permission
- ❌ **No Reverse Engineering** - Decompilation and reverse engineering prohibited

### Contact for Licensing
- **Email**: <EMAIL>
- **Phone**: ******-ECLIPSE
- **Website**: https://eclipse-softworks.com/umbra

**© 2025 Eclipse Softworks. All rights reserved.**

---

*Umbra Programming Language - The future of enterprise AI/ML development*
