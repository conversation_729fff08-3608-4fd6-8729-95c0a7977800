<?xml version="1.0" encoding="UTF-8"?>
<svg width="256" height="256" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Gradient for the main shape -->
    <linearGradient id="mainGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#764ba2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f093fb;stop-opacity:1" />
    </linearGradient>
    
    <!-- Shadow gradient -->
    <linearGradient id="shadowGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2d3748;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#1a202c;stop-opacity:0.9" />
    </linearGradient>
    
    <!-- Neural network pattern -->
    <pattern id="neuralPattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse">
      <circle cx="10" cy="10" r="1" fill="#ffffff" opacity="0.3"/>
      <circle cx="5" cy="5" r="0.5" fill="#ffffff" opacity="0.2"/>
      <circle cx="15" cy="15" r="0.5" fill="#ffffff" opacity="0.2"/>
    </pattern>
  </defs>
  
  <!-- Background circle -->
  <circle cx="128" cy="128" r="120" fill="url(#mainGradient)" stroke="#ffffff" stroke-width="4"/>
  
  <!-- Shadow/Umbra element -->
  <path d="M 60 128 Q 128 60 196 128 Q 128 196 60 128 Z" fill="url(#shadowGradient)" opacity="0.7"/>
  
  <!-- Neural network overlay -->
  <circle cx="128" cy="128" r="100" fill="url(#neuralPattern)" opacity="0.4"/>
  
  <!-- Central "U" letter -->
  <g transform="translate(128, 128)">
    <!-- Main U shape -->
    <path d="M -30 -40 L -30 10 Q -30 30 -10 30 L 10 30 Q 30 30 30 10 L 30 -40" 
          stroke="#ffffff" stroke-width="8" fill="none" stroke-linecap="round"/>
    
    <!-- AI/ML accent dots -->
    <circle cx="-20" cy="-30" r="3" fill="#ffffff" opacity="0.8"/>
    <circle cx="0" cy="-35" r="2" fill="#ffffff" opacity="0.6"/>
    <circle cx="20" cy="-30" r="3" fill="#ffffff" opacity="0.8"/>
    
    <!-- Connection lines (representing neural connections) -->
    <line x1="-20" y1="-30" x2="0" y2="-35" stroke="#ffffff" stroke-width="1" opacity="0.5"/>
    <line x1="0" y1="-35" x2="20" y2="-30" stroke="#ffffff" stroke-width="1" opacity="0.5"/>
  </g>
  
  <!-- Outer glow effect -->
  <circle cx="128" cy="128" r="120" fill="none" stroke="url(#mainGradient)" stroke-width="2" opacity="0.5"/>
</svg>
