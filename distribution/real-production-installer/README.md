# Umbra Programming Language - Complete Development Environment

**Modern, AI/ML-focused compiled programming language designed for high-performance applications**

## What's Included

This installer provides a complete Umbra development environment:

### Core Components
- **Umbra Compiler & Runtime** (92MB) - Full-featured compiler with all capabilities
- **Language Server Protocol (LSP)** - IDE integration support
- **Interactive REPL** - Real-time development and testing
- **AI/ML Integration** - Built-in machine learning primitives

### Development Tools
- **Visual C++ Redistributable 2022** - Required runtime libraries
- **Python 3.11.9** - For AI/ML interoperability
- **Comprehensive AI/ML Packages** - NumPy, Pandas, Scikit-learn, TensorFlow, PyTorch
- **Git for Windows** - Version control system
- **Visual Studio Code** - Recommended IDE with Umbra support

## Key Features

### AI/ML Capabilities
- High-performance machine learning applications
- Seamless Python/NumPy interoperability
- GPU acceleration and parallel computing
- Built-in training and inference primitives

### Development Features
- Type-safe systems programming
- Advanced debugging and profiling tools
- Cross-platform compatibility
- Comprehensive standard library

## Available Commands

```bash
# Core compilation
umbra build source.umbra          # Compile to native binary
umbra run source.umbra            # Compile and run
umbra check source.umbra          # Syntax and type checking

# Development tools
umbra repl                        # Interactive REPL
umbra lsp                         # Start LSP server
umbra debug program.umbra         # Debug program

# Project management
umbra init my-project             # Initialize new project
umbra project                     # Build current project
umbra package                     # Package management

# AI/ML integration
umbra ai                          # AI/ML ecosystem tools
umbra test                        # Testing framework
```

## Quick Start

1. **Verify Installation**
   ```cmd
   umbra --version
   ```

2. **Start Interactive REPL**
   ```cmd
   umbra repl
   ```

3. **Create Your First Program**
   ```umbra
   // hello.umbra
   fn main() -> void {
       show("Hello, Umbra Programming Language!")
   }
   ```

4. **Run Your Program**
   ```cmd
   umbra run hello.umbra
   ```

## AI/ML Development

Umbra provides native AI/ML capabilities:

```umbra
// ai_example.umbra
bring ml
bring std.io

fn main() -> void {
    // Load dataset
    let dataset := load_dataset("data/training.csv")
    
    // Train model
    let model := train linear_regression using dataset {
        features: ["x1", "x2", "x3"],
        target: "y",
        test_size: 0.2
    }
    
    // Evaluate
    let accuracy := evaluate model with dataset
    show("Model accuracy: " + accuracy.to_string() + "%")
}
```

## System Requirements

- **Windows 10/11** (64-bit)
- **4 GB RAM** minimum (8 GB recommended)
- **2 GB disk space** for complete installation
- **Internet connection** for initial setup and package downloads

## Support & Documentation

- **Website**: https://github.com/eclipse-softworks/umbra
- **Documentation**: Complete language reference and tutorials
- **Examples**: Comprehensive example programs included
- **Support**: Professional support available

## License

This software is licensed under a proprietary license agreement.
See LICENSE file for complete terms and conditions.

**Copyright (c) 2025 Eclipse Softworks. All rights reserved.**
