@echo off
echo ================================================================
echo Umbra Programming Language - Comprehensive AI/ML Package Installer
echo ================================================================
echo.
echo This will install a complete AI/ML development environment including:
echo - NumPy, Pandas, SciPy (Scientific Computing)
echo - Scikit-learn, XGBoost (Machine Learning)
echo - TensorFlow, PyTorch (Deep Learning)
echo - Matplotlib, Seaborn, Plotly (Visualization)
echo - Ju<PERSON><PERSON>, JupyterLab (Interactive Development)
echo - And many more packages...
echo.

cd /d "%~dp0"

echo Upgrading pip and core tools...
python -m pip install --upgrade pip setuptools wheel

echo.
echo Installing comprehensive AI/ML packages...
echo This may take 10-15 minutes depending on your internet connection.
echo.

python -m pip install --find-links . --no-index -r requirements.txt

if %errorlevel% equ 0 (
    echo.
    echo ================================================================
    echo ✅ Comprehensive AI/ML environment installed successfully!
    echo ================================================================
    echo.
    echo Testing core packages...
    python -c "import numpy, pandas, sklearn, matplotlib, tensorflow; print('✅ Core packages working!')"
    echo.
    echo Your Umbra AI/ML development environment is ready!
    echo.
    echo Quick test commands:
    echo   python -c "import numpy as np; print('NumPy version:', np.__version__)"
    echo   python -c "import pandas as pd; print('Pandas version:', pd.__version__)"
    echo   python -c "import sklearn; print('Scikit-learn version:', sklearn.__version__)"
    echo.
) else (
    echo.
    echo ================================================================
    echo ❌ Some packages failed to install
    echo ================================================================
    echo Please check the error messages above and try installing manually:
    echo   pip install numpy pandas scikit-learn matplotlib tensorflow
    echo.
)

echo.
echo Installation complete. Press any key to continue...
pause >nul
