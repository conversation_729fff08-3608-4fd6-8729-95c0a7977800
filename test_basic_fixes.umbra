// Test Basic Fixes - Verify core functionality works
// Tests the fixes without relying on complex module imports

show("Basic Fixes Test")
show("=================")

// Test 1: AI/ML C Runtime Integration
show("Testing AI/ML C Runtime Integration...")

let customer_data: Dataset := load_dataset("data/customer_churn.csv")
let test_model: Model := create_model("random_forest")

show("Training model with real Python backend...")
train test_model using customer_data:
    n_estimators := 100
    max_depth := 10

show("Evaluating model with real metrics...")
evaluate test_model on customer_data

show("Making prediction with real model...")
predict "test_sample" using test_model

// Test 2: Function Definition and Calling
show("Testing Function Definition and Calling...")

fn multiply_numbers(a: Integer, b: Integer) -> Integer:
    let result: Integer := a * b
    return result

fn format_message(name: String, age: Integer) -> String:
    let message: String := name + " is years old"
    return message

let product: Integer := multiply_numbers(8, 7)
show("multiply_numbers(8, 7) = ", product)

let message: String := format_message("Bob", 30)
show("format_message result: ", message)

// Test 3: Basic Mathematical Operations (using builtins)
show("Testing Basic Mathematical Operations...")

let test_number: Integer := -15
let absolute_value: Integer := abs(test_number)
show("abs(-15) = ", absolute_value)

// Test 4: Standard Library Functions (already available)
show("Testing Standard Library Functions...")

let test_string: String := "Hello, World!"
let string_length: Integer := str_len(test_string)
show("str_len('Hello, World!') = ", string_length)

show("All basic fixes tested successfully!")
show("Core functionality is working correctly.")
