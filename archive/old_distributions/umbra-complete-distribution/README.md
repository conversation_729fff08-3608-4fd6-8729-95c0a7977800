# 🚀 Umbra Programming Language - Complete Distribution

Welcome to the **Umbra Programming Language** complete distribution package! This package contains everything you need to start programming in Umbra on Windows, Linux, and macOS.

## 📦 What's Included

### 🔧 Compilers & Tools
- **`binaries/umbra-windows.exe`** - Windows compiler (6.6MB)
- **`binaries/umbra-linux`** - Linux compiler
- **`tools/windows-compilation/`** - Complete Windows development environment (100MB)
  - MinGW GCC toolchain
  - Windows runtime libraries
  - Compilation scripts

### 📚 Documentation
- **`documentation/Umbra_Programming_Language_Complete_Reference_v1.2.1_with_Logo.pdf`** - Complete reference guide (540KB)
- **`documentation/*.md`** - Technical documentation and guides
- **`logos/`** - Official Umbra logos and branding

### 💡 Examples
- **`examples/*.umbra`** - Example Umbra programs
- **`examples/test_simple.exe`** - Compiled Windows example

### 🛠️ Source Code
- **`source/`** - Umbra compiler source code (Rust)

---

## 🚀 Quick Start

### Windows Users

1. **Download the Windows compiler:**
   ```
   binaries/umbra-windows.exe
   ```

2. **For full development environment:**
   - Copy `tools/windows-compilation/` to your Windows machine
   - Open Command Prompt in that directory
   - Run: `compile.bat your_program.umbra`

3. **Example:**
   ```batch
   compile.bat examples\hello.umbra
   ```

### Linux Users

1. **Use the Linux compiler:**
   ```bash
   chmod +x binaries/umbra-linux
   ./binaries/umbra-linux build examples/hello.umbra
   ```

2. **Cross-compile for Windows:**
   ```bash
   ./tools/windows-compilation/compile_windows.sh examples/hello.umbra
   ```

---

## 📖 Learning Umbra

### Basic Syntax
```umbra
// Hello World
fn main() {
    show("Hello, World!");
    
    let name: String = "Umbra";
    let version: String = "1.2.1";
    
    show("Welcome to ", name, " v", version);
}
```

### Variables and Types
```umbra
fn main() {
    let x: Integer = 42;
    let y: Float = 3.14;
    let message: String = "Hello";
    let active: Boolean = true;
    
    show("Integer: ", x);
    show("Float: ", y);
    show("String: ", message);
    show("Boolean: ", active);
}
```

### Functions
```umbra
fn add(a: Integer, b: Integer) -> Integer {
    return a + b;
}

fn main() {
    let result: Integer = add(10, 20);
    show("Result: ", result);
}
```

### AI/ML Features
```umbra
fn main() {
    let data: Array<Float> = [1.0, 2.0, 3.0, 4.0, 5.0];
    
    // Train a simple model
    train model with data;
    
    // Make predictions
    let prediction: Float = predict model with 6.0;
    show("Prediction: ", prediction);
}
```

---

## 🛠️ Installation Guide

### Windows Installation

1. **Simple Installation:**
   - Download `binaries/umbra-windows.exe`
   - Place it in a folder (e.g., `C:\Umbra\`)
   - Add the folder to your Windows PATH
   - Open Command Prompt and run: `umbra-windows --version`

2. **Complete Development Environment:**
   - Extract `tools/windows-compilation/` to `C:\Umbra\`
   - Open Command Prompt in that directory
   - Run: `compile.bat examples\hello.umbra`
   - Your program will be compiled to `hello.exe`

### Linux Installation

1. **Download and install:**
   ```bash
   chmod +x binaries/umbra-linux
   sudo cp binaries/umbra-linux /usr/local/bin/umbra
   ```

2. **Verify installation:**
   ```bash
   umbra --version
   ```

3. **Compile programs:**
   ```bash
   umbra build your_program.umbra
   ```

---

## 📋 System Requirements

### Windows
- **OS:** Windows 10/11 (64-bit)
- **RAM:** 4GB minimum, 8GB recommended
- **Storage:** 200MB for compiler, 1GB for full development environment
- **Dependencies:** None (self-contained)

### Linux
- **OS:** Ubuntu 20.04+, Debian 11+, or equivalent
- **RAM:** 4GB minimum, 8GB recommended
- **Storage:** 100MB for compiler
- **Dependencies:** GCC, LLVM (auto-installed)

---

## 🔧 Advanced Usage

### Command Line Options
```bash
umbra --help                    # Show help
umbra --version                 # Show version
umbra build program.umbra       # Compile program
umbra build --verbose program.umbra  # Verbose compilation
umbra build --output myapp program.umbra  # Custom output name
```

### Project Structure
```
my-umbra-project/
├── src/
│   ├── main.umbra
│   └── utils.umbra
├── examples/
│   └── demo.umbra
└── README.md
```

### Building Projects
```bash
umbra build src/main.umbra --output my-app
```

---

## 🌐 Resources

- **Official Website:** https://umbra-lang.org (coming soon)
- **Documentation:** See `documentation/` folder
- **Examples:** See `examples/` folder
- **Source Code:** See `source/` folder

---

## 🤝 Support

### Getting Help
1. Check the complete reference guide in `documentation/`
2. Look at examples in `examples/` folder
3. Review the technical documentation

### Reporting Issues
- Describe your problem clearly
- Include your operating system
- Provide the Umbra code that's causing issues
- Include any error messages

---

## 📄 License

Umbra Programming Language is released under the MIT License.

---

## 🎉 Welcome to Umbra!

You're now ready to start programming in Umbra! Begin with the examples in the `examples/` folder, then explore the complete reference guide for advanced features.

**Happy coding!** 🚀

---

*Umbra Programming Language v1.2.1 - Eclipse Softworks*
