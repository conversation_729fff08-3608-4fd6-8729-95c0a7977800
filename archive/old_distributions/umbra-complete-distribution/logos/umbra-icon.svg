<?xml version="1.0" encoding="UTF-8"?>
<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Main gradient -->
    <linearGradient id="iconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    
    <!-- Shadow -->
    <linearGradient id="iconShadow" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2d3748;stop-opacity:0.6" />
      <stop offset="100%" style="stop-color:#1a202c;stop-opacity:0.8" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="128" height="128" rx="20" fill="url(#iconGradient)"/>
  
  <!-- Shadow element -->
  <path d="M 20 64 Q 64 20 108 64 Q 64 108 20 64 Z" fill="url(#iconShadow)" opacity="0.5"/>
  
  <!-- Main "U" -->
  <g transform="translate(64, 64)">
    <path d="M -20 -25 L -20 5 Q -20 20 -5 20 L 5 20 Q 20 20 20 5 L 20 -25" 
          stroke="#ffffff" stroke-width="6" fill="none" stroke-linecap="round"/>
    
    <!-- AI accent -->
    <circle cx="0" cy="-30" r="2" fill="#ffffff" opacity="0.8"/>
    <circle cx="-12" cy="-20" r="1.5" fill="#ffffff" opacity="0.6"/>
    <circle cx="12" cy="-20" r="1.5" fill="#ffffff" opacity="0.6"/>
  </g>
</svg>
