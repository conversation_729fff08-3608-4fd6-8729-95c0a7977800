# 🪟 Umbra for Windows - Setup Guide

## 🚀 Quick Setup (Recommended)

### Option 1: Simple Compiler Only
1. Download `binaries/umbra-windows.exe`
2. Create folder: `C:\Umbra\`
3. Copy `umbra-windows.exe` to `C:\Umbra\`
4. Add `C:\Umbra\` to Windows PATH
5. Test: Open Command Prompt, run `umbra-windows --version`

### Option 2: Complete Development Environment
1. Extract `tools/windows-compilation/` to `C:\Umbra\`
2. Open Command Prompt in `C:\Umbra\`
3. Test: Run `compile.bat examples\hello.umbra`
4. Your program compiles to `hello.exe`

## 📋 Step-by-Step Instructions

### Adding to Windows PATH
1. Press `Win + R`, type `sysdm.cpl`, press Enter
2. Click "Environment Variables"
3. Under "System Variables", find "Path", click "Edit"
4. Click "New", add `C:\Umbra\`
5. Click "OK" on all dialogs
6. Restart Command Prompt

### First Program
1. Create `hello.umbra`:
```umbra
fn main() {
    show("Hello from Umbra on Windows!");
}
```

2. Compile:
```batch
compile.bat hello.umbra
```

3. Run:
```batch
hello.exe
```

## 🛠️ Troubleshooting

### "Command not found"
- Make sure you added Umbra to your PATH
- Restart Command Prompt after changing PATH
- Use full path: `C:\Umbra\umbra-windows.exe`

### "Missing DLL"
- Use the complete development environment
- All dependencies are included in `tools/windows-compilation/`

### Compilation Errors
- Make sure your `.umbra` file syntax is correct
- Check the examples in `examples/` folder
- Use `--verbose` flag for detailed error messages

## ✅ Verification

Test your installation:
```batch
umbra-windows --version
compile.bat examples\test_simple.umbra
test_simple.exe
```

You should see:
```
umbra 1.0.1
HellofromUmbracompiledonWindows!
Sum:10+20=30
```

🎉 **Success! Umbra is working on Windows!**
