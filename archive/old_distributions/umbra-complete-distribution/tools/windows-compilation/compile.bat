@echo off
echo Umbra Windows Compilation Environment
echo ====================================

REM Set up environment
set PATH=%~dp0bin;%PATH%
set UMBRA_HOME=%~dp0
set MINGW_HOME=%~dp0

REM Check if Umbra file is provided
if "%1"=="" (
    echo Usage: compile.bat program.umbra
    echo Example: compile.bat hello.umbra
    exit /b 1
)

REM Check if input file exists
if not exist "%1" (
    echo Error: File %1 not found
    exit /b 1
)

REM Get filename without extension
for %%f in ("%1") do set "filename=%%~nf"

echo Compiling %1 to %filename%.exe...
echo.

REM Compile with Umbra
bin\umbra.exe build "%1" --output "%filename%.exe" --verbose

if %errorlevel% equ 0 (
    echo.
    echo ✓ Compilation successful!
    echo Output: %filename%.exe
    
    REM Test the executable
    echo.
    echo Testing executable...
    "%filename%.exe"
    
    if %errorlevel% equ 0 (
        echo ✓ Program executed successfully!
    ) else (
        echo ✗ Program execution failed
    )
) else (
    echo ✗ Compilation failed
    exit /b 1
)

pause
