/// File system utilities for Umbra standard library
/// 
/// This module provides comprehensive file system operations including
/// file I/O, directory management, path manipulation, and file metadata.

use crate::error::{UmbraError, UmbraResult};
use std::fs::{self, File, OpenOptions};
use std::io::{<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, BufWriter};
use std::path::{Path, PathBuf};
use std::time::SystemTime;

/// File system utilities
pub struct FileSystemUtils;

impl FileSystemUtils {
    /// Read entire file content as string
    pub fn read_to_string<P: AsRef<Path>>(path: P) -> UmbraResult<String> {
        fs::read_to_string(path)
            .map_err(|e| UmbraError::Runtime(format!("Failed to read file: {}", e)))
    }
    
    /// Read file content as bytes
    pub fn read_to_bytes<P: AsRef<Path>>(path: P) -> UmbraResult<Vec<u8>> {
        fs::read(path)
            .map_err(|e| UmbraError::Runtime(format!("Failed to read file: {}", e)))
    }
    
    /// Write string content to file
    pub fn write_string<P: AsRef<Path>>(path: P, content: &str) -> UmbraResult<()> {
        fs::write(path, content)
            .map_err(|e| UmbraError::Runtime(format!("Failed to write file: {}", e)))
    }
    
    /// Write bytes to file
    pub fn write_bytes<P: AsRef<Path>>(path: P, content: &[u8]) -> UmbraResult<()> {
        fs::write(path, content)
            .map_err(|e| UmbraError::Runtime(format!("Failed to write file: {}", e)))
    }
    
    /// Append string content to file
    pub fn append_string<P: AsRef<Path>>(path: P, content: &str) -> UmbraResult<()> {
        let mut file = OpenOptions::new()
            .create(true)
            .append(true)
            .open(path)
            .map_err(|e| UmbraError::Runtime(format!("Failed to open file for append: {}", e)))?;
        
        file.write_all(content.as_bytes())
            .map_err(|e| UmbraError::Runtime(format!("Failed to append to file: {}", e)))
    }
    
    /// Read file line by line
    pub fn read_lines<P: AsRef<Path>>(path: P) -> UmbraResult<Vec<String>> {
        let file = File::open(path)
            .map_err(|e| UmbraError::Runtime(format!("Failed to open file: {}", e)))?;
        
        let reader = BufReader::new(file);
        let mut lines = Vec::new();
        
        for line in reader.lines() {
            let line = line
                .map_err(|e| UmbraError::Runtime(format!("Failed to read line: {}", e)))?;
            lines.push(line);
        }
        
        Ok(lines)
    }
    
    /// Write lines to file
    pub fn write_lines<P: AsRef<Path>>(path: P, lines: &[String]) -> UmbraResult<()> {
        let file = File::create(path)
            .map_err(|e| UmbraError::Runtime(format!("Failed to create file: {}", e)))?;
        
        let mut writer = BufWriter::new(file);
        
        for line in lines {
            writeln!(writer, "{}", line)
                .map_err(|e| UmbraError::Runtime(format!("Failed to write line: {}", e)))?;
        }
        
        writer.flush()
            .map_err(|e| UmbraError::Runtime(format!("Failed to flush writer: {}", e)))
    }
    
    /// Check if file exists
    pub fn exists<P: AsRef<Path>>(path: P) -> bool {
        path.as_ref().exists()
    }
    
    /// Check if path is a file
    pub fn is_file<P: AsRef<Path>>(path: P) -> bool {
        path.as_ref().is_file()
    }
    
    /// Check if path is a directory
    pub fn is_dir<P: AsRef<Path>>(path: P) -> bool {
        path.as_ref().is_dir()
    }
    
    /// Get file size in bytes
    pub fn file_size<P: AsRef<Path>>(path: P) -> UmbraResult<u64> {
        let metadata = fs::metadata(path)
            .map_err(|e| UmbraError::Runtime(format!("Failed to get file metadata: {}", e)))?;
        
        Ok(metadata.len())
    }
    
    /// Get file modification time
    pub fn modified_time<P: AsRef<Path>>(path: P) -> UmbraResult<SystemTime> {
        let metadata = fs::metadata(path)
            .map_err(|e| UmbraError::Runtime(format!("Failed to get file metadata: {}", e)))?;
        
        metadata.modified()
            .map_err(|e| UmbraError::Runtime(format!("Failed to get modification time: {}", e)))
    }
    
    /// Get file creation time
    pub fn created_time<P: AsRef<Path>>(path: P) -> UmbraResult<SystemTime> {
        let metadata = fs::metadata(path)
            .map_err(|e| UmbraError::Runtime(format!("Failed to get file metadata: {}", e)))?;
        
        metadata.created()
            .map_err(|e| UmbraError::Runtime(format!("Failed to get creation time: {}", e)))
    }
    
    /// Create directory
    pub fn create_dir<P: AsRef<Path>>(path: P) -> UmbraResult<()> {
        fs::create_dir(path)
            .map_err(|e| UmbraError::Runtime(format!("Failed to create directory: {}", e)))
    }
    
    /// Create directory and all parent directories
    pub fn create_dir_all<P: AsRef<Path>>(path: P) -> UmbraResult<()> {
        fs::create_dir_all(path)
            .map_err(|e| UmbraError::Runtime(format!("Failed to create directories: {}", e)))
    }
    
    /// Remove file
    pub fn remove_file<P: AsRef<Path>>(path: P) -> UmbraResult<()> {
        fs::remove_file(path)
            .map_err(|e| UmbraError::Runtime(format!("Failed to remove file: {}", e)))
    }
    
    /// Remove directory
    pub fn remove_dir<P: AsRef<Path>>(path: P) -> UmbraResult<()> {
        fs::remove_dir(path)
            .map_err(|e| UmbraError::Runtime(format!("Failed to remove directory: {}", e)))
    }
    
    /// Remove directory and all contents
    pub fn remove_dir_all<P: AsRef<Path>>(path: P) -> UmbraResult<()> {
        fs::remove_dir_all(path)
            .map_err(|e| UmbraError::Runtime(format!("Failed to remove directory tree: {}", e)))
    }
    
    /// Copy file
    pub fn copy_file<P: AsRef<Path>, Q: AsRef<Path>>(from: P, to: Q) -> UmbraResult<u64> {
        fs::copy(from, to)
            .map_err(|e| UmbraError::Runtime(format!("Failed to copy file: {}", e)))
    }
    
    /// Rename/move file or directory
    pub fn rename<P: AsRef<Path>, Q: AsRef<Path>>(from: P, to: Q) -> UmbraResult<()> {
        fs::rename(from, to)
            .map_err(|e| UmbraError::Runtime(format!("Failed to rename/move: {}", e)))
    }
    
    /// List directory contents
    pub fn list_dir<P: AsRef<Path>>(path: P) -> UmbraResult<Vec<PathBuf>> {
        let entries = fs::read_dir(path)
            .map_err(|e| UmbraError::Runtime(format!("Failed to read directory: {}", e)))?;
        
        let mut paths = Vec::new();
        for entry in entries {
            let entry = entry
                .map_err(|e| UmbraError::Runtime(format!("Failed to read directory entry: {}", e)))?;
            paths.push(entry.path());
        }
        
        Ok(paths)
    }
    
    /// List directory contents with metadata
    pub fn list_dir_detailed<P: AsRef<Path>>(path: P) -> UmbraResult<Vec<FileInfo>> {
        let entries = fs::read_dir(path)
            .map_err(|e| UmbraError::Runtime(format!("Failed to read directory: {}", e)))?;
        
        let mut file_infos = Vec::new();
        for entry in entries {
            let entry = entry
                .map_err(|e| UmbraError::Runtime(format!("Failed to read directory entry: {}", e)))?;
            
            let path = entry.path();
            let metadata = entry.metadata()
                .map_err(|e| UmbraError::Runtime(format!("Failed to get metadata: {}", e)))?;
            
            let file_info = FileInfo {
                path: path.clone(),
                name: path.file_name()
                    .and_then(|n| n.to_str())
                    .unwrap_or("")
                    .to_string(),
                is_file: metadata.is_file(),
                is_dir: metadata.is_dir(),
                size: metadata.len(),
                modified: metadata.modified().ok(),
                created: metadata.created().ok(),
            };
            
            file_infos.push(file_info);
        }
        
        Ok(file_infos)
    }
    
    /// Get current working directory
    pub fn current_dir() -> UmbraResult<PathBuf> {
        std::env::current_dir()
            .map_err(|e| UmbraError::Runtime(format!("Failed to get current directory: {}", e)))
    }
    
    /// Change current working directory
    pub fn set_current_dir<P: AsRef<Path>>(path: P) -> UmbraResult<()> {
        std::env::set_current_dir(path)
            .map_err(|e| UmbraError::Runtime(format!("Failed to change directory: {}", e)))
    }
    
    /// Get absolute path
    pub fn absolute_path<P: AsRef<Path>>(path: P) -> UmbraResult<PathBuf> {
        path.as_ref().canonicalize()
            .map_err(|e| UmbraError::Runtime(format!("Failed to get absolute path: {}", e)))
    }
    
    /// Join path components
    pub fn join_paths<P: AsRef<Path>>(base: P, components: &[&str]) -> PathBuf {
        let mut path = base.as_ref().to_path_buf();
        for component in components {
            path.push(component);
        }
        path
    }
    
    /// Get file extension
    pub fn get_extension<P: AsRef<Path>>(path: P) -> Option<String> {
        path.as_ref()
            .extension()
            .and_then(|ext| ext.to_str())
            .map(|ext| ext.to_string())
    }
    
    /// Get file name without extension
    pub fn get_stem<P: AsRef<Path>>(path: P) -> Option<String> {
        path.as_ref()
            .file_stem()
            .and_then(|stem| stem.to_str())
            .map(|stem| stem.to_string())
    }
    
    /// Get parent directory
    pub fn get_parent<P: AsRef<Path>>(path: P) -> Option<PathBuf> {
        path.as_ref().parent().map(|p| p.to_path_buf())
    }
    
    /// Check if path is absolute
    pub fn is_absolute<P: AsRef<Path>>(path: P) -> bool {
        path.as_ref().is_absolute()
    }
    
    /// Check if path is relative
    pub fn is_relative<P: AsRef<Path>>(path: P) -> bool {
        path.as_ref().is_relative()
    }
    
    /// Find files matching a pattern (simple glob)
    pub fn find_files<P: AsRef<Path>>(dir: P, pattern: &str) -> UmbraResult<Vec<PathBuf>> {
        let mut matches = Vec::new();
        Self::find_files_recursive(dir.as_ref(), pattern, &mut matches)?;
        Ok(matches)
    }
    
    /// Recursive helper for find_files
    fn find_files_recursive(dir: &Path, pattern: &str, matches: &mut Vec<PathBuf>) -> UmbraResult<()> {
        let entries = fs::read_dir(dir)
            .map_err(|e| UmbraError::Runtime(format!("Failed to read directory: {}", e)))?;
        
        for entry in entries {
            let entry = entry
                .map_err(|e| UmbraError::Runtime(format!("Failed to read directory entry: {}", e)))?;
            
            let path = entry.path();
            
            if path.is_file() {
                if let Some(name) = path.file_name().and_then(|n| n.to_str()) {
                    if Self::matches_pattern(name, pattern) {
                        matches.push(path);
                    }
                }
            } else if path.is_dir() {
                Self::find_files_recursive(&path, pattern, matches)?;
            }
        }
        
        Ok(())
    }
    
    /// Simple pattern matching (supports * wildcard)
    fn matches_pattern(name: &str, pattern: &str) -> bool {
        if pattern == "*" {
            return true;
        }
        
        if !pattern.contains('*') {
            return name == pattern;
        }
        
        let parts: Vec<&str> = pattern.split('*').collect();
        if parts.is_empty() {
            return true;
        }
        
        let mut pos = 0;
        for (i, part) in parts.iter().enumerate() {
            if part.is_empty() {
                continue;
            }
            
            if i == 0 {
                // First part must match from the beginning
                if !name[pos..].starts_with(part) {
                    return false;
                }
                pos += part.len();
            } else if i == parts.len() - 1 {
                // Last part must match at the end
                return name[pos..].ends_with(part);
            } else {
                // Middle parts must be found somewhere
                if let Some(found_pos) = name[pos..].find(part) {
                    pos += found_pos + part.len();
                } else {
                    return false;
                }
            }
        }
        
        true
    }
}

/// File information structure
#[derive(Debug, Clone)]
pub struct FileInfo {
    pub path: PathBuf,
    pub name: String,
    pub is_file: bool,
    pub is_dir: bool,
    pub size: u64,
    pub modified: Option<SystemTime>,
    pub created: Option<SystemTime>,
}

/// Global file I/O functions for easy access
pub fn read_file<P: AsRef<Path>>(path: P) -> UmbraResult<String> {
    FileSystemUtils::read_to_string(path)
}

pub fn write_file<P: AsRef<Path>>(path: P, content: &str) -> UmbraResult<()> {
    FileSystemUtils::write_string(path, content)
}

pub fn append_file<P: AsRef<Path>>(path: P, content: &str) -> UmbraResult<()> {
    FileSystemUtils::append_string(path, content)
}

pub fn file_exists<P: AsRef<Path>>(path: P) -> bool {
    FileSystemUtils::exists(path)
}

pub fn is_file<P: AsRef<Path>>(path: P) -> bool {
    FileSystemUtils::is_file(path)
}

pub fn is_directory<P: AsRef<Path>>(path: P) -> bool {
    FileSystemUtils::is_dir(path)
}

pub fn create_directory<P: AsRef<Path>>(path: P) -> UmbraResult<()> {
    FileSystemUtils::create_dir_all(path)
}

pub fn remove_file<P: AsRef<Path>>(path: P) -> UmbraResult<()> {
    FileSystemUtils::remove_file(path)
}

pub fn remove_directory<P: AsRef<Path>>(path: P) -> UmbraResult<()> {
    FileSystemUtils::remove_dir_all(path)
}

pub fn copy_file<P: AsRef<Path>, Q: AsRef<Path>>(from: P, to: Q) -> UmbraResult<()> {
    FileSystemUtils::copy_file(from, to).map(|_| ())
}

pub fn move_file<P: AsRef<Path>, Q: AsRef<Path>>(from: P, to: Q) -> UmbraResult<()> {
    std::fs::rename(from, to).map_err(|e| crate::error::UmbraError::Io(e.to_string()))
}

pub fn list_directory<P: AsRef<Path>>(path: P) -> UmbraResult<Vec<FileInfo>> {
    let paths = FileSystemUtils::list_dir(path)?;
    let mut file_infos = Vec::new();
    for path_buf in paths {
        let file_info = FileInfo {
            name: path_buf.file_name()
                .and_then(|n| n.to_str())
                .unwrap_or("unknown")
                .to_string(),
            path: path_buf.clone(),
            is_file: path_buf.is_file(),
            is_dir: path_buf.is_dir(),
            size: path_buf.metadata().map(|m| m.len()).unwrap_or(0),
            modified: path_buf.metadata()
                .and_then(|m| m.modified())
                .ok(),
            created: path_buf.metadata()
                .and_then(|m| m.created())
                .ok(),
        };
        file_infos.push(file_info);
    }
    Ok(file_infos)
}

pub fn get_file_size<P: AsRef<Path>>(path: P) -> UmbraResult<u64> {
    FileSystemUtils::file_size(path)
}

pub fn get_current_directory() -> UmbraResult<PathBuf> {
    FileSystemUtils::current_dir()
}

pub fn set_current_directory<P: AsRef<Path>>(path: P) -> UmbraResult<()> {
    FileSystemUtils::set_current_dir(path)
}

pub fn get_absolute_path<P: AsRef<Path>>(path: P) -> UmbraResult<PathBuf> {
    FileSystemUtils::absolute_path(path)
}

pub fn join_paths<P: AsRef<Path>, Q: AsRef<Path>>(base: P, path: Q) -> PathBuf {
    let mut result = base.as_ref().to_path_buf();
    result.push(path);
    result
}

pub fn get_file_extension<P: AsRef<Path>>(path: P) -> Option<String> {
    FileSystemUtils::get_extension(path)
}

pub fn get_file_name<P: AsRef<Path>>(path: P) -> Option<String> {
    path.as_ref().file_name()
        .and_then(|name| name.to_str())
        .map(|s| s.to_string())
}

pub fn get_parent_directory<P: AsRef<Path>>(path: P) -> Option<PathBuf> {
    path.as_ref().parent()
        .map(|p| p.to_path_buf())
}
