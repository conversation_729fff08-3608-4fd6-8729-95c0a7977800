/// Comprehensive Map (HashMap) module for Umbra standard library
/// 
/// This module provides a complete implementation of map operations
/// that can be used directly in Umbra code through the `bring` statement.

use crate::error::{UmbraError, UmbraResult};
use std::collections::HashMap;
use std::hash::Hash;
use std::fmt::Debug;

/// The main Map type for Umbra
#[derive(<PERSON>bu<PERSON>, <PERSON>lone)]
pub struct UmbraMap<K, V> {
    map: HashMap<K, V>,
}

impl<K, V> UmbraMap<K, V>
where
    K: Eq + Hash,
{
    /// Create a new empty map
    pub fn new() -> Self {
        Self {
            map: HashMap::new(),
        }
    }

    /// Create a new map with initial capacity
    pub fn with_capacity(capacity: usize) -> Self {
        Self {
            map: HashMap::with_capacity(capacity),
        }
    }

    /// Create a map from a HashMap
    pub fn from_hashmap(map: HashMap<K, V>) -> Self {
        Self { map }
    }

    /// Create a map from an iterator of key-value pairs
    pub fn from_iter<I: IntoIterator<Item = (K, V)>>(iter: I) -> Self {
        Self {
            map: iter.into_iter().collect(),
        }
    }

    /// Get the number of key-value pairs in the map
    pub fn len(&self) -> usize {
        self.map.len()
    }

    /// Check if the map is empty
    pub fn is_empty(&self) -> bool {
        self.map.is_empty()
    }

    /// Get the capacity of the map
    pub fn capacity(&self) -> usize {
        self.map.capacity()
    }

    /// Insert a key-value pair into the map
    /// Returns the previous value if the key was already present
    pub fn insert(&mut self, key: K, value: V) -> Option<V> {
        self.map.insert(key, value)
    }

    /// Get a value by key
    pub fn get(&self, key: &K) -> Option<&V> {
        self.map.get(key)
    }

    /// Get a mutable reference to a value by key
    pub fn get_mut(&mut self, key: &K) -> Option<&mut V> {
        self.map.get_mut(key)
    }

    /// Remove a key-value pair from the map
    /// Returns the value if the key was present
    pub fn remove(&mut self, key: &K) -> Option<V> {
        self.map.remove(key)
    }

    /// Check if the map contains a key
    pub fn contains_key(&self, key: &K) -> bool {
        self.map.contains_key(key)
    }

    /// Clear all key-value pairs from the map
    pub fn clear(&mut self) {
        self.map.clear();
    }

    /// Reserve additional capacity
    pub fn reserve(&mut self, additional: usize) {
        self.map.reserve(additional);
    }

    /// Shrink the capacity to fit the current size
    pub fn shrink_to_fit(&mut self) {
        self.map.shrink_to_fit();
    }

    /// Get an iterator over the keys
    pub fn keys(&self) -> std::collections::hash_map::Keys<K, V> {
        self.map.keys()
    }

    /// Get an iterator over the values
    pub fn values(&self) -> std::collections::hash_map::Values<K, V> {
        self.map.values()
    }

    /// Get a mutable iterator over the values
    pub fn values_mut(&mut self) -> std::collections::hash_map::ValuesMut<K, V> {
        self.map.values_mut()
    }

    /// Get an iterator over key-value pairs
    pub fn iter(&self) -> std::collections::hash_map::Iter<K, V> {
        self.map.iter()
    }

    /// Get a mutable iterator over key-value pairs
    pub fn iter_mut(&mut self) -> std::collections::hash_map::IterMut<K, V> {
        self.map.iter_mut()
    }

    /// Convert to a HashMap
    pub fn to_hashmap(self) -> HashMap<K, V> {
        self.map
    }

    /// Get the entry for a key for advanced manipulation
    pub fn entry(&mut self, key: K) -> std::collections::hash_map::Entry<K, V> {
        self.map.entry(key)
    }

    /// Retain only the key-value pairs that satisfy a predicate
    pub fn retain<F>(&mut self, f: F)
    where
        F: FnMut(&K, &mut V) -> bool,
    {
        self.map.retain(f);
    }
}

impl<K, V> UmbraMap<K, V>
where
    K: Eq + Hash + Clone,
    V: Clone,
{
    /// Extend the map with key-value pairs from another map
    pub fn extend(&mut self, other: &UmbraMap<K, V>) {
        for (key, value) in &other.map {
            self.map.insert(key.clone(), value.clone());
        }
    }

    /// Merge with another map, keeping values from this map in case of conflicts
    pub fn merge(&mut self, other: &UmbraMap<K, V>) {
        for (key, value) in &other.map {
            self.map.entry(key.clone()).or_insert_with(|| value.clone());
        }
    }

    /// Create a new map by merging two maps
    pub fn merged(&self, other: &UmbraMap<K, V>) -> UmbraMap<K, V> {
        let mut result = self.clone();
        result.extend(other);
        result
    }

    /// Get all keys as a vector
    pub fn keys_vec(&self) -> Vec<K> {
        self.map.keys().cloned().collect()
    }

    /// Get all values as a vector
    pub fn values_vec(&self) -> Vec<V> {
        self.map.values().cloned().collect()
    }

    /// Get all key-value pairs as a vector of tuples
    pub fn to_vec(&self) -> Vec<(K, V)> {
        self.map.iter().map(|(k, v)| (k.clone(), v.clone())).collect()
    }

    /// Create a map from a vector of key-value pairs
    pub fn from_vec(pairs: Vec<(K, V)>) -> Self
    where
        K: Eq + Hash,
    {
        Self::from_iter(pairs)
    }
}

impl<K, V> UmbraMap<K, V>
where
    K: Eq + Hash + Clone,
    V: PartialEq,
{
    /// Check if the map contains a specific value
    pub fn contains_value(&self, value: &V) -> bool {
        self.map.values().any(|v| v == value)
    }

    /// Find the first key that maps to a specific value
    pub fn find_key(&self, value: &V) -> Option<K> {
        self.map
            .iter()
            .find(|(_, v)| *v == value)
            .map(|(k, _)| k.clone())
    }

    /// Find all keys that map to a specific value
    pub fn find_keys(&self, value: &V) -> Vec<K> {
        self.map
            .iter()
            .filter(|(_, v)| *v == value)
            .map(|(k, _)| k.clone())
            .collect()
    }

    /// Remove all key-value pairs where the value equals the given value
    pub fn remove_by_value(&mut self, value: &V) -> Vec<K> {
        let keys_to_remove: Vec<K> = self.find_keys(value);
        for key in &keys_to_remove {
            self.map.remove(key);
        }
        keys_to_remove
    }
}

impl<K, V> UmbraMap<K, V>
where
    K: Eq + Hash,
{
    /// Apply a function to each value and collect the results
    pub fn map_values<U, F>(&self, f: F) -> UmbraMap<K, U>
    where
        K: Clone,
        F: Fn(&V) -> U,
    {
        UmbraMap::from_hashmap(
            self.map
                .iter()
                .map(|(k, v)| (k.clone(), f(v)))
                .collect(),
        )
    }

    /// Filter key-value pairs based on a predicate
    pub fn filter<F>(&self, predicate: F) -> UmbraMap<K, V>
    where
        K: Clone,
        V: Clone,
        F: Fn(&K, &V) -> bool,
    {
        UmbraMap::from_hashmap(
            self.map
                .iter()
                .filter(|(k, v)| predicate(k, v))
                .map(|(k, v)| (k.clone(), v.clone()))
                .collect(),
        )
    }

    /// Filter by keys only
    pub fn filter_keys<F>(&self, predicate: F) -> UmbraMap<K, V>
    where
        K: Clone,
        V: Clone,
        F: Fn(&K) -> bool,
    {
        UmbraMap::from_hashmap(
            self.map
                .iter()
                .filter(|(k, _)| predicate(k))
                .map(|(k, v)| (k.clone(), v.clone()))
                .collect(),
        )
    }

    /// Filter by values only
    pub fn filter_values<F>(&self, predicate: F) -> UmbraMap<K, V>
    where
        K: Clone,
        V: Clone,
        F: Fn(&V) -> bool,
    {
        UmbraMap::from_hashmap(
            self.map
                .iter()
                .filter(|(_, v)| predicate(v))
                .map(|(k, v)| (k.clone(), v.clone()))
                .collect(),
        )
    }

    /// Get a value with a default if the key doesn't exist
    pub fn get_or_default(&self, key: &K, default: V) -> V
    where
        V: Clone,
    {
        self.map.get(key).cloned().unwrap_or(default)
    }

    /// Get a value or compute it if the key doesn't exist
    pub fn get_or_else<F>(&self, key: &K, f: F) -> V
    where
        V: Clone,
        F: FnOnce() -> V,
    {
        self.map.get(key).cloned().unwrap_or_else(f)
    }

    /// Update a value if the key exists
    pub fn update<F>(&mut self, key: &K, f: F) -> bool
    where
        F: FnOnce(&mut V),
    {
        if let Some(value) = self.map.get_mut(key) {
            f(value);
            true
        } else {
            false
        }
    }

    /// Insert or update a value
    pub fn upsert<F>(&mut self, key: K, default: V, f: F)
    where
        F: FnOnce(&mut V),
    {
        let entry = self.map.entry(key);
        match entry {
            std::collections::hash_map::Entry::Occupied(mut e) => {
                f(e.get_mut());
            }
            std::collections::hash_map::Entry::Vacant(e) => {
                let mut value = default;
                f(&mut value);
                e.insert(value);
            }
        }
    }

    /// Invert the map (swap keys and values)
    pub fn invert(&self) -> UmbraMap<V, K>
    where
        V: Eq + Hash + Clone,
        K: Clone,
    {
        UmbraMap::from_hashmap(
            self.map
                .iter()
                .map(|(k, v)| (v.clone(), k.clone()))
                .collect(),
        )
    }
}

// Manual PartialEq implementation
impl<K, V> PartialEq for UmbraMap<K, V>
where
    K: Eq + Hash,
    V: PartialEq,
{
    fn eq(&self, other: &Self) -> bool {
        self.map == other.map
    }
}

// Iterator implementations
impl<K, V> IntoIterator for UmbraMap<K, V> {
    type Item = (K, V);
    type IntoIter = std::collections::hash_map::IntoIter<K, V>;

    fn into_iter(self) -> Self::IntoIter {
        self.map.into_iter()
    }
}

impl<'a, K, V> IntoIterator for &'a UmbraMap<K, V> {
    type Item = (&'a K, &'a V);
    type IntoIter = std::collections::hash_map::Iter<'a, K, V>;

    fn into_iter(self) -> Self::IntoIter {
        self.map.iter()
    }
}

impl<'a, K, V> IntoIterator for &'a mut UmbraMap<K, V> {
    type Item = (&'a K, &'a mut V);
    type IntoIter = std::collections::hash_map::IterMut<'a, K, V>;

    fn into_iter(self) -> Self::IntoIter {
        self.map.iter_mut()
    }
}

// Index trait implementation
impl<K, V> std::ops::Index<&K> for UmbraMap<K, V>
where
    K: Eq + Hash,
{
    type Output = V;

    fn index(&self, key: &K) -> &Self::Output {
        &self.map[key]
    }
}

impl<K, V> std::ops::IndexMut<&K> for UmbraMap<K, V>
where
    K: Eq + Hash,
{
    fn index_mut(&mut self, key: &K) -> &mut Self::Output {
        self.map.get_mut(key).expect("Key not found in map")
    }
}

// Default implementation
impl<K, V> Default for UmbraMap<K, V>
where
    K: Eq + Hash,
{
    fn default() -> Self {
        Self::new()
    }
}

// From implementations
impl<K, V> From<HashMap<K, V>> for UmbraMap<K, V>
where
    K: Eq + Hash,
{
    fn from(map: HashMap<K, V>) -> Self {
        Self::from_hashmap(map)
    }
}

impl<K, V> From<UmbraMap<K, V>> for HashMap<K, V>
where
    K: Eq + Hash,
{
    fn from(map: UmbraMap<K, V>) -> Self {
        map.to_hashmap()
    }
}

impl<K, V> From<Vec<(K, V)>> for UmbraMap<K, V>
where
    K: Eq + Hash,
{
    fn from(pairs: Vec<(K, V)>) -> Self {
        Self::from_iter(pairs)
    }
}

/// Static utility functions for maps
pub struct MapUtils;

impl MapUtils {
    /// Create a map from two vectors (keys and values)
    pub fn from_keys_values<K, V>(keys: Vec<K>, values: Vec<V>) -> UmbraResult<UmbraMap<K, V>>
    where
        K: Eq + Hash,
    {
        if keys.len() != values.len() {
            return Err(UmbraError::Runtime(format!(
                "Keys and values vectors must have the same length: {} vs {}",
                keys.len(),
                values.len()
            )));
        }

        Ok(UmbraMap::from_iter(keys.into_iter().zip(values)))
    }

    /// Merge multiple maps into one
    pub fn merge<K, V>(maps: &[UmbraMap<K, V>]) -> UmbraMap<K, V>
    where
        K: Eq + Hash + Clone,
        V: Clone,
    {
        let mut result = UmbraMap::new();
        for map in maps {
            result.extend(map);
        }
        result
    }

    /// Find the intersection of two maps (keys present in both)
    pub fn intersect<K, V>(map1: &UmbraMap<K, V>, map2: &UmbraMap<K, V>) -> UmbraMap<K, V>
    where
        K: Eq + Hash + Clone,
        V: Clone,
    {
        let mut result = UmbraMap::new();
        for (key, value) in map1.iter() {
            if map2.contains_key(key) {
                result.insert(key.clone(), value.clone());
            }
        }
        result
    }

    /// Find the union of two maps (all keys from both maps)
    pub fn union<K, V>(map1: &UmbraMap<K, V>, map2: &UmbraMap<K, V>) -> UmbraMap<K, V>
    where
        K: Eq + Hash + Clone,
        V: Clone,
    {
        let mut result = map1.clone();
        result.extend(map2);
        result
    }

    /// Find the difference between two maps (keys in first but not second)
    pub fn difference<K, V>(map1: &UmbraMap<K, V>, map2: &UmbraMap<K, V>) -> UmbraMap<K, V>
    where
        K: Eq + Hash + Clone,
        V: Clone,
    {
        let mut result = UmbraMap::new();
        for (key, value) in map1.iter() {
            if !map2.contains_key(key) {
                result.insert(key.clone(), value.clone());
            }
        }
        result
    }

    /// Group a vector of items by a key function
    pub fn group_by<T, K, F>(items: Vec<T>, key_fn: F) -> UmbraMap<K, Vec<T>>
    where
        K: Eq + Hash,
        F: Fn(&T) -> K,
    {
        let mut result = UmbraMap::new();
        for item in items {
            let key = key_fn(&item);
            result.entry(key).or_insert_with(Vec::new).push(item);
        }
        result
    }

    /// Count occurrences of items in a vector
    pub fn count_by<T, K, F>(items: &[T], key_fn: F) -> UmbraMap<K, usize>
    where
        K: Eq + Hash,
        F: Fn(&T) -> K,
    {
        let mut result = UmbraMap::new();
        for item in items {
            let key = key_fn(item);
            *result.entry(key).or_insert(0) += 1;
        }
        result
    }

    /// Create a frequency map from a vector of items
    pub fn frequency<T>(items: &[T]) -> UmbraMap<T, usize>
    where
        T: Eq + Hash + Clone,
    {
        let mut result = UmbraMap::new();
        for item in items {
            *result.entry(item.clone()).or_insert(0) += 1;
        }
        result
    }
}
