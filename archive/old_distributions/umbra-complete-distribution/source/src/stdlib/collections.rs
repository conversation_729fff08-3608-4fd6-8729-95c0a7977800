/// Collection utilities and data structures for Umbra standard library
/// 
/// This module provides comprehensive collection types and utilities
/// including advanced operations for lists, vectors, maps, and sets.

use crate::error::{UmbraError, UmbraResult};
use std::collections::{HashMap, HashSet, VecDeque, BTreeMap, BTreeSet};
use std::hash::Hash;

/// Advanced list operations and utilities
pub struct ListUtils;

impl ListUtils {
    /// Create a new list with initial capacity
    pub fn with_capacity<T>(capacity: usize) -> Vec<T> {
        Vec::with_capacity(capacity)
    }
    
    /// Create a list from a range
    pub fn from_range(start: i32, end: i32, step: i32) -> UmbraResult<Vec<i32>> {
        if step == 0 {
            return Err(UmbraError::Runtime("Step cannot be zero".to_string()));
        }
        
        let mut result = Vec::new();
        if step > 0 {
            let mut current = start;
            while current < end {
                result.push(current);
                current += step;
            }
        } else {
            let mut current = start;
            while current > end {
                result.push(current);
                current += step;
            }
        }
        
        Ok(result)
    }
    
    /// Flatten a nested list
    pub fn flatten<T: Clone>(nested: Vec<Vec<T>>) -> Vec<T> {
        nested.into_iter().flatten().collect()
    }
    
    /// Chunk a list into smaller lists of specified size
    pub fn chunk<T: Clone>(list: Vec<T>, size: usize) -> UmbraResult<Vec<Vec<T>>> {
        if size == 0 {
            return Err(UmbraError::Runtime("Chunk size cannot be zero".to_string()));
        }
        
        Ok(list.chunks(size).map(|chunk| chunk.to_vec()).collect())
    }
    
    /// Remove duplicates from a list while preserving order
    pub fn unique<T: Clone + Eq + Hash>(list: Vec<T>) -> Vec<T> {
        let mut seen = HashSet::new();
        list.into_iter().filter(|item| seen.insert(item.clone())).collect()
    }
    
    /// Zip two lists together
    pub fn zip<T: Clone, U: Clone>(list1: Vec<T>, list2: Vec<U>) -> Vec<(T, U)> {
        list1.into_iter().zip(list2.into_iter()).collect()
    }
    
    /// Partition a list based on a predicate
    pub fn partition<T, F>(list: Vec<T>, predicate: F) -> (Vec<T>, Vec<T>)
    where
        F: Fn(&T) -> bool,
    {
        let mut true_items = Vec::new();
        let mut false_items = Vec::new();
        
        for item in list {
            if predicate(&item) {
                true_items.push(item);
            } else {
                false_items.push(item);
            }
        }
        
        (true_items, false_items)
    }
    
    /// Find the intersection of two lists
    pub fn intersect<T: Clone + Eq + Hash>(list1: Vec<T>, list2: Vec<T>) -> Vec<T> {
        let set2: HashSet<T> = list2.into_iter().collect();
        list1.into_iter().filter(|item| set2.contains(item)).collect()
    }
    
    /// Find the difference between two lists (items in first but not second)
    pub fn difference<T: Clone + Eq + Hash>(list1: Vec<T>, list2: Vec<T>) -> Vec<T> {
        let set2: HashSet<T> = list2.into_iter().collect();
        list1.into_iter().filter(|item| !set2.contains(item)).collect()
    }
    
    /// Find the union of two lists
    pub fn union<T: Clone + Eq + Hash>(list1: Vec<T>, list2: Vec<T>) -> Vec<T> {
        let mut result = list1;
        let set1: HashSet<T> = result.iter().cloned().collect();
        
        for item in list2 {
            if !set1.contains(&item) {
                result.push(item);
            }
        }
        
        result
    }
}

/// Advanced map operations and utilities
pub struct MapUtils;

impl MapUtils {
    /// Merge two hashmaps, with values from the second map taking precedence
    pub fn merge<K: Clone + Eq + Hash, V: Clone>(
        mut map1: HashMap<K, V>,
        map2: HashMap<K, V>
    ) -> HashMap<K, V> {
        for (key, value) in map2 {
            map1.insert(key, value);
        }
        map1
    }
    
    /// Filter a hashmap by keys
    pub fn filter_keys<K: Clone + Eq + Hash, V: Clone, F>(
        map: HashMap<K, V>,
        predicate: F
    ) -> HashMap<K, V>
    where
        F: Fn(&K) -> bool,
    {
        map.into_iter()
            .filter(|(key, _)| predicate(key))
            .collect()
    }
    
    /// Filter a hashmap by values
    pub fn filter_values<K: Clone + Eq + Hash, V: Clone, F>(
        map: HashMap<K, V>,
        predicate: F
    ) -> HashMap<K, V>
    where
        F: Fn(&V) -> bool,
    {
        map.into_iter()
            .filter(|(_, value)| predicate(value))
            .collect()
    }
    
    /// Transform values in a hashmap
    pub fn map_values<K: Clone + Eq + Hash, V, U, F>(
        map: HashMap<K, V>,
        transform: F
    ) -> HashMap<K, U>
    where
        F: Fn(V) -> U,
    {
        map.into_iter()
            .map(|(key, value)| (key, transform(value)))
            .collect()
    }
    
    /// Get all keys from a hashmap
    pub fn keys<K: Clone + Eq + Hash, V>(map: &HashMap<K, V>) -> Vec<K> {
        map.keys().cloned().collect()
    }
    
    /// Get all values from a hashmap
    pub fn values<K: Eq + Hash, V: Clone>(map: &HashMap<K, V>) -> Vec<V> {
        map.values().cloned().collect()
    }
    
    /// Invert a hashmap (swap keys and values)
    pub fn invert<K: Clone + Eq + Hash, V: Clone + Eq + Hash>(
        map: HashMap<K, V>
    ) -> HashMap<V, K> {
        map.into_iter().map(|(k, v)| (v, k)).collect()
    }
}

/// Set operations and utilities
pub struct SetUtils;

impl SetUtils {
    /// Create a set from a vector
    pub fn from_vec<T: Clone + Eq + Hash>(vec: Vec<T>) -> HashSet<T> {
        vec.into_iter().collect()
    }
    
    /// Convert a set to a vector
    pub fn to_vec<T: Clone>(set: HashSet<T>) -> Vec<T> {
        set.into_iter().collect()
    }
    
    /// Find the intersection of two sets
    pub fn intersect<T: Clone + Eq + Hash>(set1: HashSet<T>, set2: &HashSet<T>) -> HashSet<T> {
        set1.into_iter().filter(|item| set2.contains(item)).collect()
    }
    
    /// Find the union of two sets
    pub fn union<T: Clone + Eq + Hash>(mut set1: HashSet<T>, set2: HashSet<T>) -> HashSet<T> {
        set1.extend(set2);
        set1
    }
    
    /// Find the difference between two sets
    pub fn difference<T: Clone + Eq + Hash>(set1: HashSet<T>, set2: &HashSet<T>) -> HashSet<T> {
        set1.into_iter().filter(|item| !set2.contains(item)).collect()
    }
    
    /// Find the symmetric difference between two sets
    pub fn symmetric_difference<T: Clone + Eq + Hash>(
        set1: HashSet<T>,
        set2: HashSet<T>
    ) -> HashSet<T> {
        let diff1: HashSet<T> = set1.difference(&set2).cloned().collect();
        let diff2: HashSet<T> = set2.difference(&set1).cloned().collect();
        diff1.union(&diff2).cloned().collect()
    }
    
    /// Check if one set is a subset of another
    pub fn is_subset<T: Eq + Hash>(set1: &HashSet<T>, set2: &HashSet<T>) -> bool {
        set1.is_subset(set2)
    }
    
    /// Check if one set is a superset of another
    pub fn is_superset<T: Eq + Hash>(set1: &HashSet<T>, set2: &HashSet<T>) -> bool {
        set1.is_superset(set2)
    }
    
    /// Check if two sets are disjoint (have no common elements)
    pub fn is_disjoint<T: Eq + Hash>(set1: &HashSet<T>, set2: &HashSet<T>) -> bool {
        set1.is_disjoint(set2)
    }
}

/// Queue operations using VecDeque
pub struct QueueUtils;

impl QueueUtils {
    /// Create a new queue
    pub fn new<T>() -> VecDeque<T> {
        VecDeque::new()
    }
    
    /// Create a queue with capacity
    pub fn with_capacity<T>(capacity: usize) -> VecDeque<T> {
        VecDeque::with_capacity(capacity)
    }
    
    /// Enqueue an item (add to back)
    pub fn enqueue<T>(queue: &mut VecDeque<T>, item: T) {
        queue.push_back(item);
    }
    
    /// Dequeue an item (remove from front)
    pub fn dequeue<T>(queue: &mut VecDeque<T>) -> Option<T> {
        queue.pop_front()
    }
    
    /// Peek at the front item without removing it
    pub fn peek_front<T>(queue: &VecDeque<T>) -> Option<&T> {
        queue.front()
    }
    
    /// Peek at the back item without removing it
    pub fn peek_back<T>(queue: &VecDeque<T>) -> Option<&T> {
        queue.back()
    }
    
    /// Check if queue is empty
    pub fn is_empty<T>(queue: &VecDeque<T>) -> bool {
        queue.is_empty()
    }
    
    /// Get queue length
    pub fn len<T>(queue: &VecDeque<T>) -> usize {
        queue.len()
    }
}

/// Stack operations using Vec
pub struct StackUtils;

impl StackUtils {
    /// Create a new stack
    pub fn new<T>() -> Vec<T> {
        Vec::new()
    }
    
    /// Create a stack with capacity
    pub fn with_capacity<T>(capacity: usize) -> Vec<T> {
        Vec::with_capacity(capacity)
    }
    
    /// Push an item onto the stack
    pub fn push<T>(stack: &mut Vec<T>, item: T) {
        stack.push(item);
    }
    
    /// Pop an item from the stack
    pub fn pop<T>(stack: &mut Vec<T>) -> Option<T> {
        stack.pop()
    }
    
    /// Peek at the top item without removing it
    pub fn peek<T>(stack: &Vec<T>) -> Option<&T> {
        stack.last()
    }
    
    /// Check if stack is empty
    pub fn is_empty<T>(stack: &Vec<T>) -> bool {
        stack.is_empty()
    }
    
    /// Get stack size
    pub fn len<T>(stack: &Vec<T>) -> usize {
        stack.len()
    }
}

/// Ordered map operations using BTreeMap
pub struct OrderedMapUtils;

impl OrderedMapUtils {
    /// Create a new ordered map
    pub fn new<K: Ord, V>() -> BTreeMap<K, V> {
        BTreeMap::new()
    }
    
    /// Get the first key-value pair
    pub fn first<K: Clone + Ord, V: Clone>(map: &BTreeMap<K, V>) -> Option<(K, V)> {
        map.iter().next().map(|(k, v)| (k.clone(), v.clone()))
    }
    
    /// Get the last key-value pair
    pub fn last<K: Clone + Ord, V: Clone>(map: &BTreeMap<K, V>) -> Option<(K, V)> {
        map.iter().next_back().map(|(k, v)| (k.clone(), v.clone()))
    }
    
    /// Get a range of key-value pairs
    pub fn range<K: Clone + Ord, V: Clone>(
        map: &BTreeMap<K, V>,
        start: K,
        end: K
    ) -> Vec<(K, V)> {
        map.range(start..=end)
            .map(|(k, v)| (k.clone(), v.clone()))
            .collect()
    }
}

/// Ordered set operations using BTreeSet
pub struct OrderedSetUtils;

impl OrderedSetUtils {
    /// Create a new ordered set
    pub fn new<T: Ord>() -> BTreeSet<T> {
        BTreeSet::new()
    }
    
    /// Get the first element
    pub fn first<T: Clone + Ord>(set: &BTreeSet<T>) -> Option<T> {
        set.iter().next().cloned()
    }
    
    /// Get the last element
    pub fn last<T: Clone + Ord>(set: &BTreeSet<T>) -> Option<T> {
        set.iter().next_back().cloned()
    }
    
    /// Get a range of elements
    pub fn range<T: Clone + Ord>(set: &BTreeSet<T>, start: T, end: T) -> Vec<T> {
        set.range(start..=end).cloned().collect()
    }
}
