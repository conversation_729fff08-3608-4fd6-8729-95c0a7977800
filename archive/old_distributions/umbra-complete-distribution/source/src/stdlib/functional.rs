use crate::parser::ast::{Type, BasicType};
use crate::semantic::symbol_table::{Symbol, SymbolType, FunctionSignature, Visibility};
use crate::error::SourceLocation;

/// Higher-order functions for functional programming
pub struct FunctionalLibrary;

impl FunctionalLibrary {
    /// Get all functional programming functions
    pub fn get_functions() -> Vec<Symbol> {
        vec![
            Self::create_map_function(),
            Self::create_filter_function(),
            Self::create_reduce_function(),
            Self::create_fold_function(),
            Self::create_foreach_function(),
            Self::create_any_function(),
            Self::create_all_function(),
            Self::create_find_function(),
            Self::create_zip_function(),
            Self::create_compose_function(),
            Self::create_curry_function(),
            Self::create_partial_function(),
        ]
    }

    /// map(list: List[T], func: T -> U) -> List[U]
    fn create_map_function() -> Symbol {
        Symbol {
            name: "map".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Function(
                vec![
                    Type::List(Box::new(Type::Auto)), // List[T]
                    Type::Function(vec![Type::Auto], Box::new(Type::Auto)), // T -> U
                ],
                Box::new(Type::List(Box::new(Type::Auto))), // List[U]
            ),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![
                    Type::List(Box::new(Type::Auto)),
                    Type::Function(vec![Type::Auto], Box::new(Type::Auto)),
                ],
                return_type: Type::List(Box::new(Type::Auto)),
            }),
            visibility: Visibility::Public,
            module_path: Some("std::functional".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    /// filter(list: List[T], predicate: T -> Boolean) -> List[T]
    fn create_filter_function() -> Symbol {
        Symbol {
            name: "filter".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Function(
                vec![
                    Type::List(Box::new(Type::Auto)), // List[T]
                    Type::Function(vec![Type::Auto], Box::new(Type::Basic(BasicType::Boolean))), // T -> Boolean
                ],
                Box::new(Type::List(Box::new(Type::Auto))), // List[T]
            ),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![
                    Type::List(Box::new(Type::Auto)),
                    Type::Function(vec![Type::Auto], Box::new(Type::Basic(BasicType::Boolean))),
                ],
                return_type: Type::List(Box::new(Type::Auto)),
            }),
            visibility: Visibility::Public,
            module_path: Some("std::functional".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    /// reduce(list: List[T], func: (T, T) -> T, initial: T) -> T
    fn create_reduce_function() -> Symbol {
        Symbol {
            name: "reduce".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Function(
                vec![
                    Type::List(Box::new(Type::Auto)), // List[T]
                    Type::Function(vec![Type::Auto, Type::Auto], Box::new(Type::Auto)), // (T, T) -> T
                    Type::Auto, // initial: T
                ],
                Box::new(Type::Auto), // T
            ),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![
                    Type::List(Box::new(Type::Auto)),
                    Type::Function(vec![Type::Auto, Type::Auto], Box::new(Type::Auto)),
                    Type::Auto,
                ],
                return_type: Type::Auto,
            }),
            visibility: Visibility::Public,
            module_path: Some("std::functional".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    /// fold(list: List[T], func: (U, T) -> U, initial: U) -> U
    fn create_fold_function() -> Symbol {
        Symbol {
            name: "fold".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Function(
                vec![
                    Type::List(Box::new(Type::Auto)), // List[T]
                    Type::Function(vec![Type::Auto, Type::Auto], Box::new(Type::Auto)), // (U, T) -> U
                    Type::Auto, // initial: U
                ],
                Box::new(Type::Auto), // U
            ),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![
                    Type::List(Box::new(Type::Auto)),
                    Type::Function(vec![Type::Auto, Type::Auto], Box::new(Type::Auto)),
                    Type::Auto,
                ],
                return_type: Type::Auto,
            }),
            visibility: Visibility::Public,
            module_path: Some("std::functional".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    /// foreach(list: List[T], func: T -> Void) -> Void
    fn create_foreach_function() -> Symbol {
        Symbol {
            name: "foreach".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Function(
                vec![
                    Type::List(Box::new(Type::Auto)), // List[T]
                    Type::Function(vec![Type::Auto], Box::new(Type::Basic(BasicType::Void))), // T -> Void
                ],
                Box::new(Type::Basic(BasicType::Void)), // Void
            ),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![
                    Type::List(Box::new(Type::Auto)),
                    Type::Function(vec![Type::Auto], Box::new(Type::Basic(BasicType::Void))),
                ],
                return_type: Type::Basic(BasicType::Void),
            }),
            visibility: Visibility::Public,
            module_path: Some("std::functional".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    /// any(list: List[T], predicate: T -> Boolean) -> Boolean
    fn create_any_function() -> Symbol {
        Symbol {
            name: "any".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Function(
                vec![
                    Type::List(Box::new(Type::Auto)), // List[T]
                    Type::Function(vec![Type::Auto], Box::new(Type::Basic(BasicType::Boolean))), // T -> Boolean
                ],
                Box::new(Type::Basic(BasicType::Boolean)), // Boolean
            ),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![
                    Type::List(Box::new(Type::Auto)),
                    Type::Function(vec![Type::Auto], Box::new(Type::Basic(BasicType::Boolean))),
                ],
                return_type: Type::Basic(BasicType::Boolean),
            }),
            visibility: Visibility::Public,
            module_path: Some("std::functional".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    /// all(list: List[T], predicate: T -> Boolean) -> Boolean
    fn create_all_function() -> Symbol {
        Symbol {
            name: "all".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Function(
                vec![
                    Type::List(Box::new(Type::Auto)), // List[T]
                    Type::Function(vec![Type::Auto], Box::new(Type::Basic(BasicType::Boolean))), // T -> Boolean
                ],
                Box::new(Type::Basic(BasicType::Boolean)), // Boolean
            ),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![
                    Type::List(Box::new(Type::Auto)),
                    Type::Function(vec![Type::Auto], Box::new(Type::Basic(BasicType::Boolean))),
                ],
                return_type: Type::Basic(BasicType::Boolean),
            }),
            visibility: Visibility::Public,
            module_path: Some("std::functional".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    /// find(list: List[T], predicate: T -> Boolean) -> Optional[T]
    fn create_find_function() -> Symbol {
        Symbol {
            name: "find".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Function(
                vec![
                    Type::List(Box::new(Type::Auto)), // List[T]
                    Type::Function(vec![Type::Auto], Box::new(Type::Basic(BasicType::Boolean))), // T -> Boolean
                ],
                Box::new(Type::Optional(Box::new(Type::Auto))), // Optional[T]
            ),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![
                    Type::List(Box::new(Type::Auto)),
                    Type::Function(vec![Type::Auto], Box::new(Type::Basic(BasicType::Boolean))),
                ],
                return_type: Type::Optional(Box::new(Type::Auto)),
            }),
            visibility: Visibility::Public,
            module_path: Some("std::functional".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    /// zip(list1: List[T], list2: List[U]) -> List[(T, U)]
    fn create_zip_function() -> Symbol {
        Symbol {
            name: "zip".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Function(
                vec![
                    Type::List(Box::new(Type::Auto)), // List[T]
                    Type::List(Box::new(Type::Auto)), // List[U]
                ],
                Box::new(Type::List(Box::new(Type::Tuple(vec![Type::Auto, Type::Auto])))), // List[(T, U)]
            ),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![
                    Type::List(Box::new(Type::Auto)),
                    Type::List(Box::new(Type::Auto)),
                ],
                return_type: Type::List(Box::new(Type::Tuple(vec![Type::Auto, Type::Auto]))),
            }),
            visibility: Visibility::Public,
            module_path: Some("std::functional".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    /// compose(f: U -> V, g: T -> U) -> T -> V
    fn create_compose_function() -> Symbol {
        Symbol {
            name: "compose".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Function(
                vec![
                    Type::Function(vec![Type::Auto], Box::new(Type::Auto)), // U -> V
                    Type::Function(vec![Type::Auto], Box::new(Type::Auto)), // T -> U
                ],
                Box::new(Type::Function(vec![Type::Auto], Box::new(Type::Auto))), // T -> V
            ),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![
                    Type::Function(vec![Type::Auto], Box::new(Type::Auto)),
                    Type::Function(vec![Type::Auto], Box::new(Type::Auto)),
                ],
                return_type: Type::Function(vec![Type::Auto], Box::new(Type::Auto)),
            }),
            visibility: Visibility::Public,
            module_path: Some("std::functional".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    /// curry(func: (T, U) -> V) -> T -> U -> V
    fn create_curry_function() -> Symbol {
        Symbol {
            name: "curry".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Function(
                vec![
                    Type::Function(vec![Type::Auto, Type::Auto], Box::new(Type::Auto)), // (T, U) -> V
                ],
                Box::new(Type::Function(
                    vec![Type::Auto],
                    Box::new(Type::Function(vec![Type::Auto], Box::new(Type::Auto))),
                )), // T -> U -> V
            ),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![
                    Type::Function(vec![Type::Auto, Type::Auto], Box::new(Type::Auto)),
                ],
                return_type: Type::Function(
                    vec![Type::Auto],
                    Box::new(Type::Function(vec![Type::Auto], Box::new(Type::Auto))),
                ),
            }),
            visibility: Visibility::Public,
            module_path: Some("std::functional".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }

    /// partial(func: (T, U) -> V, arg: T) -> U -> V
    fn create_partial_function() -> Symbol {
        Symbol {
            name: "partial".to_string(),
            symbol_type: SymbolType::Function,
            type_annotation: Type::Function(
                vec![
                    Type::Function(vec![Type::Auto, Type::Auto], Box::new(Type::Auto)), // (T, U) -> V
                    Type::Auto, // T
                ],
                Box::new(Type::Function(vec![Type::Auto], Box::new(Type::Auto))), // U -> V
            ),
            location: SourceLocation::new(0, 0),
            is_mutable: false,
            is_initialized: true,
            function_signature: Some(FunctionSignature {
                parameters: vec![
                    Type::Function(vec![Type::Auto, Type::Auto], Box::new(Type::Auto)),
                    Type::Auto,
                ],
                return_type: Type::Function(vec![Type::Auto], Box::new(Type::Auto)),
            }),
            visibility: Visibility::Public,
            module_path: Some("std::functional".to_string()),
            structure_fields: None,
            trait_definition: None,
            impl_definition: None,
        }
    }
}
