/// Comprehensive Set (HashSet) module for Umbra standard library
/// 
/// This module provides a complete implementation of set operations
/// that can be used directly in Umbra code through the `bring` statement.

use crate::error::{UmbraError, UmbraResult};
use std::collections::HashSet;
use std::hash::Hash;
use std::fmt::Debug;

/// The main Set type for Umbra
#[derive(Debug, Clone, PartialEq, Eq)]
pub struct UmbraSet<T>
where
    T: Eq + Hash,
{
    set: HashSet<T>,
}

impl<T> UmbraSet<T>
where
    T: Eq + Hash,
{
    /// Create a new empty set
    pub fn new() -> Self {
        Self {
            set: HashSet::new(),
        }
    }

    /// Create a new set with initial capacity
    pub fn with_capacity(capacity: usize) -> Self {
        Self {
            set: HashSet::with_capacity(capacity),
        }
    }

    /// Create a set from a HashSet
    pub fn from_hashset(set: HashSet<T>) -> Self {
        Self { set }
    }

    /// Create a set from an iterator
    pub fn from_iter<I: IntoIterator<Item = T>>(iter: I) -> Self {
        Self {
            set: iter.into_iter().collect(),
        }
    }

    /// Create a set from a vector
    pub fn from_vec(vec: Vec<T>) -> Self {
        Self::from_iter(vec)
    }

    /// Get the number of elements in the set
    pub fn len(&self) -> usize {
        self.set.len()
    }

    /// Check if the set is empty
    pub fn is_empty(&self) -> bool {
        self.set.is_empty()
    }

    /// Get the capacity of the set
    pub fn capacity(&self) -> usize {
        self.set.capacity()
    }

    /// Insert an element into the set
    /// Returns true if the element was not already present
    pub fn insert(&mut self, value: T) -> bool {
        self.set.insert(value)
    }

    /// Remove an element from the set
    /// Returns true if the element was present
    pub fn remove(&mut self, value: &T) -> bool {
        self.set.remove(value)
    }

    /// Check if the set contains an element
    pub fn contains(&self, value: &T) -> bool {
        self.set.contains(value)
    }

    /// Clear all elements from the set
    pub fn clear(&mut self) {
        self.set.clear();
    }

    /// Reserve additional capacity
    pub fn reserve(&mut self, additional: usize) {
        self.set.reserve(additional);
    }

    /// Shrink the capacity to fit the current size
    pub fn shrink_to_fit(&mut self) {
        self.set.shrink_to_fit();
    }

    /// Get an iterator over the elements
    pub fn iter(&self) -> std::collections::hash_set::Iter<T> {
        self.set.iter()
    }

    /// Convert to a HashSet
    pub fn to_hashset(self) -> HashSet<T> {
        self.set
    }

    /// Convert to a vector
    pub fn to_vec(&self) -> Vec<T>
    where
        T: Clone,
    {
        self.set.iter().cloned().collect()
    }

    /// Retain only the elements that satisfy a predicate
    pub fn retain<F>(&mut self, f: F)
    where
        F: FnMut(&T) -> bool,
    {
        self.set.retain(f);
    }

    /// Get an arbitrary element from the set
    pub fn take(&mut self) -> Option<T>
    where
        T: Clone,
    {
        if let Some(value) = self.set.iter().next().cloned() {
            self.set.remove(&value);
            Some(value)
        } else {
            None
        }
    }

    /// Get a reference to an arbitrary element from the set
    pub fn get(&self) -> Option<&T> {
        self.set.iter().next()
    }
}

impl<T> UmbraSet<T>
where
    T: Eq + Hash + Clone,
{
    /// Extend the set with elements from another set
    pub fn extend(&mut self, other: &UmbraSet<T>) {
        for item in &other.set {
            self.set.insert(item.clone());
        }
    }

    /// Create the union of two sets
    pub fn union(&self, other: &UmbraSet<T>) -> UmbraSet<T> {
        UmbraSet::from_hashset(self.set.union(&other.set).cloned().collect())
    }

    /// Create the intersection of two sets
    pub fn intersection(&self, other: &UmbraSet<T>) -> UmbraSet<T> {
        UmbraSet::from_hashset(self.set.intersection(&other.set).cloned().collect())
    }

    /// Create the difference of two sets (elements in self but not in other)
    pub fn difference(&self, other: &UmbraSet<T>) -> UmbraSet<T> {
        UmbraSet::from_hashset(self.set.difference(&other.set).cloned().collect())
    }

    /// Create the symmetric difference of two sets (elements in either set but not both)
    pub fn symmetric_difference(&self, other: &UmbraSet<T>) -> UmbraSet<T> {
        UmbraSet::from_hashset(self.set.symmetric_difference(&other.set).cloned().collect())
    }

    /// Check if this set is a subset of another set
    pub fn is_subset(&self, other: &UmbraSet<T>) -> bool {
        self.set.is_subset(&other.set)
    }

    /// Check if this set is a superset of another set
    pub fn is_superset(&self, other: &UmbraSet<T>) -> bool {
        self.set.is_superset(&other.set)
    }

    /// Check if this set is disjoint from another set (no common elements)
    pub fn is_disjoint(&self, other: &UmbraSet<T>) -> bool {
        self.set.is_disjoint(&other.set)
    }

    /// Update this set to be the union with another set
    pub fn union_with(&mut self, other: &UmbraSet<T>) {
        self.extend(other);
    }

    /// Update this set to be the intersection with another set
    pub fn intersect_with(&mut self, other: &UmbraSet<T>) {
        self.set.retain(|item| other.set.contains(item));
    }

    /// Update this set to be the difference with another set
    pub fn difference_with(&mut self, other: &UmbraSet<T>) {
        for item in &other.set {
            self.set.remove(item);
        }
    }

    /// Update this set to be the symmetric difference with another set
    pub fn symmetric_difference_with(&mut self, other: &UmbraSet<T>) {
        for item in &other.set {
            if self.set.contains(item) {
                self.set.remove(item);
            } else {
                self.set.insert(item.clone());
            }
        }
    }

    /// Filter elements based on a predicate
    pub fn filter<F>(&self, predicate: F) -> UmbraSet<T>
    where
        F: Fn(&T) -> bool,
    {
        UmbraSet::from_hashset(
            self.set
                .iter()
                .filter(|item| predicate(item))
                .cloned()
                .collect(),
        )
    }

    /// Apply a function to each element and collect the results
    pub fn map<U, F>(&self, f: F) -> UmbraSet<U>
    where
        U: Eq + Hash,
        F: Fn(&T) -> U,
    {
        UmbraSet::from_hashset(self.set.iter().map(f).collect())
    }

    /// Check if any element satisfies a predicate
    pub fn any<F>(&self, predicate: F) -> bool
    where
        F: Fn(&T) -> bool,
    {
        self.set.iter().any(predicate)
    }

    /// Check if all elements satisfy a predicate
    pub fn all<F>(&self, predicate: F) -> bool
    where
        F: Fn(&T) -> bool,
    {
        self.set.iter().all(predicate)
    }

    /// Find the first element that satisfies a predicate
    pub fn find<F>(&self, predicate: F) -> Option<T>
    where
        F: Fn(&T) -> bool,
    {
        self.set.iter().find(|item| predicate(item)).cloned()
    }

    /// Partition the set into two sets based on a predicate
    pub fn partition<F>(&self, predicate: F) -> (UmbraSet<T>, UmbraSet<T>)
    where
        F: Fn(&T) -> bool,
    {
        let mut true_set = UmbraSet::new();
        let mut false_set = UmbraSet::new();

        for item in &self.set {
            if predicate(item) {
                true_set.insert(item.clone());
            } else {
                false_set.insert(item.clone());
            }
        }

        (true_set, false_set)
    }

    /// Fold the set into a single value
    pub fn fold<U, F>(&self, init: U, f: F) -> U
    where
        F: Fn(U, &T) -> U,
    {
        self.set.iter().fold(init, f)
    }

    /// Reduce the set to a single value
    pub fn reduce<F>(&self, f: F) -> Option<T>
    where
        F: Fn(T, &T) -> T,
    {
        let mut iter = self.set.iter();
        let first = iter.next()?.clone();
        Some(iter.fold(first, f))
    }

    /// Create the Cartesian product with another set
    pub fn cartesian_product<U>(&self, other: &UmbraSet<U>) -> UmbraSet<(T, U)>
    where
        U: Eq + Hash + Clone,
    {
        let mut result = UmbraSet::new();
        for a in &self.set {
            for b in &other.set {
                result.insert((a.clone(), b.clone()));
            }
        }
        result
    }

    /// Create the power set (set of all subsets) as a vector
    pub fn power_set(&self) -> Vec<UmbraSet<T>> {
        let items: Vec<T> = self.to_vec();
        let mut result = Vec::new();

        // Generate all possible subsets using bit manipulation
        let n = items.len();
        for i in 0..(1 << n) {
            let mut subset = UmbraSet::new();
            for j in 0..n {
                if (i >> j) & 1 == 1 {
                    subset.insert(items[j].clone());
                }
            }
            result.push(subset);
        }

        result
    }
}

// Iterator implementations
impl<T> IntoIterator for UmbraSet<T>
where
    T: Eq + Hash,
{
    type Item = T;
    type IntoIter = std::collections::hash_set::IntoIter<T>;

    fn into_iter(self) -> Self::IntoIter {
        self.set.into_iter()
    }
}

impl<'a, T> IntoIterator for &'a UmbraSet<T>
where
    T: Eq + Hash,
{
    type Item = &'a T;
    type IntoIter = std::collections::hash_set::Iter<'a, T>;

    fn into_iter(self) -> Self::IntoIter {
        self.set.iter()
    }
}

// Default implementation
impl<T> Default for UmbraSet<T>
where
    T: Eq + Hash,
{
    fn default() -> Self {
        Self::new()
    }
}

// From implementations
impl<T> From<HashSet<T>> for UmbraSet<T>
where
    T: Eq + Hash,
{
    fn from(set: HashSet<T>) -> Self {
        Self::from_hashset(set)
    }
}

impl<T> From<UmbraSet<T>> for HashSet<T>
where
    T: Eq + Hash,
{
    fn from(set: UmbraSet<T>) -> Self {
        set.to_hashset()
    }
}

impl<T> From<Vec<T>> for UmbraSet<T>
where
    T: Eq + Hash,
{
    fn from(vec: Vec<T>) -> Self {
        Self::from_vec(vec)
    }
}

/// Static utility functions for sets
pub struct SetUtils;

impl SetUtils {
    /// Create a set from a range of integers
    pub fn range(start: i32, end: i32, step: i32) -> UmbraResult<UmbraSet<i32>> {
        if step == 0 {
            return Err(UmbraError::Runtime("Step cannot be zero".to_string()));
        }

        let mut result = UmbraSet::new();
        if step > 0 {
            let mut current = start;
            while current < end {
                result.insert(current);
                current += step;
            }
        } else {
            let mut current = start;
            while current > end {
                result.insert(current);
                current += step;
            }
        }

        Ok(result)
    }

    /// Find the union of multiple sets
    pub fn union_all<T>(sets: &[UmbraSet<T>]) -> UmbraSet<T>
    where
        T: Eq + Hash + Clone,
    {
        let mut result = UmbraSet::new();
        for set in sets {
            result.extend(set);
        }
        result
    }

    /// Find the intersection of multiple sets
    pub fn intersect_all<T>(sets: &[UmbraSet<T>]) -> UmbraSet<T>
    where
        T: Eq + Hash + Clone,
    {
        if sets.is_empty() {
            return UmbraSet::new();
        }

        let mut result = sets[0].clone();
        for set in sets.iter().skip(1) {
            result.intersect_with(set);
        }
        result
    }

    /// Check if all sets are pairwise disjoint
    pub fn are_pairwise_disjoint<T>(sets: &[UmbraSet<T>]) -> bool
    where
        T: Eq + Hash + Clone,
    {
        for i in 0..sets.len() {
            for j in (i + 1)..sets.len() {
                if !sets[i].is_disjoint(&sets[j]) {
                    return false;
                }
            }
        }
        true
    }

    /// Create a set of unique elements from multiple vectors
    pub fn unique_from_vecs<T>(vecs: &[Vec<T>]) -> UmbraSet<T>
    where
        T: Eq + Hash + Clone,
    {
        let mut result = UmbraSet::new();
        for vec in vecs {
            for item in vec {
                result.insert(item.clone());
            }
        }
        result
    }

    /// Find elements that appear in exactly n sets
    pub fn elements_in_exactly_n_sets<T>(sets: &[UmbraSet<T>], n: usize) -> UmbraSet<T>
    where
        T: Eq + Hash + Clone,
    {
        let all_elements = Self::union_all(sets);
        let mut result = UmbraSet::new();

        for element in &all_elements {
            let count = sets.iter().filter(|set| set.contains(element)).count();
            if count == n {
                result.insert(element.clone());
            }
        }

        result
    }

    /// Create a frequency map from a set (all frequencies will be 1)
    pub fn to_frequency_map<T>(set: &UmbraSet<T>) -> std::collections::HashMap<T, usize>
    where
        T: Eq + Hash + Clone,
    {
        set.iter().map(|item| (item.clone(), 1)).collect()
    }

    /// Check if a collection of sets forms a partition of a universe set
    pub fn is_partition<T>(sets: &[UmbraSet<T>], universe: &UmbraSet<T>) -> bool
    where
        T: Eq + Hash + Clone,
    {
        // Check if sets are pairwise disjoint
        if !Self::are_pairwise_disjoint(sets) {
            return false;
        }

        // Check if union equals universe
        let union = Self::union_all(sets);
        union == *universe
    }

    /// Generate all possible combinations of k elements from a set
    pub fn combinations<T>(set: &UmbraSet<T>, k: usize) -> Vec<UmbraSet<T>>
    where
        T: Eq + Hash + Clone,
    {
        let items: Vec<T> = set.to_vec();
        let mut result = Vec::new();

        fn generate_combinations<T: Clone + Eq + Hash>(
            items: &[T],
            k: usize,
            start: usize,
            current: &mut Vec<T>,
            result: &mut Vec<UmbraSet<T>>,
        ) {
            if current.len() == k {
                result.push(UmbraSet::from_vec(current.clone()));
                return;
            }

            for i in start..items.len() {
                current.push(items[i].clone());
                generate_combinations(items, k, i + 1, current, result);
                current.pop();
            }
        }

        let mut current = Vec::new();
        generate_combinations(&items, k, 0, &mut current, &mut result);
        result
    }

    /// Generate all possible permutations of k elements from a set
    pub fn permutations<T>(set: &UmbraSet<T>, k: usize) -> Vec<Vec<T>>
    where
        T: Eq + Hash + Clone,
    {
        let items: Vec<T> = set.to_vec();
        let mut result = Vec::new();

        fn generate_permutations<T: Clone + Eq + Hash>(
            items: &[T],
            k: usize,
            current: &mut Vec<T>,
            used: &mut Vec<bool>,
            result: &mut Vec<Vec<T>>,
        ) {
            if current.len() == k {
                result.push(current.clone());
                return;
            }

            for i in 0..items.len() {
                if !used[i] {
                    current.push(items[i].clone());
                    used[i] = true;
                    generate_permutations(items, k, current, used, result);
                    current.pop();
                    used[i] = false;
                }
            }
        }

        let mut current = Vec::new();
        let mut used = vec![false; items.len()];
        generate_permutations(&items, k, &mut current, &mut used, &mut result);
        result
    }
}
