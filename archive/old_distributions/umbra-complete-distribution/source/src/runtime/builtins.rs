/// Built-in functions for the Umbra runtime
/// 
/// This module provides the core built-in functions that are available
/// in all Umbra programs without explicit imports.

use crate::error::{UmbraError, UmbraResult};
use crate::runtime::RuntimeValue;
use std::collections::HashMap;

/// Built-in function type
pub type BuiltinFunction = fn(&[RuntimeValue]) -> UmbraResult<RuntimeValue>;

/// Registry of built-in functions
pub struct BuiltinRegistry {
    functions: HashMap<String, BuiltinFunction>,
}

impl BuiltinRegistry {
    /// Create a new builtin registry with all standard functions
    pub fn new() -> Self {
        let mut registry = Self {
            functions: HashMap::new(),
        };
        
        registry.register_core_functions();
        registry.register_io_functions();
        registry.register_type_functions();
        registry.register_collection_functions();
        
        registry
    }
    
    /// Register a built-in function
    pub fn register(&mut self, name: &str, func: BuiltinFunction) {
        self.functions.insert(name.to_string(), func);
    }
    
    /// Get a built-in function by name
    pub fn get(&self, name: &str) -> Option<BuiltinFunction> {
        self.functions.get(name).copied()
    }
    
    /// Get all function names
    pub fn function_names(&self) -> Vec<String> {
        self.functions.keys().cloned().collect()
    }
    
    /// Register core functions
    fn register_core_functions(&mut self) {
        self.register("print", builtin_print);
        self.register("println", builtin_println);
        self.register("len", builtin_len);
        self.register("type", builtin_type);
        self.register("str", builtin_str);
        self.register("int", builtin_int);
        self.register("float", builtin_float);
        self.register("bool", builtin_bool);
    }
    
    /// Register I/O functions
    fn register_io_functions(&mut self) {
        self.register("read", builtin_read);
        self.register("readln", builtin_readln);
        self.register("input", builtin_input);
    }
    
    /// Register type conversion functions
    fn register_type_functions(&mut self) {
        self.register("to_string", builtin_to_string);
        self.register("to_int", builtin_to_int);
        self.register("to_float", builtin_to_float);
        self.register("to_bool", builtin_to_bool);
    }
    
    /// Register collection functions
    fn register_collection_functions(&mut self) {
        self.register("push", builtin_push);
        self.register("pop", builtin_pop);
        self.register("append", builtin_append);
        self.register("insert", builtin_insert);
        self.register("remove", builtin_remove);
        self.register("contains", builtin_contains);
        self.register("keys", builtin_keys);
        self.register("values", builtin_values);
    }
}

impl Default for BuiltinRegistry {
    fn default() -> Self {
        Self::new()
    }
}

// Core built-in functions

/// Print values without newline
fn builtin_print(args: &[RuntimeValue]) -> UmbraResult<RuntimeValue> {
    for (i, arg) in args.iter().enumerate() {
        if i > 0 {
            print!(" ");
        }
        print!("{}", arg.to_string());
    }
    Ok(RuntimeValue::Null)
}

/// Print values with newline
fn builtin_println(args: &[RuntimeValue]) -> UmbraResult<RuntimeValue> {
    builtin_print(args)?;
    println!();
    Ok(RuntimeValue::Null)
}

/// Get length of a collection or string
fn builtin_len(args: &[RuntimeValue]) -> UmbraResult<RuntimeValue> {
    if args.len() != 1 {
        return Err(UmbraError::Runtime("len() takes exactly 1 argument".to_string()));
    }
    
    match &args[0] {
        RuntimeValue::String(s) => Ok(RuntimeValue::Integer(s.chars().count() as i64)),
        RuntimeValue::List(list) => Ok(RuntimeValue::Integer(list.len() as i64)),
        RuntimeValue::Map(map) => Ok(RuntimeValue::Integer(map.len() as i64)),
        _ => Err(UmbraError::Runtime("len() argument must be a string, list, or map".to_string())),
    }
}

/// Get type name of a value
fn builtin_type(args: &[RuntimeValue]) -> UmbraResult<RuntimeValue> {
    if args.len() != 1 {
        return Err(UmbraError::Runtime("type() takes exactly 1 argument".to_string()));
    }
    
    let type_name = match &args[0] {
        RuntimeValue::Integer(_) => "Integer",
        RuntimeValue::Float(_) => "Float",
        RuntimeValue::String(_) => "String",
        RuntimeValue::Boolean(_) => "Boolean",
        RuntimeValue::List(_) => "List",
        RuntimeValue::Map(_) => "Map",
        RuntimeValue::Function(_) => "Function",
        RuntimeValue::Null => "Null",
        RuntimeValue::Set(_) => "Set",
        RuntimeValue::Struct(_) => "Struct",
        RuntimeValue::Object(_, _) => "Object",
    };
    
    Ok(RuntimeValue::String(type_name.to_string()))
}

/// Convert to string
fn builtin_str(args: &[RuntimeValue]) -> UmbraResult<RuntimeValue> {
    if args.len() != 1 {
        return Err(UmbraError::Runtime("str() takes exactly 1 argument".to_string()));
    }
    
    Ok(RuntimeValue::String(args[0].to_string()))
}

/// Convert to integer
fn builtin_int(args: &[RuntimeValue]) -> UmbraResult<RuntimeValue> {
    if args.len() != 1 {
        return Err(UmbraError::Runtime("int() takes exactly 1 argument".to_string()));
    }
    
    match &args[0] {
        RuntimeValue::Integer(i) => Ok(RuntimeValue::Integer(*i)),
        RuntimeValue::Float(f) => Ok(RuntimeValue::Integer(*f as i64)),
        RuntimeValue::String(s) => {
            s.parse::<i64>()
                .map(RuntimeValue::Integer)
                .map_err(|_| UmbraError::Runtime(format!("Cannot convert '{}' to integer", s)))
        }
        RuntimeValue::Boolean(b) => Ok(RuntimeValue::Integer(if *b { 1 } else { 0 })),
        _ => Err(UmbraError::Runtime("Cannot convert value to integer".to_string())),
    }
}

/// Convert to float
fn builtin_float(args: &[RuntimeValue]) -> UmbraResult<RuntimeValue> {
    if args.len() != 1 {
        return Err(UmbraError::Runtime("float() takes exactly 1 argument".to_string()));
    }
    
    match &args[0] {
        RuntimeValue::Integer(i) => Ok(RuntimeValue::Float(*i as f64)),
        RuntimeValue::Float(f) => Ok(RuntimeValue::Float(*f)),
        RuntimeValue::String(s) => {
            s.parse::<f64>()
                .map(RuntimeValue::Float)
                .map_err(|_| UmbraError::Runtime(format!("Cannot convert '{}' to float", s)))
        }
        _ => Err(UmbraError::Runtime("Cannot convert value to float".to_string())),
    }
}

/// Convert to boolean
fn builtin_bool(args: &[RuntimeValue]) -> UmbraResult<RuntimeValue> {
    if args.len() != 1 {
        return Err(UmbraError::Runtime("bool() takes exactly 1 argument".to_string()));
    }
    
    let result = match &args[0] {
        RuntimeValue::Boolean(b) => *b,
        RuntimeValue::Integer(i) => *i != 0,
        RuntimeValue::Float(f) => *f != 0.0,
        RuntimeValue::String(s) => !s.is_empty(),
        RuntimeValue::List(list) => !list.is_empty(),
        RuntimeValue::Map(map) => !map.is_empty(),
        RuntimeValue::Null => false,
        RuntimeValue::Function(_) => true,
        RuntimeValue::Set(_) => true,
        RuntimeValue::Struct(_) => true,
        RuntimeValue::Object(_, _) => true,
    };
    
    Ok(RuntimeValue::Boolean(result))
}

// I/O functions

/// Read from stdin
fn builtin_read(_args: &[RuntimeValue]) -> UmbraResult<RuntimeValue> {
    use std::io::{self, Read};
    let mut buffer = String::new();
    io::stdin().read_to_string(&mut buffer)
        .map_err(|e| UmbraError::Runtime(format!("Failed to read input: {}", e)))?;
    Ok(RuntimeValue::String(buffer))
}

/// Read line from stdin
fn builtin_readln(_args: &[RuntimeValue]) -> UmbraResult<RuntimeValue> {
    use std::io::{self, BufRead};
    let stdin = io::stdin();
    let mut line = String::new();
    stdin.lock().read_line(&mut line)
        .map_err(|e| UmbraError::Runtime(format!("Failed to read line: {}", e)))?;
    
    // Remove trailing newline
    if line.ends_with('\n') {
        line.pop();
        if line.ends_with('\r') {
            line.pop();
        }
    }
    
    Ok(RuntimeValue::String(line))
}

/// Get input with optional prompt
fn builtin_input(args: &[RuntimeValue]) -> UmbraResult<RuntimeValue> {
    if args.len() > 1 {
        return Err(UmbraError::Runtime("input() takes at most 1 argument".to_string()));
    }
    
    // Print prompt if provided
    if let Some(prompt) = args.get(0) {
        print!("{}", prompt.to_string());
        use std::io::{self, Write};
        io::stdout().flush().unwrap();
    }
    
    builtin_readln(&[])
}

// Type conversion functions

fn builtin_to_string(args: &[RuntimeValue]) -> UmbraResult<RuntimeValue> {
    builtin_str(args)
}

fn builtin_to_int(args: &[RuntimeValue]) -> UmbraResult<RuntimeValue> {
    builtin_int(args)
}

fn builtin_to_float(args: &[RuntimeValue]) -> UmbraResult<RuntimeValue> {
    builtin_float(args)
}

fn builtin_to_bool(args: &[RuntimeValue]) -> UmbraResult<RuntimeValue> {
    builtin_bool(args)
}

// Collection functions

/// Push element to list
fn builtin_push(args: &[RuntimeValue]) -> UmbraResult<RuntimeValue> {
    if args.len() != 2 {
        return Err(UmbraError::Runtime("push() takes exactly 2 arguments".to_string()));
    }
    
    match &args[0] {
        RuntimeValue::List(list) => {
            let mut new_list = list.clone();
            new_list.push(args[1].clone());
            Ok(RuntimeValue::List(new_list))
        }
        _ => Err(UmbraError::Runtime("push() first argument must be a list".to_string())),
    }
}

/// Pop element from list
fn builtin_pop(args: &[RuntimeValue]) -> UmbraResult<RuntimeValue> {
    if args.len() != 1 {
        return Err(UmbraError::Runtime("pop() takes exactly 1 argument".to_string()));
    }
    
    match &args[0] {
        RuntimeValue::List(list) => {
            if list.is_empty() {
                Err(UmbraError::Runtime("Cannot pop from empty list".to_string()))
            } else {
                Ok(list[list.len() - 1].clone())
            }
        }
        _ => Err(UmbraError::Runtime("pop() argument must be a list".to_string())),
    }
}

/// Append to list (alias for push)
fn builtin_append(args: &[RuntimeValue]) -> UmbraResult<RuntimeValue> {
    builtin_push(args)
}

/// Insert element at index
fn builtin_insert(args: &[RuntimeValue]) -> UmbraResult<RuntimeValue> {
    if args.len() != 3 {
        return Err(UmbraError::Runtime("insert() takes exactly 3 arguments".to_string()));
    }
    
    match (&args[0], &args[1]) {
        (RuntimeValue::List(list), RuntimeValue::Integer(index)) => {
            let mut new_list = list.clone();
            let idx = *index as usize;
            if idx <= new_list.len() {
                new_list.insert(idx, args[2].clone());
                Ok(RuntimeValue::List(new_list))
            } else {
                Err(UmbraError::Runtime("Index out of bounds".to_string()))
            }
        }
        _ => Err(UmbraError::Runtime("insert() requires list and integer index".to_string())),
    }
}

/// Remove element at index
fn builtin_remove(args: &[RuntimeValue]) -> UmbraResult<RuntimeValue> {
    if args.len() != 2 {
        return Err(UmbraError::Runtime("remove() takes exactly 2 arguments".to_string()));
    }
    
    match (&args[0], &args[1]) {
        (RuntimeValue::List(list), RuntimeValue::Integer(index)) => {
            let mut new_list = list.clone();
            let idx = *index as usize;
            if idx < new_list.len() {
                let removed = new_list.remove(idx);
                Ok(removed)
            } else {
                Err(UmbraError::Runtime("Index out of bounds".to_string()))
            }
        }
        _ => Err(UmbraError::Runtime("remove() requires list and integer index".to_string())),
    }
}

/// Check if collection contains value
fn builtin_contains(args: &[RuntimeValue]) -> UmbraResult<RuntimeValue> {
    if args.len() != 2 {
        return Err(UmbraError::Runtime("contains() takes exactly 2 arguments".to_string()));
    }
    
    match &args[0] {
        RuntimeValue::List(list) => {
            Ok(RuntimeValue::Boolean(list.contains(&args[1])))
        }
        RuntimeValue::Map(map) => {
            if let RuntimeValue::String(key) = &args[1] {
                Ok(RuntimeValue::Boolean(map.contains_key(key)))
            } else {
                Err(UmbraError::Runtime("Map contains() requires string key".to_string()))
            }
        }
        RuntimeValue::String(s) => {
            if let RuntimeValue::String(substr) = &args[1] {
                Ok(RuntimeValue::Boolean(s.contains(substr)))
            } else {
                Err(UmbraError::Runtime("String contains() requires string argument".to_string()))
            }
        }
        _ => Err(UmbraError::Runtime("contains() first argument must be a collection".to_string())),
    }
}

/// Get map keys
fn builtin_keys(args: &[RuntimeValue]) -> UmbraResult<RuntimeValue> {
    if args.len() != 1 {
        return Err(UmbraError::Runtime("keys() takes exactly 1 argument".to_string()));
    }
    
    match &args[0] {
        RuntimeValue::Map(map) => {
            let keys: Vec<RuntimeValue> = map.keys()
                .map(|k| RuntimeValue::String(k.clone()))
                .collect();
            Ok(RuntimeValue::List(keys))
        }
        _ => Err(UmbraError::Runtime("keys() argument must be a map".to_string())),
    }
}

/// Get map values
fn builtin_values(args: &[RuntimeValue]) -> UmbraResult<RuntimeValue> {
    if args.len() != 1 {
        return Err(UmbraError::Runtime("values() takes exactly 1 argument".to_string()));
    }
    
    match &args[0] {
        RuntimeValue::Map(map) => {
            let values: Vec<RuntimeValue> = map.values().cloned().collect();
            Ok(RuntimeValue::List(values))
        }
        _ => Err(UmbraError::Runtime("values() argument must be a map".to_string())),
    }
}
