// Assertion framework for Umbra testing
// Provides comprehensive assertion macros and functions for test validation

use crate::error::{UmbraE<PERSON><PERSON>, UmbraResult};
use std::fmt::Debug;

/// Assertion result containing details about the assertion
#[derive(Debug, Clone)]
pub struct AssertionResult {
    pub assertion_type: String,
    pub passed: bool,
    pub expected: Option<String>,
    pub actual: Option<String>,
    pub message: Option<String>,
    pub file: Option<String>,
    pub line: Option<u32>,
}

/// Assertion context for tracking assertion results
pub struct AssertionContext {
    pub results: Vec<AssertionResult>,
    pub failed_count: usize,
    pub passed_count: usize,
}

impl AssertionContext {
    pub fn new() -> Self {
        Self {
            results: Vec::new(),
            failed_count: 0,
            passed_count: 0,
        }
    }

    pub fn add_result(&mut self, result: AssertionResult) {
        if result.passed {
            self.passed_count += 1;
        } else {
            self.failed_count += 1;
        }
        self.results.push(result);
    }

    pub fn has_failures(&self) -> bool {
        self.failed_count > 0
    }

    pub fn total_assertions(&self) -> usize {
        self.results.len()
    }
}

/// Basic equality assertion
pub fn assert_eq<T: PartialEq + Debug>(
    actual: T,
    expected: T,
    message: Option<&str>,
) -> AssertionResult {
    let passed = actual == expected;
    AssertionResult {
        assertion_type: "assert_eq".to_string(),
        passed,
        expected: Some(format!("{expected:?}")),
        actual: Some(format!("{actual:?}")),
        message: message.map(|s| s.to_string()),
        file: None,
        line: None,
    }
}

/// Basic inequality assertion
pub fn assert_ne<T: PartialEq + Debug>(
    actual: T,
    expected: T,
    message: Option<&str>,
) -> AssertionResult {
    let passed = actual != expected;
    AssertionResult {
        assertion_type: "assert_ne".to_string(),
        passed,
        expected: Some(format!("not {expected:?}")),
        actual: Some(format!("{actual:?}")),
        message: message.map(|s| s.to_string()),
        file: None,
        line: None,
    }
}

/// Boolean assertion
pub fn assert_true(actual: bool, message: Option<&str>) -> AssertionResult {
    AssertionResult {
        assertion_type: "assert_true".to_string(),
        passed: actual,
        expected: Some("true".to_string()),
        actual: Some(actual.to_string()),
        message: message.map(|s| s.to_string()),
        file: None,
        line: None,
    }
}

/// Boolean false assertion
pub fn assert_false(actual: bool, message: Option<&str>) -> AssertionResult {
    AssertionResult {
        assertion_type: "assert_false".to_string(),
        passed: !actual,
        expected: Some("false".to_string()),
        actual: Some(actual.to_string()),
        message: message.map(|s| s.to_string()),
        file: None,
        line: None,
    }
}

/// Null/None assertion
pub fn assert_none<T>(actual: Option<T>, message: Option<&str>) -> AssertionResult {
    let passed = actual.is_none();
    AssertionResult {
        assertion_type: "assert_none".to_string(),
        passed,
        expected: Some("None".to_string()),
        actual: Some(if actual.is_some() { "Some(_)" } else { "None" }.to_string()),
        message: message.map(|s| s.to_string()),
        file: None,
        line: None,
    }
}

/// Some/not null assertion
pub fn assert_some<T>(actual: Option<T>, message: Option<&str>) -> AssertionResult {
    let passed = actual.is_some();
    AssertionResult {
        assertion_type: "assert_some".to_string(),
        passed,
        expected: Some("Some(_)".to_string()),
        actual: Some(if actual.is_some() { "Some(_)" } else { "None" }.to_string()),
        message: message.map(|s| s.to_string()),
        file: None,
        line: None,
    }
}

/// Floating point comparison with tolerance
pub fn assert_float_eq(
    actual: f64,
    expected: f64,
    tolerance: f64,
    message: Option<&str>,
) -> AssertionResult {
    let passed = (actual - expected).abs() <= tolerance;
    AssertionResult {
        assertion_type: "assert_float_eq".to_string(),
        passed,
        expected: Some(format!("{expected} ± {tolerance}")),
        actual: Some(actual.to_string()),
        message: message.map(|s| s.to_string()),
        file: None,
        line: None,
    }
}

/// String contains assertion
pub fn assert_contains(haystack: &str, needle: &str, message: Option<&str>) -> AssertionResult {
    let passed = haystack.contains(needle);
    AssertionResult {
        assertion_type: "assert_contains".to_string(),
        passed,
        expected: Some(format!("string containing '{needle}'")),
        actual: Some(format!("'{haystack}'")),
        message: message.map(|s| s.to_string()),
        file: None,
        line: None,
    }
}

/// String starts with assertion
pub fn assert_starts_with(text: &str, prefix: &str, message: Option<&str>) -> AssertionResult {
    let passed = text.starts_with(prefix);
    AssertionResult {
        assertion_type: "assert_starts_with".to_string(),
        passed,
        expected: Some(format!("string starting with '{prefix}'")),
        actual: Some(format!("'{text}'")),
        message: message.map(|s| s.to_string()),
        file: None,
        line: None,
    }
}

/// String ends with assertion
pub fn assert_ends_with(text: &str, suffix: &str, message: Option<&str>) -> AssertionResult {
    let passed = text.ends_with(suffix);
    AssertionResult {
        assertion_type: "assert_ends_with".to_string(),
        passed,
        expected: Some(format!("string ending with '{suffix}'")),
        actual: Some(format!("'{text}'")),
        message: message.map(|s| s.to_string()),
        file: None,
        line: None,
    }
}

/// Collection length assertion
pub fn assert_len<T>(collection: &[T], expected_len: usize, message: Option<&str>) -> AssertionResult {
    let actual_len = collection.len();
    let passed = actual_len == expected_len;
    AssertionResult {
        assertion_type: "assert_len".to_string(),
        passed,
        expected: Some(expected_len.to_string()),
        actual: Some(actual_len.to_string()),
        message: message.map(|s| s.to_string()),
        file: None,
        line: None,
    }
}

/// Collection empty assertion
pub fn assert_empty<T>(collection: &[T], message: Option<&str>) -> AssertionResult {
    let passed = collection.is_empty();
    AssertionResult {
        assertion_type: "assert_empty".to_string(),
        passed,
        expected: Some("empty collection".to_string()),
        actual: Some(format!("collection with {} items", collection.len())),
        message: message.map(|s| s.to_string()),
        file: None,
        line: None,
    }
}

/// Collection not empty assertion
pub fn assert_not_empty<T>(collection: &[T], message: Option<&str>) -> AssertionResult {
    let passed = !collection.is_empty();
    AssertionResult {
        assertion_type: "assert_not_empty".to_string(),
        passed,
        expected: Some("non-empty collection".to_string()),
        actual: Some(format!("collection with {} items", collection.len())),
        message: message.map(|s| s.to_string()),
        file: None,
        line: None,
    }
}

/// Range assertion
pub fn assert_in_range<T: PartialOrd + Debug>(
    actual: T,
    min: T,
    max: T,
    message: Option<&str>,
) -> AssertionResult {
    let passed = actual >= min && actual <= max;
    AssertionResult {
        assertion_type: "assert_in_range".to_string(),
        passed,
        expected: Some(format!("value between {min:?} and {max:?}")),
        actual: Some(format!("{actual:?}")),
        message: message.map(|s| s.to_string()),
        file: None,
        line: None,
    }
}

/// Error assertion - expects a specific error
pub fn assert_error<T, E: Debug>(
    result: Result<T, E>,
    message: Option<&str>,
) -> AssertionResult {
    let passed = result.is_err();
    AssertionResult {
        assertion_type: "assert_error".to_string(),
        passed,
        expected: Some("error result".to_string()),
        actual: Some(if result.is_err() { "error" } else { "ok" }.to_string()),
        message: message.map(|s| s.to_string()),
        file: None,
        line: None,
    }
}

/// Success assertion - expects no error
pub fn assert_ok<T, E: Debug>(
    result: Result<T, E>,
    message: Option<&str>,
) -> AssertionResult {
    let passed = result.is_ok();
    AssertionResult {
        assertion_type: "assert_ok".to_string(),
        passed,
        expected: Some("ok result".to_string()),
        actual: Some(if result.is_ok() { "ok" } else { "error" }.to_string()),
        message: message.map(|s| s.to_string()),
        file: None,
        line: None,
    }
}

/// Custom assertion with predicate function
pub fn assert_that<T: Debug>(
    actual: T,
    predicate: fn(&T) -> bool,
    description: &str,
    message: Option<&str>,
) -> AssertionResult {
    let passed = predicate(&actual);
    AssertionResult {
        assertion_type: "assert_that".to_string(),
        passed,
        expected: Some(description.to_string()),
        actual: Some(format!("{actual:?}")),
        message: message.map(|s| s.to_string()),
        file: None,
        line: None,
    }
}

/// Assertion macros for easier usage
#[macro_export]
macro_rules! assert_eq_umbra {
    ($actual:expr, $expected:expr) => {
        $crate::testing::assertions::assert_eq($actual, $expected, None)
    };
    ($actual:expr, $expected:expr, $message:expr) => {
        $crate::testing::assertions::assert_eq($actual, $expected, Some($message))
    };
}

#[macro_export]
macro_rules! assert_ne_umbra {
    ($actual:expr, $expected:expr) => {
        $crate::testing::assertions::assert_ne($actual, $expected, None)
    };
    ($actual:expr, $expected:expr, $message:expr) => {
        $crate::testing::assertions::assert_ne($actual, $expected, Some($message))
    };
}

#[macro_export]
macro_rules! assert_true_umbra {
    ($actual:expr) => {
        $crate::testing::assertions::assert_true($actual, None)
    };
    ($actual:expr, $message:expr) => {
        $crate::testing::assertions::assert_true($actual, Some($message))
    };
}

#[macro_export]
macro_rules! assert_false_umbra {
    ($actual:expr) => {
        $crate::testing::assertions::assert_false($actual, None)
    };
    ($actual:expr, $message:expr) => {
        $crate::testing::assertions::assert_false($actual, Some($message))
    };
}

/// Assertion builder for fluent API
pub struct AssertionBuilder<T> {
    value: T,
    message: Option<String>,
}

impl<T> AssertionBuilder<T> {
    pub fn new(value: T) -> Self {
        Self {
            value,
            message: None,
        }
    }

    pub fn with_message(mut self, message: &str) -> Self {
        self.message = Some(message.to_string());
        self
    }
}

impl<T: PartialEq + Debug> AssertionBuilder<T> {
    pub fn equals(self, expected: T) -> AssertionResult {
        assert_eq(self.value, expected, self.message.as_deref())
    }

    pub fn not_equals(self, expected: T) -> AssertionResult {
        assert_ne(self.value, expected, self.message.as_deref())
    }
}

impl AssertionBuilder<bool> {
    pub fn is_true(self) -> AssertionResult {
        assert_true(self.value, self.message.as_deref())
    }

    pub fn is_false(self) -> AssertionResult {
        assert_false(self.value, self.message.as_deref())
    }
}

impl<T> AssertionBuilder<Option<T>> {
    pub fn is_some(self) -> AssertionResult {
        assert_some(self.value, self.message.as_deref())
    }

    pub fn is_none(self) -> AssertionResult {
        assert_none(self.value, self.message.as_deref())
    }
}

/// Fluent assertion entry point
pub fn expect<T>(value: T) -> AssertionBuilder<T> {
    AssertionBuilder::new(value)
}

/// Soft assertions that collect multiple failures
pub struct SoftAssertions {
    context: AssertionContext,
}

impl SoftAssertions {
    pub fn new() -> Self {
        Self {
            context: AssertionContext::new(),
        }
    }

    pub fn assert_eq<T: PartialEq + Debug>(&mut self, actual: T, expected: T, message: Option<&str>) {
        let result = assert_eq(actual, expected, message);
        self.context.add_result(result);
    }

    pub fn assert_true(&mut self, actual: bool, message: Option<&str>) {
        let result = assert_true(actual, message);
        self.context.add_result(result);
    }

    pub fn assert_false(&mut self, actual: bool, message: Option<&str>) {
        let result = assert_false(actual, message);
        self.context.add_result(result);
    }

    pub fn verify_all(self) -> UmbraResult<()> {
        if self.context.has_failures() {
            let failures: Vec<_> = self.context.results.iter()
                .filter(|r| !r.passed)
                .map(|r| format!("{}: {}", r.assertion_type, r.message.as_deref().unwrap_or("assertion failed")))
                .collect();
            
            Err(UmbraError::Runtime(format!(
                "Soft assertions failed: {}",
                failures.join(", ")
            )))
        } else {
            Ok(())
        }
    }

    pub fn get_results(&self) -> &[AssertionResult] {
        &self.context.results
    }
}
