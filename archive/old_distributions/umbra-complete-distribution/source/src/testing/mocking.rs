// Mocking framework for Umbra testing
// Provides test doubles, stubs, and mocks for isolated testing

use crate::error::{UmbraE<PERSON><PERSON>, UmbraResult};
use std::collections::HashMap;
use std::any::Any;

/// Mock object trait
pub trait Mock: Send + Sync {
    fn call(&mut self, method: &str, args: Vec<Box<dyn Any>>) -> UmbraResult<Box<dyn Any>>;
    fn verify(&self) -> UmbraResult<()>;
    fn reset(&mut self);
}

/// Mock builder for creating test doubles
pub struct MockBuilder {
    name: String,
    expectations: Vec<Expectation>,
    stubs: HashMap<String, Box<dyn Fn(&[Box<dyn Any>]) -> Box<dyn Any> + Send + Sync>>,
}

/// Method call expectation
#[derive(Debug, Clone)]
pub struct Expectation {
    pub method: String,
    pub args: Vec<String>, // Simplified - would be actual types
    pub return_value: Option<String>,
    pub times: ExpectationTimes,
    pub called_count: usize,
}

/// Expectation call count
#[derive(Debug, <PERSON>lone)]
pub enum ExpectationTimes {
    Never,
    Once,
    Exactly(usize),
    AtLeast(usize),
    AtMost(usize),
    Between(usize, usize),
}

impl MockBuilder {
    pub fn new(name: &str) -> Self {
        Self {
            name: name.to_string(),
            expectations: Vec::new(),
            stubs: HashMap::new(),
        }
    }

    pub fn expect_call(self, method: &str) -> ExpectationBuilder {
        ExpectationBuilder::new(self, method)
    }

    pub fn stub_method<F>(mut self, method: &str, func: F) -> Self
    where
        F: Fn(&[Box<dyn Any>]) -> Box<dyn Any> + Send + Sync + 'static,
    {
        self.stubs.insert(method.to_string(), Box::new(func));
        self
    }

    pub fn build(self) -> Box<dyn Mock> {
        Box::new(MockObject {
            name: self.name,
            expectations: self.expectations,
            stubs: self.stubs,
            call_history: Vec::new(),
        })
    }
}

/// Expectation builder for fluent API
pub struct ExpectationBuilder {
    mock_builder: MockBuilder,
    expectation: Expectation,
}

impl ExpectationBuilder {
    fn new(mock_builder: MockBuilder, method: &str) -> Self {
        Self {
            mock_builder,
            expectation: Expectation {
                method: method.to_string(),
                args: Vec::new(),
                return_value: None,
                times: ExpectationTimes::Once,
                called_count: 0,
            },
        }
    }

    pub fn with_args(mut self, args: Vec<&str>) -> Self {
        self.expectation.args = args.iter().map(|s| s.to_string()).collect();
        self
    }

    pub fn returns(mut self, value: &str) -> Self {
        self.expectation.return_value = Some(value.to_string());
        self
    }

    pub fn times(mut self, times: ExpectationTimes) -> Self {
        self.expectation.times = times;
        self
    }

    pub fn once(self) -> MockBuilder {
        self.times(ExpectationTimes::Once).finish()
    }

    pub fn never(self) -> MockBuilder {
        self.times(ExpectationTimes::Never).finish()
    }

    pub fn exactly(self, count: usize) -> MockBuilder {
        self.times(ExpectationTimes::Exactly(count)).finish()
    }

    pub fn at_least(self, count: usize) -> MockBuilder {
        self.times(ExpectationTimes::AtLeast(count)).finish()
    }

    pub fn at_most(self, count: usize) -> MockBuilder {
        self.times(ExpectationTimes::AtMost(count)).finish()
    }

    fn finish(mut self) -> MockBuilder {
        self.mock_builder.expectations.push(self.expectation);
        self.mock_builder
    }
}

/// Mock object implementation
struct MockObject {
    name: String,
    expectations: Vec<Expectation>,
    stubs: HashMap<String, Box<dyn Fn(&[Box<dyn Any>]) -> Box<dyn Any> + Send + Sync>>,
    call_history: Vec<MethodCall>,
}

/// Method call record
#[derive(Debug, Clone)]
struct MethodCall {
    method: String,
    args: Vec<String>, // Simplified
    timestamp: std::time::Instant,
}

impl Mock for MockObject {
    fn call(&mut self, method: &str, args: Vec<Box<dyn Any>>) -> UmbraResult<Box<dyn Any>> {
        // Record the call
        self.call_history.push(MethodCall {
            method: method.to_string(),
            args: Vec::new(), // Would convert args to strings
            timestamp: std::time::Instant::now(),
        });

        // Update expectation call counts
        for expectation in &mut self.expectations {
            if expectation.method == method {
                expectation.called_count += 1;
            }
        }

        // Check if method is stubbed
        if let Some(stub) = self.stubs.get(method) {
            return Ok(stub(&args));
        }

        // Return default value
        Ok(Box::new(()))
    }

    fn verify(&self) -> UmbraResult<()> {
        for expectation in &self.expectations {
            let satisfied = match &expectation.times {
                ExpectationTimes::Never => expectation.called_count == 0,
                ExpectationTimes::Once => expectation.called_count == 1,
                ExpectationTimes::Exactly(n) => expectation.called_count == *n,
                ExpectationTimes::AtLeast(n) => expectation.called_count >= *n,
                ExpectationTimes::AtMost(n) => expectation.called_count <= *n,
                ExpectationTimes::Between(min, max) => {
                    expectation.called_count >= *min && expectation.called_count <= *max
                }
            };

            if !satisfied {
                return Err(UmbraError::Runtime(format!(
                    "Mock '{}' expectation failed for method '{}': expected {:?}, called {} times",
                    self.name, expectation.method, expectation.times, expectation.called_count
                )));
            }
        }

        Ok(())
    }

    fn reset(&mut self) {
        for expectation in &mut self.expectations {
            expectation.called_count = 0;
        }
        self.call_history.clear();
    }
}

/// Spy object for recording method calls
pub struct Spy {
    name: String,
    call_history: Vec<MethodCall>,
}

impl Spy {
    pub fn new(name: &str) -> Self {
        Self {
            name: name.to_string(),
            call_history: Vec::new(),
        }
    }

    pub fn record_call(&mut self, method: &str, args: Vec<String>) {
        self.call_history.push(MethodCall {
            method: method.to_string(),
            args,
            timestamp: std::time::Instant::now(),
        });
    }

    pub fn was_called(&self, method: &str) -> bool {
        self.call_history.iter().any(|call| call.method == method)
    }

    pub fn call_count(&self, method: &str) -> usize {
        self.call_history.iter().filter(|call| call.method == method).count()
    }

    pub fn get_calls(&self, method: &str) -> Vec<&MethodCall> {
        self.call_history.iter().filter(|call| call.method == method).collect()
    }

    pub fn reset(&mut self) {
        self.call_history.clear();
    }
}

/// Stub object for providing predetermined responses
pub struct Stub {
    name: String,
    responses: HashMap<String, Box<dyn Any + Send + Sync>>,
}

impl Stub {
    pub fn new(name: &str) -> Self {
        Self {
            name: name.to_string(),
            responses: HashMap::new(),
        }
    }

    pub fn when_called<T: Any + Send + Sync + 'static>(&mut self, method: &str, response: T) {
        self.responses.insert(method.to_string(), Box::new(response));
    }

    pub fn call<T: Any + 'static>(&self, method: &str) -> Option<&T> {
        self.responses.get(method)?.downcast_ref::<T>()
    }
}

/// Fake object for providing working implementations
pub trait Fake {
    fn reset(&mut self);
}

/// Mock registry for managing test doubles
pub struct MockRegistry {
    mocks: HashMap<String, Box<dyn Mock>>,
    spies: HashMap<String, Spy>,
    stubs: HashMap<String, Stub>,
}

impl MockRegistry {
    pub fn new() -> Self {
        Self {
            mocks: HashMap::new(),
            spies: HashMap::new(),
            stubs: HashMap::new(),
        }
    }

    pub fn register_mock(&mut self, name: String, mock: Box<dyn Mock>) {
        self.mocks.insert(name, mock);
    }

    pub fn register_spy(&mut self, name: String, spy: Spy) {
        self.spies.insert(name, spy);
    }

    pub fn register_stub(&mut self, name: String, stub: Stub) {
        self.stubs.insert(name, stub);
    }

    pub fn get_mock(&mut self, name: &str) -> Option<&mut Box<dyn Mock>> {
        self.mocks.get_mut(name)
    }

    pub fn get_spy(&mut self, name: &str) -> Option<&mut Spy> {
        self.spies.get_mut(name)
    }

    pub fn get_stub(&self, name: &str) -> Option<&Stub> {
        self.stubs.get(name)
    }

    pub fn verify_all(&self) -> UmbraResult<()> {
        for (name, mock) in &self.mocks {
            mock.verify().map_err(|e| {
                UmbraError::Runtime(format!("Mock '{name}' verification failed: {e}"))
            })?;
        }
        Ok(())
    }

    pub fn reset_all(&mut self) {
        for mock in self.mocks.values_mut() {
            mock.reset();
        }
        for spy in self.spies.values_mut() {
            spy.reset();
        }
    }
}

impl Default for MockRegistry {
    fn default() -> Self {
        Self::new()
    }
}
