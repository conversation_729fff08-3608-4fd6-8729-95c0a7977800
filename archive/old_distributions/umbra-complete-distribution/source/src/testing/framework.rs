// Core testing framework implementation
// Provides the main testing framework interface and coordination

use crate::error::UmbraResult;
use crate::testing::*;
use std::path::Path;

/// Main testing framework facade
pub struct UmbraTestFramework {
    inner: TestingFramework,
}

impl UmbraTestFramework {
    /// Create a new testing framework with default configuration
    pub fn new() -> Self {
        let config = TestConfig::default();
        let mut framework = TestingFramework::new(config);
        
        // Add default runners
        framework.add_runner(Box::new(runner::UnitTestRunner::new(
            std::time::Duration::from_secs(30)
        )));
        
        framework.add_runner(Box::new(runner::IntegrationTestRunner::new(
            std::time::Duration::from_secs(300)
        )));
        
        framework.add_runner(Box::new(property::PropertyTestRunner::new(
            PropertyTestConfig::default()
        )));
        
        framework.add_runner(Box::new(performance::PerformanceTestRunner::new(
            PerformanceTestConfig::default()
        )));
        
        framework.add_runner(Box::new(ai_ml::AiMlTestRunner::new()));
        
        // Add default reporters
        framework.add_reporter(Box::new(reporting::ConsoleReporter::new(false)));
        
        Self { inner: framework }
    }

    /// Create framework with custom configuration
    pub fn with_config(config: TestConfig) -> Self {
        let framework = TestingFramework::new(config);
        Self { inner: framework }
    }

    /// Discover and run all tests in a directory
    pub fn run_tests(&mut self, test_dir: &Path) -> UmbraResult<Vec<TestResult>> {
        self.inner.discover_tests(test_dir)?;
        self.inner.run_all_tests()
    }

    /// Add a custom test runner
    pub fn add_runner(&mut self, runner: Box<dyn TestRunner>) {
        self.inner.add_runner(runner);
    }

    /// Add a custom test reporter
    pub fn add_reporter(&mut self, reporter: Box<dyn TestReporter>) {
        self.inner.add_reporter(reporter);
    }

    /// Get test statistics
    pub fn get_statistics(&self) -> TestStatistics {
        self.inner.get_statistics()
    }
}

impl Default for UmbraTestFramework {
    fn default() -> Self {
        Self::new()
    }
}
