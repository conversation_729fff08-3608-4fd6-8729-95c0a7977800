// Code coverage analysis for Umbra tests
use crate::error::{UmbraError, UmbraResult};
use std::collections::{HashMap, HashSet};
use std::path::{Path, PathBuf};
use std::fs;
use serde::{Deserialize, Serialize};

/// Code coverage analyzer
#[derive(Debug, <PERSON>lone)]
pub struct CoverageAnalyzer {
    /// Source files being analyzed
    pub source_files: HashMap<PathBuf, SourceFile>,
    /// Coverage data collected during test execution
    pub coverage_data: CoverageData,
    /// Configuration for coverage analysis
    pub config: CoverageConfig,
}

#[derive(Debug, Clone)]
pub struct SourceFile {
    pub path: PathBuf,
    pub content: String,
    pub lines: Vec<String>,
    pub executable_lines: HashSet<usize>,
    pub covered_lines: HashSet<usize>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CoverageData {
    pub total_lines: usize,
    pub executable_lines: usize,
    pub covered_lines: usize,
    pub line_coverage: f64,
    pub branch_coverage: f64,
    pub function_coverage: f64,
    pub file_coverage: HashMap<String, FileCoverage>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct FileCoverage {
    pub path: String,
    pub total_lines: usize,
    pub executable_lines: usize,
    pub covered_lines: usize,
    pub coverage_percentage: f64,
    pub uncovered_lines: Vec<usize>,
}

#[derive(Debug, Clone)]
pub struct CoverageConfig {
    pub include_patterns: Vec<String>,
    pub exclude_patterns: Vec<String>,
    pub minimum_coverage: f64,
    pub report_format: CoverageReportFormat,
    pub output_directory: PathBuf,
}

#[derive(Debug, Clone)]
pub enum CoverageReportFormat {
    Html,
    Json,
    Lcov,
    Text,
}

impl CoverageAnalyzer {
    pub fn new(config: CoverageConfig) -> Self {
        Self {
            source_files: HashMap::new(),
            coverage_data: CoverageData {
                total_lines: 0,
                executable_lines: 0,
                covered_lines: 0,
                line_coverage: 0.0,
                branch_coverage: 0.0,
                function_coverage: 0.0,
                file_coverage: HashMap::new(),
            },
            config,
        }
    }

    /// Analyze source files for coverage
    pub fn analyze_sources<P: AsRef<Path>>(&mut self, source_dir: P) -> UmbraResult<()> {
        let source_dir = source_dir.as_ref();
        
        // Find all Umbra source files
        let umbra_files = self.find_umbra_files(source_dir)?;
        
        for file_path in umbra_files {
            if self.should_include_file(&file_path) {
                let source_file = self.analyze_source_file(&file_path)?;
                self.source_files.insert(file_path, source_file);
            }
        }
        
        Ok(())
    }

    fn find_umbra_files<P: AsRef<Path>>(&self, dir: P) -> UmbraResult<Vec<PathBuf>> {
        let mut files = Vec::new();
        
        fn visit_dir(dir: &Path, files: &mut Vec<PathBuf>) -> UmbraResult<()> {
            if dir.is_dir() {
                for entry in fs::read_dir(dir)
                    .map_err(|e| UmbraError::Runtime(format!("Failed to read directory: {}", e)))? {
                    let entry = entry
                        .map_err(|e| UmbraError::Runtime(format!("Failed to read directory entry: {}", e)))?;
                    let path = entry.path();
                    
                    if path.is_dir() {
                        visit_dir(&path, files)?;
                    } else if path.extension().and_then(|s| s.to_str()) == Some("umbra") {
                        files.push(path);
                    }
                }
            }
            Ok(())
        }
        
        visit_dir(dir.as_ref(), &mut files)?;
        Ok(files)
    }

    fn should_include_file(&self, file_path: &Path) -> bool {
        let path_str = file_path.to_string_lossy();
        
        // Check exclude patterns first
        for pattern in &self.config.exclude_patterns {
            if path_str.contains(pattern) {
                return false;
            }
        }
        
        // Check include patterns
        if self.config.include_patterns.is_empty() {
            return true;
        }
        
        for pattern in &self.config.include_patterns {
            if path_str.contains(pattern) {
                return true;
            }
        }
        
        false
    }

    fn analyze_source_file(&self, file_path: &Path) -> UmbraResult<SourceFile> {
        let content = fs::read_to_string(file_path)
            .map_err(|e| UmbraError::Runtime(format!("Failed to read source file: {}", e)))?;
        
        let lines: Vec<String> = content.lines().map(|s| s.to_string()).collect();
        let executable_lines = self.find_executable_lines(&lines);
        
        Ok(SourceFile {
            path: file_path.to_path_buf(),
            content,
            lines,
            executable_lines,
            covered_lines: HashSet::new(),
        })
    }

    fn find_executable_lines(&self, lines: &[String]) -> HashSet<usize> {
        let mut executable = HashSet::new();
        
        for (i, line) in lines.iter().enumerate() {
            let trimmed = line.trim();
            
            // Skip empty lines and comments
            if trimmed.is_empty() || trimmed.starts_with("//") || trimmed.starts_with("/*") {
                continue;
            }
            
            // Skip certain keywords that don't represent executable code
            if trimmed.starts_with("bring ") || 
               trimmed.starts_with("export ") ||
               trimmed == "{" || 
               trimmed == "}" {
                continue;
            }
            
            // This is a simplified heuristic - in a real implementation,
            // you'd parse the AST to determine executable statements
            executable.insert(i + 1); // Line numbers are 1-based
        }
        
        executable
    }

    /// Record coverage data from test execution
    pub fn record_coverage(&mut self, file_path: &Path, covered_lines: &[usize]) -> UmbraResult<()> {
        if let Some(source_file) = self.source_files.get_mut(file_path) {
            for &line_num in covered_lines {
                source_file.covered_lines.insert(line_num);
            }
        }
        Ok(())
    }

    /// Calculate final coverage statistics
    pub fn calculate_coverage(&mut self) -> UmbraResult<()> {
        let mut total_executable = 0;
        let mut total_covered = 0;
        let mut file_coverage = HashMap::new();
        
        for (path, source_file) in &self.source_files {
            let executable_count = source_file.executable_lines.len();
            let covered_count = source_file.covered_lines.len();
            let coverage_percentage = if executable_count > 0 {
                (covered_count as f64 / executable_count as f64) * 100.0
            } else {
                100.0
            };
            
            let uncovered_lines: Vec<usize> = source_file.executable_lines
                .difference(&source_file.covered_lines)
                .copied()
                .collect();
            
            file_coverage.insert(
                path.to_string_lossy().to_string(),
                FileCoverage {
                    path: path.to_string_lossy().to_string(),
                    total_lines: source_file.lines.len(),
                    executable_lines: executable_count,
                    covered_lines: covered_count,
                    coverage_percentage,
                    uncovered_lines,
                }
            );
            
            total_executable += executable_count;
            total_covered += covered_count;
        }
        
        let line_coverage = if total_executable > 0 {
            (total_covered as f64 / total_executable as f64) * 100.0
        } else {
            100.0
        };
        
        self.coverage_data = CoverageData {
            total_lines: self.source_files.values().map(|f| f.lines.len()).sum(),
            executable_lines: total_executable,
            covered_lines: total_covered,
            line_coverage,
            branch_coverage: 0.0, // TODO: Implement branch coverage
            function_coverage: 0.0, // TODO: Implement function coverage
            file_coverage,
        };
        
        Ok(())
    }

    /// Generate coverage report
    pub fn generate_report(&self) -> UmbraResult<()> {
        match self.config.report_format {
            CoverageReportFormat::Html => self.generate_html_report(),
            CoverageReportFormat::Json => self.generate_json_report(),
            CoverageReportFormat::Lcov => self.generate_lcov_report(),
            CoverageReportFormat::Text => self.generate_text_report(),
        }
    }

    fn generate_html_report(&self) -> UmbraResult<()> {
        let output_path = self.config.output_directory.join("coverage.html");
        
        let html_content = format!(r#"
<!DOCTYPE html>
<html>
<head>
    <title>Umbra Code Coverage Report</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .summary {{ background: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px; }}
        .file {{ margin-bottom: 15px; }}
        .coverage-bar {{ width: 200px; height: 20px; background: #ddd; border-radius: 10px; overflow: hidden; }}
        .coverage-fill {{ height: 100%; background: #4CAF50; }}
        .low-coverage {{ background: #f44336; }}
        .medium-coverage {{ background: #ff9800; }}
        table {{ border-collapse: collapse; width: 100%; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
    </style>
</head>
<body>
    <h1>Umbra Code Coverage Report</h1>
    
    <div class="summary">
        <h2>Summary</h2>
        <p><strong>Line Coverage:</strong> {:.2}% ({}/{} lines)</p>
        <p><strong>Files:</strong> {}</p>
    </div>
    
    <h2>File Coverage</h2>
    <table>
        <tr>
            <th>File</th>
            <th>Coverage</th>
            <th>Lines</th>
            <th>Covered</th>
            <th>Uncovered</th>
        </tr>
"#, 
            self.coverage_data.line_coverage,
            self.coverage_data.covered_lines,
            self.coverage_data.executable_lines,
            self.coverage_data.file_coverage.len()
        );

        // Add file rows
        let mut html_content = html_content;
        for (_, file_cov) in &self.coverage_data.file_coverage {
            html_content.push_str(&format!(
                r#"        <tr>
            <td>{}</td>
            <td>{:.2}%</td>
            <td>{}</td>
            <td>{}</td>
            <td>{}</td>
        </tr>
"#,
                file_cov.path,
                file_cov.coverage_percentage,
                file_cov.executable_lines,
                file_cov.covered_lines,
                file_cov.uncovered_lines.len()
            ));
        }

        html_content.push_str(r#"    </table>
</body>
</html>"#);

        fs::write(&output_path, html_content)
            .map_err(|e| UmbraError::Runtime(format!("Failed to write HTML report: {}", e)))?;

        println!("📊 HTML coverage report generated: {}", output_path.display());
        Ok(())
    }

    fn generate_json_report(&self) -> UmbraResult<()> {
        let output_path = self.config.output_directory.join("coverage.json");
        
        let json_content = serde_json::to_string_pretty(&self.coverage_data)
            .map_err(|e| UmbraError::Runtime(format!("Failed to serialize coverage data: {}", e)))?;
        
        fs::write(&output_path, json_content)
            .map_err(|e| UmbraError::Runtime(format!("Failed to write JSON report: {}", e)))?;

        println!("📊 JSON coverage report generated: {}", output_path.display());
        Ok(())
    }

    fn generate_lcov_report(&self) -> UmbraResult<()> {
        let output_path = self.config.output_directory.join("coverage.lcov");
        
        let mut lcov_content = String::new();
        
        for (_, file_cov) in &self.coverage_data.file_coverage {
            lcov_content.push_str(&format!("SF:{}\n", file_cov.path));
            
            // Add line coverage data
            for line_num in 1..=file_cov.total_lines {
                if file_cov.uncovered_lines.contains(&line_num) {
                    lcov_content.push_str(&format!("DA:{},0\n", line_num));
                } else {
                    lcov_content.push_str(&format!("DA:{},1\n", line_num));
                }
            }
            
            lcov_content.push_str(&format!("LF:{}\n", file_cov.executable_lines));
            lcov_content.push_str(&format!("LH:{}\n", file_cov.covered_lines));
            lcov_content.push_str("end_of_record\n");
        }
        
        fs::write(&output_path, lcov_content)
            .map_err(|e| UmbraError::Runtime(format!("Failed to write LCOV report: {}", e)))?;

        println!("📊 LCOV coverage report generated: {}", output_path.display());
        Ok(())
    }

    fn generate_text_report(&self) -> UmbraResult<()> {
        let output_path = self.config.output_directory.join("coverage.txt");
        
        let mut text_content = String::new();
        text_content.push_str("UMBRA CODE COVERAGE REPORT\n");
        text_content.push_str("==========================\n\n");
        
        text_content.push_str(&format!("Overall Coverage: {:.2}%\n", self.coverage_data.line_coverage));
        text_content.push_str(&format!("Lines Covered: {}/{}\n", self.coverage_data.covered_lines, self.coverage_data.executable_lines));
        text_content.push_str(&format!("Files Analyzed: {}\n\n", self.coverage_data.file_coverage.len()));
        
        text_content.push_str("File Coverage:\n");
        text_content.push_str("--------------\n");
        
        for (_, file_cov) in &self.coverage_data.file_coverage {
            text_content.push_str(&format!(
                "{}: {:.2}% ({}/{} lines)\n",
                file_cov.path,
                file_cov.coverage_percentage,
                file_cov.covered_lines,
                file_cov.executable_lines
            ));
        }
        
        fs::write(&output_path, text_content)
            .map_err(|e| UmbraError::Runtime(format!("Failed to write text report: {}", e)))?;

        println!("📊 Text coverage report generated: {}", output_path.display());
        Ok(())
    }

    /// Check if coverage meets minimum threshold
    pub fn meets_threshold(&self) -> bool {
        self.coverage_data.line_coverage >= self.config.minimum_coverage
    }
}

impl Default for CoverageConfig {
    fn default() -> Self {
        Self {
            include_patterns: vec!["src/".to_string()],
            exclude_patterns: vec!["tests/".to_string(), "examples/".to_string()],
            minimum_coverage: 80.0,
            report_format: CoverageReportFormat::Html,
            output_directory: PathBuf::from("coverage"),
        }
    }
}
