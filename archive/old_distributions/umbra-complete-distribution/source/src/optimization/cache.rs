/// Compilation cache implementation
/// 
/// Provides caching mechanisms to speed up repeated compilations

use crate::error::{UmbraError, UmbraResult};
use std::collections::HashMap;
use std::fs;
use std::path::{Path, PathBuf};
use std::time::SystemTime;
use serde::{Deserialize, Serialize};

/// Cache entry metadata
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct CacheEntry {
    /// Source file path
    pub source_path: PathBuf,
    
    /// Source file modification time
    pub source_mtime: SystemTime,
    
    /// Compilation timestamp
    pub compiled_at: SystemTime,
    
    /// Compilation success status
    pub success: bool,
    
    /// Hash of source content
    pub content_hash: u64,
    
    /// Dependencies that affect this compilation
    pub dependencies: Vec<PathBuf>,
}

/// Compilation cache
pub struct CompilationCache {
    /// Cache directory
    cache_dir: PathBuf,
    
    /// In-memory cache entries
    entries: HashMap<PathBuf, CacheEntry>,
    
    /// Cache hit counter
    hit_count: usize,
    
    /// Cache miss counter
    miss_count: usize,
    
    /// Maximum cache size (number of entries)
    max_entries: usize,
}

impl CompilationCache {
    /// Create a new compilation cache
    pub fn new(cache_dir: Option<String>) -> UmbraResult<Self> {
        let cache_dir = match cache_dir {
            Some(dir) => PathBuf::from(dir),
            None => {
                // Use default cache directory
                let mut default_dir = std::env::temp_dir();
                default_dir.push("umbra_cache");
                default_dir
            }
        };
        
        // Create cache directory if it doesn't exist
        if !cache_dir.exists() {
            fs::create_dir_all(&cache_dir)?;
        }
        
        let mut cache = Self {
            cache_dir,
            entries: HashMap::new(),
            hit_count: 0,
            miss_count: 0,
            max_entries: 10000, // Default max entries
        };
        
        // Load existing cache entries
        cache.load_cache()?;
        
        Ok(cache)
    }
    
    /// Check if a file is cached and up-to-date
    pub fn is_cached(&mut self, file_path: &Path) -> UmbraResult<bool> {
        if let Some(entry) = self.entries.get(file_path) {
            // Check if source file has been modified
            let metadata = fs::metadata(file_path)?;
            let current_mtime = metadata.modified()?;
            
            if current_mtime <= entry.source_mtime {
                // Check content hash for extra safety
                let current_hash = self.calculate_content_hash(file_path)?;
                if current_hash == entry.content_hash {
                    self.hit_count += 1;
                    return Ok(true);
                }
            }
        }
        
        self.miss_count += 1;
        Ok(false)
    }
    
    /// Check if a cached entry is stale
    pub fn is_stale(&self, file_path: &Path) -> UmbraResult<bool> {
        if let Some(entry) = self.entries.get(file_path) {
            // Check if source file has been modified
            let metadata = fs::metadata(file_path)?;
            let current_mtime = metadata.modified()?;
            
            Ok(current_mtime > entry.source_mtime)
        } else {
            Ok(true) // Not cached = stale
        }
    }
    
    /// Update cache with compilation result
    pub fn update(&mut self, file_path: &Path, success: bool) -> UmbraResult<()> {
        let metadata = fs::metadata(file_path)?;
        let mtime = metadata.modified()?;
        let content_hash = self.calculate_content_hash(file_path)?;
        
        let entry = CacheEntry {
            source_path: file_path.to_path_buf(),
            source_mtime: mtime,
            compiled_at: SystemTime::now(),
            success,
            content_hash,
            dependencies: Vec::new(), // TODO: Track dependencies
        };
        
        self.entries.insert(file_path.to_path_buf(), entry);
        
        // Enforce cache size limit
        if self.entries.len() > self.max_entries {
            self.evict_oldest_entries()?;
        }
        
        // Persist cache
        self.save_cache()?;
        
        Ok(())
    }
    
    /// Calculate content hash for a file
    fn calculate_content_hash(&self, file_path: &Path) -> UmbraResult<u64> {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};
        
        let content = fs::read_to_string(file_path)?;
        let mut hasher = DefaultHasher::new();
        content.hash(&mut hasher);
        Ok(hasher.finish())
    }
    
    /// Load cache from disk
    fn load_cache(&mut self) -> UmbraResult<()> {
        let cache_file = self.cache_dir.join("cache.json");
        
        if cache_file.exists() {
            let content = fs::read_to_string(&cache_file)?;
            match serde_json::from_str::<HashMap<PathBuf, CacheEntry>>(&content) {
                Ok(entries) => {
                    self.entries = entries;
                }
                Err(_) => {
                    // Invalid cache file, start fresh
                    self.entries.clear();
                }
            }
        }
        
        Ok(())
    }
    
    /// Save cache to disk
    fn save_cache(&self) -> UmbraResult<()> {
        let cache_file = self.cache_dir.join("cache.json");
        let content = serde_json::to_string_pretty(&self.entries)
            .map_err(|e| UmbraError::CodeGen(format!("Failed to serialize cache: {e}")))?;
        
        fs::write(&cache_file, content)?;
        Ok(())
    }
    
    /// Evict oldest cache entries to maintain size limit
    fn evict_oldest_entries(&mut self) -> UmbraResult<()> {
        let target_size = self.max_entries * 3 / 4; // Remove 25% of entries

        if self.entries.len() <= target_size {
            return Ok(());
        }

        // Sort entries by compilation time and collect paths to remove
        let mut entries: Vec<_> = self.entries.iter().map(|(path, entry)| (path.clone(), entry.compiled_at)).collect();
        entries.sort_by_key(|(_, compiled_at)| *compiled_at);

        let to_remove = self.entries.len() - target_size;
        let paths_to_remove: Vec<_> = entries.iter().take(to_remove).map(|(path, _)| path.clone()).collect();

        // Remove the oldest entries
        for path in paths_to_remove {
            self.entries.remove(&path);
        }

        Ok(())
    }
    
    /// Clear all cache entries
    pub fn clear(&mut self) -> UmbraResult<()> {
        self.entries.clear();
        self.hit_count = 0;
        self.miss_count = 0;
        
        // Remove cache file
        let cache_file = self.cache_dir.join("cache.json");
        if cache_file.exists() {
            fs::remove_file(&cache_file)?;
        }
        
        Ok(())
    }
    
    /// Get cache hit count
    pub fn get_hit_count(&self) -> usize {
        self.hit_count
    }
    
    /// Get cache miss count
    pub fn get_miss_count(&self) -> usize {
        self.miss_count
    }
    
    /// Get cache hit ratio
    pub fn get_hit_ratio(&self) -> f64 {
        let total = self.hit_count + self.miss_count;
        if total == 0 {
            0.0
        } else {
            self.hit_count as f64 / total as f64
        }
    }
    
    /// Get number of cached entries
    pub fn entry_count(&self) -> usize {
        self.entries.len()
    }
    
    /// Set maximum number of cache entries
    pub fn set_max_entries(&mut self, max_entries: usize) {
        self.max_entries = max_entries;
    }
    
    /// Add dependency to a cache entry
    pub fn add_dependency(&mut self, file_path: &Path, dependency: &Path) -> UmbraResult<()> {
        if let Some(entry) = self.entries.get_mut(file_path) {
            if !entry.dependencies.contains(&dependency.to_path_buf()) {
                entry.dependencies.push(dependency.to_path_buf());
            }
        }
        Ok(())
    }
    
    /// Check if any dependencies have changed
    pub fn dependencies_changed(&self, file_path: &Path) -> UmbraResult<bool> {
        if let Some(entry) = self.entries.get(file_path) {
            for dep in &entry.dependencies {
                if dep.exists() {
                    let metadata = fs::metadata(dep)?;
                    let dep_mtime = metadata.modified()?;
                    
                    if dep_mtime > entry.compiled_at {
                        return Ok(true);
                    }
                }
            }
        }
        Ok(false)
    }
    
    /// Get cache statistics
    pub fn get_stats(&self) -> CacheStats {
        CacheStats {
            hit_count: self.hit_count,
            miss_count: self.miss_count,
            entry_count: self.entries.len(),
            max_entries: self.max_entries,
            cache_dir: self.cache_dir.clone(),
        }
    }
}

/// Cache statistics
#[derive(Debug, Clone)]
pub struct CacheStats {
    pub hit_count: usize,
    pub miss_count: usize,
    pub entry_count: usize,
    pub max_entries: usize,
    pub cache_dir: PathBuf,
}

impl CacheStats {
    pub fn hit_ratio(&self) -> f64 {
        let total = self.hit_count + self.miss_count;
        if total == 0 {
            0.0
        } else {
            self.hit_count as f64 / total as f64
        }
    }
    
    pub fn total_accesses(&self) -> usize {
        self.hit_count + self.miss_count
    }
}
