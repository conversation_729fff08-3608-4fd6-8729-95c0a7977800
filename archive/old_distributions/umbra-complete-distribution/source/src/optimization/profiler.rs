/// Runtime performance monitoring and profiling tools
/// 
/// Provides profiling capabilities for Umbra programs

use crate::error::UmbraResult;
use std::collections::HashMap;
use std::time::{Duration, Instant};
use serde::{Deserialize, Serialize};

/// Performance profiler for Umbra programs
pub struct UmbraProfiler {
    /// Function call statistics
    function_stats: HashMap<String, FunctionStats>,
    
    /// Memory allocation tracking
    memory_stats: MemoryStats,
    
    /// Overall program statistics
    program_stats: ProgramStats,
    
    /// Profiling start time
    start_time: Instant,
    
    /// Whether profiling is currently active
    active: bool,
    
    /// Sampling interval for performance monitoring
    sample_interval: Duration,
    
    /// Maximum number of samples to keep
    max_samples: usize,
}

/// Statistics for a single function
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FunctionStats {
    /// Function name
    pub name: String,
    
    /// Number of times called
    pub call_count: u64,
    
    /// Total time spent in function
    pub total_time: Duration,
    
    /// Minimum execution time
    pub min_time: Duration,
    
    /// Maximum execution time
    pub max_time: Duration,
    
    /// Average execution time
    pub avg_time: Duration,
    
    /// Memory allocated by this function
    pub memory_allocated: u64,
    
    /// Memory deallocated by this function
    pub memory_deallocated: u64,
}

/// Memory usage statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MemoryStats {
    /// Total memory allocated
    pub total_allocated: u64,
    
    /// Total memory deallocated
    pub total_deallocated: u64,
    
    /// Current memory usage
    pub current_usage: u64,
    
    /// Peak memory usage
    pub peak_usage: u64,
    
    /// Number of allocations
    pub allocation_count: u64,
    
    /// Number of deallocations
    pub deallocation_count: u64,
    
    /// Average allocation size
    pub avg_allocation_size: f64,
    
    /// Memory fragmentation estimate
    pub fragmentation_ratio: f64,
}

/// Overall program statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProgramStats {
    /// Total execution time
    pub total_execution_time: Duration,
    
    /// Number of function calls
    pub total_function_calls: u64,
    
    /// Number of AI/ML operations
    pub ai_operations_count: u64,
    
    /// Time spent in AI/ML operations
    pub ai_operations_time: Duration,
    
    /// Number of error handling operations
    pub error_handling_count: u64,
    
    /// Time spent in error handling
    pub error_handling_time: Duration,
    
    /// CPU usage percentage
    pub cpu_usage: f64,
    
    /// Memory efficiency ratio
    pub memory_efficiency: f64,
}

impl UmbraProfiler {
    /// Create a new profiler
    pub fn new() -> Self {
        Self {
            function_stats: HashMap::new(),
            memory_stats: MemoryStats::new(),
            program_stats: ProgramStats::new(),
            start_time: Instant::now(),
            active: false,
            sample_interval: Duration::from_millis(100),
            max_samples: 10000,
        }
    }
    
    /// Start profiling
    pub fn start(&mut self) {
        self.active = true;
        self.start_time = Instant::now();
        self.reset_stats();
    }
    
    /// Stop profiling
    pub fn stop(&mut self) {
        self.active = false;
        self.program_stats.total_execution_time = self.start_time.elapsed();
    }
    
    /// Record a function call
    pub fn record_function_call(&mut self, function_name: &str, execution_time: Duration, memory_used: u64) {
        if !self.active {
            return;
        }
        
        let stats = self.function_stats.entry(function_name.to_string()).or_insert_with(|| {
            FunctionStats::new(function_name.to_string())
        });
        
        stats.call_count += 1;
        stats.total_time += execution_time;
        
        if stats.call_count == 1 {
            stats.min_time = execution_time;
            stats.max_time = execution_time;
        } else {
            if execution_time < stats.min_time {
                stats.min_time = execution_time;
            }
            if execution_time > stats.max_time {
                stats.max_time = execution_time;
            }
        }
        
        stats.avg_time = stats.total_time / stats.call_count as u32;
        stats.memory_allocated += memory_used;
        
        // Update program stats
        self.program_stats.total_function_calls += 1;
    }
    
    /// Record memory allocation
    pub fn record_allocation(&mut self, size: u64) {
        if !self.active {
            return;
        }
        
        self.memory_stats.total_allocated += size;
        self.memory_stats.current_usage += size;
        self.memory_stats.allocation_count += 1;
        
        if self.memory_stats.current_usage > self.memory_stats.peak_usage {
            self.memory_stats.peak_usage = self.memory_stats.current_usage;
        }
        
        self.memory_stats.update_averages();
    }
    
    /// Record memory deallocation
    pub fn record_deallocation(&mut self, size: u64) {
        if !self.active {
            return;
        }
        
        self.memory_stats.total_deallocated += size;
        self.memory_stats.current_usage = self.memory_stats.current_usage.saturating_sub(size);
        self.memory_stats.deallocation_count += 1;
        
        self.memory_stats.update_averages();
    }
    
    /// Record AI/ML operation
    pub fn record_ai_operation(&mut self, operation_time: Duration) {
        if !self.active {
            return;
        }
        
        self.program_stats.ai_operations_count += 1;
        self.program_stats.ai_operations_time += operation_time;
    }
    
    /// Record error handling operation
    pub fn record_error_handling(&mut self, handling_time: Duration) {
        if !self.active {
            return;
        }
        
        self.program_stats.error_handling_count += 1;
        self.program_stats.error_handling_time += handling_time;
    }
    
    /// Get function statistics
    pub fn get_function_stats(&self, function_name: &str) -> Option<&FunctionStats> {
        self.function_stats.get(function_name)
    }
    
    /// Get all function statistics
    pub fn get_all_function_stats(&self) -> &HashMap<String, FunctionStats> {
        &self.function_stats
    }
    
    /// Get memory statistics
    pub fn get_memory_stats(&self) -> &MemoryStats {
        &self.memory_stats
    }
    
    /// Get program statistics
    pub fn get_program_stats(&self) -> &ProgramStats {
        &self.program_stats
    }
    
    /// Generate performance report
    pub fn generate_report(&self) -> PerformanceReport {
        PerformanceReport {
            function_stats: self.function_stats.clone(),
            memory_stats: self.memory_stats.clone(),
            program_stats: self.program_stats.clone(),
            generated_at: std::time::SystemTime::now(),
        }
    }
    
    /// Export profiling data to JSON
    pub fn export_to_json(&self) -> UmbraResult<String> {
        let report = self.generate_report();
        serde_json::to_string_pretty(&report)
            .map_err(|e| crate::error::UmbraError::CodeGen(format!("Failed to export profiling data: {e}")))
    }
    
    /// Reset all statistics
    pub fn reset_stats(&mut self) {
        self.function_stats.clear();
        self.memory_stats = MemoryStats::new();
        self.program_stats = ProgramStats::new();
    }
    
    /// Get top functions by execution time
    pub fn get_top_functions_by_time(&self, limit: usize) -> Vec<&FunctionStats> {
        let mut functions: Vec<_> = self.function_stats.values().collect();
        functions.sort_by(|a, b| b.total_time.cmp(&a.total_time));
        functions.into_iter().take(limit).collect()
    }
    
    /// Get top functions by call count
    pub fn get_top_functions_by_calls(&self, limit: usize) -> Vec<&FunctionStats> {
        let mut functions: Vec<_> = self.function_stats.values().collect();
        functions.sort_by(|a, b| b.call_count.cmp(&a.call_count));
        functions.into_iter().take(limit).collect()
    }
    
    /// Get top functions by memory usage
    pub fn get_top_functions_by_memory(&self, limit: usize) -> Vec<&FunctionStats> {
        let mut functions: Vec<_> = self.function_stats.values().collect();
        functions.sort_by(|a, b| b.memory_allocated.cmp(&a.memory_allocated));
        functions.into_iter().take(limit).collect()
    }
}

impl FunctionStats {
    fn new(name: String) -> Self {
        Self {
            name,
            call_count: 0,
            total_time: Duration::ZERO,
            min_time: Duration::ZERO,
            max_time: Duration::ZERO,
            avg_time: Duration::ZERO,
            memory_allocated: 0,
            memory_deallocated: 0,
        }
    }
}

impl MemoryStats {
    fn new() -> Self {
        Self {
            total_allocated: 0,
            total_deallocated: 0,
            current_usage: 0,
            peak_usage: 0,
            allocation_count: 0,
            deallocation_count: 0,
            avg_allocation_size: 0.0,
            fragmentation_ratio: 0.0,
        }
    }
    
    fn update_averages(&mut self) {
        if self.allocation_count > 0 {
            self.avg_allocation_size = self.total_allocated as f64 / self.allocation_count as f64;
        }
        
        // Simple fragmentation estimate
        if self.total_allocated > 0 {
            self.fragmentation_ratio = (self.total_allocated - self.current_usage) as f64 / self.total_allocated as f64;
        }
    }
}

impl ProgramStats {
    fn new() -> Self {
        Self {
            total_execution_time: Duration::ZERO,
            total_function_calls: 0,
            ai_operations_count: 0,
            ai_operations_time: Duration::ZERO,
            error_handling_count: 0,
            error_handling_time: Duration::ZERO,
            cpu_usage: 0.0,
            memory_efficiency: 0.0,
        }
    }
}

/// Complete performance report
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceReport {
    pub function_stats: HashMap<String, FunctionStats>,
    pub memory_stats: MemoryStats,
    pub program_stats: ProgramStats,
    pub generated_at: std::time::SystemTime,
}

impl Default for UmbraProfiler {
    fn default() -> Self {
        Self::new()
    }
}
