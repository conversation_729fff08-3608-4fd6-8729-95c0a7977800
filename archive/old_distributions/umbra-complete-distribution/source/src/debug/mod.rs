/// Debugging support for Umbra applications
/// 
/// Provides comprehensive debugging capabilities including breakpoints,
/// variable inspection, stack traces, and step-through debugging.

pub mod debugger;
pub mod breakpoint;
pub mod inspector;
pub mod stack_trace;
pub mod debug_info;

use crate::error::UmbraResult;
use std::collections::HashMap;
use std::path::{Path, PathBuf};
use serde::{Deserialize, Serialize};

/// Debug session manager
pub struct DebugSession {
    /// Session ID
    pub id: String,
    
    /// Target executable path
    pub executable: PathBuf,
    
    /// Source files mapping
    pub source_files: HashMap<PathBuf, String>,
    
    /// Debug information
    pub debug_info: debug_info::DebugInfo,
    
    /// Breakpoint manager
    pub breakpoints: breakpoint::BreakpointManager,
    
    /// Variable inspector
    pub inspector: inspector::VariableInspector,
    
    /// Stack trace analyzer
    pub stack_trace: stack_trace::StackTraceAnalyzer,
    
    /// Current execution state
    pub state: DebugState,
}

/// Debug execution state
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DebugState {
    /// Not started
    NotStarted,
    /// Running normally
    Running,
    /// Paused at breakpoint
    Paused {
        location: SourceLocation,
        reason: PauseReason,
    },
    /// Stepping through code
    Stepping {
        step_type: StepType,
        location: SourceLocation,
    },
    /// Execution finished
    Finished {
        exit_code: i32,
    },
    /// Error occurred
    Error {
        message: String,
    },
}

/// Reason for pausing execution
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PauseReason {
    /// Hit a breakpoint
    Breakpoint { id: u32 },
    /// Step completed
    Step,
    /// Exception occurred
    Exception { message: String },
    /// User requested pause
    UserRequested,
}

/// Step type for debugging
#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub enum StepType {
    /// Step into function calls
    Into,
    /// Step over function calls
    Over,
    /// Step out of current function
    Out,
}

/// Source location for debugging
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SourceLocation {
    /// File path
    pub file: PathBuf,
    /// Line number (1-based)
    pub line: u32,
    /// Column number (1-based)
    pub column: u32,
}

/// Debug configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DebugConfig {
    /// Enable debug information generation
    pub generate_debug_info: bool,
    
    /// Debug information format
    pub debug_format: DebugFormat,
    
    /// Enable source maps
    pub source_maps: bool,
    
    /// Enable variable inspection
    pub variable_inspection: bool,
    
    /// Enable stack trace analysis
    pub stack_traces: bool,
    
    /// Maximum stack depth to analyze
    pub max_stack_depth: usize,
    
    /// Enable performance profiling during debug
    pub profiling: bool,
}

/// Debug information format
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DebugFormat {
    /// DWARF debug format
    Dwarf,
    /// Custom Umbra debug format
    Umbra,
    /// JSON debug format
    Json,
}

impl DebugSession {
    /// Create a new debug session
    pub fn new(executable: PathBuf, source_files: HashMap<PathBuf, String>) -> UmbraResult<Self> {
        let id = uuid::Uuid::new_v4().to_string();
        let debug_info = debug_info::DebugInfo::load_from_executable(&executable)?;
        
        Ok(Self {
            id,
            executable,
            source_files,
            debug_info,
            breakpoints: breakpoint::BreakpointManager::new(),
            inspector: inspector::VariableInspector::new(),
            stack_trace: stack_trace::StackTraceAnalyzer::new(),
            state: DebugState::NotStarted,
        })
    }
    
    /// Start debugging session
    pub fn start(&mut self) -> UmbraResult<()> {
        self.state = DebugState::Running;
        println!("🐛 Debug session started: {}", self.id);
        Ok(())
    }
    
    /// Pause execution
    pub fn pause(&mut self, reason: PauseReason) -> UmbraResult<()> {
        if let DebugState::Running = self.state {
            let location = self.get_current_location()?;
            self.state = DebugState::Paused { location, reason };
            println!("⏸️  Execution paused");
        }
        Ok(())
    }
    
    /// Resume execution
    pub fn resume(&mut self) -> UmbraResult<()> {
        if let DebugState::Paused { .. } = self.state {
            self.state = DebugState::Running;
            println!("▶️  Execution resumed");
        }
        Ok(())
    }
    
    /// Step through code
    pub fn step(&mut self, step_type: StepType) -> UmbraResult<()> {
        let location = self.get_current_location()?;
        self.state = DebugState::Stepping { step_type, location };
        println!("👣 Stepping: {step_type:?}");
        Ok(())
    }
    
    /// Stop debugging session
    pub fn stop(&mut self) -> UmbraResult<()> {
        self.state = DebugState::Finished { exit_code: 0 };
        println!("🛑 Debug session stopped");
        Ok(())
    }
    
    /// Get current execution location
    fn get_current_location(&self) -> UmbraResult<SourceLocation> {
        // In a real implementation, this would get the current location from the runtime
        Ok(SourceLocation {
            file: PathBuf::from("src/main.umbra"),
            line: 1,
            column: 1,
        })
    }
    
    /// Add breakpoint
    pub fn add_breakpoint(&mut self, file: &Path, line: u32) -> UmbraResult<u32> {
        self.breakpoints.add(file, line)
    }
    
    /// Remove breakpoint
    pub fn remove_breakpoint(&mut self, id: u32) -> UmbraResult<bool> {
        self.breakpoints.remove(id)
    }
    
    /// List all breakpoints
    pub fn list_breakpoints(&self) -> Vec<&breakpoint::Breakpoint> {
        self.breakpoints.list()
    }
    
    /// Inspect variable
    pub fn inspect_variable(&self, name: &str) -> UmbraResult<inspector::VariableValue> {
        self.inspector.inspect(name)
    }
    
    /// Get current stack trace
    pub fn get_stack_trace(&self) -> UmbraResult<Vec<stack_trace::StackFrame>> {
        self.stack_trace.get_current_trace()
    }
    
    /// Evaluate expression in current context
    pub fn evaluate_expression(&self, expression: &str) -> UmbraResult<inspector::VariableValue> {
        self.inspector.evaluate(expression)
    }
    
    /// Get local variables in current scope
    pub fn get_local_variables(&self) -> UmbraResult<HashMap<String, inspector::VariableValue>> {
        self.inspector.get_locals()
    }
    
    /// Get session state
    pub fn get_state(&self) -> &DebugState {
        &self.state
    }
    
    /// Check if session is active
    pub fn is_active(&self) -> bool {
        matches!(self.state, DebugState::Running | DebugState::Paused { .. } | DebugState::Stepping { .. })
    }
}

impl Default for DebugConfig {
    fn default() -> Self {
        Self {
            generate_debug_info: true,
            debug_format: DebugFormat::Umbra,
            source_maps: true,
            variable_inspection: true,
            stack_traces: true,
            max_stack_depth: 100,
            profiling: false,
        }
    }
}

impl SourceLocation {
    /// Create a new source location
    pub fn new(file: PathBuf, line: u32, column: u32) -> Self {
        Self { file, line, column }
    }
    
    /// Format location as string
    pub fn format(&self) -> String {
        format!("{}:{}:{}", self.file.display(), self.line, self.column)
    }
}

/// Debug session manager for handling multiple sessions
pub struct DebugManager {
    /// Active debug sessions
    sessions: HashMap<String, DebugSession>,
    
    /// Global debug configuration
    config: DebugConfig,
}

impl DebugManager {
    /// Create a new debug manager
    pub fn new() -> Self {
        Self {
            sessions: HashMap::new(),
            config: DebugConfig::default(),
        }
    }
    
    /// Create a new debug session
    pub fn create_session(&mut self, executable: PathBuf, source_files: HashMap<PathBuf, String>) -> UmbraResult<String> {
        let session = DebugSession::new(executable, source_files)?;
        let id = session.id.clone();
        self.sessions.insert(id.clone(), session);
        Ok(id)
    }
    
    /// Get debug session by ID
    pub fn get_session(&self, id: &str) -> Option<&DebugSession> {
        self.sessions.get(id)
    }
    
    /// Get mutable debug session by ID
    pub fn get_session_mut(&mut self, id: &str) -> Option<&mut DebugSession> {
        self.sessions.get_mut(id)
    }
    
    /// Remove debug session
    pub fn remove_session(&mut self, id: &str) -> bool {
        self.sessions.remove(id).is_some()
    }
    
    /// List all active sessions
    pub fn list_sessions(&self) -> Vec<&str> {
        self.sessions.keys().map(|s| s.as_str()).collect()
    }
    
    /// Get debug configuration
    pub fn config(&self) -> &DebugConfig {
        &self.config
    }
    
    /// Update debug configuration
    pub fn update_config(&mut self, config: DebugConfig) {
        self.config = config;
    }
}

impl Default for DebugManager {
    fn default() -> Self {
        Self::new()
    }
}
