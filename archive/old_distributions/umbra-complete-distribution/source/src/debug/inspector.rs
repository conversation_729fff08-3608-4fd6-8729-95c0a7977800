/// Variable inspection for Umbra debugger
/// 
/// Provides runtime variable inspection, expression evaluation, and value formatting.

use crate::error::UmbraResult;
use std::collections::HashMap;
use serde::{Deserialize, Serialize};

/// Variable inspector for debugging
pub struct VariableInspector {
    /// Current scope variables
    current_scope: HashMap<String, VariableValue>,
    
    /// Global variables
    global_scope: HashMap<String, VariableValue>,
    
    /// Function parameters
    parameters: HashMap<String, VariableValue>,
    
    /// Watch expressions
    watch_expressions: HashMap<String, String>,
}

/// Variable value representation for debugging
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VariableValue {
    /// Variable name
    pub name: String,
    
    /// Variable type
    pub var_type: String,
    
    /// String representation of value
    pub value: String,
    
    /// Raw value for structured data
    pub raw_value: Option<serde_json::Value>,
    
    /// Whether the variable can be modified
    pub mutable: bool,
    
    /// Memory address (for reference types)
    pub address: Option<String>,
    
    /// Child variables (for structured types)
    pub children: Vec<VariableValue>,
    
    /// Variable scope
    pub scope: VariableScope,
}

/// Variable scope types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum VariableScope {
    /// Local variable in current function
    Local,
    /// Function parameter
    Parameter,
    /// Global variable
    Global,
    /// Static variable
    Static,
    /// Member variable of an object
    Member,
}

/// Expression evaluation result
#[derive(Debug, Clone)]
pub struct EvaluationResult {
    /// Evaluated value
    pub value: VariableValue,
    
    /// Whether evaluation was successful
    pub success: bool,
    
    /// Error message if evaluation failed
    pub error: Option<String>,
    
    /// Side effects of evaluation
    pub side_effects: Vec<String>,
}

impl VariableInspector {
    /// Create a new variable inspector
    pub fn new() -> Self {
        Self {
            current_scope: HashMap::new(),
            global_scope: HashMap::new(),
            parameters: HashMap::new(),
            watch_expressions: HashMap::new(),
        }
    }
    
    /// Inspect a variable by name
    pub fn inspect(&self, name: &str) -> UmbraResult<VariableValue> {
        // Check current scope first
        if let Some(value) = self.current_scope.get(name) {
            return Ok(value.clone());
        }
        
        // Check parameters
        if let Some(value) = self.parameters.get(name) {
            return Ok(value.clone());
        }
        
        // Check global scope
        if let Some(value) = self.global_scope.get(name) {
            return Ok(value.clone());
        }
        
        // Variable not found
        Err(crate::error::UmbraError::Runtime(format!("Variable '{name}' not found")))
    }
    
    /// Evaluate an expression in the current context
    pub fn evaluate(&self, expression: &str) -> UmbraResult<VariableValue> {
        // For now, implement basic expression evaluation
        // In a real implementation, this would parse and evaluate the expression
        
        if expression.starts_with('$') {
            // Special debug variables
            self.evaluate_debug_variable(expression)
        } else if expression.contains('.') {
            // Member access
            self.evaluate_member_access(expression)
        } else if expression.contains('[') {
            // Array/index access
            self.evaluate_index_access(expression)
        } else {
            // Simple variable lookup
            self.inspect(expression)
        }
    }
    
    /// Evaluate debug variables (e.g., $line, $function, $file)
    fn evaluate_debug_variable(&self, var: &str) -> UmbraResult<VariableValue> {
        match var {
            "$line" => Ok(VariableValue {
                name: "$line".to_string(),
                var_type: "i32".to_string(),
                value: "42".to_string(), // Placeholder
                raw_value: Some(serde_json::Value::Number(42.into())),
                mutable: false,
                address: None,
                children: Vec::new(),
                scope: VariableScope::Static,
            }),
            "$function" => Ok(VariableValue {
                name: "$function".to_string(),
                var_type: "string".to_string(),
                value: "main".to_string(), // Placeholder
                raw_value: Some(serde_json::Value::String("main".to_string())),
                mutable: false,
                address: None,
                children: Vec::new(),
                scope: VariableScope::Static,
            }),
            "$file" => Ok(VariableValue {
                name: "$file".to_string(),
                var_type: "string".to_string(),
                value: "src/main.umbra".to_string(), // Placeholder
                raw_value: Some(serde_json::Value::String("src/main.umbra".to_string())),
                mutable: false,
                address: None,
                children: Vec::new(),
                scope: VariableScope::Static,
            }),
            _ => Err(crate::error::UmbraError::Runtime(format!("Unknown debug variable: {var}"))),
        }
    }
    
    /// Evaluate member access (e.g., obj.field)
    fn evaluate_member_access(&self, expression: &str) -> UmbraResult<VariableValue> {
        let parts: Vec<&str> = expression.split('.').collect();
        if parts.len() != 2 {
            return Err(crate::error::UmbraError::Runtime("Invalid member access".to_string()));
        }
        
        let object_name = parts[0];
        let member_name = parts[1];
        
        // Get the object
        let object = self.inspect(object_name)?;
        
        // Find the member
        for child in &object.children {
            if child.name == member_name {
                return Ok(child.clone());
            }
        }
        
        Err(crate::error::UmbraError::Runtime(format!("Member '{member_name}' not found in '{object_name}'")))
    }
    
    /// Evaluate index access (e.g., arr[0])
    fn evaluate_index_access(&self, expression: &str) -> UmbraResult<VariableValue> {
        if let Some(bracket_pos) = expression.find('[') {
            let array_name = &expression[..bracket_pos];
            let index_part = &expression[bracket_pos + 1..];
            
            if let Some(close_bracket) = index_part.find(']') {
                let index_str = &index_part[..close_bracket];
                
                // Parse index
                let index: usize = index_str.parse()
                    .map_err(|_| crate::error::UmbraError::Runtime("Invalid array index".to_string()))?;
                
                // Get the array
                let array = self.inspect(array_name)?;
                
                // Get the element
                if index < array.children.len() {
                    Ok(array.children[index].clone())
                } else {
                    Err(crate::error::UmbraError::Runtime("Array index out of bounds".to_string()))
                }
            } else {
                Err(crate::error::UmbraError::Runtime("Malformed array access".to_string()))
            }
        } else {
            Err(crate::error::UmbraError::Runtime("Invalid index expression".to_string()))
        }
    }
    
    /// Get all local variables
    pub fn get_locals(&self) -> UmbraResult<HashMap<String, VariableValue>> {
        Ok(self.current_scope.clone())
    }
    
    /// Get all global variables
    pub fn get_globals(&self) -> UmbraResult<HashMap<String, VariableValue>> {
        Ok(self.global_scope.clone())
    }
    
    /// Get function parameters
    pub fn get_parameters(&self) -> UmbraResult<HashMap<String, VariableValue>> {
        Ok(self.parameters.clone())
    }
    
    /// Update variable value (for debugging modifications)
    pub fn update_variable(&mut self, name: &str, new_value: &str) -> UmbraResult<()> {
        // Check current scope first
        if let Some(var) = self.current_scope.get_mut(name) {
            if var.mutable {
                var.value = new_value.to_string();
                println!("🔄 Updated variable '{name}' = {new_value}");
                return Ok(());
            } else {
                return Err(crate::error::UmbraError::Runtime(format!("Variable '{name}' is not mutable")));
            }
        }
        
        // Check global scope
        if let Some(var) = self.global_scope.get_mut(name) {
            if var.mutable {
                var.value = new_value.to_string();
                println!("🔄 Updated global variable '{name}' = {new_value}");
                return Ok(());
            } else {
                return Err(crate::error::UmbraError::Runtime(format!("Global variable '{name}' is not mutable")));
            }
        }
        
        Err(crate::error::UmbraError::Runtime(format!("Variable '{name}' not found")))
    }
    
    /// Add watch expression
    pub fn add_watch(&mut self, name: String, expression: String) -> UmbraResult<()> {
        self.watch_expressions.insert(name.clone(), expression);
        println!("👁️  Added watch: {} = {}", name, self.watch_expressions[&name]);
        Ok(())
    }
    
    /// Remove watch expression
    pub fn remove_watch(&mut self, name: &str) -> bool {
        if self.watch_expressions.remove(name).is_some() {
            println!("❌ Removed watch: {name}");
            true
        } else {
            false
        }
    }
    
    /// Evaluate all watch expressions
    pub fn evaluate_watches(&self) -> HashMap<String, EvaluationResult> {
        let mut results = HashMap::new();
        
        for (name, expression) in &self.watch_expressions {
            let result = match self.evaluate(expression) {
                Ok(value) => EvaluationResult {
                    value,
                    success: true,
                    error: None,
                    side_effects: Vec::new(),
                },
                Err(error) => EvaluationResult {
                    value: VariableValue {
                        name: name.clone(),
                        var_type: "error".to_string(),
                        value: "N/A".to_string(),
                        raw_value: None,
                        mutable: false,
                        address: None,
                        children: Vec::new(),
                        scope: VariableScope::Local,
                    },
                    success: false,
                    error: Some(error.to_string()),
                    side_effects: Vec::new(),
                },
            };
            
            results.insert(name.clone(), result);
        }
        
        results
    }
    
    /// Update current scope variables (called by debugger runtime)
    pub fn update_scope(&mut self, variables: HashMap<String, VariableValue>) {
        self.current_scope = variables;
    }
    
    /// Update global variables (called by debugger runtime)
    pub fn update_globals(&mut self, variables: HashMap<String, VariableValue>) {
        self.global_scope = variables;
    }
    
    /// Update function parameters (called by debugger runtime)
    pub fn update_parameters(&mut self, variables: HashMap<String, VariableValue>) {
        self.parameters = variables;
    }
    
    /// Format variable for display
    pub fn format_variable(&self, var: &VariableValue, max_depth: usize) -> String {
        self.format_variable_recursive(var, 0, max_depth)
    }
    
    /// Recursively format variable with indentation
    fn format_variable_recursive(&self, var: &VariableValue, depth: usize, max_depth: usize) -> String {
        let indent = "  ".repeat(depth);
        let mut result = format!("{}{}: {} = {}", indent, var.name, var.var_type, var.value);
        
        if depth < max_depth && !var.children.is_empty() {
            result.push_str(" {\n");
            for child in &var.children {
                result.push_str(&self.format_variable_recursive(child, depth + 1, max_depth));
                result.push('\n');
            }
            result.push_str(&format!("{indent}}}"));
        }
        
        result
    }
}

impl Default for VariableInspector {
    fn default() -> Self {
        Self::new()
    }
}
