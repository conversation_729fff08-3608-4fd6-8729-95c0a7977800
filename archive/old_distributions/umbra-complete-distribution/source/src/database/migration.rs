/// Database migration system for Umbra
/// Provides schema versioning and automated database updates

use crate::error::{UmbraError, UmbraResult};
use crate::database::connection::{DatabaseConnection, DatabaseValue};
use crate::database::annotations::DatabaseMapping;
use std::collections::HashMap;
use std::sync::Arc;
use std::path::{Path, PathBuf};

/// Migration definition
#[derive(Debug, Clone)]
pub struct Migration {
    pub id: String,
    pub name: String,
    pub version: u64,
    pub up_sql: String,
    pub down_sql: String,
    pub dependencies: Vec<String>,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub applied_at: Option<chrono::DateTime<chrono::Utc>>,
    pub checksum: String,
}

/// Migration status
#[derive(Debug, <PERSON>lone, PartialEq)]
pub enum MigrationStatus {
    Pending,
    Applied,
    Failed,
    RolledBack,
}

/// Migration operation type
#[derive(Debug, Clone)]
pub enum MigrationOperation {
    CreateTable {
        name: String,
        columns: Vec<ColumnDefinition>,
        constraints: Vec<ConstraintDefinition>,
        indexes: Vec<IndexDefinition>,
    },
    DropTable {
        name: String,
    },
    AddColumn {
        table: String,
        column: ColumnDefinition,
    },
    DropColumn {
        table: String,
        column: String,
    },
    ModifyColumn {
        table: String,
        column: String,
        new_definition: ColumnDefinition,
    },
    AddIndex {
        table: String,
        index: IndexDefinition,
    },
    DropIndex {
        table: String,
        index: String,
    },
    AddConstraint {
        table: String,
        constraint: ConstraintDefinition,
    },
    DropConstraint {
        table: String,
        constraint: String,
    },
    RenameTable {
        old_name: String,
        new_name: String,
    },
    RenameColumn {
        table: String,
        old_name: String,
        new_name: String,
    },
    ExecuteSQL {
        sql: String,
    },
}

#[derive(Debug, Clone)]
pub struct ColumnDefinition {
    pub name: String,
    pub data_type: String,
    pub is_nullable: bool,
    pub default_value: Option<String>,
    pub is_primary_key: bool,
    pub is_auto_increment: bool,
    pub max_length: Option<u32>,
    pub precision: Option<u32>,
    pub scale: Option<u32>,
}

#[derive(Debug, Clone)]
pub struct ConstraintDefinition {
    pub name: String,
    pub constraint_type: ConstraintType,
    pub columns: Vec<String>,
    pub referenced_table: Option<String>,
    pub referenced_columns: Option<Vec<String>>,
    pub on_delete: Option<String>,
    pub on_update: Option<String>,
    pub check_expression: Option<String>,
}

#[derive(Debug, Clone)]
pub enum ConstraintType {
    PrimaryKey,
    ForeignKey,
    Unique,
    Check,
    NotNull,
}

#[derive(Debug, Clone)]
pub struct IndexDefinition {
    pub name: String,
    pub columns: Vec<String>,
    pub is_unique: bool,
    pub index_type: Option<String>,
    pub where_clause: Option<String>,
}

/// Migration manager
pub struct MigrationManager {
    connection: Arc<dyn DatabaseConnection>,
    migrations_table: String,
    migrations_dir: PathBuf,
    migrations: HashMap<String, Migration>,
}

impl MigrationManager {
    pub fn new(connection: Arc<dyn DatabaseConnection>, migrations_dir: PathBuf) -> Self {
        Self {
            connection,
            migrations_table: "umbra_migrations".to_string(),
            migrations_dir,
            migrations: HashMap::new(),
        }
    }

    /// Initialize migration system
    pub fn initialize(&self) -> UmbraResult<()> {
        self.create_migrations_table()?;
        Ok(())
    }

    /// Create migrations table if it doesn't exist
    fn create_migrations_table(&self) -> UmbraResult<()> {
        let sql = format!(
            r#"
            CREATE TABLE IF NOT EXISTS {} (
                id VARCHAR(255) PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                version BIGINT NOT NULL,
                up_sql TEXT NOT NULL,
                down_sql TEXT NOT NULL,
                checksum VARCHAR(64) NOT NULL,
                applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            "#,
            self.migrations_table
        );

        self.connection.execute(&sql, &[])?;
        Ok(())
    }

    /// Load migrations from directory
    pub fn load_migrations(&mut self) -> UmbraResult<()> {
        if !self.migrations_dir.exists() {
            std::fs::create_dir_all(&self.migrations_dir)?;
        }

        for entry in std::fs::read_dir(&self.migrations_dir)? {
            let entry = entry?;
            let path = entry.path();
            
            if path.extension().and_then(|s| s.to_str()) == Some("sql") {
                let migration = self.parse_migration_file(&path)?;
                self.migrations.insert(migration.id.clone(), migration);
            }
        }

        Ok(())
    }

    /// Parse migration file
    fn parse_migration_file(&self, path: &Path) -> UmbraResult<Migration> {
        let content = std::fs::read_to_string(path)?;
        let filename = path.file_stem()
            .and_then(|s| s.to_str())
            .ok_or_else(|| UmbraError::DatabaseMigration("Invalid migration filename".to_string()))?;

        // Parse filename: version_name.sql
        let parts: Vec<&str> = filename.splitn(2, '_').collect();
        if parts.len() != 2 {
            return Err(UmbraError::DatabaseMigration(
                "Migration filename must be in format: version_name.sql".to_string()
            ));
        }

        let version: u64 = parts[0].parse()
            .map_err(|_| UmbraError::DatabaseMigration("Invalid version number".to_string()))?;
        let name = parts[1].replace('_', " ");

        // Split content into up and down sections
        let sections = self.parse_migration_content(&content)?;

        let migration = Migration {
            id: filename.to_string(),
            name,
            version,
            up_sql: sections.0,
            down_sql: sections.1,
            dependencies: vec![], // Would be parsed from comments
            created_at: chrono::Utc::now(),
            applied_at: None,
            checksum: self.calculate_checksum(&content),
        };

        Ok(migration)
    }

    /// Parse migration content into up and down sections
    fn parse_migration_content(&self, content: &str) -> UmbraResult<(String, String)> {
        let mut up_sql = String::new();
        let mut down_sql = String::new();
        let mut current_section = "up";

        for line in content.lines() {
            let trimmed = line.trim();
            
            if trimmed.starts_with("-- +migrate Up") {
                current_section = "up";
                continue;
            } else if trimmed.starts_with("-- +migrate Down") {
                current_section = "down";
                continue;
            }

            match current_section {
                "up" => {
                    up_sql.push_str(line);
                    up_sql.push('\n');
                }
                "down" => {
                    down_sql.push_str(line);
                    down_sql.push('\n');
                }
                _ => {}
            }
        }

        Ok((up_sql.trim().to_string(), down_sql.trim().to_string()))
    }

    /// Calculate checksum for migration content
    fn calculate_checksum(&self, content: &str) -> String {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};

        let mut hasher = DefaultHasher::new();
        content.hash(&mut hasher);
        format!("{:x}", hasher.finish())
    }

    /// Get applied migrations
    pub fn get_applied_migrations(&self) -> UmbraResult<Vec<Migration>> {
        let sql = format!("SELECT * FROM {} ORDER BY version", self.migrations_table);
        let rows = self.connection.query(&sql, &[])?;

        let mut migrations = Vec::new();
        for row in rows {
            let migration = Migration {
                id: row.get_string("id")?,
                name: row.get_string("name")?,
                version: row.get_int("version")? as u64,
                up_sql: row.get_string("up_sql")?,
                down_sql: row.get_string("down_sql")?,
                dependencies: vec![], // Would be parsed from stored data
                created_at: chrono::Utc::now(), // Would be parsed from timestamp
                applied_at: Some(chrono::Utc::now()), // Would be parsed from timestamp
                checksum: row.get_string("checksum")?,
            };
            migrations.push(migration);
        }

        Ok(migrations)
    }

    /// Get pending migrations
    pub fn get_pending_migrations(&self) -> UmbraResult<Vec<&Migration>> {
        let applied = self.get_applied_migrations()?;
        let applied_ids: std::collections::HashSet<String> = applied.into_iter().map(|m| m.id).collect();

        let mut pending: Vec<&Migration> = self.migrations
            .values()
            .filter(|m| !applied_ids.contains(&m.id))
            .collect();

        // Sort by version
        pending.sort_by_key(|m| m.version);

        Ok(pending)
    }

    /// Apply pending migrations
    pub fn migrate(&self) -> UmbraResult<Vec<String>> {
        let pending = self.get_pending_migrations()?;
        let mut applied = Vec::new();

        for migration in pending {
            self.apply_migration(migration)?;
            applied.push(migration.id.clone());
        }

        Ok(applied)
    }

    /// Apply a single migration
    fn apply_migration(&self, migration: &Migration) -> UmbraResult<()> {
        // Begin transaction
        let transaction = self.connection.begin_transaction()?;

        // Execute migration SQL
        match transaction.execute(&migration.up_sql, &[]) {
            Ok(_) => {
                // Record migration as applied
                let insert_sql = format!(
                    "INSERT INTO {} (id, name, version, up_sql, down_sql, checksum) VALUES (?, ?, ?, ?, ?, ?)",
                    self.migrations_table
                );
                
                transaction.execute(&insert_sql, &[
                    DatabaseValue::String(migration.id.clone()),
                    DatabaseValue::String(migration.name.clone()),
                    DatabaseValue::Int64(migration.version as i64),
                    DatabaseValue::String(migration.up_sql.clone()),
                    DatabaseValue::String(migration.down_sql.clone()),
                    DatabaseValue::String(migration.checksum.clone()),
                ])?;

                transaction.commit()?;
                log::info!("Applied migration: {}", migration.name);
                Ok(())
            }
            Err(e) => {
                transaction.rollback()?;
                Err(UmbraError::DatabaseMigration(format!(
                    "Failed to apply migration {}: {}", migration.name, e
                )))
            }
        }
    }

    /// Rollback last migration
    pub fn rollback(&self) -> UmbraResult<String> {
        let applied = self.get_applied_migrations()?;
        if let Some(last_migration) = applied.last() {
            self.rollback_migration(last_migration)?;
            Ok(last_migration.id.clone())
        } else {
            Err(UmbraError::DatabaseMigration("No migrations to rollback".to_string()))
        }
    }

    /// Rollback a specific migration
    fn rollback_migration(&self, migration: &Migration) -> UmbraResult<()> {
        // Begin transaction
        let transaction = self.connection.begin_transaction()?;

        // Execute rollback SQL
        match transaction.execute(&migration.down_sql, &[]) {
            Ok(_) => {
                // Remove migration record
                let delete_sql = format!("DELETE FROM {} WHERE id = ?", self.migrations_table);
                transaction.execute(&delete_sql, &[DatabaseValue::String(migration.id.clone())])?;

                transaction.commit()?;
                log::info!("Rolled back migration: {}", migration.name);
                Ok(())
            }
            Err(e) => {
                transaction.rollback()?;
                Err(UmbraError::DatabaseMigration(format!(
                    "Failed to rollback migration {}: {}", migration.name, e
                )))
            }
        }
    }

    /// Generate migration from database mapping
    pub fn generate_migration_from_mapping(
        &self,
        name: &str,
        mapping: &DatabaseMapping,
    ) -> UmbraResult<Migration> {
        let version = chrono::Utc::now().timestamp() as u64;
        let id = format!("{}_{}", version, name.replace(' ', "_"));

        let up_sql = self.generate_create_table_sql(mapping)?;
        let down_sql = format!("DROP TABLE IF EXISTS {};", mapping.table_name);

        let migration = Migration {
            id,
            name: name.to_string(),
            version,
            up_sql,
            down_sql,
            dependencies: vec![],
            created_at: chrono::Utc::now(),
            applied_at: None,
            checksum: String::new(), // Would be calculated
        };

        Ok(migration)
    }

    /// Generate CREATE TABLE SQL from database mapping
    fn generate_create_table_sql(&self, mapping: &DatabaseMapping) -> UmbraResult<String> {
        let mut sql = format!("CREATE TABLE {} (\n", mapping.table_name);
        let mut columns = Vec::new();

        // Add columns
        for field_mapping in mapping.fields.values() {
            let mut column_def = format!("  {} {}", 
                field_mapping.column_name, 
                field_mapping.data_type.as_ref().unwrap_or(&"TEXT".to_string())
            );

            if !field_mapping.is_nullable {
                column_def.push_str(" NOT NULL");
            }

            if field_mapping.is_auto_increment {
                column_def.push_str(" AUTO_INCREMENT");
            }

            if let Some(ref default) = field_mapping.default_value {
                column_def.push_str(&format!(" DEFAULT {}", self.format_default_value(default)));
            }

            columns.push(column_def);
        }

        // Add primary key constraint
        if let Some(ref pk) = mapping.primary_key {
            columns.push(format!("  PRIMARY KEY ({})", pk));
        }

        sql.push_str(&columns.join(",\n"));
        sql.push_str("\n);");

        Ok(sql)
    }

    fn format_default_value(&self, default: &crate::database::annotations::DefaultValue) -> String {
        match default {
            crate::database::annotations::DefaultValue::Literal(s) => format!("'{}'", s),
            crate::database::annotations::DefaultValue::Function(f) => f.clone(),
            crate::database::annotations::DefaultValue::CurrentTimestamp => "CURRENT_TIMESTAMP".to_string(),
            crate::database::annotations::DefaultValue::CurrentDate => "CURRENT_DATE".to_string(),
            crate::database::annotations::DefaultValue::CurrentTime => "CURRENT_TIME".to_string(),
            crate::database::annotations::DefaultValue::Uuid => "UUID()".to_string(),
            crate::database::annotations::DefaultValue::Sequence(s) => s.clone(),
        }
    }

    /// Create new migration file
    pub fn create_migration(&self, name: &str) -> UmbraResult<PathBuf> {
        let version = chrono::Utc::now().timestamp();
        let filename = format!("{}_{}.sql", version, name.replace(' ', "_"));
        let filepath = self.migrations_dir.join(&filename);

        let content = format!(
            r#"-- +migrate Up
-- Add your up migration SQL here

-- +migrate Down
-- Add your down migration SQL here
"#
        );

        std::fs::write(&filepath, content)?;
        Ok(filepath)
    }
}

/// Initialize migration system
pub fn initialize_migration_system() -> UmbraResult<()> {
    log::info!("Initializing database migration system");
    // This would set up migration directories and default configurations
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::database::{DatabaseConfig, DatabaseType};
    use crate::database::connection::ConnectionFactory;
    use tempfile::TempDir;

    #[test]
    fn test_migration_creation() {
        let temp_dir = TempDir::new().unwrap();
        let config = DatabaseConfig::new(DatabaseType::SQLite);
        let connection = ConnectionFactory::create_connection(&config).unwrap();
        
        let manager = MigrationManager::new(connection, temp_dir.path().to_path_buf());
        
        let migration_path = manager.create_migration("create_users_table").unwrap();
        assert!(migration_path.exists());
        
        let content = std::fs::read_to_string(&migration_path).unwrap();
        assert!(content.contains("-- +migrate Up"));
        assert!(content.contains("-- +migrate Down"));
    }

    #[test]
    fn test_migration_parsing() {
        let content = r#"
-- +migrate Up
CREATE TABLE users (id INTEGER PRIMARY KEY);

-- +migrate Down
DROP TABLE users;
"#;
        
        let temp_dir = TempDir::new().unwrap();
        let config = DatabaseConfig::new(DatabaseType::SQLite);
        let connection = ConnectionFactory::create_connection(&config).unwrap();
        let manager = MigrationManager::new(connection, temp_dir.path().to_path_buf());
        
        let (up_sql, down_sql) = manager.parse_migration_content(content).unwrap();
        assert!(up_sql.contains("CREATE TABLE users"));
        assert!(down_sql.contains("DROP TABLE users"));
    }
}
