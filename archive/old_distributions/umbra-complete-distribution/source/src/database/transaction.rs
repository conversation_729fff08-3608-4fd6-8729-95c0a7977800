/// Transaction management for Umbra database operations
/// Provides ACID transaction support with nested transactions and savepoints

use crate::error::{UmbraError, UmbraResult};
use crate::database::connection::{DatabaseConnection, DatabaseValue, QueryResult, Row, Transaction};
use std::sync::{Arc, Mutex};

/// Transaction isolation levels
#[derive(Debug, Clone, Copy, PartialEq)]
pub enum IsolationLevel {
    ReadUncommitted,
    ReadCommitted,
    RepeatableRead,
    Serializable,
}

impl IsolationLevel {
    pub fn to_sql(&self) -> &'static str {
        match self {
            IsolationLevel::ReadUncommitted => "READ UNCOMMITTED",
            IsolationLevel::ReadCommitted => "READ COMMITTED",
            IsolationLevel::RepeatableRead => "REPEATABLE READ",
            IsolationLevel::Serializable => "SERIALIZABLE",
        }
    }
}

/// Transaction options
#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub struct TransactionOptions {
    pub isolation_level: Option<IsolationLevel>,
    pub read_only: bool,
    pub timeout: Option<std::time::Duration>,
    pub retry_on_conflict: bool,
    pub max_retries: u32,
}

impl Default for TransactionOptions {
    fn default() -> Self {
        Self {
            isolation_level: None,
            read_only: false,
            timeout: Some(std::time::Duration::from_secs(30)),
            retry_on_conflict: true,
            max_retries: 3,
        }
    }
}

/// Transaction status
#[derive(Debug, Clone, PartialEq)]
pub enum TransactionStatus {
    Active,
    Committed,
    RolledBack,
    Failed,
}

/// Savepoint for nested transactions
#[derive(Debug, Clone)]
pub struct Savepoint {
    pub name: String,
    pub created_at: chrono::DateTime<chrono::Utc>,
}

/// Enhanced transaction manager with nested transaction support
pub struct TransactionManager {
    connection: Arc<dyn DatabaseConnection>,
    current_transaction: Option<Arc<Mutex<ManagedTransaction>>>,
    savepoint_counter: u32,
}

/// Managed transaction with enhanced features
pub struct ManagedTransaction {
    id: String,
    status: TransactionStatus,
    options: TransactionOptions,
    savepoints: Vec<Savepoint>,
    started_at: chrono::DateTime<chrono::Utc>,
    operations: Vec<TransactionOperation>,
    connection: Arc<dyn DatabaseConnection>,
    underlying_transaction: Option<Box<dyn Transaction>>,
}

/// Transaction operation for audit trail
#[derive(Debug, Clone)]
pub struct TransactionOperation {
    pub operation_type: OperationType,
    pub query: String,
    pub parameters: Vec<String>, // Simplified parameter representation
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub rows_affected: Option<u64>,
    pub execution_time: std::time::Duration,
}

#[derive(Debug, Clone)]
pub enum OperationType {
    Select,
    Insert,
    Update,
    Delete,
    CreateTable,
    DropTable,
    AlterTable,
    CreateIndex,
    DropIndex,
    Other(String),
}

impl TransactionManager {
    pub fn new(connection: Arc<dyn DatabaseConnection>) -> Self {
        Self {
            connection,
            current_transaction: None,
            savepoint_counter: 0,
        }
    }

    /// Begin a new transaction
    pub fn begin_transaction(&mut self, options: TransactionOptions) -> UmbraResult<Arc<Mutex<ManagedTransaction>>> {
        if self.current_transaction.is_some() {
            return Err(UmbraError::DatabaseTransaction(
                "Transaction already active".to_string()
            ));
        }

        let underlying_tx = self.connection.begin_transaction()?;
        
        let transaction = ManagedTransaction {
            id: uuid::Uuid::new_v4().to_string(),
            status: TransactionStatus::Active,
            options,
            savepoints: Vec::new(),
            started_at: chrono::Utc::now(),
            operations: Vec::new(),
            connection: Arc::clone(&self.connection),
            underlying_transaction: Some(underlying_tx),
        };

        let managed_tx = Arc::new(Mutex::new(transaction));
        self.current_transaction = Some(Arc::clone(&managed_tx));

        // Set isolation level if specified
        if let Some(isolation_level) = managed_tx.lock().unwrap().options.isolation_level {
            self.set_isolation_level(isolation_level)?;
        }

        // Set read-only mode if specified
        if managed_tx.lock().unwrap().options.read_only {
            self.set_read_only(true)?;
        }

        Ok(managed_tx)
    }

    /// Get current active transaction
    pub fn current_transaction(&self) -> Option<Arc<Mutex<ManagedTransaction>>> {
        self.current_transaction.clone()
    }

    /// Execute query within current transaction
    pub fn execute(&mut self, query: &str, params: &[DatabaseValue]) -> UmbraResult<QueryResult> {
        let tx = self.current_transaction.as_ref()
            .ok_or_else(|| UmbraError::DatabaseTransaction("No active transaction".to_string()))?;

        let start_time = std::time::Instant::now();
        let result = {
            let transaction = tx.lock().unwrap();
            if transaction.status != TransactionStatus::Active {
                return Err(UmbraError::DatabaseTransaction("Transaction is not active".to_string()));
            }

            if let Some(ref underlying_tx) = transaction.underlying_transaction {
                underlying_tx.execute(query, params)
            } else {
                return Err(UmbraError::DatabaseTransaction("No underlying transaction".to_string()));
            }
        };

        let execution_time = start_time.elapsed();

        // Record operation
        let operation = TransactionOperation {
            operation_type: Self::determine_operation_type(query),
            query: query.to_string(),
            parameters: params.iter().map(|p| format!("{:?}", p)).collect(),
            timestamp: chrono::Utc::now(),
            rows_affected: result.as_ref().ok().map(|r| r.rows_affected),
            execution_time,
        };

        tx.lock().unwrap().operations.push(operation);

        result
    }

    /// Execute query and return results within current transaction
    pub fn query(&mut self, query: &str, params: &[DatabaseValue]) -> UmbraResult<Vec<Row>> {
        let tx = self.current_transaction.as_ref()
            .ok_or_else(|| UmbraError::DatabaseTransaction("No active transaction".to_string()))?;

        let start_time = std::time::Instant::now();
        let result = {
            let transaction = tx.lock().unwrap();
            if transaction.status != TransactionStatus::Active {
                return Err(UmbraError::DatabaseTransaction("Transaction is not active".to_string()));
            }

            if let Some(ref underlying_tx) = transaction.underlying_transaction {
                underlying_tx.query(query, params)
            } else {
                return Err(UmbraError::DatabaseTransaction("No underlying transaction".to_string()));
            }
        };

        let execution_time = start_time.elapsed();

        // Record operation
        let operation = TransactionOperation {
            operation_type: Self::determine_operation_type(query),
            query: query.to_string(),
            parameters: params.iter().map(|p| format!("{:?}", p)).collect(),
            timestamp: chrono::Utc::now(),
            rows_affected: None, // Query operations don't have rows_affected
            execution_time,
        };

        tx.lock().unwrap().operations.push(operation);

        result
    }

    /// Create a savepoint
    pub fn create_savepoint(&mut self, name: Option<String>) -> UmbraResult<String> {
        let savepoint_name = name.unwrap_or_else(|| {
            self.savepoint_counter += 1;
            format!("sp_{}", self.savepoint_counter)
        });

        // Execute SAVEPOINT command
        let savepoint_sql = format!("SAVEPOINT {}", savepoint_name);
        self.execute(&savepoint_sql, &[])?;

        // Record savepoint
        if let Some(ref tx) = self.current_transaction {
            let savepoint = Savepoint {
                name: savepoint_name.clone(),
                created_at: chrono::Utc::now(),
            };
            tx.lock().unwrap().savepoints.push(savepoint);
        }

        Ok(savepoint_name)
    }

    /// Rollback to savepoint
    pub fn rollback_to_savepoint(&mut self, savepoint_name: &str) -> UmbraResult<()> {
        // Check if savepoint exists
        if let Some(ref tx) = self.current_transaction {
            let transaction = tx.lock().unwrap();
            if !transaction.savepoints.iter().any(|sp| sp.name == savepoint_name) {
                return Err(UmbraError::DatabaseTransaction(
                    format!("Savepoint '{}' not found", savepoint_name)
                ));
            }
        } else {
            return Err(UmbraError::DatabaseTransaction("No active transaction".to_string()));
        }

        // Execute ROLLBACK TO SAVEPOINT command
        let rollback_sql = format!("ROLLBACK TO SAVEPOINT {}", savepoint_name);
        self.execute(&rollback_sql, &[])?;

        // Remove savepoints created after this one
        if let Some(ref tx) = self.current_transaction {
            let mut transaction = tx.lock().unwrap();
            if let Some(savepoint_index) = transaction.savepoints
                .iter()
                .position(|sp| sp.name == savepoint_name) {
                transaction.savepoints.truncate(savepoint_index + 1);
            }
        }

        Ok(())
    }

    /// Release savepoint
    pub fn release_savepoint(&mut self, savepoint_name: &str) -> UmbraResult<()> {
        if self.current_transaction.is_none() {
            return Err(UmbraError::DatabaseTransaction("No active transaction".to_string()));
        }

        // Execute RELEASE SAVEPOINT command
        let release_sql = format!("RELEASE SAVEPOINT {}", savepoint_name);
        self.execute(&release_sql, &[])?;

        // Remove savepoint from list
        if let Some(ref tx) = self.current_transaction {
            let mut transaction = tx.lock().unwrap();
            transaction.savepoints.retain(|sp| sp.name != savepoint_name);
        }

        Ok(())
    }

    /// Commit current transaction
    pub fn commit(&mut self) -> UmbraResult<()> {
        let tx = self.current_transaction.take()
            .ok_or_else(|| UmbraError::DatabaseTransaction("No active transaction".to_string()))?;

        let result = {
            let mut transaction = tx.lock().unwrap();
            if transaction.status != TransactionStatus::Active {
                return Err(UmbraError::DatabaseTransaction("Transaction is not active".to_string()));
            }

            if let Some(ref underlying_tx) = transaction.underlying_transaction {
                let result = underlying_tx.commit();
                transaction.status = if result.is_ok() {
                    TransactionStatus::Committed
                } else {
                    TransactionStatus::Failed
                };
                result
            } else {
                Err(UmbraError::DatabaseTransaction("No underlying transaction".to_string()))
            }
        };

        result
    }

    /// Rollback current transaction
    pub fn rollback(&mut self) -> UmbraResult<()> {
        let tx = self.current_transaction.take()
            .ok_or_else(|| UmbraError::DatabaseTransaction("No active transaction".to_string()))?;

        let result = {
            let mut transaction = tx.lock().unwrap();
            if transaction.status != TransactionStatus::Active {
                return Err(UmbraError::DatabaseTransaction("Transaction is not active".to_string()));
            }

            if let Some(ref underlying_tx) = transaction.underlying_transaction {
                let result = underlying_tx.rollback();
                transaction.status = if result.is_ok() {
                    TransactionStatus::RolledBack
                } else {
                    TransactionStatus::Failed
                };
                result
            } else {
                Err(UmbraError::DatabaseTransaction("No underlying transaction".to_string()))
            }
        };

        result
    }

    /// Set transaction isolation level
    fn set_isolation_level(&self, level: IsolationLevel) -> UmbraResult<()> {
        let sql = format!("SET TRANSACTION ISOLATION LEVEL {}", level.to_sql());
        self.connection.execute(&sql, &[])?;
        Ok(())
    }

    /// Set read-only mode
    fn set_read_only(&self, read_only: bool) -> UmbraResult<()> {
        let sql = if read_only {
            "SET TRANSACTION READ ONLY"
        } else {
            "SET TRANSACTION READ WRITE"
        };
        self.connection.execute(sql, &[])?;
        Ok(())
    }

    /// Determine operation type from SQL query
    fn determine_operation_type(query: &str) -> OperationType {
        let query_upper = query.trim().to_uppercase();
        
        if query_upper.starts_with("SELECT") {
            OperationType::Select
        } else if query_upper.starts_with("INSERT") {
            OperationType::Insert
        } else if query_upper.starts_with("UPDATE") {
            OperationType::Update
        } else if query_upper.starts_with("DELETE") {
            OperationType::Delete
        } else if query_upper.starts_with("CREATE TABLE") {
            OperationType::CreateTable
        } else if query_upper.starts_with("DROP TABLE") {
            OperationType::DropTable
        } else if query_upper.starts_with("ALTER TABLE") {
            OperationType::AlterTable
        } else if query_upper.starts_with("CREATE INDEX") {
            OperationType::CreateIndex
        } else if query_upper.starts_with("DROP INDEX") {
            OperationType::DropIndex
        } else {
            OperationType::Other(query_upper.split_whitespace().next().unwrap_or("UNKNOWN").to_string())
        }
    }

    /// Get transaction statistics
    pub fn get_transaction_stats(&self) -> Option<TransactionStats> {
        self.current_transaction.as_ref().map(|tx| {
            let transaction = tx.lock().unwrap();
            TransactionStats {
                id: transaction.id.clone(),
                status: transaction.status.clone(),
                started_at: transaction.started_at,
                duration: chrono::Utc::now().signed_duration_since(transaction.started_at),
                operation_count: transaction.operations.len(),
                savepoint_count: transaction.savepoints.len(),
                total_execution_time: transaction.operations
                    .iter()
                    .map(|op| op.execution_time)
                    .sum(),
            }
        })
    }
}

/// Transaction statistics
#[derive(Debug, Clone)]
pub struct TransactionStats {
    pub id: String,
    pub status: TransactionStatus,
    pub started_at: chrono::DateTime<chrono::Utc>,
    pub duration: chrono::Duration,
    pub operation_count: usize,
    pub savepoint_count: usize,
    pub total_execution_time: std::time::Duration,
}

/// Transaction scope helper for automatic cleanup
pub struct TransactionScope<'a> {
    manager: &'a mut TransactionManager,
    transaction: Arc<Mutex<ManagedTransaction>>,
    auto_commit: bool,
}

impl<'a> TransactionScope<'a> {
    pub fn new(manager: &'a mut TransactionManager, options: TransactionOptions) -> UmbraResult<Self> {
        let transaction = manager.begin_transaction(options)?;
        Ok(Self {
            manager,
            transaction,
            auto_commit: true,
        })
    }

    /// Disable auto-commit (requires manual commit/rollback)
    pub fn manual_commit(mut self) -> Self {
        self.auto_commit = false;
        self
    }

    /// Execute query within transaction scope
    pub fn execute(&mut self, query: &str, params: &[DatabaseValue]) -> UmbraResult<QueryResult> {
        self.manager.execute(query, params)
    }

    /// Query within transaction scope
    pub fn query(&mut self, query: &str, params: &[DatabaseValue]) -> UmbraResult<Vec<Row>> {
        self.manager.query(query, params)
    }

    /// Commit transaction
    pub fn commit(mut self) -> UmbraResult<()> {
        self.auto_commit = false; // Prevent auto-rollback in drop
        self.manager.commit()
    }

    /// Rollback transaction
    pub fn rollback(mut self) -> UmbraResult<()> {
        self.auto_commit = false; // Prevent auto-rollback in drop
        self.manager.rollback()
    }
}

impl<'a> Drop for TransactionScope<'a> {
    fn drop(&mut self) {
        if self.auto_commit {
            // Auto-rollback if not explicitly committed
            let _ = self.manager.rollback();
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::database::{DatabaseConfig, DatabaseType};
    use crate::database::connection::ConnectionFactory;

    #[test]
    fn test_transaction_manager_creation() {
        let config = DatabaseConfig::new(DatabaseType::SQLite);
        let connection = ConnectionFactory::create_connection(&config).unwrap();
        let manager = TransactionManager::new(connection);
        
        assert!(manager.current_transaction.is_none());
        assert_eq!(manager.savepoint_counter, 0);
    }

    #[test]
    fn test_transaction_options_default() {
        let options = TransactionOptions::default();
        assert!(options.isolation_level.is_none());
        assert!(!options.read_only);
        assert!(options.retry_on_conflict);
        assert_eq!(options.max_retries, 3);
    }

    #[test]
    fn test_operation_type_determination() {
        assert!(matches!(
            TransactionManager::determine_operation_type("SELECT * FROM users"),
            OperationType::Select
        ));
        assert!(matches!(
            TransactionManager::determine_operation_type("INSERT INTO users VALUES (1, 'John')"),
            OperationType::Insert
        ));
        assert!(matches!(
            TransactionManager::determine_operation_type("UPDATE users SET name = 'Jane'"),
            OperationType::Update
        ));
        assert!(matches!(
            TransactionManager::determine_operation_type("DELETE FROM users WHERE id = 1"),
            OperationType::Delete
        ));
    }

    #[test]
    fn test_isolation_level_sql() {
        assert_eq!(IsolationLevel::ReadUncommitted.to_sql(), "READ UNCOMMITTED");
        assert_eq!(IsolationLevel::ReadCommitted.to_sql(), "READ COMMITTED");
        assert_eq!(IsolationLevel::RepeatableRead.to_sql(), "REPEATABLE READ");
        assert_eq!(IsolationLevel::Serializable.to_sql(), "SERIALIZABLE");
    }
}
