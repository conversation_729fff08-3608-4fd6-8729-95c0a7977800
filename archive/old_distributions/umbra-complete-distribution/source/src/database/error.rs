/// Database-specific error handling for Umbra
/// Provides detailed error types and recovery mechanisms

use crate::error::UmbraError;
use std::fmt;

/// Database-specific error types
#[derive(Debug, Clone)]
pub enum DatabaseError {
    /// Connection-related errors
    Connection(ConnectionError),
    
    /// Query execution errors
    Query(QueryError),
    
    /// Transaction errors
    Transaction(TransactionError),
    
    /// Schema-related errors
    Schema(SchemaError),
    
    /// Migration errors
    Migration(MigrationError),
    
    /// Data validation errors
    Validation(ValidationError),
    
    /// Timeout errors
    Timeout(TimeoutError),
    
    /// Constraint violation errors
    Constraint(ConstraintError),
    
    /// Authentication/authorization errors
    Auth(AuthError),
}

#[derive(Debug, Clone)]
pub struct ConnectionError {
    pub error_type: ConnectionErrorType,
    pub message: String,
    pub database_type: String,
    pub connection_string: Option<String>,
    pub retry_count: u32,
    pub is_recoverable: bool,
}

#[derive(Debug, Clone)]
pub enum ConnectionErrorType {
    FailedToConnect,
    ConnectionLost,
    ConnectionTimeout,
    InvalidConnectionString,
    DatabaseNotFound,
    HostUnreachable,
    PortClosed,
    SslError,
    AuthenticationFailed,
    PermissionDenied,
}

#[derive(Debug, Clone)]
pub struct QueryError {
    pub error_type: QueryErrorType,
    pub message: String,
    pub query: Option<String>,
    pub line_number: Option<u32>,
    pub column_number: Option<u32>,
    pub error_code: Option<String>,
}

#[derive(Debug, Clone)]
pub enum QueryErrorType {
    SyntaxError,
    SemanticError,
    ExecutionError,
    TypeMismatch,
    ColumnNotFound,
    TableNotFound,
    IndexNotFound,
    DuplicateKey,
    ForeignKeyViolation,
    CheckConstraintViolation,
    NotNullViolation,
    DataTooLong,
    NumericOverflow,
    DivisionByZero,
    InvalidFunction,
    PermissionDenied,
}

#[derive(Debug, Clone)]
pub struct TransactionError {
    pub error_type: TransactionErrorType,
    pub message: String,
    pub transaction_id: Option<String>,
    pub isolation_level: Option<String>,
}

#[derive(Debug, Clone)]
pub enum TransactionErrorType {
    BeginFailed,
    CommitFailed,
    RollbackFailed,
    DeadlockDetected,
    SerializationFailure,
    TransactionAborted,
    InvalidIsolationLevel,
    NestedTransactionNotSupported,
}

#[derive(Debug, Clone)]
pub struct SchemaError {
    pub error_type: SchemaErrorType,
    pub message: String,
    pub object_name: Option<String>,
    pub object_type: Option<String>,
}

#[derive(Debug, Clone)]
pub enum SchemaErrorType {
    TableNotFound,
    ColumnNotFound,
    IndexNotFound,
    ConstraintNotFound,
    ViewNotFound,
    ProcedureNotFound,
    InvalidSchema,
    SchemaVersionMismatch,
    CircularDependency,
}

#[derive(Debug, Clone)]
pub struct MigrationError {
    pub error_type: MigrationErrorType,
    pub message: String,
    pub migration_name: Option<String>,
    pub version: Option<String>,
}

#[derive(Debug, Clone)]
pub enum MigrationErrorType {
    MigrationNotFound,
    MigrationAlreadyApplied,
    MigrationFailed,
    RollbackFailed,
    InvalidMigrationFile,
    MigrationConflict,
    VersionMismatch,
}

#[derive(Debug, Clone)]
pub struct ValidationError {
    pub error_type: ValidationType,
    pub message: String,
    pub field_name: Option<String>,
    pub value: Option<String>,
    pub constraint: Option<String>,
}

#[derive(Debug, Clone)]
pub enum ValidationType {
    RequiredField,
    InvalidType,
    InvalidFormat,
    ValueTooLong,
    ValueTooShort,
    ValueOutOfRange,
    InvalidEmail,
    InvalidUrl,
    InvalidDate,
    CustomValidation,
}

#[derive(Debug, Clone)]
pub struct TimeoutError {
    pub operation: String,
    pub timeout_duration: std::time::Duration,
    pub elapsed_time: std::time::Duration,
}

#[derive(Debug, Clone)]
pub struct ConstraintError {
    pub constraint_type: ConstraintType,
    pub constraint_name: String,
    pub table_name: String,
    pub column_names: Vec<String>,
    pub message: String,
}

#[derive(Debug, Clone)]
pub enum ConstraintType {
    PrimaryKey,
    ForeignKey,
    Unique,
    Check,
    NotNull,
    Default,
}

#[derive(Debug, Clone)]
pub struct AuthError {
    pub error_type: AuthErrorType,
    pub message: String,
    pub username: Option<String>,
    pub database: Option<String>,
}

#[derive(Debug, Clone)]
pub enum AuthErrorType {
    InvalidCredentials,
    UserNotFound,
    PasswordExpired,
    AccountLocked,
    InsufficientPrivileges,
    DatabaseAccessDenied,
    ConnectionLimitExceeded,
}

impl fmt::Display for DatabaseError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            DatabaseError::Connection(err) => write!(f, "Connection error: {}", err.message),
            DatabaseError::Query(err) => write!(f, "Query error: {}", err.message),
            DatabaseError::Transaction(err) => write!(f, "Transaction error: {}", err.message),
            DatabaseError::Schema(err) => write!(f, "Schema error: {}", err.message),
            DatabaseError::Migration(err) => write!(f, "Migration error: {}", err.message),
            DatabaseError::Validation(err) => write!(f, "Validation error: {}", err.message),
            DatabaseError::Timeout(err) => write!(f, "Timeout error: {}", err.operation),
            DatabaseError::Constraint(err) => write!(f, "Constraint error: {}", err.message),
            DatabaseError::Auth(err) => write!(f, "Authentication error: {}", err.message),
        }
    }
}

impl std::error::Error for DatabaseError {}

impl From<DatabaseError> for UmbraError {
    fn from(err: DatabaseError) -> Self {
        match err {
            DatabaseError::Connection(_) => UmbraError::DatabaseConnection(err.to_string()),
            DatabaseError::Query(_) => UmbraError::DatabaseQuery(err.to_string()),
            DatabaseError::Transaction(_) => UmbraError::DatabaseTransaction(err.to_string()),
            DatabaseError::Migration(_) => UmbraError::DatabaseMigration(err.to_string()),
            _ => UmbraError::Database(err.to_string()),
        }
    }
}

/// Error recovery strategies
pub struct ErrorRecovery;

impl ErrorRecovery {
    /// Determine if an error is recoverable
    pub fn is_recoverable(error: &DatabaseError) -> bool {
        match error {
            DatabaseError::Connection(conn_err) => conn_err.is_recoverable,
            DatabaseError::Query(query_err) => match query_err.error_type {
                QueryErrorType::ExecutionError | QueryErrorType::TypeMismatch => false,
                QueryErrorType::SyntaxError | QueryErrorType::SemanticError => false,
                _ => true,
            },
            DatabaseError::Transaction(trans_err) => match trans_err.error_type {
                TransactionErrorType::DeadlockDetected => true,
                TransactionErrorType::SerializationFailure => true,
                _ => false,
            },
            DatabaseError::Timeout(_) => true,
            _ => false,
        }
    }

    /// Get suggested retry delay for recoverable errors
    pub fn get_retry_delay(error: &DatabaseError, attempt: u32) -> Option<std::time::Duration> {
        if !Self::is_recoverable(error) {
            return None;
        }

        let base_delay = match error {
            DatabaseError::Connection(_) => std::time::Duration::from_millis(1000),
            DatabaseError::Transaction(_) => std::time::Duration::from_millis(100),
            DatabaseError::Timeout(_) => std::time::Duration::from_millis(500),
            _ => std::time::Duration::from_millis(200),
        };

        // Exponential backoff with jitter
        let delay = base_delay * 2_u32.pow(attempt.min(5));
        let jitter = std::time::Duration::from_millis(fastrand::u64(0..=100));
        Some(delay + jitter)
    }

    /// Get maximum retry attempts for an error type
    pub fn get_max_retries(error: &DatabaseError) -> u32 {
        match error {
            DatabaseError::Connection(_) => 3,
            DatabaseError::Transaction(_) => 5,
            DatabaseError::Timeout(_) => 2,
            _ => 1,
        }
    }
}

/// Database error context for better debugging
#[derive(Debug, Clone)]
pub struct ErrorContext {
    pub operation: String,
    pub query: Option<String>,
    pub parameters: Vec<String>,
    pub connection_info: Option<String>,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub stack_trace: Option<String>,
}

impl ErrorContext {
    pub fn new(operation: String) -> Self {
        Self {
            operation,
            query: None,
            parameters: vec![],
            connection_info: None,
            timestamp: chrono::Utc::now(),
            stack_trace: None,
        }
    }

    pub fn with_query(mut self, query: String) -> Self {
        self.query = Some(query);
        self
    }

    pub fn with_parameters(mut self, params: Vec<String>) -> Self {
        self.parameters = params;
        self
    }

    pub fn with_connection_info(mut self, info: String) -> Self {
        self.connection_info = Some(info);
        self
    }
}

/// Enhanced error result type with context
pub type DatabaseResult<T> = Result<T, (DatabaseError, ErrorContext)>;

/// Helper macro for creating database errors with context
#[macro_export]
macro_rules! db_error {
    ($error_type:expr, $context:expr) => {
        Err(($error_type, $context))
    };
    ($error_type:expr, $operation:expr) => {
        Err(($error_type, ErrorContext::new($operation.to_string())))
    };
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_error_recovery_is_recoverable() {
        let conn_error = DatabaseError::Connection(ConnectionError {
            error_type: ConnectionErrorType::ConnectionTimeout,
            message: "Connection timeout".to_string(),
            database_type: "postgresql".to_string(),
            connection_string: None,
            retry_count: 0,
            is_recoverable: true,
        });

        assert!(ErrorRecovery::is_recoverable(&conn_error));
    }

    #[test]
    fn test_error_recovery_retry_delay() {
        let timeout_error = DatabaseError::Timeout(TimeoutError {
            operation: "SELECT".to_string(),
            timeout_duration: std::time::Duration::from_secs(30),
            elapsed_time: std::time::Duration::from_secs(35),
        });

        let delay = ErrorRecovery::get_retry_delay(&timeout_error, 1);
        assert!(delay.is_some());
        assert!(delay.unwrap() >= std::time::Duration::from_millis(500));
    }

    #[test]
    fn test_error_context() {
        let context = ErrorContext::new("SELECT".to_string())
            .with_query("SELECT * FROM users".to_string())
            .with_parameters(vec!["user_id=1".to_string()]);

        assert_eq!(context.operation, "SELECT");
        assert_eq!(context.query, Some("SELECT * FROM users".to_string()));
        assert_eq!(context.parameters.len(), 1);
    }
}
