/// Database annotations for Umbra structs and fields
/// Provides declarative database mapping through annotations

use crate::error::{UmbraError, UmbraResult};
use crate::parser::ast::{StructureDef, Field, Type, Annotation, Expression, Literal};
use std::collections::HashMap;

/// Database-specific annotations
#[derive(Debug, Clone)]
pub enum DatabaseAnnotation {
    /// @table annotation for struct
    Table(TableAnnotation),
    
    /// @column annotation for field
    Column(ColumnAnnotation),
    
    /// @primary_key annotation
    PrimaryKey(PrimaryKeyAnnotation),
    
    /// @foreign_key annotation
    ForeignKey(ForeignKeyAnnotation),
    
    /// @index annotation
    Index(IndexAnnotation),
    
    /// @unique annotation
    Unique(UniqueAnnotation),
    
    /// @not_null annotation
    NotNull,
    
    /// @default annotation
    Default(DefaultAnnotation),
    
    /// @auto_increment annotation
    AutoIncrement,
    
    /// @timestamp annotation
    Timestamp(TimestampAnnotation),
    
    /// @json annotation for JSON fields
    Json,
    
    /// @encrypted annotation for encrypted fields
    Encrypted(EncryptionAnnotation),
    
    /// @validation annotation
    Validation(ValidationAnnotation),
}

#[derive(Debug, Clone)]
pub struct TableAnnotation {
    pub name: Option<String>,
    pub schema: Option<String>,
    pub engine: Option<String>,
    pub charset: Option<String>,
    pub collation: Option<String>,
    pub comment: Option<String>,
}

#[derive(Debug, Clone)]
pub struct ColumnAnnotation {
    pub name: Option<String>,
    pub data_type: Option<String>,
    pub length: Option<u32>,
    pub precision: Option<u32>,
    pub scale: Option<u32>,
    pub comment: Option<String>,
}

#[derive(Debug, Clone)]
pub struct PrimaryKeyAnnotation {
    pub auto_increment: bool,
    pub sequence_name: Option<String>,
}

#[derive(Debug, Clone)]
pub struct ForeignKeyAnnotation {
    pub table: String,
    pub column: String,
    pub on_delete: Option<ReferentialAction>,
    pub on_update: Option<ReferentialAction>,
    pub constraint_name: Option<String>,
}

#[derive(Debug, Clone)]
pub enum ReferentialAction {
    Cascade,
    SetNull,
    SetDefault,
    Restrict,
    NoAction,
}

#[derive(Debug, Clone)]
pub struct IndexAnnotation {
    pub name: Option<String>,
    pub columns: Vec<String>,
    pub unique: bool,
    pub index_type: Option<IndexType>,
}

#[derive(Debug, Clone)]
pub enum IndexType {
    BTree,
    Hash,
    Gin,
    Gist,
    Spgist,
    Brin,
}

#[derive(Debug, Clone)]
pub struct UniqueAnnotation {
    pub constraint_name: Option<String>,
    pub columns: Vec<String>,
}

#[derive(Debug, Clone)]
pub struct DefaultAnnotation {
    pub value: DefaultValue,
}

#[derive(Debug, Clone)]
pub enum DefaultValue {
    Literal(String),
    Function(String),
    CurrentTimestamp,
    CurrentDate,
    CurrentTime,
    Uuid,
    Sequence(String),
}

#[derive(Debug, Clone)]
pub struct TimestampAnnotation {
    pub on_create: bool,
    pub on_update: bool,
    pub format: Option<String>,
}

#[derive(Debug, Clone)]
pub struct EncryptionAnnotation {
    pub algorithm: String,
    pub key_field: Option<String>,
}

#[derive(Debug, Clone)]
pub struct ValidationAnnotation {
    pub rules: Vec<ValidationRule>,
}

#[derive(Debug, Clone)]
pub struct ValidationRule {
    pub rule_type: String,
    pub parameters: HashMap<String, String>,
    pub message: Option<String>,
}

/// Database mapping information extracted from annotations
#[derive(Debug, Clone)]
pub struct DatabaseMapping {
    pub table_name: String,
    pub schema: Option<String>,
    pub fields: HashMap<String, FieldMapping>,
    pub primary_key: Option<String>,
    pub indexes: Vec<IndexMapping>,
    pub constraints: Vec<ConstraintMapping>,
}

#[derive(Debug, Clone)]
pub struct FieldMapping {
    pub field_name: String,
    pub column_name: String,
    pub data_type: Option<String>,
    pub is_nullable: bool,
    pub is_primary_key: bool,
    pub is_auto_increment: bool,
    pub default_value: Option<DefaultValue>,
    pub foreign_key: Option<ForeignKeyAnnotation>,
    pub validation_rules: Vec<ValidationRule>,
    pub is_encrypted: bool,
    pub is_json: bool,
    pub is_timestamp: Option<TimestampAnnotation>,
}

#[derive(Debug, Clone)]
pub struct IndexMapping {
    pub name: String,
    pub columns: Vec<String>,
    pub is_unique: bool,
    pub index_type: Option<IndexType>,
}

#[derive(Debug, Clone)]
pub struct ConstraintMapping {
    pub name: String,
    pub constraint_type: ConstraintType,
    pub definition: String,
}

#[derive(Debug, Clone)]
pub enum ConstraintType {
    PrimaryKey,
    ForeignKey,
    Unique,
    Check,
    NotNull,
}

/// Annotation parser for database mappings
pub struct AnnotationParser;

impl AnnotationParser {
    /// Parse database annotations from a struct definition
    pub fn parse_struct_annotations(struct_def: &StructureDef) -> UmbraResult<DatabaseMapping> {
        let mut mapping = DatabaseMapping {
            table_name: Self::default_table_name(&struct_def.name),
            schema: None,
            fields: HashMap::new(),
            primary_key: None,
            indexes: vec![],
            constraints: vec![],
        };

        // Parse struct-level annotations
        for annotation in &struct_def.annotations {
            match Self::parse_database_annotation(annotation)? {
                Some(DatabaseAnnotation::Table(table_ann)) => {
                    if let Some(name) = table_ann.name {
                        mapping.table_name = name;
                    }
                    mapping.schema = table_ann.schema;
                }
                Some(DatabaseAnnotation::Index(index_ann)) => {
                    mapping.indexes.push(IndexMapping {
                        name: index_ann.name.unwrap_or_else(|| format!("idx_{}", mapping.table_name)),
                        columns: index_ann.columns,
                        is_unique: index_ann.unique,
                        index_type: index_ann.index_type,
                    });
                }
                _ => {} // Other annotations handled at field level
            }
        }

        // Parse field annotations
        for field in &struct_def.fields {
            let field_mapping = Self::parse_field_annotations(field)?;
            
            if field_mapping.is_primary_key {
                mapping.primary_key = Some(field_mapping.field_name.clone());
            }
            
            mapping.fields.insert(field.name.clone(), field_mapping);
        }

        Ok(mapping)
    }

    /// Parse annotations for a single field
    fn parse_field_annotations(field: &Field) -> UmbraResult<FieldMapping> {
        let mut field_mapping = FieldMapping {
            field_name: field.name.clone(),
            column_name: Self::default_column_name(&field.name),
            data_type: None,
            is_nullable: Self::is_nullable_type(&field.field_type),
            is_primary_key: false,
            is_auto_increment: false,
            default_value: None,
            foreign_key: None,
            validation_rules: vec![],
            is_encrypted: false,
            is_json: false,
            is_timestamp: None,
        };

        for annotation in &field.annotations {
            match Self::parse_database_annotation(annotation)? {
                Some(DatabaseAnnotation::Column(col_ann)) => {
                    if let Some(name) = col_ann.name {
                        field_mapping.column_name = name;
                    }
                    field_mapping.data_type = col_ann.data_type;
                }
                Some(DatabaseAnnotation::PrimaryKey(pk_ann)) => {
                    field_mapping.is_primary_key = true;
                    field_mapping.is_auto_increment = pk_ann.auto_increment;
                }
                Some(DatabaseAnnotation::ForeignKey(fk_ann)) => {
                    field_mapping.foreign_key = Some(fk_ann);
                }
                Some(DatabaseAnnotation::NotNull) => {
                    field_mapping.is_nullable = false;
                }
                Some(DatabaseAnnotation::Default(default_ann)) => {
                    field_mapping.default_value = Some(default_ann.value);
                }
                Some(DatabaseAnnotation::AutoIncrement) => {
                    field_mapping.is_auto_increment = true;
                }
                Some(DatabaseAnnotation::Timestamp(ts_ann)) => {
                    field_mapping.is_timestamp = Some(ts_ann);
                }
                Some(DatabaseAnnotation::Json) => {
                    field_mapping.is_json = true;
                }
                Some(DatabaseAnnotation::Encrypted(_)) => {
                    field_mapping.is_encrypted = true;
                }
                Some(DatabaseAnnotation::Validation(val_ann)) => {
                    field_mapping.validation_rules = val_ann.rules;
                }
                _ => {} // Other annotations
            }
        }

        Ok(field_mapping)
    }

    /// Parse a single annotation into database annotation
    fn parse_database_annotation(annotation: &Annotation) -> UmbraResult<Option<DatabaseAnnotation>> {
        match annotation.name.as_str() {
            "table" => Ok(Some(DatabaseAnnotation::Table(Self::parse_table_annotation(annotation)?))),
            "column" => Ok(Some(DatabaseAnnotation::Column(Self::parse_column_annotation(annotation)?))),
            "primary_key" => Ok(Some(DatabaseAnnotation::PrimaryKey(Self::parse_primary_key_annotation(annotation)?))),
            "foreign_key" => Ok(Some(DatabaseAnnotation::ForeignKey(Self::parse_foreign_key_annotation(annotation)?))),
            "index" => Ok(Some(DatabaseAnnotation::Index(Self::parse_index_annotation(annotation)?))),
            "unique" => Ok(Some(DatabaseAnnotation::Unique(Self::parse_unique_annotation(annotation)?))),
            "not_null" => Ok(Some(DatabaseAnnotation::NotNull)),
            "default" => Ok(Some(DatabaseAnnotation::Default(Self::parse_default_annotation(annotation)?))),
            "auto_increment" => Ok(Some(DatabaseAnnotation::AutoIncrement)),
            "timestamp" => Ok(Some(DatabaseAnnotation::Timestamp(Self::parse_timestamp_annotation(annotation)?))),
            "json" => Ok(Some(DatabaseAnnotation::Json)),
            "encrypted" => Ok(Some(DatabaseAnnotation::Encrypted(Self::parse_encryption_annotation(annotation)?))),
            "validation" => Ok(Some(DatabaseAnnotation::Validation(Self::parse_validation_annotation(annotation)?))),
            _ => Ok(None), // Not a database annotation
        }
    }

    fn parse_table_annotation(annotation: &Annotation) -> UmbraResult<TableAnnotation> {
        let mut table_ann = TableAnnotation {
            name: None,
            schema: None,
            engine: None,
            charset: None,
            collation: None,
            comment: None,
        };

        for arg in &annotation.arguments {
            match arg.name.as_str() {
                "name" => table_ann.name = Self::extract_string_value(&arg.value)?,
                "schema" => table_ann.schema = Self::extract_string_value(&arg.value)?,
                "engine" => table_ann.engine = Self::extract_string_value(&arg.value)?,
                "charset" => table_ann.charset = Self::extract_string_value(&arg.value)?,
                "collation" => table_ann.collation = Self::extract_string_value(&arg.value)?,
                "comment" => table_ann.comment = Self::extract_string_value(&arg.value)?,
                _ => return Err(UmbraError::Database(format!("Unknown table annotation argument: {}", arg.name))),
            }
        }

        Ok(table_ann)
    }

    fn parse_column_annotation(annotation: &Annotation) -> UmbraResult<ColumnAnnotation> {
        let mut col_ann = ColumnAnnotation {
            name: None,
            data_type: None,
            length: None,
            precision: None,
            scale: None,
            comment: None,
        };

        for arg in &annotation.arguments {
            match arg.name.as_str() {
                "name" => col_ann.name = Self::extract_string_value(&arg.value)?,
                "type" => col_ann.data_type = Self::extract_string_value(&arg.value)?,
                "length" => col_ann.length = Self::extract_int_value(&arg.value)?.map(|v| v as u32),
                "precision" => col_ann.precision = Self::extract_int_value(&arg.value)?.map(|v| v as u32),
                "scale" => col_ann.scale = Self::extract_int_value(&arg.value)?.map(|v| v as u32),
                "comment" => col_ann.comment = Self::extract_string_value(&arg.value)?,
                _ => return Err(UmbraError::Database(format!("Unknown column annotation argument: {}", arg.name))),
            }
        }

        Ok(col_ann)
    }

    fn parse_primary_key_annotation(annotation: &Annotation) -> UmbraResult<PrimaryKeyAnnotation> {
        let mut pk_ann = PrimaryKeyAnnotation {
            auto_increment: false,
            sequence_name: None,
        };

        for arg in &annotation.arguments {
            match arg.name.as_str() {
                "auto_increment" => pk_ann.auto_increment = Self::extract_bool_value(&arg.value)?.unwrap_or(false),
                "sequence" => pk_ann.sequence_name = Self::extract_string_value(&arg.value)?,
                _ => return Err(UmbraError::Database(format!("Unknown primary_key annotation argument: {}", arg.name))),
            }
        }

        Ok(pk_ann)
    }

    fn parse_foreign_key_annotation(annotation: &Annotation) -> UmbraResult<ForeignKeyAnnotation> {
        let mut fk_ann = ForeignKeyAnnotation {
            table: String::new(),
            column: String::new(),
            on_delete: None,
            on_update: None,
            constraint_name: None,
        };

        for arg in &annotation.arguments {
            match arg.name.as_str() {
                "table" => {
                    fk_ann.table = Self::extract_string_value(&arg.value)?
                        .ok_or_else(|| UmbraError::Database("Foreign key table is required".to_string()))?;
                }
                "column" => {
                    fk_ann.column = Self::extract_string_value(&arg.value)?
                        .ok_or_else(|| UmbraError::Database("Foreign key column is required".to_string()))?;
                }
                "on_delete" => fk_ann.on_delete = Self::parse_referential_action(&arg.value)?,
                "on_update" => fk_ann.on_update = Self::parse_referential_action(&arg.value)?,
                "name" => fk_ann.constraint_name = Self::extract_string_value(&arg.value)?,
                _ => return Err(UmbraError::Database(format!("Unknown foreign_key annotation argument: {}", arg.name))),
            }
        }

        if fk_ann.table.is_empty() || fk_ann.column.is_empty() {
            return Err(UmbraError::Database("Foreign key requires table and column".to_string()));
        }

        Ok(fk_ann)
    }

    fn parse_index_annotation(annotation: &Annotation) -> UmbraResult<IndexAnnotation> {
        let mut index_ann = IndexAnnotation {
            name: None,
            columns: vec![],
            unique: false,
            index_type: None,
        };

        for arg in &annotation.arguments {
            match arg.name.as_str() {
                "name" => index_ann.name = Self::extract_string_value(&arg.value)?,
                "columns" => index_ann.columns = Self::extract_string_array(&arg.value)?,
                "unique" => index_ann.unique = Self::extract_bool_value(&arg.value)?.unwrap_or(false),
                "type" => index_ann.index_type = Self::parse_index_type(&arg.value)?,
                _ => return Err(UmbraError::Database(format!("Unknown index annotation argument: {}", arg.name))),
            }
        }

        Ok(index_ann)
    }

    fn parse_unique_annotation(annotation: &Annotation) -> UmbraResult<UniqueAnnotation> {
        let mut unique_ann = UniqueAnnotation {
            constraint_name: None,
            columns: vec![],
        };

        for arg in &annotation.arguments {
            match arg.name.as_str() {
                "name" => unique_ann.constraint_name = Self::extract_string_value(&arg.value)?,
                "columns" => unique_ann.columns = Self::extract_string_array(&arg.value)?,
                _ => return Err(UmbraError::Database(format!("Unknown unique annotation argument: {}", arg.name))),
            }
        }

        Ok(unique_ann)
    }

    fn parse_default_annotation(annotation: &Annotation) -> UmbraResult<DefaultAnnotation> {
        if annotation.arguments.is_empty() {
            return Err(UmbraError::Database("Default annotation requires a value".to_string()));
        }

        let value_arg = &annotation.arguments[0];
        let default_value = match Self::extract_string_value(&value_arg.value)? {
            Some(s) => match s.as_str() {
                "CURRENT_TIMESTAMP" => DefaultValue::CurrentTimestamp,
                "CURRENT_DATE" => DefaultValue::CurrentDate,
                "CURRENT_TIME" => DefaultValue::CurrentTime,
                "UUID()" => DefaultValue::Uuid,
                _ if s.starts_with("NEXTVAL(") => DefaultValue::Sequence(s),
                _ => DefaultValue::Literal(s),
            },
            None => return Err(UmbraError::Database("Default value is required".to_string())),
        };

        Ok(DefaultAnnotation { value: default_value })
    }

    fn parse_timestamp_annotation(annotation: &Annotation) -> UmbraResult<TimestampAnnotation> {
        let mut ts_ann = TimestampAnnotation {
            on_create: true,
            on_update: false,
            format: None,
        };

        for arg in &annotation.arguments {
            match arg.name.as_str() {
                "on_create" => ts_ann.on_create = Self::extract_bool_value(&arg.value)?.unwrap_or(true),
                "on_update" => ts_ann.on_update = Self::extract_bool_value(&arg.value)?.unwrap_or(false),
                "format" => ts_ann.format = Self::extract_string_value(&arg.value)?,
                _ => return Err(UmbraError::Database(format!("Unknown timestamp annotation argument: {}", arg.name))),
            }
        }

        Ok(ts_ann)
    }

    fn parse_encryption_annotation(annotation: &Annotation) -> UmbraResult<EncryptionAnnotation> {
        let mut enc_ann = EncryptionAnnotation {
            algorithm: "AES-256".to_string(),
            key_field: None,
        };

        for arg in &annotation.arguments {
            match arg.name.as_str() {
                "algorithm" => {
                    enc_ann.algorithm = Self::extract_string_value(&arg.value)?
                        .unwrap_or_else(|| "AES-256".to_string());
                }
                "key_field" => enc_ann.key_field = Self::extract_string_value(&arg.value)?,
                _ => return Err(UmbraError::Database(format!("Unknown encrypted annotation argument: {}", arg.name))),
            }
        }

        Ok(enc_ann)
    }

    fn parse_validation_annotation(annotation: &Annotation) -> UmbraResult<ValidationAnnotation> {
        let mut rules = vec![];

        for arg in &annotation.arguments {
            let rule = ValidationRule {
                rule_type: arg.name.clone(),
                parameters: HashMap::new(), // Would extract from arg.value
                message: None,
            };
            rules.push(rule);
        }

        Ok(ValidationAnnotation { rules })
    }

    // Helper methods for extracting values from expressions
    fn extract_string_value(expr: &Expression) -> UmbraResult<Option<String>> {
        match expr {
            Expression::Literal(Literal::String(s)) => Ok(Some(s.clone())),
            _ => Ok(None),
        }
    }

    fn extract_int_value(expr: &Expression) -> UmbraResult<Option<i64>> {
        match expr {
            Expression::Literal(Literal::Integer(i)) => Ok(Some(*i)),
            _ => Ok(None),
        }
    }

    fn extract_bool_value(expr: &Expression) -> UmbraResult<Option<bool>> {
        match expr {
            Expression::Literal(Literal::Boolean(b)) => Ok(Some(*b)),
            _ => Ok(None),
        }
    }

    fn extract_string_array(expr: &Expression) -> UmbraResult<Vec<String>> {
        // Simplified - would parse array literal
        Ok(vec![])
    }

    fn parse_referential_action(expr: &Expression) -> UmbraResult<Option<ReferentialAction>> {
        if let Some(action_str) = Self::extract_string_value(expr)? {
            match action_str.to_uppercase().as_str() {
                "CASCADE" => Ok(Some(ReferentialAction::Cascade)),
                "SET NULL" => Ok(Some(ReferentialAction::SetNull)),
                "SET DEFAULT" => Ok(Some(ReferentialAction::SetDefault)),
                "RESTRICT" => Ok(Some(ReferentialAction::Restrict)),
                "NO ACTION" => Ok(Some(ReferentialAction::NoAction)),
                _ => Err(UmbraError::Database(format!("Unknown referential action: {}", action_str))),
            }
        } else {
            Ok(None)
        }
    }

    fn parse_index_type(expr: &Expression) -> UmbraResult<Option<IndexType>> {
        if let Some(type_str) = Self::extract_string_value(expr)? {
            match type_str.to_uppercase().as_str() {
                "BTREE" => Ok(Some(IndexType::BTree)),
                "HASH" => Ok(Some(IndexType::Hash)),
                "GIN" => Ok(Some(IndexType::Gin)),
                "GIST" => Ok(Some(IndexType::Gist)),
                "SPGIST" => Ok(Some(IndexType::Spgist)),
                "BRIN" => Ok(Some(IndexType::Brin)),
                _ => Err(UmbraError::Database(format!("Unknown index type: {}", type_str))),
            }
        } else {
            Ok(None)
        }
    }

    // Helper methods for naming conventions
    fn default_table_name(struct_name: &str) -> String {
        Self::to_snake_case(struct_name)
    }

    fn default_column_name(field_name: &str) -> String {
        Self::to_snake_case(field_name)
    }

    fn to_snake_case(s: &str) -> String {
        let mut result = String::new();
        let mut chars = s.chars().peekable();

        while let Some(ch) = chars.next() {
            if ch.is_uppercase() && !result.is_empty() {
                result.push('_');
            }
            result.push(ch.to_lowercase().next().unwrap());
        }

        result
    }

    fn is_nullable_type(field_type: &Type) -> bool {
        // Check if type is Option<T> or nullable
        match field_type {
            Type::Optional(_) => true,
            _ => false,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::parser::ast::{AnnotationArgument, BasicType};

    #[test]
    fn test_annotation_parsing() {
        let table_annotation = Annotation {
            name: "table".to_string(),
            arguments: vec![
                AnnotationArgument {
                    name: "name".to_string(),
                    value: Expression::Literal(Literal::String("users".to_string())),
                }
            ],
            location: crate::error::SourceLocation::new(1, 1),
        };

        let parsed = AnnotationParser::parse_table_annotation(&table_annotation).unwrap();
        assert_eq!(parsed.name, Some("users".to_string()));
    }

    #[test]
    fn test_naming_conventions() {
        assert_eq!(AnnotationParser::to_snake_case("UserProfile"), "user_profile");
        assert_eq!(AnnotationParser::to_snake_case("ID"), "i_d");
        assert_eq!(AnnotationParser::to_snake_case("simple"), "simple");
    }

    #[test]
    fn test_nullable_type_detection() {
        let nullable_type = Type::Optional(Box::new(Type::Basic(BasicType::String)));
        let non_nullable_type = Type::Basic(BasicType::String);

        assert!(AnnotationParser::is_nullable_type(&nullable_type));
        assert!(!AnnotationParser::is_nullable_type(&non_nullable_type));
    }
}
