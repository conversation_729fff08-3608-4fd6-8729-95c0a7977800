/// Database schema introspection and validation for Umbra
/// Provides runtime schema information and validation capabilities

use crate::error::UmbraResult;
use crate::database::connection::{DatabaseConnection, DatabaseSchema, TableSchema, ColumnSchema};
use crate::database::annotations::DatabaseMapping;
use std::collections::HashMap;
use std::sync::Arc;

/// Schema manager for database introspection
pub struct SchemaManager {
    connection: Arc<dyn DatabaseConnection>,
    cached_schema: Option<DatabaseSchema>,
    cache_timestamp: Option<chrono::DateTime<chrono::Utc>>,
    cache_ttl: chrono::Duration,
}

impl SchemaManager {
    pub fn new(connection: Arc<dyn DatabaseConnection>) -> Self {
        Self {
            connection,
            cached_schema: None,
            cache_timestamp: None,
            cache_ttl: chrono::Duration::minutes(5), // 5 minute cache
        }
    }

    /// Get database schema with caching
    pub fn get_schema(&mut self) -> UmbraResult<&DatabaseSchema> {
        let now = chrono::Utc::now();

        // Check if cache is valid
        let cache_valid = if let (Some(_), Some(timestamp)) = (&self.cached_schema, self.cache_timestamp) {
            now.signed_duration_since(timestamp) < self.cache_ttl
        } else {
            false
        };

        if !cache_valid {
            // Refresh cache
            let schema = self.connection.get_schema()?;
            self.cached_schema = Some(schema);
            self.cache_timestamp = Some(now);
        }

        Ok(self.cached_schema.as_ref().unwrap())
    }

    /// Validate that a table exists
    pub fn validate_table_exists(&mut self, table_name: &str) -> UmbraResult<bool> {
        let schema = self.get_schema()?;
        Ok(schema.tables.iter().any(|t| t.name == table_name))
    }

    /// Validate that a column exists in a table
    pub fn validate_column_exists(&mut self, table_name: &str, column_name: &str) -> UmbraResult<bool> {
        let schema = self.get_schema()?;
        
        if let Some(table) = schema.tables.iter().find(|t| t.name == table_name) {
            Ok(table.columns.iter().any(|c| c.name == column_name))
        } else {
            Ok(false)
        }
    }

    /// Get table information
    pub fn get_table_info(&mut self, table_name: &str) -> UmbraResult<Option<&TableSchema>> {
        let schema = self.get_schema()?;
        Ok(schema.tables.iter().find(|t| t.name == table_name))
    }

    /// Get column information
    pub fn get_column_info(&mut self, table_name: &str, column_name: &str) -> UmbraResult<Option<&ColumnSchema>> {
        if let Some(table) = self.get_table_info(table_name)? {
            Ok(table.columns.iter().find(|c| c.name == column_name))
        } else {
            Ok(None)
        }
    }

    /// Validate database mapping against actual schema
    pub fn validate_mapping(&mut self, mapping: &DatabaseMapping) -> UmbraResult<Vec<ValidationError>> {
        let mut errors = Vec::new();

        // Check if table exists
        if !self.validate_table_exists(&mapping.table_name)? {
            errors.push(ValidationError {
                error_type: ValidationErrorType::TableNotFound,
                message: format!("Table '{}' does not exist", mapping.table_name),
                table_name: Some(mapping.table_name.clone()),
                column_name: None,
            });
            return Ok(errors); // Can't validate further without table
        }

        let table_name = mapping.table_name.clone();
        let table_info = self.get_table_info(&table_name)?.unwrap();

        // Validate each field mapping
        for (field_name, field_mapping) in &mapping.fields {
            // Check if column exists
            if let Some(column_info) = table_info.columns.iter().find(|c| c.name == field_mapping.column_name) {
                // Validate data type compatibility
                if let Some(ref expected_type) = field_mapping.data_type {
                    if !Self::is_type_compatible_static(expected_type, &column_info.data_type) {
                        errors.push(ValidationError {
                            error_type: ValidationErrorType::TypeMismatch,
                            message: format!(
                                "Field '{}' expects type '{}' but column '{}' has type '{}'",
                                field_name, expected_type, field_mapping.column_name, column_info.data_type
                            ),
                            table_name: Some(mapping.table_name.clone()),
                            column_name: Some(field_mapping.column_name.clone()),
                        });
                    }
                }

                // Validate nullability
                if !field_mapping.is_nullable && column_info.is_nullable {
                    errors.push(ValidationError {
                        error_type: ValidationErrorType::NullabilityMismatch,
                        message: format!(
                            "Field '{}' is not nullable but column '{}' allows NULL",
                            field_name, field_mapping.column_name
                        ),
                        table_name: Some(mapping.table_name.clone()),
                        column_name: Some(field_mapping.column_name.clone()),
                    });
                }
            } else {
                errors.push(ValidationError {
                    error_type: ValidationErrorType::ColumnNotFound,
                    message: format!("Column '{}' not found in table '{}'", field_mapping.column_name, mapping.table_name),
                    table_name: Some(mapping.table_name.clone()),
                    column_name: Some(field_mapping.column_name.clone()),
                });
            }
        }

        // Validate primary key
        if let Some(ref pk_field) = mapping.primary_key {
            if let Some(field_mapping) = mapping.fields.get(pk_field) {
                if let Some(column_info) = table_info.columns.iter().find(|c| c.name == field_mapping.column_name) {
                    if !column_info.is_primary_key {
                        errors.push(ValidationError {
                            error_type: ValidationErrorType::PrimaryKeyMismatch,
                            message: format!(
                                "Field '{}' is marked as primary key but column '{}' is not a primary key",
                                pk_field, field_mapping.column_name
                            ),
                            table_name: Some(mapping.table_name.clone()),
                            column_name: Some(field_mapping.column_name.clone()),
                        });
                    }
                }
            }
        }

        Ok(errors)
    }

    /// Check if two data types are compatible
    fn is_type_compatible(&self, expected: &str, actual: &str) -> bool {
        Self::is_type_compatible_static(expected, actual)
    }

    /// Static version of type compatibility check
    fn is_type_compatible_static(expected: &str, actual: &str) -> bool {
        let expected_normalized = Self::normalize_type_static(expected);
        let actual_normalized = Self::normalize_type_static(actual);
        
        // Exact match
        if expected_normalized == actual_normalized {
            return true;
        }

        // Compatible type mappings
        match (expected_normalized.as_str(), actual_normalized.as_str()) {
            ("integer", "int") | ("int", "integer") => true,
            ("integer", "bigint") | ("bigint", "integer") => true,
            ("text", "varchar") | ("varchar", "text") => true,
            ("text", "string") | ("string", "text") => true,
            ("real", "float") | ("float", "real") => true,
            ("real", "double") | ("double", "real") => true,
            ("boolean", "bool") | ("bool", "boolean") => true,
            _ => false,
        }
    }

    /// Normalize type name for comparison
    fn normalize_type(&self, type_name: &str) -> String {
        Self::normalize_type_static(type_name)
    }

    /// Static version of type normalization
    fn normalize_type_static(type_name: &str) -> String {
        type_name.to_lowercase()
            .split('(').next().unwrap_or("")
            .trim()
            .to_string()
    }

    /// Generate schema diff between mapping and actual schema
    pub fn generate_schema_diff(&mut self, mapping: &DatabaseMapping) -> UmbraResult<SchemaDiff> {
        let mut diff = SchemaDiff {
            table_name: mapping.table_name.clone(),
            operations: Vec::new(),
        };

        // Check if table exists
        if !self.validate_table_exists(&mapping.table_name)? {
            diff.operations.push(SchemaDiffOperation::CreateTable {
                table: mapping.clone(),
            });
            return Ok(diff);
        }

        let table_info = self.get_table_info(&mapping.table_name)?.unwrap();
        let existing_columns: HashMap<String, &ColumnSchema> = table_info.columns
            .iter()
            .map(|c| (c.name.clone(), c))
            .collect();

        // Check for missing columns
        for (field_name, field_mapping) in &mapping.fields {
            if !existing_columns.contains_key(&field_mapping.column_name) {
                diff.operations.push(SchemaDiffOperation::AddColumn {
                    column_name: field_mapping.column_name.clone(),
                    field_mapping: field_mapping.clone(),
                });
            }
        }

        // Check for extra columns (columns in DB but not in mapping)
        let mapped_columns: std::collections::HashSet<String> = mapping.fields
            .values()
            .map(|f| f.column_name.clone())
            .collect();

        for column_name in existing_columns.keys() {
            if !mapped_columns.contains(column_name) {
                diff.operations.push(SchemaDiffOperation::DropColumn {
                    column_name: column_name.clone(),
                });
            }
        }

        Ok(diff)
    }

    /// Clear schema cache
    pub fn clear_cache(&mut self) {
        self.cached_schema = None;
        self.cache_timestamp = None;
    }

    /// Set cache TTL
    pub fn set_cache_ttl(&mut self, ttl: chrono::Duration) {
        self.cache_ttl = ttl;
    }
}

/// Schema validation error
#[derive(Debug, Clone)]
pub struct ValidationError {
    pub error_type: ValidationErrorType,
    pub message: String,
    pub table_name: Option<String>,
    pub column_name: Option<String>,
}

#[derive(Debug, Clone)]
pub enum ValidationErrorType {
    TableNotFound,
    ColumnNotFound,
    TypeMismatch,
    NullabilityMismatch,
    PrimaryKeyMismatch,
    ForeignKeyMismatch,
    ConstraintViolation,
}

/// Schema difference representation
#[derive(Debug, Clone)]
pub struct SchemaDiff {
    pub table_name: String,
    pub operations: Vec<SchemaDiffOperation>,
}

#[derive(Debug, Clone)]
pub enum SchemaDiffOperation {
    CreateTable {
        table: DatabaseMapping,
    },
    DropTable,
    AddColumn {
        column_name: String,
        field_mapping: crate::database::annotations::FieldMapping,
    },
    DropColumn {
        column_name: String,
    },
    ModifyColumn {
        column_name: String,
        old_definition: ColumnSchema,
        new_definition: crate::database::annotations::FieldMapping,
    },
    AddIndex {
        index_name: String,
        columns: Vec<String>,
        is_unique: bool,
    },
    DropIndex {
        index_name: String,
    },
}

/// Schema comparison utilities
pub struct SchemaComparator;

impl SchemaComparator {
    /// Compare two database schemas
    pub fn compare_schemas(old_schema: &DatabaseSchema, new_schema: &DatabaseSchema) -> Vec<SchemaChange> {
        let mut changes = Vec::new();

        // Compare tables
        let old_tables: HashMap<String, &TableSchema> = old_schema.tables
            .iter()
            .map(|t| (t.name.clone(), t))
            .collect();

        let new_tables: HashMap<String, &TableSchema> = new_schema.tables
            .iter()
            .map(|t| (t.name.clone(), t))
            .collect();

        // Find added tables
        for (table_name, table) in &new_tables {
            if !old_tables.contains_key(table_name) {
                changes.push(SchemaChange::TableAdded {
                    table: (*table).clone(),
                });
            }
        }

        // Find removed tables
        for (table_name, table) in &old_tables {
            if !new_tables.contains_key(table_name) {
                changes.push(SchemaChange::TableRemoved {
                    table: (*table).clone(),
                });
            }
        }

        // Find modified tables
        for (table_name, new_table) in &new_tables {
            if let Some(old_table) = old_tables.get(table_name) {
                let table_changes = Self::compare_tables(old_table, new_table);
                changes.extend(table_changes);
            }
        }

        changes
    }

    /// Compare two table schemas
    fn compare_tables(old_table: &TableSchema, new_table: &TableSchema) -> Vec<SchemaChange> {
        let mut changes = Vec::new();

        let old_columns: HashMap<String, &ColumnSchema> = old_table.columns
            .iter()
            .map(|c| (c.name.clone(), c))
            .collect();

        let new_columns: HashMap<String, &ColumnSchema> = new_table.columns
            .iter()
            .map(|c| (c.name.clone(), c))
            .collect();

        // Find added columns
        for (column_name, column) in &new_columns {
            if !old_columns.contains_key(column_name) {
                changes.push(SchemaChange::ColumnAdded {
                    table_name: new_table.name.clone(),
                    column: (*column).clone(),
                });
            }
        }

        // Find removed columns
        for (column_name, column) in &old_columns {
            if !new_columns.contains_key(column_name) {
                changes.push(SchemaChange::ColumnRemoved {
                    table_name: old_table.name.clone(),
                    column: (*column).clone(),
                });
            }
        }

        // Find modified columns
        for (column_name, new_column) in &new_columns {
            if let Some(old_column) = old_columns.get(column_name) {
                if Self::columns_differ(old_column, new_column) {
                    changes.push(SchemaChange::ColumnModified {
                        table_name: new_table.name.clone(),
                        old_column: (*old_column).clone(),
                        new_column: (*new_column).clone(),
                    });
                }
            }
        }

        changes
    }

    /// Check if two columns are different
    fn columns_differ(old_column: &ColumnSchema, new_column: &ColumnSchema) -> bool {
        old_column.data_type != new_column.data_type ||
        old_column.is_nullable != new_column.is_nullable ||
        old_column.default_value != new_column.default_value ||
        old_column.is_primary_key != new_column.is_primary_key ||
        old_column.is_unique != new_column.is_unique ||
        old_column.max_length != new_column.max_length
    }
}

/// Schema change representation
#[derive(Debug, Clone)]
pub enum SchemaChange {
    TableAdded {
        table: TableSchema,
    },
    TableRemoved {
        table: TableSchema,
    },
    ColumnAdded {
        table_name: String,
        column: ColumnSchema,
    },
    ColumnRemoved {
        table_name: String,
        column: ColumnSchema,
    },
    ColumnModified {
        table_name: String,
        old_column: ColumnSchema,
        new_column: ColumnSchema,
    },
    IndexAdded {
        table_name: String,
        index_name: String,
    },
    IndexRemoved {
        table_name: String,
        index_name: String,
    },
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::database::{DatabaseConfig, DatabaseType};
    use crate::database::connection::ConnectionFactory;

    #[test]
    fn test_schema_manager_creation() {
        let config = DatabaseConfig::new(DatabaseType::SQLite);
        let connection = ConnectionFactory::create_connection(&config).unwrap();
        let manager = SchemaManager::new(connection);
        
        assert!(manager.cached_schema.is_none());
        assert!(manager.cache_timestamp.is_none());
    }

    #[test]
    fn test_type_compatibility() {
        let config = DatabaseConfig::new(DatabaseType::SQLite);
        let connection = ConnectionFactory::create_connection(&config).unwrap();
        let manager = SchemaManager::new(connection);
        
        assert!(manager.is_type_compatible("INTEGER", "INT"));
        assert!(manager.is_type_compatible("TEXT", "VARCHAR"));
        assert!(manager.is_type_compatible("REAL", "FLOAT"));
        assert!(!manager.is_type_compatible("INTEGER", "TEXT"));
    }

    #[test]
    fn test_type_normalization() {
        let config = DatabaseConfig::new(DatabaseType::SQLite);
        let connection = ConnectionFactory::create_connection(&config).unwrap();
        let manager = SchemaManager::new(connection);
        
        assert_eq!(manager.normalize_type("VARCHAR(255)"), "varchar");
        assert_eq!(manager.normalize_type("INTEGER"), "integer");
        assert_eq!(manager.normalize_type("DECIMAL(10,2)"), "decimal");
    }
}
