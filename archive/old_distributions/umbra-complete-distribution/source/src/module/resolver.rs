use std::collections::HashMap;
use std::path::{Path, PathBuf};

use crate::error::{UmbraE<PERSON>r, UmbraResult};
use crate::parser::ast::{ModulePath, ImportType};
use crate::semantic::symbol_table::{Symbol, SymbolTable};

/// Resolves module paths and handles namespace resolution
#[derive(Debug)]
pub struct ModuleResolver {
    /// Maps module paths to their resolved file paths
    module_paths: HashMap<String, PathBuf>,
    /// Maps module names to their symbol tables
    module_symbols: HashMap<String, SymbolTable>,
    /// Current module search paths
    search_paths: Vec<PathBuf>,
    /// Current module being processed
    current_module: Option<String>,
}

impl ModuleResolver {
    pub fn new() -> Self {
        let mut resolver = Self {
            module_paths: HashMap::new(),
            module_symbols: HashMap::new(),
            search_paths: Vec::new(),
            current_module: None,
        };
        
        // Add default search paths
        resolver.add_search_path(".");
        resolver.add_search_path("./src");
        resolver.add_search_path("./lib");
        
        resolver
    }

    /// Add a search path for modules
    pub fn add_search_path<P: AsRef<Path>>(&mut self, path: P) {
        self.search_paths.push(path.as_ref().to_path_buf());
    }

    /// Set the current module being processed
    pub fn set_current_module(&mut self, module_name: String) {
        self.current_module = Some(module_name);
    }

    /// Resolve a module path to a file path
    pub fn resolve_module_path(&self, module_path: &ModulePath) -> UmbraResult<PathBuf> {
        let module_name = module_path.to_string();
        
        // Check if already resolved
        if let Some(path) = self.module_paths.get(&module_name) {
            return Ok(path.clone());
        }

        // Convert module path to file path (e.g., std::io -> std/io.umbra)
        let file_path = module_path.segments.join("/") + ".umbra";
        
        // Search in all search paths
        for search_path in &self.search_paths {
            let full_path = search_path.join(&file_path);
            if full_path.exists() {
                return Ok(full_path);
            }
        }

        Err(UmbraError::Module(format!(
            "Module '{module_name}' not found in search paths"
        )))
    }

    /// Register a module's symbols
    pub fn register_module_symbols(&mut self, module_name: String, symbols: SymbolTable) {
        self.module_symbols.insert(module_name, symbols);
    }

    /// Resolve an import statement
    pub fn resolve_import(&self, import: &ImportType) -> UmbraResult<Vec<(String, Symbol)>> {
        match import {
            ImportType::Module(module_path) => {
                let module_name = module_path.to_string();
                self.get_all_exported_symbols(&module_name)
            }
            ImportType::ModuleAs(module_path, alias) => {
                let module_name = module_path.to_string();
                let symbols = self.get_all_exported_symbols(&module_name)?;
                // Prefix all symbols with the alias
                Ok(symbols.into_iter().map(|(name, symbol)| {
                    (format!("{alias}::{name}"), symbol)
                }).collect())
            }
            ImportType::Symbol(module_path, symbol_name) => {
                let module_name = module_path.to_string();
                if let Some(symbol) = self.get_exported_symbol(&module_name, symbol_name)? {
                    Ok(vec![(symbol_name.clone(), symbol)])
                } else {
                    Ok(vec![])
                }
            }
            ImportType::SymbolAs(module_path, symbol_name, alias) => {
                let module_name = module_path.to_string();
                if let Some(symbol) = self.get_exported_symbol(&module_name, symbol_name)? {
                    Ok(vec![(alias.clone(), symbol)])
                } else {
                    Ok(vec![])
                }
            }
            ImportType::Wildcard(module_path) => {
                let module_name = module_path.to_string();
                self.get_all_exported_symbols(&module_name)
            }
            ImportType::Multiple(module_path, items) => {
                let module_name = module_path.to_string();
                let mut result = Vec::new();
                for item in items {
                    if let Some(symbol) = self.get_exported_symbol(&module_name, &item.name)? {
                        let import_name = item.alias.as_ref().unwrap_or(&item.name);
                        result.push((import_name.clone(), symbol));
                    }
                }
                Ok(result)
            }
        }
    }

    /// Get all exported symbols from a module
    fn get_all_exported_symbols(&self, module_name: &str) -> UmbraResult<Vec<(String, Symbol)>> {
        if let Some(symbol_table) = self.module_symbols.get(module_name) {
            // For now, return all symbols as exported
            // In a full implementation, this would filter by visibility
            Ok(symbol_table.get_all_symbols().into_iter().collect())
        } else {
            Err(UmbraError::Module(format!(
                "Module '{module_name}' not loaded"
            )))
        }
    }

    /// Get a specific exported symbol from a module
    fn get_exported_symbol(&self, module_name: &str, symbol_name: &str) -> UmbraResult<Option<Symbol>> {
        if let Some(symbol_table) = self.module_symbols.get(module_name) {
            Ok(symbol_table.lookup(symbol_name).cloned())
        } else {
            Err(UmbraError::Module(format!(
                "Module '{module_name}' not loaded"
            )))
        }
    }

    /// Resolve a qualified identifier to a symbol
    pub fn resolve_qualified_identifier(&self, module_path: &ModulePath) -> UmbraResult<Option<Symbol>> {
        if module_path.segments.len() < 2 {
            return Ok(None);
        }

        let module_segments = &module_path.segments[..module_path.segments.len() - 1];
        let symbol_name = &module_path.segments[module_path.segments.len() - 1];
        
        let module_name = module_segments.join("::");
        self.get_exported_symbol(&module_name, symbol_name)
    }

    /// Check if a module is loaded
    pub fn is_module_loaded(&self, module_name: &str) -> bool {
        self.module_symbols.contains_key(module_name)
    }

    /// Get the current module name
    pub fn current_module(&self) -> Option<&str> {
        self.current_module.as_deref()
    }
}
