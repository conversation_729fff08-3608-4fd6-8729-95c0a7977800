use std::collections::HashMap;
use std::fs;
use std::path::{Path, PathBuf};
use serde::{Deserialize, Serialize};

use crate::error::{UmbraError, UmbraResult};

/// Package manifest (package.umbra file)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PackageManifest {
    pub package: PackageInfo,
    pub dependencies: Option<HashMap<String, DependencySpec>>,
    pub dev_dependencies: Option<HashMap<String, DependencySpec>>,
    pub build_dependencies: Option<HashMap<String, DependencySpec>>,
    pub features: Option<HashMap<String, Vec<String>>>,
    pub workspace: Option<WorkspaceConfig>,
    pub build: Option<BuildConfig>,
    pub target: Option<HashMap<String, TargetConfig>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PackageInfo {
    pub name: String,
    pub version: String,
    pub description: Option<String>,
    pub authors: Option<Vec<String>>,
    pub license: Option<String>,
    pub repository: Option<String>,
    pub homepage: Option<String>,
    pub documentation: Option<String>,
    pub readme: Option<String>,
    pub keywords: Option<Vec<String>>,
    pub categories: Option<Vec<String>>,
    pub edition: Option<String>,
    pub publish: Option<bool>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(untagged)]
pub enum DependencySpec {
    Simple(String),
    Detailed {
        version: Option<String>,
        path: Option<String>,
        git: Option<String>,
        branch: Option<String>,
        tag: Option<String>,
        rev: Option<String>,
        features: Option<Vec<String>>,
        default_features: Option<bool>,
        optional: Option<bool>,
    },
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WorkspaceConfig {
    pub members: Option<Vec<String>>,
    pub exclude: Option<Vec<String>>,
    pub default_members: Option<Vec<String>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BuildConfig {
    pub script: Option<String>,
    pub links: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TargetConfig {
    pub dependencies: Option<HashMap<String, DependencySpec>>,
    pub dev_dependencies: Option<HashMap<String, DependencySpec>>,
    pub build_dependencies: Option<HashMap<String, DependencySpec>>,
}

/// Package manager for handling dependencies and manifests
#[derive(Debug)]
pub struct PackageManager {
    /// Current package manifest
    manifest: Option<PackageManifest>,
    /// Package root directory
    root_dir: Option<PathBuf>,
    /// Dependency cache
    dependency_cache: HashMap<String, PackageManifest>,
}

impl PackageManager {
    pub fn new() -> Self {
        Self {
            manifest: None,
            root_dir: None,
            dependency_cache: HashMap::new(),
        }
    }

    /// Load package manifest from a directory
    pub fn load_manifest<P: AsRef<Path>>(&mut self, dir: P) -> UmbraResult<&PackageManifest> {
        let dir = dir.as_ref();
        let manifest_path = dir.join("package.umbra");

        if !manifest_path.exists() {
            return Err(UmbraError::Module(format!(
                "No package.umbra found in {}",
                dir.display()
            )));
        }

        let content = fs::read_to_string(&manifest_path)
            .map_err(|e| UmbraError::Module(format!("Failed to read package.umbra: {e}")))?;

        let manifest: PackageManifest = toml::from_str(&content)
            .map_err(|e| UmbraError::Module(format!("Failed to parse package.umbra: {e}")))?;

        self.root_dir = Some(dir.to_path_buf());
        self.manifest = Some(manifest);

        Ok(self.manifest.as_ref().unwrap())
    }

    /// Create a new package manifest
    pub fn create_manifest<P: AsRef<Path>>(
        &mut self,
        dir: P,
        name: String,
        version: String,
    ) -> UmbraResult<()> {
        let dir = dir.as_ref();
        let manifest_path = dir.join("package.umbra");

        if manifest_path.exists() {
            return Err(UmbraError::Module(
                "package.umbra already exists".to_string(),
            ));
        }

        let manifest = PackageManifest {
            package: PackageInfo {
                name,
                version,
                description: None,
                authors: None,
                license: None,
                repository: None,
                homepage: None,
                documentation: None,
                readme: None,
                keywords: None,
                categories: None,
                edition: Some("2024".to_string()),
                publish: Some(true),
            },
            dependencies: None,
            dev_dependencies: None,
            build_dependencies: None,
            features: None,
            workspace: None,
            build: None,
            target: None,
        };

        let content = toml::to_string_pretty(&manifest)
            .map_err(|e| UmbraError::Module(format!("Failed to serialize manifest: {e}")))?;

        fs::write(&manifest_path, content)
            .map_err(|e| UmbraError::Module(format!("Failed to write package.umbra: {e}")))?;

        self.root_dir = Some(dir.to_path_buf());
        self.manifest = Some(manifest);

        Ok(())
    }

    /// Add a dependency to the manifest
    pub fn add_dependency(&mut self, name: String, spec: DependencySpec) -> UmbraResult<()> {
        let manifest = self.manifest.as_mut().ok_or_else(|| {
            UmbraError::Module("No manifest loaded".to_string())
        })?;

        if manifest.dependencies.is_none() {
            manifest.dependencies = Some(HashMap::new());
        }

        manifest.dependencies.as_mut().unwrap().insert(name, spec);
        self.save_manifest()
    }

    /// Remove a dependency from the manifest
    pub fn remove_dependency(&mut self, name: &str) -> UmbraResult<bool> {
        let manifest = self.manifest.as_mut().ok_or_else(|| {
            UmbraError::Module("No manifest loaded".to_string())
        })?;

        let removed = if let Some(deps) = manifest.dependencies.as_mut() {
            deps.remove(name).is_some()
        } else {
            false
        };

        if removed {
            self.save_manifest()?;
        }

        Ok(removed)
    }

    /// Get all dependencies
    pub fn get_dependencies(&self) -> Option<&HashMap<String, DependencySpec>> {
        self.manifest.as_ref()?.dependencies.as_ref()
    }

    /// Resolve all dependencies
    pub fn resolve_dependencies(&mut self) -> UmbraResult<Vec<(String, PathBuf)>> {
        let dependencies = if let Some(manifest) = &self.manifest {
            manifest.dependencies.clone()
        } else {
            return Err(UmbraError::Module("No manifest loaded".to_string()));
        };

        let mut resolved = Vec::new();

        if let Some(dependencies) = dependencies {
            for (name, spec) in dependencies {
                let path = self.resolve_dependency(&name, &spec)?;
                resolved.push((name, path));
            }
        }

        Ok(resolved)
    }

    /// Resolve a single dependency
    fn resolve_dependency(&mut self, name: &str, spec: &DependencySpec) -> UmbraResult<PathBuf> {
        match spec {
            DependencySpec::Simple(version) => {
                // For simple version specs, look in standard locations
                self.find_package_by_version(name, version)
            }
            DependencySpec::Detailed { path: Some(path), .. } => {
                // Local path dependency
                let root = self.root_dir.as_ref().unwrap();
                Ok(root.join(path))
            }
            DependencySpec::Detailed { git: Some(git_url), .. } => {
                // Git dependency - for now, just return an error
                Err(UmbraError::Module(format!(
                    "Git dependencies not yet supported: {git_url}"
                )))
            }
            DependencySpec::Detailed { version: Some(version), .. } => {
                // Version dependency
                self.find_package_by_version(name, version)
            }
            _ => Err(UmbraError::Module(format!(
                "Invalid dependency specification for {name}"
            ))),
        }
    }

    /// Find a package by name and version
    fn find_package_by_version(&self, name: &str, version: &str) -> UmbraResult<PathBuf> {
        // Standard package locations
        let search_paths = vec![
            PathBuf::from("./deps"),
            PathBuf::from("./vendor"),
            PathBuf::from("~/.umbra/packages"),
            PathBuf::from("/usr/local/lib/umbra/packages"),
        ];

        for search_path in search_paths {
            let package_path = search_path.join(format!("{name}-{version}"));
            if package_path.exists() {
                return Ok(package_path);
            }

            let package_path = search_path.join(name);
            if package_path.exists() {
                return Ok(package_path);
            }
        }

        Err(UmbraError::Module(format!(
            "Package '{name}' version '{version}' not found"
        )))
    }

    /// Save the current manifest to disk
    fn save_manifest(&self) -> UmbraResult<()> {
        let manifest = self.manifest.as_ref().ok_or_else(|| {
            UmbraError::Module("No manifest to save".to_string())
        })?;

        let root_dir = self.root_dir.as_ref().ok_or_else(|| {
            UmbraError::Module("No root directory set".to_string())
        })?;

        let manifest_path = root_dir.join("package.umbra");
        let content = toml::to_string_pretty(manifest)
            .map_err(|e| UmbraError::Module(format!("Failed to serialize manifest: {e}")))?;

        fs::write(&manifest_path, content)
            .map_err(|e| UmbraError::Module(format!("Failed to write package.umbra: {e}")))?;

        Ok(())
    }

    /// Get the current manifest
    pub fn manifest(&self) -> Option<&PackageManifest> {
        self.manifest.as_ref()
    }

    /// Get the package root directory
    pub fn root_dir(&self) -> Option<&PathBuf> {
        self.root_dir.as_ref()
    }

    /// Check if a package is a workspace
    pub fn is_workspace(&self) -> bool {
        self.manifest.as_ref()
            .and_then(|m| m.workspace.as_ref())
            .is_some()
    }

    /// Get workspace members
    pub fn workspace_members(&self) -> Option<&Vec<String>> {
        self.manifest.as_ref()?
            .workspace.as_ref()?
            .members.as_ref()
    }
}
