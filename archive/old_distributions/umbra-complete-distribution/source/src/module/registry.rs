use std::collections::{HashMap, HashSet};
use std::path::PathBuf;

use crate::error::{UmbraError, UmbraResult};
use crate::parser::ast::Program;

/// Registry for tracking loaded modules and their dependencies
#[derive(Debug)]
pub struct ModuleRegistry {
    /// Maps module names to their parsed ASTs
    modules: HashMap<String, Program>,
    /// Maps module names to their file paths
    module_files: HashMap<String, PathBuf>,
    /// Dependency graph: module -> set of modules it depends on
    dependencies: HashMap<String, HashSet<String>>,
    /// Reverse dependency graph: module -> set of modules that depend on it
    dependents: HashMap<String, HashSet<String>>,
    /// Modules currently being loaded (for cycle detection)
    loading_stack: Vec<String>,
}

impl ModuleRegistry {
    pub fn new() -> Self {
        Self {
            modules: HashMap::new(),
            module_files: HashMap::new(),
            dependencies: HashMap::new(),
            dependents: HashMap::new(),
            loading_stack: Vec::new(),
        }
    }

    /// Register a module with its AST and file path
    pub fn register_module(&mut self, name: String, program: Program, file_path: PathBuf) -> UmbraResult<()> {
        // Check for circular dependencies
        if self.loading_stack.contains(&name) {
            let cycle = self.loading_stack.iter()
                .skip_while(|&module| module != &name)
                .chain(std::iter::once(&name))
                .cloned()
                .collect::<Vec<_>>()
                .join(" -> ");
            return Err(UmbraError::Module(format!(
                "Circular dependency detected: {cycle}"
            )));
        }

        self.modules.insert(name.clone(), program);
        self.module_files.insert(name.clone(), file_path);
        
        // Initialize dependency sets
        self.dependencies.entry(name.clone()).or_default();
        self.dependents.entry(name).or_default();
        
        Ok(())
    }

    /// Add a dependency relationship
    pub fn add_dependency(&mut self, dependent: String, dependency: String) -> UmbraResult<()> {
        // Check for circular dependencies
        if self.would_create_cycle(&dependent, &dependency) {
            return Err(UmbraError::Module(format!(
                "Adding dependency '{dependent}' -> '{dependency}' would create a circular dependency"
            )));
        }

        self.dependencies.entry(dependent.clone()).or_default().insert(dependency.clone());
        self.dependents.entry(dependency).or_default().insert(dependent);
        
        Ok(())
    }

    /// Check if adding a dependency would create a cycle
    fn would_create_cycle(&self, from: &str, to: &str) -> bool {
        if from == to {
            return true;
        }

        let mut visited = HashSet::new();
        let mut stack = vec![to];

        while let Some(current) = stack.pop() {
            if visited.contains(current) {
                continue;
            }
            visited.insert(current);

            if current == from {
                return true;
            }

            if let Some(deps) = self.dependencies.get(current) {
                for dep in deps {
                    if !visited.contains(dep.as_str()) {
                        stack.push(dep);
                    }
                }
            }
        }

        false
    }

    /// Get a module's AST
    pub fn get_module(&self, name: &str) -> Option<&Program> {
        self.modules.get(name)
    }

    /// Get a module's file path
    pub fn get_module_file(&self, name: &str) -> Option<&PathBuf> {
        self.module_files.get(name)
    }

    /// Get all dependencies of a module
    pub fn get_dependencies(&self, name: &str) -> Option<&HashSet<String>> {
        self.dependencies.get(name)
    }

    /// Get all modules that depend on a given module
    pub fn get_dependents(&self, name: &str) -> Option<&HashSet<String>> {
        self.dependents.get(name)
    }

    /// Check if a module is registered
    pub fn is_registered(&self, name: &str) -> bool {
        self.modules.contains_key(name)
    }

    /// Get topological order of modules for compilation
    pub fn get_compilation_order(&self) -> UmbraResult<Vec<String>> {
        let mut result = Vec::new();
        let mut visited = HashSet::new();
        let mut temp_visited = HashSet::new();

        for module in self.modules.keys() {
            if !visited.contains(module) {
                self.topological_sort_visit(module, &mut visited, &mut temp_visited, &mut result)?;
            }
        }

        result.reverse();
        Ok(result)
    }

    fn topological_sort_visit(
        &self,
        module: &str,
        visited: &mut HashSet<String>,
        temp_visited: &mut HashSet<String>,
        result: &mut Vec<String>,
    ) -> UmbraResult<()> {
        if temp_visited.contains(module) {
            return Err(UmbraError::Module(format!(
                "Circular dependency detected involving module '{module}'"
            )));
        }

        if visited.contains(module) {
            return Ok(());
        }

        temp_visited.insert(module.to_string());

        if let Some(deps) = self.dependencies.get(module) {
            for dep in deps {
                self.topological_sort_visit(dep, visited, temp_visited, result)?;
            }
        }

        temp_visited.remove(module);
        visited.insert(module.to_string());
        result.push(module.to_string());

        Ok(())
    }

    /// Start loading a module (for cycle detection)
    pub fn start_loading(&mut self, module_name: String) {
        self.loading_stack.push(module_name);
    }

    /// Finish loading a module
    pub fn finish_loading(&mut self, module_name: &str) {
        if let Some(pos) = self.loading_stack.iter().position(|x| x == module_name) {
            self.loading_stack.remove(pos);
        }
    }

    /// Get all registered module names
    pub fn get_all_modules(&self) -> Vec<&String> {
        self.modules.keys().collect()
    }

    /// Clear all modules (for testing)
    pub fn clear(&mut self) {
        self.modules.clear();
        self.module_files.clear();
        self.dependencies.clear();
        self.dependents.clear();
        self.loading_stack.clear();
    }
}
