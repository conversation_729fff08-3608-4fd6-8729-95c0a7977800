/// Comprehensive module system for Umbra
/// 
/// This module provides full module resolution, import/export handling,
/// visibility controls, and namespace management.

use crate::error::{UmbraError, UmbraResult};
use crate::parser::ast::{Program, Statement, ImportStatement, ExportStatement};
use crate::semantic::symbol_table::{Symbol, SymbolTable, Visibility};
use std::collections::{HashMap, HashSet};
use std::path::{Path, PathBuf};
use std::fs;

/// Module system manager
pub struct ModuleSystem {
    /// Loaded modules
    modules: HashMap<String, Module>,
    /// Module dependency graph
    dependencies: HashMap<String, Vec<String>>,
    /// Module search paths
    search_paths: Vec<PathBuf>,
    /// Current module being processed
    current_module: Option<String>,
    /// Module loading cache
    loading_cache: HashSet<String>,
    /// Standard library modules
    stdlib_modules: HashMap<String, StdlibModule>,
}

/// Represents a loaded module
#[derive(Debug, Clone)]
pub struct Module {
    /// Module name
    pub name: String,
    /// Module file path
    pub path: PathBuf,
    /// Module AST
    pub ast: Program,
    /// Module symbol table
    pub symbols: SymbolTable,
    /// Exported symbols
    pub exports: HashMap<String, ExportedSymbol>,
    /// Imported symbols
    pub imports: HashMap<String, ImportedSymbol>,
    /// Module dependencies
    pub dependencies: Vec<String>,
    /// Module visibility
    pub visibility: ModuleVisibility,
}

/// Exported symbol information
#[derive(Debug, Clone)]
pub struct ExportedSymbol {
    /// Symbol name
    pub name: String,
    /// Original symbol
    pub symbol: Symbol,
    /// Export alias (if any)
    pub alias: Option<String>,
    /// Export visibility
    pub visibility: Visibility,
}

/// Imported symbol information
#[derive(Debug, Clone)]
pub struct ImportedSymbol {
    /// Symbol name in current module
    pub local_name: String,
    /// Original symbol name
    pub original_name: String,
    /// Source module
    pub source_module: String,
    /// Import type
    pub import_type: ImportType,
}

/// Import types
#[derive(Debug, Clone, PartialEq)]
pub enum ImportType {
    /// Import specific symbol: bring module::symbol
    Named,
    /// Import all symbols: bring module::*
    Wildcard,
    /// Import module: bring module
    Module,
    /// Import with alias: bring module::symbol as alias
    Aliased(String),
}

/// Module visibility levels
#[derive(Debug, Clone, PartialEq)]
pub enum ModuleVisibility {
    /// Public module (can be imported by anyone)
    Public,
    /// Internal module (can only be imported within same package)
    Internal,
    /// Private module (cannot be imported)
    Private,
}

/// Standard library module
#[derive(Debug, Clone)]
pub struct StdlibModule {
    /// Module name
    pub name: String,
    /// Module symbols
    pub symbols: SymbolTable,
    /// Module documentation
    pub documentation: String,
}

impl ModuleSystem {
    pub fn new() -> Self {
        let mut system = Self {
            modules: HashMap::new(),
            dependencies: HashMap::new(),
            search_paths: Vec::new(),
            current_module: None,
            loading_cache: HashSet::new(),
            stdlib_modules: HashMap::new(),
        };

        system.initialize_search_paths();
        system.initialize_stdlib_modules();
        system
    }

    /// Initialize default search paths
    fn initialize_search_paths(&mut self) {
        self.search_paths.push(PathBuf::from("."));
        self.search_paths.push(PathBuf::from("./src"));
        self.search_paths.push(PathBuf::from("./lib"));
        self.search_paths.push(PathBuf::from("./modules"));
        
        // Add system-wide module paths
        if let Ok(home) = std::env::var("HOME") {
            self.search_paths.push(PathBuf::from(home).join(".umbra/modules"));
        }
        
        // Add system module path
        self.search_paths.push(PathBuf::from("/usr/local/lib/umbra/modules"));
        self.search_paths.push(PathBuf::from("/usr/lib/umbra/modules"));
    }

    /// Initialize standard library modules
    fn initialize_stdlib_modules(&mut self) {
        // Core modules
        self.register_stdlib_module("std", "Standard library core functions");
        self.register_stdlib_module("std::io", "Input/output operations");
        self.register_stdlib_module("std::fs", "File system operations");
        self.register_stdlib_module("std::math", "Mathematical functions");
        self.register_stdlib_module("std::string", "String manipulation");
        self.register_stdlib_module("std::collections", "Collection types and operations");
        self.register_stdlib_module("std::memory", "Memory management");
        self.register_stdlib_module("std::testing", "Testing framework");
        
        // AI/ML modules
        self.register_stdlib_module("std::ai", "AI/ML core functionality");
        self.register_stdlib_module("std::ai::dataset", "Dataset operations");
        self.register_stdlib_module("std::ai::model", "Model operations");
        self.register_stdlib_module("std::ai::training", "Training utilities");
        self.register_stdlib_module("std::ai::evaluation", "Evaluation utilities");
        
        // System modules
        self.register_stdlib_module("std::sys", "System operations");
        self.register_stdlib_module("std::net", "Network operations");
        self.register_stdlib_module("std::time", "Time and date operations");
        self.register_stdlib_module("std::json", "JSON processing");
        self.register_stdlib_module("std::xml", "XML processing");
    }

    /// Register a standard library module
    fn register_stdlib_module(&mut self, name: &str, documentation: &str) {
        let module = StdlibModule {
            name: name.to_string(),
            symbols: SymbolTable::new(),
            documentation: documentation.to_string(),
        };
        
        self.stdlib_modules.insert(name.to_string(), module);
    }

    /// Add a module search path
    pub fn add_search_path<P: AsRef<Path>>(&mut self, path: P) {
        self.search_paths.push(path.as_ref().to_path_buf());
    }

    /// Load a module by name
    pub fn load_module(&mut self, module_name: &str) -> UmbraResult<&Module> {
        // Check if module is already loaded
        if self.modules.contains_key(module_name) {
            return Ok(self.modules.get(module_name).unwrap());
        }

        // Check for circular dependencies
        if self.loading_cache.contains(module_name) {
            return Err(UmbraError::Runtime(format!("Circular dependency detected: {}", module_name)));
        }

        // Check if it's a standard library module
        if self.is_stdlib_module(module_name) {
            return self.load_stdlib_module(module_name);
        }

        // Mark as loading
        self.loading_cache.insert(module_name.to_string());

        // Find module file
        let module_path = self.resolve_module_path(module_name)?;
        
        // Load and parse module
        let module = self.load_module_from_file(module_name, &module_path)?;
        
        // Process imports
        self.process_module_imports(&module.name.clone())?;
        
        // Remove from loading cache
        self.loading_cache.remove(module_name);
        
        // Store module
        self.modules.insert(module_name.to_string(), module);
        
        Ok(self.modules.get(module_name).unwrap())
    }

    /// Check if module is a standard library module
    fn is_stdlib_module(&self, module_name: &str) -> bool {
        self.stdlib_modules.contains_key(module_name)
    }

    /// Load a standard library module
    fn load_stdlib_module(&mut self, module_name: &str) -> UmbraResult<&Module> {
        if let Some(stdlib_module) = self.stdlib_modules.get(module_name) {
            // Create module from stdlib module
            let module = Module {
                name: module_name.to_string(),
                path: PathBuf::from(format!("<stdlib:{}>", module_name)),
                ast: Program {
                    statements: Vec::new(),
                    location: crate::error::SourceLocation { line: 1, column: 1 }
                }, // Empty AST for stdlib
                symbols: stdlib_module.symbols.clone(),
                exports: HashMap::new(), // TODO: Populate from stdlib
                imports: HashMap::new(),
                dependencies: Vec::new(),
                visibility: ModuleVisibility::Public,
            };
            
            self.modules.insert(module_name.to_string(), module);
            Ok(self.modules.get(module_name).unwrap())
        } else {
            Err(UmbraError::Runtime(format!("Standard library module not found: {}", module_name)))
        }
    }

    /// Resolve module name to file path
    fn resolve_module_path(&self, module_name: &str) -> UmbraResult<PathBuf> {
        // Convert module name to file path (e.g., "std::io" -> "std/io.umbra")
        let file_path = module_name.replace("::", "/") + ".umbra";
        
        // Search in all search paths
        for search_path in &self.search_paths {
            let full_path = search_path.join(&file_path);
            if full_path.exists() {
                return Ok(full_path);
            }
        }
        
        Err(UmbraError::Runtime(format!("Module not found: {}", module_name)))
    }

    /// Load module from file
    fn load_module_from_file(&mut self, module_name: &str, path: &Path) -> UmbraResult<Module> {
        // Read file content
        let content = fs::read_to_string(path)
            .map_err(|e| UmbraError::Runtime(format!("Failed to read module file {}: {}", path.display(), e)))?;

        // Parse module
        let mut lexer = crate::lexer::Lexer::new(content.clone());
        let tokens = lexer.tokenize()?;
        let mut parser = crate::parser::Parser::new(tokens);
        let ast = parser.parse()?;

        // Analyze module
        let mut analyzer = crate::semantic::analyzer::SemanticAnalyzer::new();
        let symbols = analyzer.analyze(&ast)?;

        // Extract exports and imports
        let (exports, imports, dependencies) = self.extract_module_metadata(&ast)?;

        Ok(Module {
            name: module_name.to_string(),
            path: path.to_path_buf(),
            ast,
            symbols: crate::semantic::symbol_table::SymbolTable::new(),
            exports,
            imports,
            dependencies,
            visibility: ModuleVisibility::Public, // Default visibility
        })
    }

    /// Extract module metadata (exports, imports, dependencies)
    fn extract_module_metadata(&self, ast: &Program) -> UmbraResult<(
        HashMap<String, ExportedSymbol>,
        HashMap<String, ImportedSymbol>,
        Vec<String>
    )> {
        let mut exports = HashMap::new();
        let mut imports = HashMap::new();
        let mut dependencies = Vec::new();

        for statement in &ast.statements {
            match statement {
                Statement::Import(import_stmt) => {
                    self.process_import_statement(import_stmt, &mut imports, &mut dependencies)?;
                }
                Statement::Export(export_stmt) => {
                    self.process_export_statement(export_stmt, &mut exports)?;
                }
                _ => {}
            }
        }

        Ok((exports, imports, dependencies))
    }

    /// Process import statement
    fn process_import_statement(
        &self,
        import_stmt: &ImportStatement,
        imports: &mut HashMap<String, ImportedSymbol>,
        dependencies: &mut Vec<String>,
    ) -> UmbraResult<()> {
        let module_name = match &import_stmt.import_type {
            crate::parser::ast::ImportType::Module(path) => path.to_string(),
            crate::parser::ast::ImportType::ModuleAs(path, _) => path.to_string(),
            crate::parser::ast::ImportType::Symbol(path, _) => path.to_string(),
            crate::parser::ast::ImportType::SymbolAs(path, _, _) => path.to_string(),
            crate::parser::ast::ImportType::Wildcard(path) => path.to_string(),
            crate::parser::ast::ImportType::Multiple(path, _) => path.to_string(),
        };
        dependencies.push(module_name.clone());

        match &import_stmt.import_type {
            crate::parser::ast::ImportType::Multiple(_, symbols) => {
                for symbol in symbols {
                    let imported_symbol = ImportedSymbol {
                        local_name: symbol.name.clone(),
                        original_name: symbol.name.clone(),
                        source_module: module_name.clone(),
                        import_type: ImportType::Named,
                    };
                    imports.insert(symbol.name.clone(), imported_symbol);
                }
            }
            crate::parser::ast::ImportType::Wildcard(_) => {
                // Wildcard import - will be resolved later
                let imported_symbol = ImportedSymbol {
                    local_name: "*".to_string(),
                    original_name: "*".to_string(),
                    source_module: module_name.clone(),
                    import_type: ImportType::Wildcard,
                };
                imports.insert("*".to_string(), imported_symbol);
            }
            crate::parser::ast::ImportType::Module(_) => {
                let imported_symbol = ImportedSymbol {
                    local_name: module_name.clone(),
                    original_name: module_name.clone(),
                    source_module: module_name.clone(),
                    import_type: ImportType::Module,
                };
                imports.insert(module_name.clone(), imported_symbol);
            }
            crate::parser::ast::ImportType::SymbolAs(_, symbol, alias) => {
                let imported_symbol = ImportedSymbol {
                    local_name: alias.clone(),
                    original_name: symbol.clone(),
                    source_module: module_name.clone(),
                    import_type: ImportType::Aliased(alias.clone()),
                };
                imports.insert(alias.clone(), imported_symbol);
            }
            crate::parser::ast::ImportType::ModuleAs(module_path, alias) => {
                // TODO: Implement module alias import
                let module_name = module_path.segments.join("::");
                println!("Importing module {} as {}", module_name, alias);
            },
            crate::parser::ast::ImportType::Symbol(module_path, symbol) => {
                // TODO: Implement symbol import
                let module_name = module_path.segments.join("::");
                println!("Importing symbol {} from {}", symbol, module_name);
            },
        }

        Ok(())
    }

    /// Process export statement
    fn process_export_statement(
        &self,
        export_stmt: &ExportStatement,
        exports: &mut HashMap<String, ExportedSymbol>,
    ) -> UmbraResult<()> {
        match &export_stmt.export_type {
            crate::parser::ast::ExportType::Function(name) => {
                let exported_symbol = ExportedSymbol {
                    name: name.clone(),
                    symbol: Symbol::default(), // TODO: Get actual symbol
                    alias: None,
                    visibility: Visibility::Public,
                };
                exports.insert(name.clone(), exported_symbol);
            }
            crate::parser::ast::ExportType::Multiple(names) => {
                for name in names {
                    let exported_symbol = ExportedSymbol {
                        name: name.clone(),
                        symbol: Symbol::default(), // TODO: Get actual symbol
                        alias: None,
                        visibility: Visibility::Public,
                    };
                    exports.insert(name.clone(), exported_symbol);
                }
            }
            _ => {
                // Handle other export types later
            }
        }

        Ok(())
    }

    /// Process module imports (load dependencies)
    fn process_module_imports(&mut self, module_name: &str) -> UmbraResult<()> {
        let dependencies = if let Some(module) = self.modules.get(module_name) {
            module.dependencies.clone()
        } else {
            return Ok(());
        };

        for dependency in dependencies {
            self.load_module(&dependency)?;
        }

        Ok(())
    }

    /// Resolve symbol in module context
    pub fn resolve_symbol(&self, module_name: &str, symbol_name: &str) -> Option<&Symbol> {
        if let Some(module) = self.modules.get(module_name) {
            // Check local symbols first
            if let Some(symbol) = module.symbols.lookup(symbol_name) {
                return Some(symbol);
            }

            // Check imported symbols
            if let Some(imported) = module.imports.get(symbol_name) {
                if let Some(source_module) = self.modules.get(&imported.source_module) {
                    return source_module.symbols.lookup(&imported.original_name);
                }
            }

            // Check wildcard imports
            for imported in module.imports.values() {
                if imported.import_type == ImportType::Wildcard {
                    if let Some(source_module) = self.modules.get(&imported.source_module) {
                        if let Some(symbol) = source_module.symbols.lookup(symbol_name) {
                            return Some(symbol);
                        }
                    }
                }
            }
        }

        None
    }

    /// Get module by name
    pub fn get_module(&self, module_name: &str) -> Option<&Module> {
        self.modules.get(module_name)
    }

    /// Get all loaded modules
    pub fn get_all_modules(&self) -> &HashMap<String, Module> {
        &self.modules
    }

    /// Check if module is loaded
    pub fn is_module_loaded(&self, module_name: &str) -> bool {
        self.modules.contains_key(module_name)
    }

    /// Get module dependencies
    pub fn get_module_dependencies(&self, module_name: &str) -> Vec<String> {
        self.modules.get(module_name)
            .map(|m| m.dependencies.clone())
            .unwrap_or_default()
    }

    /// Validate module dependencies (check for cycles)
    pub fn validate_dependencies(&self) -> UmbraResult<()> {
        for (module_name, dependencies) in &self.dependencies {
            self.check_circular_dependency(module_name, dependencies, &mut HashSet::new())?;
        }
        Ok(())
    }

    /// Check for circular dependencies
    fn check_circular_dependency(
        &self,
        module_name: &str,
        dependencies: &[String],
        visited: &mut HashSet<String>,
    ) -> UmbraResult<()> {
        if visited.contains(module_name) {
            return Err(UmbraError::Runtime(format!("Circular dependency detected: {}", module_name)));
        }

        visited.insert(module_name.to_string());

        for dependency in dependencies {
            if let Some(dep_dependencies) = self.dependencies.get(dependency) {
                self.check_circular_dependency(dependency, dep_dependencies, visited)?;
            }
        }

        visited.remove(module_name);
        Ok(())
    }
}

impl Default for ModuleSystem {
    fn default() -> Self {
        Self::new()
    }
}
