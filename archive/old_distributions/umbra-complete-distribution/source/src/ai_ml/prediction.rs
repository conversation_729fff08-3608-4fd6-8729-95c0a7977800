use crate::error::{<PERSON><PERSON><PERSON><PERSON>r, UmbraResult};
use crate::ai_ml::{Dataset, Model};
use std::fs::File;
use std::io::Write;
use std::process::Command;
use serde::{Deserialize, Serialize};

/// Prediction manager for AI/ML models
pub struct PredictionManager {
    pub working_dir: String,
    pub python_executable: String,
}

impl PredictionManager {
    pub fn new(working_dir: String) -> Self {
        Self {
            working_dir,
            python_executable: "python3".to_string(),
        }
    }

    /// Make predictions using a trained model
    pub fn predict(
        &self,
        model: &Model,
        input_data: &Dataset,
        model_path: &str,
        output_path: Option<&str>,
    ) -> UmbraResult<PredictionResults> {
        if !model.trained {
            return Err(UmbraError::Runtime("Model must be trained before making predictions".to_string()));
        }

        // Save input data to temporary file
        let input_path = format!("{}/prediction_input.csv", self.working_dir);
        input_data.to_csv(&input_path)?;

        // Generate prediction script
        let script_content = self.generate_prediction_script(model, &input_path, model_path, output_path)?;
        let script_path = format!("{}/predict.py", self.working_dir);
        
        let mut script_file = File::create(&script_path)
            .map_err(|e| UmbraError::Runtime(format!("Failed to create prediction script: {}", e)))?;
        
        script_file.write_all(script_content.as_bytes())
            .map_err(|e| UmbraError::Runtime(format!("Failed to write prediction script: {}", e)))?;

        // Execute prediction script
        println!("🔮 Making predictions...");
        let output = Command::new(&self.python_executable)
            .arg(&script_path)
            .current_dir(&self.working_dir)
            .output()
            .map_err(|e| UmbraError::Runtime(format!("Failed to execute prediction script: {}", e)))?;

        if !output.status.success() {
            let stderr = String::from_utf8_lossy(&output.stderr);
            return Err(UmbraError::Runtime(format!("Prediction failed: {}", stderr)));
        }

        // Load prediction results
        self.load_prediction_results(input_data.shape.0)
    }

    fn generate_prediction_script(
        &self,
        model: &Model,
        input_path: &str,
        model_path: &str,
        output_path: Option<&str>,
    ) -> UmbraResult<String> {
        match model.model_type {
            crate::ai_ml::ModelType::NeuralNetwork => {
                self.generate_neural_network_prediction_script(input_path, model_path, output_path)
            }
            crate::ai_ml::ModelType::LinearRegression => {
                self.generate_linear_regression_prediction_script(input_path, model_path, output_path)
            }
            _ => Err(UmbraError::Runtime(format!("Prediction not implemented for {:?}", model.model_type))),
        }
    }

    fn generate_neural_network_prediction_script(
        &self,
        input_path: &str,
        model_path: &str,
        output_path: Option<&str>,
    ) -> UmbraResult<String> {
        let output_file = output_path.unwrap_or("predictions.csv");
        
        let script = format!(r#"
import numpy as np
import pandas as pd
import tensorflow as tf
from tensorflow import keras
from sklearn.preprocessing import StandardScaler
import json

# Load input data
data = pd.read_csv('{}')
X = data.values

# Preprocess data (assuming same preprocessing as training)
scaler = StandardScaler()
X = scaler.fit_transform(X)

# Load model
model = keras.models.load_model('{}')

# Make predictions
predictions = model.predict(X)

# Process predictions based on output type
if predictions.shape[1] == 1:
    # Binary classification or regression
    if model.layers[-1].activation.__name__ == 'sigmoid':
        # Binary classification
        pred_classes = (predictions > 0.5).astype(int).flatten()
        pred_probs = predictions.flatten()
        results = pd.DataFrame({{
            'prediction': pred_classes,
            'probability': pred_probs
        }})
    else:
        # Regression
        results = pd.DataFrame({{
            'prediction': predictions.flatten()
        }})
else:
    # Multi-class classification
    pred_classes = np.argmax(predictions, axis=1)
    pred_probs = np.max(predictions, axis=1)
    results = pd.DataFrame({{
        'prediction': pred_classes,
        'confidence': pred_probs
    }})
    
    # Add probability columns for each class
    for i in range(predictions.shape[1]):
        results[f'class_{{i}}_prob'] = predictions[:, i]

# Save predictions
results.to_csv('{}', index=False)

# Save detailed results as JSON
prediction_details = {{
    'model_path': '{}',
    'input_shape': X.shape,
    'output_shape': predictions.shape,
    'predictions': predictions.tolist(),
    'summary': {{
        'total_samples': len(predictions),
        'prediction_type': 'classification' if predictions.shape[1] > 1 or model.layers[-1].activation.__name__ == 'sigmoid' else 'regression'
    }}
}}

with open('prediction_results.json', 'w') as f:
    json.dump(prediction_details, f, indent=2)

print(f"Predictions saved to {}")
print(f"Total samples processed: {{len(predictions)}}")
"#, input_path, model_path, output_file, model_path, output_file);

        Ok(script)
    }

    fn generate_linear_regression_prediction_script(
        &self,
        input_path: &str,
        model_path: &str,
        output_path: Option<&str>,
    ) -> UmbraResult<String> {
        let output_file = output_path.unwrap_or("predictions.csv");
        
        let script = format!(r#"
import numpy as np
import pandas as pd
import joblib
from sklearn.preprocessing import StandardScaler
import json

# Load input data
data = pd.read_csv('{}')
X = data.values

# Load model and scaler
model = joblib.load('{}')
scaler = joblib.load('{}_scaler.pkl')

# Preprocess data
X = scaler.transform(X)

# Make predictions
predictions = model.predict(X)

# Create results DataFrame
results = pd.DataFrame({{
    'prediction': predictions
}})

# Save predictions
results.to_csv('{}', index=False)

# Save detailed results as JSON
prediction_details = {{
    'model_path': '{}',
    'input_shape': X.shape,
    'predictions': predictions.tolist(),
    'model_coefficients': model.coef_.tolist(),
    'model_intercept': model.intercept_,
    'summary': {{
        'total_samples': len(predictions),
        'prediction_type': 'regression',
        'mean_prediction': float(np.mean(predictions)),
        'std_prediction': float(np.std(predictions))
    }}
}}

with open('prediction_results.json', 'w') as f:
    json.dump(prediction_details, f, indent=2)

print(f"Predictions saved to {}")
print(f"Total samples processed: {{len(predictions)}}")
print(f"Mean prediction: {{np.mean(predictions):.4f}}")
print(f"Std prediction: {{np.std(predictions):.4f}}")
"#, input_path, model_path, model_path, output_file, model_path, output_file);

        Ok(script)
    }

    fn load_prediction_results(&self, num_samples: usize) -> UmbraResult<PredictionResults> {
        // Load detailed results from JSON
        let results_path = format!("{}/prediction_results.json", self.working_dir);
        if std::path::Path::new(&results_path).exists() {
            let file = File::open(&results_path)
                .map_err(|e| UmbraError::Runtime(format!("Failed to open prediction results: {}", e)))?;
            
            let json_results: serde_json::Value = serde_json::from_reader(file)
                .map_err(|e| UmbraError::Runtime(format!("Failed to parse prediction results: {}", e)))?;

            let predictions: Vec<Vec<f64>> = json_results["predictions"]
                .as_array()
                .unwrap_or(&Vec::new())
                .iter()
                .map(|v| {
                    if v.is_array() {
                        v.as_array().unwrap().iter().filter_map(|x| x.as_f64()).collect()
                    } else {
                        vec![v.as_f64().unwrap_or(0.0)]
                    }
                })
                .collect();

            let prediction_type = json_results["summary"]["prediction_type"]
                .as_str()
                .unwrap_or("unknown")
                .to_string();

            let confidence_scores = if prediction_type == "classification" {
                // Extract confidence scores for classification
                predictions.iter().map(|pred| {
                    if pred.len() == 1 {
                        pred[0] // Binary classification probability
                    } else {
                        pred.iter().fold(0.0f64, |a, &b| a.max(b)) // Max probability for multi-class
                    }
                }).collect()
            } else {
                Vec::new()
            };

            let statistics = self.calculate_prediction_statistics(&predictions);

            return Ok(PredictionResults {
                predictions,
                confidence_scores,
                prediction_type,
                model_info: ModelPredictionInfo {
                    input_shape: json_results["input_shape"].as_array()
                        .map(|arr| arr.iter().filter_map(|v| v.as_u64().map(|x| x as usize)).collect())
                        .unwrap_or_default(),
                    output_shape: json_results["output_shape"].as_array()
                        .map(|arr| arr.iter().filter_map(|v| v.as_u64().map(|x| x as usize)).collect())
                        .unwrap_or_default(),
                    total_samples: json_results["summary"]["total_samples"].as_u64().unwrap_or(0) as usize,
                },
                statistics,
            });
        }

        // Fallback: create minimal results
        Ok(PredictionResults {
            predictions: Vec::new(),
            confidence_scores: Vec::new(),
            prediction_type: "unknown".to_string(),
            model_info: ModelPredictionInfo {
                input_shape: Vec::new(),
                output_shape: Vec::new(),
                total_samples: num_samples,
            },
            statistics: PredictionStatistics {
                mean: 0.0,
                std: 0.0,
                min: 0.0,
                max: 0.0,
                median: 0.0,
            },
        })
    }

    fn calculate_prediction_statistics(&self, predictions: &[Vec<f64>]) -> PredictionStatistics {
        if predictions.is_empty() {
            return PredictionStatistics {
                mean: 0.0,
                std: 0.0,
                min: 0.0,
                max: 0.0,
                median: 0.0,
            };
        }

        // Flatten predictions for statistics (take first value if multi-dimensional)
        let values: Vec<f64> = predictions.iter().map(|pred| pred.first().copied().unwrap_or(0.0)).collect();
        
        let n = values.len() as f64;
        let mean = values.iter().sum::<f64>() / n;
        let variance = values.iter().map(|x| (x - mean).powi(2)).sum::<f64>() / n;
        let std = variance.sqrt();
        
        let mut sorted_values = values.clone();
        sorted_values.sort_by(|a, b| a.partial_cmp(b).unwrap());
        
        let min = sorted_values.first().copied().unwrap_or(0.0);
        let max = sorted_values.last().copied().unwrap_or(0.0);
        let median = if sorted_values.len() % 2 == 0 {
            let mid = sorted_values.len() / 2;
            (sorted_values[mid - 1] + sorted_values[mid]) / 2.0
        } else {
            sorted_values[sorted_values.len() / 2]
        };

        PredictionStatistics {
            mean,
            std,
            min,
            max,
            median,
        }
    }

    /// Batch prediction for large datasets
    pub fn batch_predict(
        &self,
        model: &Model,
        input_data: &Dataset,
        model_path: &str,
        batch_size: usize,
        output_path: Option<&str>,
    ) -> UmbraResult<PredictionResults> {
        let total_samples = input_data.shape.0;
        let num_batches = (total_samples + batch_size - 1) / batch_size;
        
        println!("Processing {} samples in {} batches of size {}", total_samples, num_batches, batch_size);
        
        let mut all_predictions = Vec::new();
        let mut all_confidence_scores = Vec::new();
        
        for batch_idx in 0..num_batches {
            let start_idx = batch_idx * batch_size;
            let end_idx = std::cmp::min(start_idx + batch_size, total_samples);
            
            // Create batch dataset
            let batch_data = Dataset {
                name: format!("{}_batch_{}", input_data.name, batch_idx),
                data: input_data.data[start_idx..end_idx].to_vec(),
                labels: None,
                feature_names: input_data.feature_names.clone(),
                shape: (end_idx - start_idx, input_data.shape.1),
            };
            
            // Process batch
            let batch_results = self.predict(model, &batch_data, model_path, None)?;
            all_predictions.extend(batch_results.predictions);
            all_confidence_scores.extend(batch_results.confidence_scores);
            
            println!("Processed batch {}/{}", batch_idx + 1, num_batches);
        }
        
        // Save combined results if output path specified
        if let Some(output_file) = output_path {
            self.save_predictions(&all_predictions, &all_confidence_scores, output_file)?;
        }
        
        let statistics = self.calculate_prediction_statistics(&all_predictions);

        Ok(PredictionResults {
            predictions: all_predictions,
            confidence_scores: all_confidence_scores,
            prediction_type: "batch".to_string(),
            model_info: ModelPredictionInfo {
                input_shape: vec![total_samples, input_data.shape.1],
                output_shape: Vec::new(),
                total_samples,
            },
            statistics,
        })
    }

    fn save_predictions(
        &self,
        predictions: &[Vec<f64>],
        confidence_scores: &[f64],
        output_path: &str,
    ) -> UmbraResult<()> {
        use std::io::Write;
        
        let mut file = File::create(output_path)
            .map_err(|e| UmbraError::Runtime(format!("Failed to create output file: {}", e)))?;
        
        // Write header
        if predictions.first().map(|p| p.len()).unwrap_or(0) == 1 {
            writeln!(file, "prediction,confidence")?;
            for (pred, conf) in predictions.iter().zip(confidence_scores.iter()) {
                writeln!(file, "{},{}", pred[0], conf)?;
            }
        } else {
            // Multi-class predictions
            let num_classes = predictions.first().map(|p| p.len()).unwrap_or(0);
            let header = (0..num_classes).map(|i| format!("class_{}", i)).collect::<Vec<_>>().join(",");
            writeln!(file, "{}", header)?;
            
            for pred in predictions {
                let pred_str = pred.iter().map(|p| p.to_string()).collect::<Vec<_>>().join(",");
                writeln!(file, "{}", pred_str)?;
            }
        }
        
        Ok(())
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PredictionResults {
    pub predictions: Vec<Vec<f64>>,
    pub confidence_scores: Vec<f64>,
    pub prediction_type: String,
    pub model_info: ModelPredictionInfo,
    pub statistics: PredictionStatistics,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModelPredictionInfo {
    pub input_shape: Vec<usize>,
    pub output_shape: Vec<usize>,
    pub total_samples: usize,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PredictionStatistics {
    pub mean: f64,
    pub std: f64,
    pub min: f64,
    pub max: f64,
    pub median: f64,
}

impl Default for PredictionManager {
    fn default() -> Self {
        Self::new("./umbra_prediction".to_string())
    }
}
