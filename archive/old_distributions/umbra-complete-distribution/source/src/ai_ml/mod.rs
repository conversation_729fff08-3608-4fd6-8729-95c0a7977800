// AI/ML functionality implementation
pub mod dataset;
pub mod model;
pub mod training;
pub mod evaluation;
pub mod prediction;
pub mod executor;

pub use dataset::*;
#[allow(ambiguous_glob_reexports)]
pub use model::*;
#[allow(ambiguous_glob_reexports)]
pub use training::*;
pub use evaluation::*;
pub use prediction::*;
pub use executor::*;

use crate::error::{UmbraError, UmbraResult};
use std::collections::HashMap;

/// AI/ML framework manager
pub struct AIFramework {
    pub frameworks: HashMap<String, FrameworkInfo>,
    pub models: HashMap<String, ModelInfo>,
    pub datasets: HashMap<String, DatasetInfo>,
}

#[derive(Debug, Clone)]
pub struct FrameworkInfo {
    pub name: String,
    pub version: String,
    pub available: bool,
    pub gpu_support: bool,
}

#[derive(Debug, Clone)]
pub struct ModelInfo {
    pub name: String,
    pub model_type: String,
    pub input_shape: Vec<usize>,
    pub output_shape: Vec<usize>,
    pub parameters: usize,
    pub trained: bool,
}

#[derive(Debug, Clone)]
pub struct DatasetInfo {
    pub name: String,
    pub path: String,
    pub size: usize,
    pub features: usize,
    pub labels: Option<usize>,
}

impl AIFramework {
    pub fn new() -> Self {
        Self {
            frameworks: HashMap::new(),
            models: HashMap::new(),
            datasets: HashMap::new(),
        }
    }

    /// Check available AI/ML frameworks
    pub fn check_frameworks(&mut self) -> UmbraResult<()> {
        // Check for Python and common ML libraries
        self.check_python_framework()?;
        self.check_numpy_framework()?;
        self.check_pytorch_framework()?;
        self.check_tensorflow_framework()?;
        self.check_scikit_learn_framework()?;
        
        Ok(())
    }

    fn check_python_framework(&mut self) -> UmbraResult<()> {
        let available = std::process::Command::new("python3")
            .arg("--version")
            .output()
            .is_ok();

        self.frameworks.insert("python".to_string(), FrameworkInfo {
            name: "Python".to_string(),
            version: if available { "3.x".to_string() } else { "Not found".to_string() },
            available,
            gpu_support: false,
        });

        Ok(())
    }

    fn check_numpy_framework(&mut self) -> UmbraResult<()> {
        let available = std::process::Command::new("python3")
            .arg("-c")
            .arg("import numpy; print(numpy.__version__)")
            .output()
            .map(|output| output.status.success())
            .unwrap_or(false);

        self.frameworks.insert("numpy".to_string(), FrameworkInfo {
            name: "NumPy".to_string(),
            version: if available { "1.x".to_string() } else { "Not found".to_string() },
            available,
            gpu_support: false,
        });

        Ok(())
    }

    fn check_pytorch_framework(&mut self) -> UmbraResult<()> {
        let available = std::process::Command::new("python3")
            .arg("-c")
            .arg("import torch; print(torch.__version__)")
            .output()
            .map(|output| output.status.success())
            .unwrap_or(false);

        let gpu_support = if available {
            std::process::Command::new("python3")
                .arg("-c")
                .arg("import torch; print(torch.cuda.is_available())")
                .output()
                .map(|output| {
                    String::from_utf8_lossy(&output.stdout).trim() == "True"
                })
                .unwrap_or(false)
        } else {
            false
        };

        self.frameworks.insert("pytorch".to_string(), FrameworkInfo {
            name: "PyTorch".to_string(),
            version: if available { "2.x".to_string() } else { "Not found".to_string() },
            available,
            gpu_support,
        });

        Ok(())
    }

    fn check_tensorflow_framework(&mut self) -> UmbraResult<()> {
        let available = std::process::Command::new("python3")
            .arg("-c")
            .arg("import tensorflow as tf; print(tf.__version__)")
            .output()
            .map(|output| output.status.success())
            .unwrap_or(false);

        let gpu_support = if available {
            std::process::Command::new("python3")
                .arg("-c")
                .arg("import tensorflow as tf; print(len(tf.config.list_physical_devices('GPU')) > 0)")
                .output()
                .map(|output| {
                    String::from_utf8_lossy(&output.stdout).trim() == "True"
                })
                .unwrap_or(false)
        } else {
            false
        };

        self.frameworks.insert("tensorflow".to_string(), FrameworkInfo {
            name: "TensorFlow".to_string(),
            version: if available { "2.x".to_string() } else { "Not found".to_string() },
            available,
            gpu_support,
        });

        Ok(())
    }

    fn check_scikit_learn_framework(&mut self) -> UmbraResult<()> {
        let available = std::process::Command::new("python3")
            .arg("-c")
            .arg("import sklearn; print(sklearn.__version__)")
            .output()
            .map(|output| output.status.success())
            .unwrap_or(false);

        self.frameworks.insert("scikit-learn".to_string(), FrameworkInfo {
            name: "Scikit-Learn".to_string(),
            version: if available { "1.x".to_string() } else { "Not found".to_string() },
            available,
            gpu_support: false,
        });

        Ok(())
    }

    /// Install AI/ML frameworks
    pub fn install_frameworks(&self, frameworks: &[String], gpu: bool) -> UmbraResult<()> {
        for framework in frameworks {
            match framework.as_str() {
                "numpy" => self.install_numpy()?,
                "pytorch" => self.install_pytorch(gpu)?,
                "tensorflow" => self.install_tensorflow(gpu)?,
                "scikit-learn" => self.install_scikit_learn()?,
                _ => return Err(UmbraError::Runtime(format!("Unknown framework: {}", framework))),
            }
        }
        Ok(())
    }

    fn install_numpy(&self) -> UmbraResult<()> {
        let status = std::process::Command::new("pip3")
            .args(&["install", "numpy"])
            .status()
            .map_err(|e| UmbraError::Runtime(format!("Failed to install NumPy: {}", e)))?;

        if !status.success() {
            return Err(UmbraError::Runtime("Failed to install NumPy".to_string()));
        }

        Ok(())
    }

    fn install_pytorch(&self, gpu: bool) -> UmbraResult<()> {
        let package = if gpu { "torch torchvision torchaudio" } else { "torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu" };
        
        let status = std::process::Command::new("pip3")
            .args(&["install", package])
            .status()
            .map_err(|e| UmbraError::Runtime(format!("Failed to install PyTorch: {}", e)))?;

        if !status.success() {
            return Err(UmbraError::Runtime("Failed to install PyTorch".to_string()));
        }

        Ok(())
    }

    fn install_tensorflow(&self, gpu: bool) -> UmbraResult<()> {
        let package = if gpu { "tensorflow[and-cuda]" } else { "tensorflow" };
        
        let status = std::process::Command::new("pip3")
            .args(&["install", package])
            .status()
            .map_err(|e| UmbraError::Runtime(format!("Failed to install TensorFlow: {}", e)))?;

        if !status.success() {
            return Err(UmbraError::Runtime("Failed to install TensorFlow".to_string()));
        }

        Ok(())
    }

    fn install_scikit_learn(&self) -> UmbraResult<()> {
        let status = std::process::Command::new("pip3")
            .args(&["install", "scikit-learn"])
            .status()
            .map_err(|e| UmbraError::Runtime(format!("Failed to install Scikit-Learn: {}", e)))?;

        if !status.success() {
            return Err(UmbraError::Runtime("Failed to install Scikit-Learn".to_string()));
        }

        Ok(())
    }
}

impl Default for AIFramework {
    fn default() -> Self {
        Self::new()
    }
}
