/// PyTorch Integration for Umbra
/// 
/// Provides seamless integration with PyTorch for deep learning operations,
/// including tensor operations, neural networks, and model training.

use crate::error::{UmbraError, UmbraResult};
use crate::interop::{InteropConfig, python::PythonFFI};
use pyo3::prelude::*;
use pyo3::types::PyDict;

/// PyTorch bridge for Umbra
pub struct PyTorchBridge {
    /// PyTorch module
    torch_module: PyObject,
    /// PyTorch neural network module
    nn_module: PyObject,
    /// Configuration
    config: InteropConfig,
}

/// PyTorch tensor wrapper
#[derive(Debug, Clone)]
pub struct UmbraTensor {
    /// Python PyTorch tensor object
    tensor: PyObject,
    /// Tensor shape
    shape: Vec<usize>,
    /// Data type
    dtype: String,
    /// Device (cpu, cuda, etc.)
    device: String,
    /// Requires gradient
    requires_grad: bool,
}

/// PyTorch model wrapper
#[derive(Debug, <PERSON>lone)]
pub struct UmbraModel {
    /// Python PyTorch model object
    model: PyObject,
    /// Model name
    name: String,
    /// Model parameters count
    param_count: usize,
}

/// Training configuration
#[derive(Debug, Clone)]
pub struct TrainingConfig {
    /// Learning rate
    pub learning_rate: f64,
    /// Batch size
    pub batch_size: usize,
    /// Number of epochs
    pub epochs: usize,
    /// Optimizer type
    pub optimizer: String,
    /// Loss function
    pub loss_function: String,
    /// Device to use
    pub device: String,
}

impl PyTorchBridge {
    /// Create a new PyTorch bridge
    pub fn new(python_ffi: &PythonFFI, config: &InteropConfig) -> UmbraResult<Self> {
        Python::with_gil(|py| {
            // Import PyTorch
            let torch = py.import("torch")
                .map_err(|e| UmbraError::Runtime(format!("Failed to import PyTorch: {}. Install with: pip install torch", e)))?;

            // Import torch.nn
            let nn = py.import("torch.nn")
                .map_err(|e| UmbraError::Runtime(format!("Failed to import torch.nn: {}", e)))?;

            Ok(Self {
                torch_module: torch.into(),
                nn_module: nn.into(),
                config: config.clone(),
            })
        })
    }

    /// Create a tensor from data
    pub fn tensor_from_data(&self, data: &[f64], shape: &[usize], requires_grad: bool) -> UmbraResult<UmbraTensor> {
        Python::with_gil(|py| {
            let torch = self.torch_module.as_ref(py);
            
            // Convert data to Python list
            let py_list = pyo3::types::PyList::new(py, data);

            // Create tensor
            let tensor = torch.call_method1("tensor", (py_list,))?;
            
            // Reshape if needed
            let reshaped_tensor = if shape.len() > 1 {
                let shape_tuple = shape.iter().map(|&s| s as i64).collect::<Vec<_>>();
                tensor.call_method1("reshape", (shape_tuple,))?
            } else {
                tensor
            };

            // Set requires_grad
            let final_tensor = if requires_grad {
                reshaped_tensor.call_method1("requires_grad_", (true,))?
            } else {
                reshaped_tensor
            };

            self.wrap_tensor(final_tensor)
        })
    }

    /// Create zeros tensor
    pub fn zeros(&self, shape: &[usize], requires_grad: bool, device: Option<&str>) -> UmbraResult<UmbraTensor> {
        Python::with_gil(|py| {
            let torch = self.torch_module.as_ref(py);
            let shape_tuple = shape.iter().map(|&s| s as i64).collect::<Vec<_>>();
            
            let kwargs = PyDict::new(py);
            if let Some(dev) = device {
                kwargs.set_item("device", dev)?;
            }
            kwargs.set_item("requires_grad", requires_grad)?;

            let tensor = torch.call_method("zeros", (shape_tuple,), Some(kwargs))?;
            self.wrap_tensor(tensor)
        })
    }

    /// Create random tensor
    pub fn randn(&self, shape: &[usize], requires_grad: bool, device: Option<&str>) -> UmbraResult<UmbraTensor> {
        Python::with_gil(|py| {
            let torch = self.torch_module.as_ref(py);
            let shape_tuple = shape.iter().map(|&s| s as i64).collect::<Vec<_>>();
            
            let kwargs = PyDict::new(py);
            if let Some(dev) = device {
                kwargs.set_item("device", dev)?;
            }
            kwargs.set_item("requires_grad", requires_grad)?;

            let tensor = torch.call_method("randn", (shape_tuple,), Some(kwargs))?;
            self.wrap_tensor(tensor)
        })
    }

    /// Tensor addition
    pub fn add(&self, a: &UmbraTensor, b: &UmbraTensor) -> UmbraResult<UmbraTensor> {
        Python::with_gil(|py| {
            let result = a.tensor.as_ref(py).call_method1("add", (b.tensor.as_ref(py),))?;
            self.wrap_tensor(result)
        })
    }

    /// Matrix multiplication
    pub fn matmul(&self, a: &UmbraTensor, b: &UmbraTensor) -> UmbraResult<UmbraTensor> {
        Python::with_gil(|py| {
            let torch = self.torch_module.as_ref(py);
            let result = torch.call_method1("matmul", (a.tensor.as_ref(py), b.tensor.as_ref(py)))?;
            self.wrap_tensor(result)
        })
    }

    /// Create a linear layer
    pub fn linear_layer(&self, in_features: usize, out_features: usize, bias: bool) -> UmbraResult<PyObject> {
        Python::with_gil(|py| {
            let nn = self.nn_module.as_ref(py);
            let kwargs = pyo3::types::PyDict::new(py);
            kwargs.set_item("bias", bias)?;
            let layer = nn.call_method("Linear", (in_features, out_features), Some(kwargs))?;
            Ok(layer.into())
        }).map_err(|e: PyErr| UmbraError::Runtime(format!("Failed to create linear layer: {}", e)))
    }

    /// Create a simple neural network
    pub fn create_mlp(&self, layer_sizes: &[usize], activation: &str) -> UmbraResult<UmbraModel> {
        Python::with_gil(|py| {
            // Create a simple MLP using Sequential
            let nn = self.nn_module.as_ref(py);
            let sequential = nn.getattr("Sequential")?;
            
            let layers = pyo3::types::PyList::empty(py);
            
            for i in 0..layer_sizes.len() - 1 {
                // Add linear layer
                let linear = nn.call_method1("Linear", (layer_sizes[i], layer_sizes[i + 1]))?;
                layers.append(linear)?;
                
                // Add activation (except for last layer)
                if i < layer_sizes.len() - 2 {
                    let activation_layer = match activation {
                        "relu" => nn.getattr("ReLU")?.call0()?,
                        "sigmoid" => nn.getattr("Sigmoid")?.call0()?,
                        "tanh" => nn.getattr("Tanh")?.call0()?,
                        _ => nn.getattr("ReLU")?.call0()?, // Default to ReLU
                    };
                    layers.append(activation_layer)?;
                }
            }
            
            let model = sequential.call1((layers,))?;
            
            // Count parameters
            let param_count = self.count_parameters(&model)?;
            
            Ok(UmbraModel {
                model: model.into(),
                name: "MLP".to_string(),
                param_count,
            })
        }).map_err(|e: PyErr| UmbraError::Runtime(format!("Failed to create MLP: {}", e)))
    }

    /// Forward pass through a model
    pub fn forward(&self, model: &UmbraModel, input: &UmbraTensor) -> UmbraResult<UmbraTensor> {
        Python::with_gil(|py| {
            let result = model.model.as_ref(py).call1((input.tensor.as_ref(py),))?;
            self.wrap_tensor(result)
        })
    }

    /// Create an optimizer
    pub fn create_optimizer(&self, model: &UmbraModel, optimizer_type: &str, learning_rate: f64) -> UmbraResult<PyObject> {
        Python::with_gil(|py| {
            let torch = self.torch_module.as_ref(py);
            let optim = torch.getattr("optim")?;
            
            let parameters = model.model.as_ref(py).call_method0("parameters")?;
            
            let optimizer = match optimizer_type {
                "adam" => optim.getattr("Adam")?.call1((parameters,))?,
                "sgd" => {
                    let kwargs = pyo3::types::PyDict::new(py);
                    kwargs.set_item("lr", learning_rate)?;
                    optim.getattr("SGD")?.call((parameters,), Some(kwargs))?
                },
                "rmsprop" => optim.getattr("RMSprop")?.call1((parameters,))?,
                _ => optim.getattr("Adam")?.call1((parameters,))?, // Default to Adam
            };
            
            Ok(optimizer.into())
        }).map_err(|e: PyErr| UmbraError::Runtime(format!("Failed to create optimizer: {}", e)))
    }

    /// Create a loss function
    pub fn create_loss_function(&self, loss_type: &str) -> UmbraResult<PyObject> {
        Python::with_gil(|py| {
            let nn = self.nn_module.as_ref(py);
            
            let loss_fn = match loss_type {
                "mse" => nn.getattr("MSELoss")?.call0()?,
                "cross_entropy" => nn.getattr("CrossEntropyLoss")?.call0()?,
                "bce" => nn.getattr("BCELoss")?.call0()?,
                _ => nn.getattr("MSELoss")?.call0()?, // Default to MSE
            };
            
            Ok(loss_fn.into())
        }).map_err(|e: PyErr| UmbraError::Runtime(format!("Failed to create loss function: {}", e)))
    }

    /// Perform backward pass
    pub fn backward(&self, loss_tensor: &UmbraTensor) -> UmbraResult<()> {
        Python::with_gil(|py| {
            loss_tensor.tensor.as_ref(py).call_method0("backward")?;
            Ok(())
        }).map_err(|e: PyErr| UmbraError::Runtime(format!("Backward pass failed: {}", e)))
    }

    /// Check if CUDA is available
    pub fn is_cuda_available(&self) -> bool {
        Python::with_gil(|py| {
            let torch = self.torch_module.as_ref(py);
            torch.getattr("cuda")
                .and_then(|cuda| cuda.call_method0("is_available"))
                .and_then(|result| result.extract::<bool>())
                .unwrap_or(false)
        })
    }

    /// Get PyTorch version
    pub fn version(&self) -> UmbraResult<String> {
        Python::with_gil(|py| {
            let version = self.torch_module.as_ref(py).getattr("__version__")?;
            Ok(version.extract()?)
        }).map_err(|e: PyErr| UmbraError::Runtime(format!("Failed to get PyTorch version: {}", e)))
    }

    /// Convert tensor to CPU
    pub fn to_cpu(&self, tensor: &UmbraTensor) -> UmbraResult<UmbraTensor> {
        Python::with_gil(|py| {
            let result = tensor.tensor.as_ref(py).call_method0("cpu")?;
            self.wrap_tensor(result)
        })
    }

    /// Convert tensor to CUDA
    pub fn to_cuda(&self, tensor: &UmbraTensor, device_id: Option<i32>) -> UmbraResult<UmbraTensor> {
        Python::with_gil(|py| {
            let result = if let Some(id) = device_id {
                tensor.tensor.as_ref(py).call_method1("cuda", (id,))?
            } else {
                tensor.tensor.as_ref(py).call_method0("cuda")?
            };
            self.wrap_tensor(result)
        })
    }

    /// Wrap a PyTorch tensor
    fn wrap_tensor(&self, tensor: &PyAny) -> UmbraResult<UmbraTensor> {
        let shape: Vec<usize> = tensor.getattr("shape")?
            .extract::<Vec<i64>>()?
            .into_iter()
            .map(|x| x as usize)
            .collect();
        
        let dtype: String = tensor.getattr("dtype")?
            .call_method0("__str__")?
            .extract()?;
        
        let device: String = tensor.getattr("device")?
            .call_method0("__str__")?
            .extract()?;
        
        let requires_grad: bool = tensor.getattr("requires_grad")?.extract()?;

        Ok(UmbraTensor {
            tensor: tensor.into(),
            shape,
            dtype,
            device,
            requires_grad,
        })
    }

    /// Count model parameters
    fn count_parameters(&self, model: &PyAny) -> UmbraResult<usize> {
        Python::with_gil(|py| {
            let parameters = model.call_method0("parameters")?;
            let mut count = 0usize;
            
            for param in parameters.iter()? {
                let param = param?;
                let numel: usize = param.call_method0("numel")?.extract()?;
                count += numel;
            }
            
            Ok(count)
        }).map_err(|e: PyErr| UmbraError::Runtime(format!("Failed to count parameters: {}", e)))
    }
}

impl UmbraTensor {
    /// Get tensor shape
    pub fn shape(&self) -> &[usize] {
        &self.shape
    }

    /// Get tensor data type
    pub fn dtype(&self) -> &str {
        &self.dtype
    }

    /// Get tensor device
    pub fn device(&self) -> &str {
        &self.device
    }

    /// Check if tensor requires gradient
    pub fn requires_grad(&self) -> bool {
        self.requires_grad
    }

    /// Get total number of elements
    pub fn numel(&self) -> usize {
        self.shape.iter().product()
    }
}

impl UmbraModel {
    /// Get model name
    pub fn name(&self) -> &str {
        &self.name
    }

    /// Get parameter count
    pub fn param_count(&self) -> usize {
        self.param_count
    }
}

impl Default for TrainingConfig {
    fn default() -> Self {
        Self {
            learning_rate: 0.001,
            batch_size: 32,
            epochs: 10,
            optimizer: "adam".to_string(),
            loss_function: "mse".to_string(),
            device: "cpu".to_string(),
        }
    }
}
