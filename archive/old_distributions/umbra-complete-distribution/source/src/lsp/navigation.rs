/// Navigation support for Umbra LSP
/// 
/// Provides go-to definition, find references, and symbol navigation
/// features for IDE integration.

use crate::parser::ast::Program;
use crate::semantic::symbol_table::SymbolTable;
use tower_lsp::lsp_types::*;

/// Navigation provider for Umbra language
pub struct NavigationProvider;

impl NavigationProvider {
    /// Create a new navigation provider
    pub fn new() -> Self {
        Self
    }
    
    /// Find definition of symbol at position
    pub fn find_definition(
        &self,
        content: &str,
        position: Position,
        symbols: Option<&SymbolTable>,
        ast: Option<&Program>,
    ) -> Option<Location> {
        let symbol_name = self.get_symbol_at_position(content, position)?;

        if let Some(symbols) = symbols {
            if let Some(symbol) = symbols.lookup(&symbol_name) {
                // Convert symbol location to LSP Location
                return Some(Location {
                    uri: Url::parse("file://current").ok()?, // In real implementation, use actual URI
                    range: Range {
                        start: Position {
                            line: symbol.location.line as u32,
                            character: symbol.location.column as u32,
                        },
                        end: Position {
                            line: symbol.location.line as u32,
                            character: (symbol.location.column + symbol_name.len()) as u32,
                        },
                    },
                });
            }
        }

        // If not found in symbol table, search AST
        if let Some(ast) = ast {
            return self.find_definition_in_ast(ast, &symbol_name);
        }

        None
    }
    
    /// Find all references to symbol at position
    pub fn find_references(
        &self,
        content: &str,
        position: Position,
        include_declaration: bool,
        symbols: Option<&SymbolTable>,
        ast: Option<&Program>,
    ) -> Vec<Location> {
        let symbol_name = match self.get_symbol_at_position(content, position) {
            Some(name) => name,
            None => return Vec::new(),
        };

        let mut references = Vec::new();

        // Include declaration if requested
        if include_declaration {
            if let Some(def_location) = self.find_definition(content, position, symbols, ast) {
                references.push(def_location);
            }
        }

        // Find all references in the content
        let lines: Vec<&str> = content.lines().collect();
        for (line_idx, line) in lines.iter().enumerate() {
            let mut char_idx = 0;
            while let Some(pos) = line[char_idx..].find(&symbol_name) {
                let actual_pos = char_idx + pos;

                // Check if this is a whole word match (not part of another identifier)
                let is_word_boundary = (actual_pos == 0 || !line.chars().nth(actual_pos - 1).unwrap_or(' ').is_alphanumeric()) &&
                    (actual_pos + symbol_name.len() >= line.len() ||
                     !line.chars().nth(actual_pos + symbol_name.len()).unwrap_or(' ').is_alphanumeric());

                if is_word_boundary {
                    references.push(Location {
                        uri: Url::parse("file://current").unwrap(), // In real implementation, use actual URI
                        range: Range {
                            start: Position {
                                line: line_idx as u32,
                                character: actual_pos as u32,
                            },
                            end: Position {
                                line: line_idx as u32,
                                character: (actual_pos + symbol_name.len()) as u32,
                            },
                        },
                    });
                }

                char_idx = actual_pos + 1;
            }
        }

        references
    }

    /// Get symbol name at the given position
    fn get_symbol_at_position(&self, content: &str, position: Position) -> Option<String> {
        let lines: Vec<&str> = content.lines().collect();
        let line_idx = position.line as usize;
        let char_idx = position.character as usize;

        if line_idx >= lines.len() {
            return None;
        }

        let line = lines[line_idx];
        if char_idx >= line.len() {
            return None;
        }

        // Find the start and end of the identifier at the cursor position
        let chars: Vec<char> = line.chars().collect();

        // Check if we're on an identifier character
        if !chars[char_idx].is_alphanumeric() && chars[char_idx] != '_' {
            return None;
        }

        // Find the start of the identifier
        let mut start = char_idx;
        while start > 0 && (chars[start - 1].is_alphanumeric() || chars[start - 1] == '_') {
            start -= 1;
        }

        // Find the end of the identifier
        let mut end = char_idx;
        while end < chars.len() && (chars[end].is_alphanumeric() || chars[end] == '_') {
            end += 1;
        }

        if start < end {
            Some(chars[start..end].iter().collect())
        } else {
            None
        }
    }

    /// Find definition in AST
    fn find_definition_in_ast(&self, ast: &Program, symbol_name: &str) -> Option<Location> {
        // This is a simplified implementation
        // In a real implementation, we would traverse the AST to find function/variable definitions

        for statement in &ast.statements {
            match statement {
                crate::parser::ast::Statement::Function(func) => {
                    if func.name == symbol_name {
                        return Some(Location {
                            uri: Url::parse("file://current").unwrap(),
                            range: Range {
                                start: Position { line: 0, character: 0 }, // Would use actual position from AST
                                end: Position { line: 0, character: symbol_name.len() as u32 },
                            },
                        });
                    }
                }
                crate::parser::ast::Statement::Variable(var) => {
                    if var.name == symbol_name {
                        return Some(Location {
                            uri: Url::parse("file://current").unwrap(),
                            range: Range {
                                start: Position { line: 0, character: 0 }, // Would use actual position from AST
                                end: Position { line: 0, character: symbol_name.len() as u32 },
                            },
                        });
                    }
                }
                _ => {}
            }
        }

        None
    }

    /// Get document symbols for outline view
    pub fn get_document_symbols(
        &self,
        content: &str,
        uri: &Url,
        symbols: Option<&SymbolTable>,
        ast: Option<&Program>,
    ) -> Vec<DocumentSymbol> {
        // In a real implementation, we would:
        // 1. Walk through the AST
        // 2. Extract all top-level symbols (functions, structs, etc.)
        // 3. Create hierarchical document symbols
        
        // For now, return empty vector as placeholder
        Vec::new()
    }
    
    /// Get workspace symbols for global search
    pub fn get_workspace_symbols(
        &self,
        query: &str,
        workspace_symbols: &[(Url, SymbolTable)],
    ) -> Vec<SymbolInformation> {
        let mut results = Vec::new();
        
        for (uri, symbol_table) in workspace_symbols {
            for (name, symbol) in symbol_table.symbols() {
                if name.to_lowercase().contains(&query.to_lowercase()) {
                    results.push(SymbolInformation {
                        name: name.clone(),
                        kind: self.symbol_to_symbol_kind(symbol),
                        tags: None,
                        deprecated: None,
                        location: Location::new(
                            uri.clone(),
                            Range::new(Position::new(0, 0), Position::new(0, 0)),
                        ),
                        container_name: None,
                    });
                }
            }
        }
        
        results
    }
    
    /// Convert Umbra symbol to LSP symbol kind
    fn symbol_to_symbol_kind(&self, symbol: &crate::semantic::symbol_table::Symbol) -> SymbolKind {
        use crate::semantic::symbol_table::SymbolType;
        
        match symbol.symbol_type() {
            SymbolType::Function => SymbolKind::FUNCTION,
            SymbolType::Variable => SymbolKind::VARIABLE,
            SymbolType::Structure => SymbolKind::STRUCT,
            SymbolType::Parameter => SymbolKind::VARIABLE,
            SymbolType::Trait => SymbolKind::INTERFACE,
            SymbolType::Implementation => SymbolKind::CLASS,
        }
    }
}

impl Default for NavigationProvider {
    fn default() -> Self {
        Self::new()
    }
}
