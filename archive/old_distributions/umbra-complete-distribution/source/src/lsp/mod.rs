/// Language Server Protocol implementation for Umbra
/// 
/// Provides IDE integration with syntax highlighting, auto-completion,
/// error diagnostics, and navigation features.

pub mod server;
pub mod completion;
pub mod diagnostics;
pub mod navigation;
pub mod highlighting;
pub mod formatting;
pub mod refactoring;
pub mod performance;
pub mod testing_integration;

use crate::error::UmbraResult;
use crate::lexer::Lexer;
use crate::parser::Parser;
use crate::semantic::analyzer::SemanticAnalyzer;
use std::collections::HashMap;
use std::path::PathBuf;
use tokio::sync::RwLock;
use tower_lsp::lsp_types::*;

/// Document state in the LSP server
#[derive(Debug, Clone)]
pub struct DocumentState {
    /// Document URI
    pub uri: Url,
    
    /// Document content
    pub content: String,
    
    /// Document version
    pub version: i32,
    
    /// Parsed AST (if available)
    pub ast: Option<crate::parser::ast::Program>,
    
    /// Semantic analysis results
    pub symbols: Option<crate::semantic::symbol_table::SymbolTable>,
    
    /// Current diagnostics
    pub diagnostics: Vec<Diagnostic>,
    
    /// Last modification time
    pub last_modified: std::time::SystemTime,
}

/// LSP server state
pub struct UmbraLanguageServer {
    /// Open documents
    pub documents: RwLock<HashMap<Url, DocumentState>>,

    /// Workspace root
    pub workspace_root: Option<PathBuf>,

    /// Client capabilities
    pub client_capabilities: Option<ClientCapabilities>,

    /// Server capabilities
    pub server_capabilities: ServerCapabilities,

    /// Performance optimizer
    pub performance_optimizer: RwLock<performance::PerformanceOptimizer>,

    /// Testing integration provider
    pub testing_provider: RwLock<testing_integration::TestIntegrationProvider>,
}

impl UmbraLanguageServer {
    /// Create a new language server instance
    pub fn new() -> Self {
        Self {
            documents: RwLock::new(HashMap::new()),
            workspace_root: None,
            client_capabilities: None,
            server_capabilities: Self::create_server_capabilities(),
            performance_optimizer: RwLock::new(performance::PerformanceOptimizer::new()),
            testing_provider: RwLock::new(testing_integration::TestIntegrationProvider::new()),
        }
    }
    
    /// Create server capabilities
    fn create_server_capabilities() -> ServerCapabilities {
        ServerCapabilities {
            text_document_sync: Some(TextDocumentSyncCapability::Kind(
                TextDocumentSyncKind::INCREMENTAL,
            )),
            completion_provider: Some(CompletionOptions {
                resolve_provider: Some(true),
                trigger_characters: Some(vec![".".to_string(), ":".to_string()]),
                all_commit_characters: None,
                work_done_progress_options: WorkDoneProgressOptions::default(),
                completion_item: None,
            }),
            hover_provider: Some(HoverProviderCapability::Simple(true)),
            definition_provider: Some(OneOf::Left(true)),
            references_provider: Some(OneOf::Left(true)),
            document_symbol_provider: Some(OneOf::Left(true)),
            workspace_symbol_provider: Some(OneOf::Left(true)),
            code_action_provider: Some(CodeActionProviderCapability::Simple(true)),
            document_formatting_provider: Some(OneOf::Left(true)),
            document_range_formatting_provider: Some(OneOf::Left(true)),
            rename_provider: Some(OneOf::Left(true)),
            semantic_tokens_provider: Some(
                SemanticTokensServerCapabilities::SemanticTokensRegistrationOptions(
                    SemanticTokensRegistrationOptions {
                        text_document_registration_options: {
                            TextDocumentRegistrationOptions {
                                document_selector: Some(vec![DocumentFilter {
                                    language: Some("umbra".to_string()),
                                    scheme: Some("file".to_string()),
                                    pattern: Some("**/*.umbra".to_string()),
                                }]),
                            }
                        },
                        semantic_tokens_options: SemanticTokensOptions {
                            work_done_progress_options: WorkDoneProgressOptions::default(),
                            legend: SemanticTokensLegend {
                                token_types: vec![
                                    SemanticTokenType::KEYWORD,
                                    SemanticTokenType::STRING,
                                    SemanticTokenType::NUMBER,
                                    SemanticTokenType::COMMENT,
                                    SemanticTokenType::FUNCTION,
                                    SemanticTokenType::VARIABLE,
                                    SemanticTokenType::TYPE,
                                    SemanticTokenType::OPERATOR,
                                ],
                                token_modifiers: vec![
                                    SemanticTokenModifier::DECLARATION,
                                    SemanticTokenModifier::DEFINITION,
                                    SemanticTokenModifier::READONLY,
                                ],
                            },
                            range: Some(true),
                            full: Some(SemanticTokensFullOptions::Bool(true)),
                        },
                        static_registration_options: StaticRegistrationOptions::default(),
                    },
                ),
            ),
            ..ServerCapabilities::default()
        }
    }
    
    /// Parse and analyze a document
    pub async fn analyze_document(&self, uri: &Url, content: &str) -> UmbraResult<DocumentState> {
        let mut diagnostics = Vec::new();

        // Try to get cached results from performance optimizer
        let (ast, symbols) = {
            let mut optimizer = self.performance_optimizer.write().await;
            match optimizer.get_or_parse_document(uri, content) {
                Ok((cached_ast, cached_symbols)) => (cached_ast, cached_symbols),
                Err(_) => {
                    // Fallback to manual parsing if optimizer fails
                    self.manual_parse_document(content, &mut diagnostics).await
                }
            }
        };
        
        Ok(DocumentState {
            uri: uri.clone(),
            content: content.to_string(),
            version: 0,
            ast,
            symbols,
            diagnostics,
            last_modified: std::time::SystemTime::now(),
        })
    }

    /// Manual document parsing (fallback when optimizer fails)
    async fn manual_parse_document(
        &self,
        content: &str,
        diagnostics: &mut Vec<Diagnostic>,
    ) -> (Option<crate::parser::ast::Program>, Option<crate::semantic::symbol_table::SymbolTable>) {
        let mut ast = None;
        let mut symbols = None;

        // Lexical analysis
        let mut lexer = Lexer::new(content.to_string());
        let tokens = match lexer.tokenize() {
            Ok(tokens) => tokens,
            Err(error) => {
                diagnostics.push(self.error_to_diagnostic(&error));
                return (ast, symbols);
            }
        };

        // Parsing
        let mut parser = Parser::new(tokens);
        let program = match parser.parse() {
            Ok(program) => {
                ast = Some(program.clone());
                program
            }
            Err(error) => {
                diagnostics.push(self.error_to_diagnostic(&error));
                return (ast, symbols);
            }
        };

        // Semantic analysis
        let mut analyzer = SemanticAnalyzer::new();
        match analyzer.analyze(&program) {
            Ok(_) => {
                symbols = Some(analyzer.symbol_table().clone());
            }
            Err(error) => {
                diagnostics.push(self.error_to_diagnostic(&error));
            }
        }

        (ast, symbols)
    }
    
    /// Convert UmbraError to LSP Diagnostic
    fn error_to_diagnostic(&self, error: &crate::error::UmbraError) -> Diagnostic {
        use crate::error::UmbraError;
        
        let (message, range) = match error {
            UmbraError::Lexical { message, line, column } => {
                let pos = Position::new((*line as u32).saturating_sub(1), *column as u32);
                (message.clone(), Range::new(pos, pos))
            }
            UmbraError::Parse { message, line, column } => {
                let pos = Position::new((*line as u32).saturating_sub(1), *column as u32);
                (message.clone(), Range::new(pos, pos))
            }
            UmbraError::Semantic { message, line, column } => {
                let pos = Position::new((*line as u32).saturating_sub(1), *column as u32);
                (message.clone(), Range::new(pos, pos))
            }
            _ => {
                let pos = Position::new(0, 0);
                (error.to_string(), Range::new(pos, pos))
            }
        };
        
        Diagnostic::new(
            range,
            Some(DiagnosticSeverity::ERROR),
            None,
            Some("umbra".to_string()),
            message,
            None,
            None,
        )
    }
    
    /// Get document by URI
    pub async fn get_document(&self, uri: &Url) -> Option<DocumentState> {
        let documents = self.documents.read().await;
        documents.get(uri).cloned()
    }
    
    /// Update document content
    pub async fn update_document(&self, uri: Url, content: String, version: i32) -> UmbraResult<()> {
        let document_state = self.analyze_document(&uri, &content).await?;
        let mut updated_state = document_state;
        updated_state.version = version;
        
        let mut documents = self.documents.write().await;
        documents.insert(uri, updated_state);
        
        Ok(())
    }
    
    /// Remove document
    pub async fn remove_document(&self, uri: &Url) {
        let mut documents = self.documents.write().await;
        documents.remove(uri);
    }
    
    /// Get all diagnostics for a document
    pub async fn get_diagnostics(&self, uri: &Url) -> Vec<Diagnostic> {
        if let Some(document) = self.get_document(uri).await {
            document.diagnostics
        } else {
            Vec::new()
        }
    }
    
    /// Get workspace symbols
    pub async fn get_workspace_symbols(&self, query: &str) -> Vec<SymbolInformation> {
        let mut symbols = Vec::new();
        let documents = self.documents.read().await;
        
        for (uri, document) in documents.iter() {
            if let Some(symbol_table) = &document.symbols {
                // Convert symbol table to LSP symbols
                for (name, symbol) in symbol_table.symbols() {
                    if name.contains(query) {
                        let symbol_info = SymbolInformation {
                            name: name.clone(),
                            kind: self.symbol_to_symbol_kind(symbol),
                            tags: None,
                            deprecated: None,
                            location: Location::new(
                                uri.clone(),
                                Range::new(Position::new(0, 0), Position::new(0, 0)),
                            ),
                            container_name: None,
                        };
                        symbols.push(symbol_info);
                    }
                }
            }
        }
        
        symbols
    }
    
    /// Convert Umbra symbol to LSP symbol kind
    fn symbol_to_symbol_kind(&self, symbol: &crate::semantic::symbol_table::Symbol) -> SymbolKind {
        use crate::semantic::symbol_table::SymbolType;

        match symbol.symbol_type() {
            SymbolType::Function => SymbolKind::FUNCTION,
            SymbolType::Variable => SymbolKind::VARIABLE,
            SymbolType::Structure => SymbolKind::STRUCT,
            SymbolType::Parameter => SymbolKind::VARIABLE,
            SymbolType::Trait => SymbolKind::INTERFACE,
            SymbolType::Implementation => SymbolKind::CLASS,
        }
    }

    /// Get performance metrics
    pub async fn get_performance_metrics(&self) -> performance::PerformanceMetrics {
        let optimizer = self.performance_optimizer.read().await;
        optimizer.get_metrics().clone()
    }

    /// Update performance optimization settings
    pub async fn update_performance_settings(&self, settings: performance::OptimizationSettings) {
        let mut optimizer = self.performance_optimizer.write().await;
        optimizer.update_settings(settings);
    }

    /// Get cache statistics
    pub async fn get_cache_stats(&self) -> performance::CacheStats {
        let optimizer = self.performance_optimizer.read().await;
        optimizer.get_cache_stats()
    }

    /// Clear performance caches
    pub async fn clear_performance_caches(&self) {
        let mut optimizer = self.performance_optimizer.write().await;
        optimizer.clear_caches();
    }

    /// Toggle specific optimization
    pub async fn toggle_optimization(&self, optimization: performance::OptimizationType, enabled: bool) {
        let mut optimizer = self.performance_optimizer.write().await;
        optimizer.toggle_optimization(optimization, enabled);
    }

    /// Invalidate document cache
    pub async fn invalidate_document_cache(&self, uri: &Url) {
        let mut optimizer = self.performance_optimizer.write().await;
        optimizer.invalidate_document(uri);
    }

    /// Discover tests in workspace
    pub async fn discover_tests(&self, workspace_path: &std::path::Path) -> crate::error::UmbraResult<testing_integration::TestDiscoveryResult> {
        let provider = self.testing_provider.read().await;
        provider.discover_tests(workspace_path).await
    }

    /// Execute a specific test
    pub async fn execute_test(&self, request: testing_integration::TestExecutionRequest) -> crate::error::UmbraResult<testing_integration::TestExecutionResult> {
        let provider = self.testing_provider.read().await;
        provider.execute_test(request).await
    }

    /// Execute all tests in workspace
    pub async fn execute_all_tests(&self, workspace_path: &std::path::Path) -> crate::error::UmbraResult<Vec<testing_integration::TestExecutionResult>> {
        let provider = self.testing_provider.read().await;
        provider.execute_all_tests(workspace_path).await
    }

    /// Get test results
    pub async fn get_test_results(&self, test_id: &str) -> Option<crate::testing::TestResult> {
        let provider = self.testing_provider.read().await;
        provider.get_test_results(test_id).await
    }

    /// Generate test report
    pub async fn generate_test_report(&self, workspace_path: &std::path::Path, format: testing_integration::TestOutputFormat) -> crate::error::UmbraResult<String> {
        let provider = self.testing_provider.read().await;
        provider.generate_test_report(workspace_path, format).await
    }

    /// Update testing configuration
    pub async fn update_testing_config(&self, config: testing_integration::TestIntegrationConfig) {
        let mut provider = self.testing_provider.write().await;
        provider.update_config(config).await;
    }

    /// Clear test caches
    pub async fn clear_test_caches(&self) {
        let provider = self.testing_provider.read().await;
        provider.clear_caches().await;
    }
}

impl Default for UmbraLanguageServer {
    fn default() -> Self {
        Self::new()
    }
}
