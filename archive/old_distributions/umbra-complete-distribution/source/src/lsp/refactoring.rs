use tower_lsp::lsp_types::{
    CodeAction, CodeActionKind, CodeActionParams, Position, Range, TextEdit, WorkspaceEdit,
    Url,
};
use crate::parser::ast::Program;
use crate::semantic::symbol_table::SymbolTable;
use crate::error::UmbraResult;
use std::collections::HashMap;

/// Code refactoring provider for Umbra language
pub struct RefactoringProvider {
    /// Available refactoring actions
    actions: HashMap<String, RefactoringAction>,
}

/// Types of refactoring actions
#[derive(Debug, Clone)]
pub enum RefactoringAction {
    /// Extract method from selected code
    ExtractMethod,
    /// Rename symbol
    RenameSymbol,
    /// Inline variable
    InlineVariable,
    /// Extract variable
    ExtractVariable,
    /// Move function to different module
    MoveFunction,
    /// Convert to AI/ML optimized code
    OptimizeForAiMl,
    /// Add error handling
    AddErrorHandling,
    /// Simplify expression
    SimplifyExpression,
}

impl RefactoringProvider {
    /// Create a new refactoring provider
    pub fn new() -> Self {
        let mut actions = HashMap::new();
        
        actions.insert("extract_method".to_string(), RefactoringAction::ExtractMethod);
        actions.insert("rename_symbol".to_string(), RefactoringAction::RenameSymbol);
        actions.insert("inline_variable".to_string(), RefactoringAction::InlineVariable);
        actions.insert("extract_variable".to_string(), RefactoringAction::ExtractVariable);
        actions.insert("move_function".to_string(), RefactoringAction::MoveFunction);
        actions.insert("optimize_ai_ml".to_string(), RefactoringAction::OptimizeForAiMl);
        actions.insert("add_error_handling".to_string(), RefactoringAction::AddErrorHandling);
        actions.insert("simplify_expression".to_string(), RefactoringAction::SimplifyExpression);
        
        Self { actions }
    }

    /// Get available code actions for a range
    pub fn get_code_actions(
        &self,
        params: CodeActionParams,
        content: &str,
        ast: Option<&Program>,
        symbols: Option<&SymbolTable>,
    ) -> UmbraResult<Vec<CodeAction>> {
        let mut code_actions = Vec::new();
        let range = params.range;
        let uri = params.text_document.uri;
        
        // Analyze the selected range to determine applicable refactorings
        let selected_text = self.extract_range_text(content, range);
        
        // Extract method refactoring
        if self.can_extract_method(&selected_text, ast) {
            code_actions.push(self.create_extract_method_action(&uri, range, &selected_text)?);
        }
        
        // Extract variable refactoring
        if self.can_extract_variable(&selected_text, ast) {
            code_actions.push(self.create_extract_variable_action(&uri, range, &selected_text)?);
        }
        
        // Rename symbol refactoring
        if let Some(symbol_name) = self.get_symbol_at_position(content, range.start, symbols) {
            code_actions.push(self.create_rename_symbol_action(&uri, range, &symbol_name)?);
        }
        
        // AI/ML optimization
        if self.can_optimize_for_ai_ml(&selected_text, ast) {
            code_actions.push(self.create_ai_ml_optimization_action(&uri, range, &selected_text)?);
        }
        
        // Add error handling
        if self.needs_error_handling(&selected_text, ast) {
            code_actions.push(self.create_add_error_handling_action(&uri, range, &selected_text)?);
        }
        
        // Simplify expression
        if self.can_simplify_expression(&selected_text, ast) {
            code_actions.push(self.create_simplify_expression_action(&uri, range, &selected_text)?);
        }

        // Umbra-specific refactorings

        // Convert print to show (Umbra-specific)
        if selected_text.contains("print(") {
            code_actions.push(self.create_convert_print_to_show_action(&uri, range, &selected_text)?);
        }

        // Convert import to bring (Umbra-specific)
        if selected_text.contains("import ") {
            code_actions.push(self.create_convert_import_to_bring_action(&uri, range, &selected_text)?);
        }

        // Convert assignment operator (Umbra-specific)
        if selected_text.contains(" = ") && !selected_text.contains(" := ") {
            code_actions.push(self.create_convert_assignment_action(&uri, range, &selected_text)?);
        }

        Ok(code_actions)
    }

    /// Extract text from a range
    fn extract_range_text(&self, content: &str, range: Range) -> String {
        let lines: Vec<&str> = content.lines().collect();
        let start_line = range.start.line as usize;
        let end_line = range.end.line as usize;
        
        if start_line >= lines.len() || end_line >= lines.len() {
            return String::new();
        }
        
        if start_line == end_line {
            // Single line selection
            let line = lines[start_line];
            let start_char = range.start.character as usize;
            let end_char = range.end.character as usize;
            
            if start_char < line.len() && end_char <= line.len() {
                line[start_char..end_char].to_string()
            } else {
                String::new()
            }
        } else {
            // Multi-line selection
            let mut result = String::new();
            
            // First line
            let first_line = lines[start_line];
            let start_char = range.start.character as usize;
            if start_char < first_line.len() {
                result.push_str(&first_line[start_char..]);
            }
            
            // Middle lines
            for i in (start_line + 1)..end_line {
                result.push('\n');
                result.push_str(lines[i]);
            }
            
            // Last line
            if end_line > start_line {
                result.push('\n');
                let last_line = lines[end_line];
                let end_char = range.end.character as usize;
                if end_char <= last_line.len() {
                    result.push_str(&last_line[..end_char]);
                }
            }
            
            result
        }
    }

    /// Check if selected text can be extracted as a method
    fn can_extract_method(&self, selected_text: &str, _ast: Option<&Program>) -> bool {
        // Basic heuristics for method extraction
        selected_text.lines().count() > 1 && 
        selected_text.contains("let ") &&
        !selected_text.trim().starts_with("//")
    }

    /// Check if selected text can be extracted as a variable
    fn can_extract_variable(&self, selected_text: &str, _ast: Option<&Program>) -> bool {
        // Check if it's an expression that can be extracted
        let trimmed = selected_text.trim();
        !trimmed.is_empty() && 
        !trimmed.contains('\n') &&
        !trimmed.starts_with("let ") &&
        !trimmed.starts_with("//")
    }

    /// Get symbol name at position
    fn get_symbol_at_position(&self, content: &str, position: Position, symbols: Option<&SymbolTable>) -> Option<String> {
        let lines: Vec<&str> = content.lines().collect();
        let line_idx = position.line as usize;
        let char_idx = position.character as usize;
        
        if line_idx >= lines.len() {
            return None;
        }
        
        let line = lines[line_idx];
        if char_idx >= line.len() {
            return None;
        }
        
        // Find word boundaries around the position
        let chars: Vec<char> = line.chars().collect();
        let mut start = char_idx;
        let mut end = char_idx;
        
        // Find start of identifier
        while start > 0 && (chars[start - 1].is_alphanumeric() || chars[start - 1] == '_') {
            start -= 1;
        }
        
        // Find end of identifier
        while end < chars.len() && (chars[end].is_alphanumeric() || chars[end] == '_') {
            end += 1;
        }
        
        if start < end {
            let symbol_name: String = chars[start..end].iter().collect();
            
            // Check if it's a valid symbol in the symbol table
            if let Some(symbols) = symbols {
                if symbols.lookup(&symbol_name).is_some() {
                    return Some(symbol_name);
                }
            }
            
            // Return the identifier even if not in symbol table (might be a keyword or new symbol)
            if !symbol_name.is_empty() && symbol_name.chars().next().unwrap().is_alphabetic() {
                Some(symbol_name)
            } else {
                None
            }
        } else {
            None
        }
    }

    /// Check if code can be optimized for AI/ML
    fn can_optimize_for_ai_ml(&self, selected_text: &str, _ast: Option<&Program>) -> bool {
        selected_text.contains("repeat ") || 
        selected_text.contains("matrix") ||
        selected_text.contains("array") ||
        selected_text.contains("calculate")
    }

    /// Check if code needs error handling
    fn needs_error_handling(&self, selected_text: &str, _ast: Option<&Program>) -> bool {
        (selected_text.contains("file") || 
         selected_text.contains("network") ||
         selected_text.contains("parse") ||
         selected_text.contains("convert")) &&
        !selected_text.contains("try") &&
        !selected_text.contains("catch")
    }

    /// Check if expression can be simplified
    fn can_simplify_expression(&self, selected_text: &str, _ast: Option<&Program>) -> bool {
        selected_text.contains("+ 0") ||
        selected_text.contains("* 1") ||
        selected_text.contains("- 0") ||
        selected_text.contains("/ 1") ||
        selected_text.contains("== true") ||
        selected_text.contains("== false")
    }

    /// Create extract method code action
    fn create_extract_method_action(&self, uri: &Url, range: Range, selected_text: &str) -> UmbraResult<CodeAction> {
        let method_name = "extracted_method";
        let new_method = format!(
            "\ndefine {}() {{\n{}\n}}\n",
            method_name,
            selected_text.lines()
                .map(|line| format!("    {}", line))
                .collect::<Vec<_>>()
                .join("\n")
        );
        
        let call_replacement = format!("{}()", method_name);
        
        let mut changes = HashMap::new();
        changes.insert(uri.clone(), vec![
            // Replace selected text with method call
            TextEdit {
                range,
                new_text: call_replacement,
            },
            // Add method definition at the end
            TextEdit {
                range: Range::new(
                    Position::new(u32::MAX, 0),
                    Position::new(u32::MAX, 0),
                ),
                new_text: new_method,
            },
        ]);
        
        Ok(CodeAction {
            title: "Extract Method".to_string(),
            kind: Some(CodeActionKind::REFACTOR_EXTRACT),
            diagnostics: None,
            edit: Some(WorkspaceEdit {
                changes: Some(changes),
                document_changes: None,
                change_annotations: None,
            }),
            command: None,
            is_preferred: Some(false),
            disabled: None,
            data: None,
        })
    }

    /// Create extract variable code action
    fn create_extract_variable_action(&self, uri: &Url, range: Range, selected_text: &str) -> UmbraResult<CodeAction> {
        let variable_name = "extracted_var";
        let variable_declaration = format!("let {} = {};\n", variable_name, selected_text.trim());
        
        let mut changes = HashMap::new();
        changes.insert(uri.clone(), vec![
            // Add variable declaration before the line
            TextEdit {
                range: Range::new(
                    Position::new(range.start.line, 0),
                    Position::new(range.start.line, 0),
                ),
                new_text: variable_declaration,
            },
            // Replace selected text with variable name
            TextEdit {
                range,
                new_text: variable_name.to_string(),
            },
        ]);
        
        Ok(CodeAction {
            title: "Extract Variable".to_string(),
            kind: Some(CodeActionKind::REFACTOR_EXTRACT),
            diagnostics: None,
            edit: Some(WorkspaceEdit {
                changes: Some(changes),
                document_changes: None,
                change_annotations: None,
            }),
            command: None,
            is_preferred: Some(false),
            disabled: None,
            data: None,
        })
    }

    /// Create rename symbol code action
    fn create_rename_symbol_action(&self, uri: &Url, range: Range, symbol_name: &str) -> UmbraResult<CodeAction> {
        let new_name = format!("{}_renamed", symbol_name);

        let mut changes = HashMap::new();
        changes.insert(uri.clone(), vec![
            TextEdit {
                range,
                new_text: new_name,
            },
        ]);

        Ok(CodeAction {
            title: format!("Rename '{}' to '{}_renamed'", symbol_name, symbol_name),
            kind: Some(CodeActionKind::REFACTOR),
            diagnostics: None,
            edit: Some(WorkspaceEdit {
                changes: Some(changes),
                document_changes: None,
                change_annotations: None,
            }),
            command: None,
            is_preferred: Some(false),
            disabled: None,
            data: None,
        })
    }

    /// Create AI/ML optimization code action
    fn create_ai_ml_optimization_action(&self, uri: &Url, range: Range, selected_text: &str) -> UmbraResult<CodeAction> {
        let optimized_code = self.optimize_for_ai_ml(selected_text);

        let mut changes = HashMap::new();
        changes.insert(uri.clone(), vec![
            TextEdit {
                range,
                new_text: optimized_code,
            },
        ]);

        Ok(CodeAction {
            title: "Optimize for AI/ML Performance".to_string(),
            kind: Some(CodeActionKind::REFACTOR_REWRITE),
            diagnostics: None,
            edit: Some(WorkspaceEdit {
                changes: Some(changes),
                document_changes: None,
                change_annotations: None,
            }),
            command: None,
            is_preferred: Some(false),
            disabled: None,
            data: None,
        })
    }

    /// Create add error handling code action
    fn create_add_error_handling_action(&self, uri: &Url, range: Range, selected_text: &str) -> UmbraResult<CodeAction> {
        let wrapped_code = format!(
            "try {{\n{}\n}} catch (error) {{\n    // Handle error\n    print(\"Error: \" + error)\n}}",
            selected_text.lines()
                .map(|line| format!("    {}", line))
                .collect::<Vec<_>>()
                .join("\n")
        );

        let mut changes = HashMap::new();
        changes.insert(uri.clone(), vec![
            TextEdit {
                range,
                new_text: wrapped_code,
            },
        ]);

        Ok(CodeAction {
            title: "Add Error Handling".to_string(),
            kind: Some(CodeActionKind::REFACTOR_REWRITE),
            diagnostics: None,
            edit: Some(WorkspaceEdit {
                changes: Some(changes),
                document_changes: None,
                change_annotations: None,
            }),
            command: None,
            is_preferred: Some(false),
            disabled: None,
            data: None,
        })
    }

    /// Create simplify expression code action
    fn create_simplify_expression_action(&self, uri: &Url, range: Range, selected_text: &str) -> UmbraResult<CodeAction> {
        let simplified = self.simplify_expression(selected_text);

        let mut changes = HashMap::new();
        changes.insert(uri.clone(), vec![
            TextEdit {
                range,
                new_text: simplified,
            },
        ]);

        Ok(CodeAction {
            title: "Simplify Expression".to_string(),
            kind: Some(CodeActionKind::REFACTOR_REWRITE),
            diagnostics: None,
            edit: Some(WorkspaceEdit {
                changes: Some(changes),
                document_changes: None,
                change_annotations: None,
            }),
            command: None,
            is_preferred: Some(false),
            disabled: None,
            data: None,
        })
    }

    /// Optimize code for AI/ML performance
    fn optimize_for_ai_ml(&self, code: &str) -> String {
        let mut optimized = code.to_string();

        // Replace simple loops with vectorized operations
        if optimized.contains("repeat ") {
            optimized = optimized.replace(
                "repeat ",
                "// Vectorized operation\nrepeat "
            );
        }

        // Add parallel processing hints
        if optimized.contains("matrix") || optimized.contains("array") {
            optimized = format!("// Consider using parallel processing\n{}", optimized);
        }

        optimized
    }

    /// Simplify mathematical expressions
    fn simplify_expression(&self, expr: &str) -> String {
        let mut simplified = expr.to_string();

        // Remove redundant operations
        simplified = simplified.replace(" + 0", "");
        simplified = simplified.replace(" - 0", "");
        simplified = simplified.replace(" * 1", "");
        simplified = simplified.replace(" / 1", "");
        simplified = simplified.replace(" == true", "");
        simplified = simplified.replace(" == false", " == false"); // Keep this for clarity

        // Remove extra spaces
        while simplified.contains("  ") {
            simplified = simplified.replace("  ", " ");
        }

        simplified.trim().to_string()
    }

    /// Create action to convert print() to show() (Umbra-specific)
    fn create_convert_print_to_show_action(
        &self,
        uri: &Url,
        range: Range,
        selected_text: &str,
    ) -> UmbraResult<CodeAction> {
        let new_text = selected_text.replace("print(", "show(");

        let mut changes = HashMap::new();
        changes.insert(uri.clone(), vec![TextEdit {
            range,
            new_text,
        }]);

        Ok(CodeAction {
            title: "Convert print() to show() (Umbra syntax)".to_string(),
            kind: Some(CodeActionKind::REFACTOR),
            edit: Some(WorkspaceEdit {
                changes: Some(changes),
                ..WorkspaceEdit::default()
            }),
            ..CodeAction::default()
        })
    }

    /// Create action to convert import to bring (Umbra-specific)
    fn create_convert_import_to_bring_action(
        &self,
        uri: &Url,
        range: Range,
        selected_text: &str,
    ) -> UmbraResult<CodeAction> {
        let new_text = selected_text.replace("import ", "bring ");

        let mut changes = HashMap::new();
        changes.insert(uri.clone(), vec![TextEdit {
            range,
            new_text,
        }]);

        Ok(CodeAction {
            title: "Convert import to bring (Umbra syntax)".to_string(),
            kind: Some(CodeActionKind::REFACTOR),
            edit: Some(WorkspaceEdit {
                changes: Some(changes),
                ..WorkspaceEdit::default()
            }),
            ..CodeAction::default()
        })
    }

    /// Create action to convert assignment operator (Umbra-specific)
    fn create_convert_assignment_action(
        &self,
        uri: &Url,
        range: Range,
        selected_text: &str,
    ) -> UmbraResult<CodeAction> {
        let new_text = selected_text.replace(" = ", " := ");

        let mut changes = HashMap::new();
        changes.insert(uri.clone(), vec![TextEdit {
            range,
            new_text,
        }]);

        Ok(CodeAction {
            title: "Convert = to := (Umbra assignment operator)".to_string(),
            kind: Some(CodeActionKind::REFACTOR),
            edit: Some(WorkspaceEdit {
                changes: Some(changes),
                ..WorkspaceEdit::default()
            }),
            ..CodeAction::default()
        })
    }
}

impl Default for RefactoringProvider {
    fn default() -> Self {
        Self::new()
    }
}
