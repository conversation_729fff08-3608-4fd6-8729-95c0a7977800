/// Syntax highlighting support for Umbra LSP
/// 
/// Provides semantic token generation for advanced syntax highlighting
/// in IDEs that support the Language Server Protocol.

use crate::lexer::{Lex<PERSON>, Token, TokenType};
use crate::parser::ast::Program;
use tower_lsp::lsp_types::*;

/// Syntax highlighting provider for Umbra language
pub struct HighlightingProvider {
    /// Token legend for semantic highlighting
    legend: SemanticTokensLegend,
}

impl HighlightingProvider {
    /// Create a new highlighting provider
    pub fn new() -> Self {
        Self {
            legend: Self::create_token_legend(),
        }
    }
    
    /// Create the semantic tokens legend
    fn create_token_legend() -> SemanticTokensLegend {
        SemanticTokensLegend {
            token_types: vec![
                SemanticTokenType::KEYWORD,
                SemanticTokenType::STRING,
                SemanticTokenType::NUMBER,
                SemanticTokenType::COMMENT,
                SemanticTokenType::FUNCTION,
                SemanticTokenType::VARIABLE,
                SemanticTokenType::TYPE,
                SemanticTokenType::OPERATOR,
                SemanticTokenType::PARAMETER,
                SemanticTokenType::PROPERTY,
                SemanticTokenType::ENUM_MEMBER,
                SemanticTokenType::STRUCT,
                SemanticTokenType::CLASS,
                SemanticTokenType::INTERFACE,
                SemanticTokenType::ENUM,
                SemanticTokenType::TYPE_PARAMETER,
                SemanticTokenType::DECORATOR,
                SemanticTokenType::EVENT,
                SemanticTokenType::MACRO,
                SemanticTokenType::NAMESPACE,
                SemanticTokenType::REGEXP,
            ],
            token_modifiers: vec![
                SemanticTokenModifier::DECLARATION,
                SemanticTokenModifier::DEFINITION,
                SemanticTokenModifier::READONLY,
                SemanticTokenModifier::STATIC,
                SemanticTokenModifier::DEPRECATED,
                SemanticTokenModifier::ABSTRACT,
                SemanticTokenModifier::ASYNC,
                SemanticTokenModifier::MODIFICATION,
                SemanticTokenModifier::DOCUMENTATION,
                SemanticTokenModifier::DEFAULT_LIBRARY,
            ],
        }
    }
    
    /// Get semantic tokens for a document
    pub fn get_semantic_tokens(
        &self,
        content: &str,
        ast: Option<&Program>,
    ) -> Vec<SemanticToken> {
        let mut tokens = Vec::new();
        
        // Tokenize the content
        let mut lexer = Lexer::new(content.to_string());
        if let Ok(lexer_tokens) = lexer.tokenize() {
            tokens.extend(self.convert_lexer_tokens_to_semantic(&lexer_tokens, content));
        }
        
        // Add semantic information from AST if available
        if let Some(program) = ast {
            tokens.extend(self.extract_semantic_tokens_from_ast(program, content));
        }
        
        // Sort tokens by position
        tokens.sort_by(|a, b| {
            a.delta_line.cmp(&b.delta_line)
                .then(a.delta_start.cmp(&b.delta_start))
        });
        
        // Convert to delta encoding
        self.encode_semantic_tokens(tokens)
    }
    
    /// Convert lexer tokens to semantic tokens
    fn convert_lexer_tokens_to_semantic(
        &self,
        tokens: &[Token],
        content: &str,
    ) -> Vec<SemanticToken> {
        let mut semantic_tokens = Vec::new();
        let lines: Vec<&str> = content.lines().collect();
        
        for token in tokens {
            let token_type_index = self.get_token_type_index(&token.token_type);
            let modifiers = self.get_token_modifiers(&token.token_type);
            
            if let Some(type_index) = token_type_index {
                // Calculate position (simplified - in real implementation would use token positions)
                let line = 0; // Would need to track actual line numbers
                let start = 0; // Would need to track actual column positions
                let length = token.lexeme.len() as u32;
                
                semantic_tokens.push(SemanticToken {
                    delta_line: line,
                    delta_start: start,
                    length,
                    token_type: type_index,
                    token_modifiers_bitset: modifiers,
                });
            }
        }
        
        semantic_tokens
    }
    
    /// Extract semantic tokens from AST
    fn extract_semantic_tokens_from_ast(
        &self,
        _program: &Program,
        _content: &str,
    ) -> Vec<SemanticToken> {
        let semantic_tokens = Vec::new();

        // TODO: Implement comprehensive AST-based semantic highlighting
        // This would include:
        // 1. Function definitions with FUNCTION token type and DECLARATION modifier
        // 2. Variable declarations with VARIABLE token type and DECLARATION modifier
        // 3. Function calls with FUNCTION token type
        // 4. Type annotations with TYPE token type
        // 5. Parameters with PARAMETER token type
        // 6. Struct fields with PROPERTY token type
        // 7. AI/ML specific constructs with special highlighting

        // Example of what we would add:
        // - Function definitions: token_type = 4 (FUNCTION), modifiers = 1 (DECLARATION)
        // - Variable declarations: token_type = 5 (VARIABLE), modifiers = 1 (DECLARATION)
        // - Function calls: token_type = 4 (FUNCTION), modifiers = 0
        // - AI/ML operations: token_type = 4 (FUNCTION), modifiers = 64 (DEFAULT_LIBRARY)

        semantic_tokens
    }
    
    /// Get token type index for a lexer token type
    fn get_token_type_index(&self, token_type: &TokenType) -> Option<u32> {
        match token_type {
            // Keywords
            TokenType::Define | TokenType::Let | TokenType::When | TokenType::Otherwise |
            TokenType::Repeat | TokenType::In | TokenType::Return |
            TokenType::Import | TokenType::Export | TokenType::Module | TokenType::Structure => Some(0), // KEYWORD

            // AI/ML specific keywords
            TokenType::Train | TokenType::Evaluate => Some(0), // KEYWORD (with special highlighting)

            // Literals
            TokenType::String(_) => Some(1), // STRING
            TokenType::Integer(_) | TokenType::Float(_) => Some(2), // NUMBER
            TokenType::Comment => Some(3), // COMMENT

            // Functions and identifiers
            TokenType::Identifier => Some(5), // VARIABLE (default for identifiers)

            // Operators
            TokenType::Plus | TokenType::Minus | TokenType::Multiply | TokenType::Divide |
            TokenType::Modulo | TokenType::Equal | TokenType::NotEqual | TokenType::Less |
            TokenType::Greater | TokenType::LessEqual | TokenType::GreaterEqual |
            TokenType::And | TokenType::Or | TokenType::Not | TokenType::Assign => Some(7), // OPERATOR

            // Delimiters and punctuation
            TokenType::LeftParen | TokenType::RightParen | TokenType::LeftBrace | TokenType::RightBrace |
            TokenType::LeftBracket | TokenType::RightBracket | TokenType::Comma | TokenType::Semicolon |
            TokenType::Dot | TokenType::Colon => None, // No special highlighting for delimiters

            // Special tokens
            TokenType::Eof => None,

            _ => None,
        }
    }
    
    /// Get token modifiers for a lexer token type
    fn get_token_modifiers(&self, token_type: &TokenType) -> u32 {
        match token_type {
            // Declaration modifiers
            TokenType::Define => 1, // DECLARATION
            TokenType::Let => 1, // DECLARATION
            TokenType::Structure => 1, // DECLARATION
            TokenType::Module => 1, // DECLARATION

            // AI/ML specific modifiers
            TokenType::Train | TokenType::Evaluate => 64, // DEFAULT_LIBRARY (special highlighting for AI/ML)

            // Import/Export modifiers
            TokenType::Import | TokenType::Export => 2, // DEFINITION

            _ => 0,
        }
    }
    
    /// Encode semantic tokens using delta encoding
    fn encode_semantic_tokens(&self, mut tokens: Vec<SemanticToken>) -> Vec<SemanticToken> {
        if tokens.is_empty() {
            return tokens;
        }
        
        // Sort by line and column
        tokens.sort_by(|a, b| {
            a.delta_line.cmp(&b.delta_line)
                .then(a.delta_start.cmp(&b.delta_start))
        });
        
        // Convert to delta encoding
        let mut prev_line = 0;
        let mut prev_start = 0;
        
        for token in &mut tokens {
            let current_line = token.delta_line;
            let current_start = token.delta_start;
            
            if current_line == prev_line {
                token.delta_line = 0;
                token.delta_start = current_start - prev_start;
            } else {
                token.delta_line = current_line - prev_line;
                token.delta_start = current_start;
            }
            
            prev_line = current_line;
            prev_start = current_start;
        }
        
        tokens
    }
    
    /// Get the token legend
    pub fn get_legend(&self) -> &SemanticTokensLegend {
        &self.legend
    }
    
    /// Get semantic tokens for a range
    pub fn get_semantic_tokens_range(
        &self,
        content: &str,
        _range: Range,
        ast: Option<&Program>,
    ) -> Vec<SemanticToken> {
        // TODO: Implement efficient range-based token extraction
        // This would:
        // 1. Parse only the specified range
        // 2. Extract tokens only within the range
        // 3. Adjust token positions relative to range start

        // For now, return all tokens (less efficient but functional)
        self.get_semantic_tokens(content, ast)
    }

    /// Check if a token type represents an AI/ML construct
    fn is_ai_ml_token(&self, token_type: &TokenType) -> bool {
        matches!(token_type,
            TokenType::Train |
            TokenType::Evaluate |
            TokenType::Identifier // Would need context to determine if it's AI/ML related
        )
    }

    /// Get enhanced highlighting for AI/ML constructs
    fn get_ai_ml_token_modifiers(&self, _token_value: &str) -> u32 {
        // AI/ML constructs get special highlighting
        64 // DEFAULT_LIBRARY modifier for distinctive appearance
    }
}

impl Default for HighlightingProvider {
    fn default() -> Self {
        Self::new()
    }
}
