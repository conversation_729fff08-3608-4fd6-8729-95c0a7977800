/// LSP server implementation using tower-lsp
/// 
/// Implements the Language Server Protocol for Umbra language

use super::UmbraLanguageServer;
use std::path::PathBuf;
use tower_lsp::jsonrpc::Result;
use tower_lsp::lsp_types::*;
use tower_lsp::{LanguageServer, LspService, Server};

#[tower_lsp::async_trait]
impl LanguageServer for UmbraLanguageServer {
    async fn initialize(&self, params: InitializeParams) -> Result<InitializeResult> {
        // Store client capabilities
        let server = self;
        // Note: In a real implementation, we'd need proper mutable access
        
        // Extract workspace root
        let workspace_root = params
            .root_uri
            .and_then(|uri| uri.to_file_path().ok())
            .or_else(|| params.root_path.map(PathBuf::from));
        
        Ok(InitializeResult {
            capabilities: self.server_capabilities.clone(),
            server_info: Some(ServerInfo {
                name: "Umbra Language Server".to_string(),
                version: Some("1.0.0".to_string()),
            }),
        })
    }

    async fn initialized(&self, _: InitializedParams) {
        // Server is now initialized and ready to receive requests
    }

    async fn shutdown(&self) -> Result<()> {
        Ok(())
    }

    async fn did_open(&self, params: DidOpenTextDocumentParams) {
        let uri = params.text_document.uri;
        let content = params.text_document.text;
        let version = params.text_document.version;
        
        if let Err(error) = self.update_document(uri.clone(), content, version).await {
            eprintln!("Failed to open document {uri}: {error}");
        }
        
        // Send diagnostics
        let diagnostics = self.get_diagnostics(&uri).await;
        // Note: In a real implementation, we'd send diagnostics to the client
    }

    async fn did_change(&self, params: DidChangeTextDocumentParams) {
        let uri = params.text_document.uri;
        let version = params.text_document.version;
        
        // Apply changes
        if let Some(mut document) = self.get_document(&uri).await {
            for change in params.content_changes {
                match change.range {
                    Some(range) => {
                        // Incremental change
                        if let Ok(new_content) = apply_text_edit(&document.content, &change.text, range) {
                            document.content = new_content;
                        }
                    }
                    None => {
                        // Full document change
                        document.content = change.text;
                    }
                }
            }
            
            if let Err(error) = self.update_document(uri.clone(), document.content, version).await {
                eprintln!("Failed to update document {uri}: {error}");
            }
            
            // Send updated diagnostics
            let diagnostics = self.get_diagnostics(&uri).await;
            // Note: In a real implementation, we'd send diagnostics to the client
        }
    }

    async fn did_close(&self, params: DidCloseTextDocumentParams) {
        self.remove_document(&params.text_document.uri).await;
    }

    async fn completion(&self, params: CompletionParams) -> Result<Option<CompletionResponse>> {
        let uri = &params.text_document_position.text_document.uri;
        let position = params.text_document_position.position;
        
        if let Some(document) = self.get_document(uri).await {
            let completions = self.get_completions(&document, position).await;
            Ok(Some(CompletionResponse::Array(completions)))
        } else {
            Ok(None)
        }
    }

    async fn hover(&self, params: HoverParams) -> Result<Option<Hover>> {
        let uri = &params.text_document_position_params.text_document.uri;
        let position = params.text_document_position_params.position;
        
        if let Some(document) = self.get_document(uri).await {
            if let Some(hover_info) = self.get_hover_info(&document, position).await {
                return Ok(Some(Hover {
                    contents: HoverContents::Scalar(MarkedString::String(hover_info)),
                    range: None,
                }));
            }
        }
        
        Ok(None)
    }

    async fn goto_definition(&self, params: GotoDefinitionParams) -> Result<Option<GotoDefinitionResponse>> {
        let uri = &params.text_document_position_params.text_document.uri;
        let position = params.text_document_position_params.position;
        
        if let Some(document) = self.get_document(uri).await {
            if let Some(location) = self.get_definition_location(&document, position).await {
                return Ok(Some(GotoDefinitionResponse::Scalar(location)));
            }
        }
        
        Ok(None)
    }

    async fn references(&self, params: ReferenceParams) -> Result<Option<Vec<Location>>> {
        let uri = &params.text_document_position.text_document.uri;
        let position = params.text_document_position.position;
        
        if let Some(document) = self.get_document(uri).await {
            let references = self.find_references(&document, position, params.context.include_declaration).await;
            if !references.is_empty() {
                return Ok(Some(references));
            }
        }
        
        Ok(None)
    }

    async fn document_symbol(&self, params: DocumentSymbolParams) -> Result<Option<DocumentSymbolResponse>> {
        let uri = &params.text_document.uri;
        
        if let Some(document) = self.get_document(uri).await {
            let symbols = self.get_document_symbols(&document).await;
            if !symbols.is_empty() {
                return Ok(Some(DocumentSymbolResponse::Flat(symbols)));
            }
        }
        
        Ok(None)
    }

    async fn semantic_tokens_full(&self, params: SemanticTokensParams) -> Result<Option<SemanticTokensResult>> {
        let uri = &params.text_document.uri;
        
        if let Some(document) = self.get_document(uri).await {
            let tokens = self.get_semantic_tokens(&document).await;
            if !tokens.is_empty() {
                return Ok(Some(SemanticTokensResult::Tokens(SemanticTokens {
                    result_id: None,
                    data: tokens,
                })));
            }
        }
        
        Ok(None)
    }

    async fn formatting(&self, params: DocumentFormattingParams) -> Result<Option<Vec<TextEdit>>> {
        let uri = params.text_document.uri.clone();

        let documents = self.documents.read().await;
        if let Some(document) = documents.get(&uri) {
            let formatting_provider = super::formatting::FormattingProvider::new();
            match formatting_provider.format_document(
                &document.content,
                params,
                document.ast.as_ref(),
            ) {
                Ok(edits) => Ok(Some(edits)),
                Err(error) => {
                    eprintln!("Formatting error: {error}");
                    Ok(None)
                }
            }
        } else {
            Ok(None)
        }
    }

    async fn range_formatting(
        &self,
        params: DocumentRangeFormattingParams,
    ) -> Result<Option<Vec<TextEdit>>> {
        let uri = params.text_document.uri.clone();

        let documents = self.documents.read().await;
        if let Some(document) = documents.get(&uri) {
            let formatting_provider = super::formatting::FormattingProvider::new();
            match formatting_provider.format_range(
                &document.content,
                params,
                document.ast.as_ref(),
            ) {
                Ok(edits) => Ok(Some(edits)),
                Err(error) => {
                    eprintln!("Range formatting error: {error}");
                    Ok(None)
                }
            }
        } else {
            Ok(None)
        }
    }

    async fn code_action(&self, params: CodeActionParams) -> Result<Option<CodeActionResponse>> {
        let uri = params.text_document.uri.clone();

        let documents = self.documents.read().await;
        if let Some(document) = documents.get(&uri) {
            let refactoring_provider = super::refactoring::RefactoringProvider::new();
            match refactoring_provider.get_code_actions(
                params,
                &document.content,
                document.ast.as_ref(),
                document.symbols.as_ref(),
            ) {
                Ok(actions) => {
                    let response: Vec<CodeActionOrCommand> = actions
                        .into_iter()
                        .map(CodeActionOrCommand::CodeAction)
                        .collect();
                    Ok(Some(response))
                }
                Err(error) => {
                    eprintln!("Code action error: {error}");
                    Ok(None)
                }
            }
        } else {
            Ok(None)
        }
    }
}

impl UmbraLanguageServer {
    /// Get completions for a position in the document
    async fn get_completions(&self, document: &super::DocumentState, position: Position) -> Vec<CompletionItem> {
        let mut completions = Vec::new();
        
        // Add keyword completions
        let keywords = vec![
            "fn", "let", "when", "otherwise", "repeat", "for", "in", "return",
            "train", "evaluate", "predict", "model", "dataset", "tensor",
            "import", "export", "module", "struct", "enum", "try", "catch", "finally",
            "Some", "None", "Ok", "Err", "true", "false"
        ];
        
        for keyword in keywords {
            completions.push(CompletionItem {
                label: keyword.to_string(),
                kind: Some(CompletionItemKind::KEYWORD),
                detail: Some(format!("Umbra keyword: {keyword}")),
                ..CompletionItem::default()
            });
        }
        
        // Add function completions from symbol table
        if let Some(symbols) = &document.symbols {
            for (name, symbol) in symbols.symbols() {
                use crate::semantic::symbol_table::SymbolType;
                
                let (kind, detail) = match symbol.symbol_type() {
                    SymbolType::Function => (CompletionItemKind::FUNCTION, "Function"),
                    SymbolType::Variable => (CompletionItemKind::VARIABLE, "Variable"),
                    SymbolType::Structure => (CompletionItemKind::STRUCT, "Structure"),
                    SymbolType::Parameter => (CompletionItemKind::VARIABLE, "Parameter"),
                    SymbolType::Trait => (CompletionItemKind::INTERFACE, "Trait"),
                    SymbolType::Implementation => (CompletionItemKind::CLASS, "Implementation"),
                };
                
                completions.push(CompletionItem {
                    label: name.clone(),
                    kind: Some(kind),
                    detail: Some(format!("{detail}: {name}")),
                    ..CompletionItem::default()
                });
            }
        }
        
        completions
    }
    
    /// Get hover information for a position
    async fn get_hover_info(&self, document: &super::DocumentState, position: Position) -> Option<String> {
        // Find the symbol at the given position
        let symbol_at_position = self.find_symbol_at_position(document, position).await?;

        // Get symbol information from the symbol table
        if let Some(symbols) = &document.symbols {
            if let Some(symbol) = symbols.lookup(&symbol_at_position) {
                let type_info = match symbol.symbol_type() {
                    crate::semantic::symbol_table::SymbolType::Function => {
                        format!("Function: {}", symbol_at_position)
                    }
                    crate::semantic::symbol_table::SymbolType::Variable => {
                        format!("Variable: {} : {}", symbol_at_position, symbol.type_annotation)
                    }
                    crate::semantic::symbol_table::SymbolType::Structure => {
                        format!("Struct: {}", symbol_at_position)
                    }
                    crate::semantic::symbol_table::SymbolType::Parameter => {
                        format!("Parameter: {} : {}", symbol_at_position, symbol.type_annotation)
                    }
                    crate::semantic::symbol_table::SymbolType::Trait => {
                        format!("Trait: {}", symbol_at_position)
                    }
                    crate::semantic::symbol_table::SymbolType::Implementation => {
                        format!("Implementation: {}", symbol_at_position)
                    }
                };

                return Some(format!("{}\n\nDefined at line {}", type_info, symbol.location.line));
            }
        }

        // Check if it's a built-in function or keyword
        let builtins = vec![
            // I/O Functions
            ("show", "Built-in function: show(value) - Display a value to output"),
            ("read_line", "Built-in function: read_line() -> String - Read a line from input"),
            ("read_file", "Built-in function: read_file(path: String) -> String - Read file contents"),
            ("write_file", "Built-in function: write_file(path: String, content: String) - Write to file"),

            // Math Functions
            ("abs", "Built-in function: abs(x: Integer) -> Integer - Absolute value"),
            ("max", "Built-in function: max(a: Integer, b: Integer) -> Integer - Maximum value"),
            ("min", "Built-in function: min(a: Integer, b: Integer) -> Integer - Minimum value"),
            ("sqrt", "Built-in function: sqrt(x: Float) -> Float - Square root"),
            ("pow", "Built-in function: pow(base: Float, exp: Float) -> Float - Power"),
            ("sin", "Built-in function: sin(x: Float) -> Float - Sine"),
            ("cos", "Built-in function: cos(x: Float) -> Float - Cosine"),
            ("tan", "Built-in function: tan(x: Float) -> Float - Tangent"),
            ("floor", "Built-in function: floor(x: Float) -> Integer - Floor"),
            ("ceil", "Built-in function: ceil(x: Float) -> Integer - Ceiling"),
            ("round", "Built-in function: round(x: Float) -> Integer - Round to nearest integer"),

            // String Functions
            ("str_len", "Built-in function: str_len(s: String) -> Integer - String length"),
            ("to_upper", "Built-in function: to_upper(s: String) -> String - Convert to uppercase"),
            ("to_lower", "Built-in function: to_lower(s: String) -> String - Convert to lowercase"),
            ("str_contains", "Built-in function: str_contains(haystack: String, needle: String) -> Boolean - Check if string contains substring"),
            ("str_split", "Built-in function: str_split(s: String, delimiter: String) -> List<String> - Split string"),
            ("str_trim", "Built-in function: str_trim(s: String) -> String - Trim whitespace"),

            // Type Conversion
            ("to_string", "Built-in function: to_string(value) -> String - Convert value to string"),
            ("to_integer", "Built-in function: to_integer(value) -> Integer - Convert value to integer"),
            ("to_float", "Built-in function: to_float(value) -> Float - Convert value to float"),
            ("to_boolean", "Built-in function: to_boolean(value) -> Boolean - Convert value to boolean"),

            // AI/ML Functions
            ("train", "AI/ML keyword: train - Train a machine learning model"),
            ("evaluate", "AI/ML keyword: evaluate - Evaluate model performance"),
            ("predict", "AI/ML keyword: predict - Make predictions with a trained model"),
            ("visualize", "AI/ML keyword: visualize - Create data visualizations"),
            ("load_model", "AI/ML function: load_model(path: String) - Load a pre-trained model"),
            ("save_model", "AI/ML function: save_model(model, path: String) - Save a trained model"),
            ("load_dataset", "AI/ML function: load_dataset(path: String) - Load a dataset"),
            ("normalize", "AI/ML function: normalize(data) - Normalize data values"),

            // Keywords
            ("fn", "Keyword: fn - Function declaration"),
            ("let", "Keyword: let - Variable declaration with := assignment"),
            ("when", "Keyword: when - Conditional expression (if-then-else)"),
            ("otherwise", "Keyword: otherwise - Default case in when expression"),
            ("repeat", "Keyword: repeat - Loop over iterable items"),
            ("while", "Keyword: while - Loop while condition is true"),
            ("for", "Keyword: for - For loop with range"),
            ("in", "Keyword: in - Used in for/repeat loops"),
            ("return", "Keyword: return - Return value from function"),
            ("bring", "Keyword: bring - Import module (Umbra's import statement)"),
            ("export", "Keyword: export - Export symbol from module"),
            ("using", "Keyword: using - Use trait implementation"),
            ("trait", "Keyword: trait - Define a trait"),
            ("impl", "Keyword: impl - Implement trait or methods"),

            // Types
            ("void", "Type: void - No return value"),
            ("Integer", "Type: Integer - 32-bit signed integer"),
            ("Float", "Type: Float - 64-bit floating point number"),
            ("String", "Type: String - UTF-8 encoded string"),
            ("Boolean", "Type: Boolean - true or false value"),
            ("List", "Type: List<T> - Dynamic array of elements"),
            ("Map", "Type: Map<K, V> - Key-value mapping"),
            ("Set", "Type: Set<T> - Unique collection of elements"),

            // Literals
            ("true", "Boolean literal: true"),
            ("false", "Boolean literal: false"),
        ];

        for (name, description) in builtins {
            if symbol_at_position == name {
                return Some(description.to_string());
            }
        }

        None
    }
    
    /// Get definition location for a symbol at position
    async fn get_definition_location(&self, document: &super::DocumentState, position: Position) -> Option<Location> {
        let symbol_at_position = self.find_symbol_at_position(document, position).await?;

        // Check if symbol exists in current document's symbol table
        if let Some(symbols) = &document.symbols {
            if let Some(symbol) = symbols.lookup(&symbol_at_position) {
                let location = &symbol.location;
                return Some(Location::new(
                    document.uri.clone(),
                    Range::new(
                        Position::new((location.line as u32).saturating_sub(1), location.column as u32),
                        Position::new((location.line as u32).saturating_sub(1), (location.column + symbol_at_position.len()) as u32),
                    ),
                ));
            }
        }

        // Search in other documents in the workspace
        let documents = self.documents.read().await;
        for (uri, doc) in documents.iter() {
            if uri == &document.uri {
                continue; // Skip current document
            }

            if let Some(symbols) = &doc.symbols {
                if let Some(symbol) = symbols.lookup(&symbol_at_position) {
                    let location = &symbol.location;
                    return Some(Location::new(
                        uri.clone(),
                        Range::new(
                            Position::new((location.line as u32).saturating_sub(1), location.column as u32),
                            Position::new((location.line as u32).saturating_sub(1), (location.column + symbol_at_position.len()) as u32),
                        ),
                    ));
                }
            }
        }

        None
    }
    
    /// Find all references to a symbol
    async fn find_references(&self, document: &super::DocumentState, position: Position, include_declaration: bool) -> Vec<Location> {
        let symbol_at_position = match self.find_symbol_at_position(document, position).await {
            Some(symbol) => symbol,
            None => return Vec::new(),
        };

        let mut references = Vec::new();
        let documents = self.documents.read().await;

        // Search through all documents
        for (uri, doc) in documents.iter() {
            // Find all occurrences of the symbol in the document content
            let lines: Vec<&str> = doc.content.lines().collect();

            for (line_idx, line) in lines.iter().enumerate() {
                let mut start_pos = 0;
                while let Some(pos) = line[start_pos..].find(&symbol_at_position) {
                    let actual_pos = start_pos + pos;

                    // Check if this is a whole word match (not part of another identifier)
                    let is_word_boundary = {
                        let before_ok = actual_pos == 0 ||
                            !line.chars().nth(actual_pos - 1).unwrap_or(' ').is_alphanumeric();
                        let after_ok = actual_pos + symbol_at_position.len() >= line.len() ||
                            !line.chars().nth(actual_pos + symbol_at_position.len()).unwrap_or(' ').is_alphanumeric();
                        before_ok && after_ok
                    };

                    if is_word_boundary {
                        let location = Location::new(
                            uri.clone(),
                            Range::new(
                                Position::new(line_idx as u32, actual_pos as u32),
                                Position::new(line_idx as u32, (actual_pos + symbol_at_position.len()) as u32),
                            ),
                        );

                        // Check if this is the declaration
                        let is_declaration = if let Some(symbols) = &doc.symbols {
                            if let Some(symbol) = symbols.lookup(&symbol_at_position) {
                                let symbol_location = &symbol.location;
                                symbol_location.line == (line_idx + 1) &&
                                symbol_location.column == actual_pos
                            } else {
                                false
                            }
                        } else {
                            false
                        };

                        // Add to references if it's not a declaration or if we want to include declarations
                        if !is_declaration || include_declaration {
                            references.push(location);
                        }
                    }

                    start_pos = actual_pos + 1;
                }
            }
        }

        references
    }
    
    /// Get document symbols
    async fn get_document_symbols(&self, document: &super::DocumentState) -> Vec<SymbolInformation> {
        let mut symbols = Vec::new();
        
        if let Some(symbol_table) = &document.symbols {
            for (name, symbol) in symbol_table.symbols() {
                symbols.push(SymbolInformation {
                    name: name.clone(),
                    kind: self.symbol_to_symbol_kind(symbol),
                    tags: None,
                    deprecated: None,
                    location: Location::new(
                        document.uri.clone(),
                        Range::new(Position::new(0, 0), Position::new(0, 0)),
                    ),
                    container_name: None,
                });
            }
        }
        
        symbols
    }
    
    /// Format document
    async fn format_document(&self, document: &super::DocumentState) -> Option<String> {
        let lines: Vec<&str> = document.content.lines().collect();
        let mut formatted_lines = Vec::new();
        let mut indent_level: usize = 0;
        let indent_size = 4; // 4 spaces per indent level

        for line in lines {
            let trimmed = line.trim();

            // Skip empty lines
            if trimmed.is_empty() {
                formatted_lines.push(String::new());
                continue;
            }

            // Decrease indent for closing braces/keywords
            if trimmed.starts_with('}') || trimmed.starts_with("otherwise") || trimmed.starts_with("end") {
                indent_level = indent_level.saturating_sub(1);
            }

            // Apply current indentation
            let indent = " ".repeat(indent_level * indent_size);
            formatted_lines.push(format!("{}{}", indent, trimmed));

            // Increase indent for opening braces/keywords
            if trimmed.ends_with('{') ||
               trimmed.starts_with("fn ") ||
               trimmed.starts_with("when ") ||
               trimmed.starts_with("repeat ") ||
               trimmed.starts_with("for ") ||
               trimmed.starts_with("struct ") ||
               trimmed.starts_with("enum ") {
                indent_level += 1;
            }
        }

        Some(formatted_lines.join("\n"))
    }
    
    /// Get semantic tokens for syntax highlighting
    async fn get_semantic_tokens(&self, document: &super::DocumentState) -> Vec<SemanticToken> {
        let mut tokens = Vec::new();
        let lines: Vec<&str> = document.content.lines().collect();

        let keywords = vec![
            "fn", "let", "when", "otherwise", "repeat", "for", "in", "return",
            "train", "evaluate", "predict", "model", "dataset", "tensor",
            "bring", "export", "module", "struct", "enum", "try", "catch", "finally",
            "Some", "None", "Ok", "Err", "true", "false", "void"
        ];

        let types = vec!["Integer", "Float", "String", "Boolean"];
        let operators = vec!["=", "+", "-", "*", "/", "==", "!=", "<", ">", "<=", ">=", "and", "or", "not"];

        for (line_idx, line) in lines.iter().enumerate() {
            let mut char_idx = 0;
            let chars: Vec<char> = line.chars().collect();

            while char_idx < chars.len() {
                let remaining: String = chars[char_idx..].iter().collect();

                // Skip whitespace
                if chars[char_idx].is_whitespace() {
                    char_idx += 1;
                    continue;
                }

                // Check for string literals
                if chars[char_idx] == '"' || chars[char_idx] == '\'' {
                    let quote = chars[char_idx];
                    let start = char_idx;
                    char_idx += 1;

                    // Find end of string
                    while char_idx < chars.len() && chars[char_idx] != quote {
                        if chars[char_idx] == '\\' && char_idx + 1 < chars.len() {
                            char_idx += 2; // Skip escaped character
                        } else {
                            char_idx += 1;
                        }
                    }
                    if char_idx < chars.len() {
                        char_idx += 1; // Include closing quote
                    }

                    tokens.push(SemanticToken {
                        delta_line: if tokens.is_empty() { line_idx as u32 } else { 0 },
                        delta_start: start as u32,
                        length: (char_idx - start) as u32,
                        token_type: 1, // STRING
                        token_modifiers_bitset: 0,
                    });
                    continue;
                }

                // Check for numbers
                if chars[char_idx].is_ascii_digit() {
                    let start = char_idx;
                    while char_idx < chars.len() && (chars[char_idx].is_ascii_digit() || chars[char_idx] == '.') {
                        char_idx += 1;
                    }

                    tokens.push(SemanticToken {
                        delta_line: if tokens.is_empty() { line_idx as u32 } else { 0 },
                        delta_start: start as u32,
                        length: (char_idx - start) as u32,
                        token_type: 2, // NUMBER
                        token_modifiers_bitset: 0,
                    });
                    continue;
                }

                // Check for comments
                if char_idx + 1 < chars.len() && chars[char_idx] == '/' && chars[char_idx + 1] == '/' {
                    let start = char_idx;
                    char_idx = chars.len(); // Rest of line is comment

                    tokens.push(SemanticToken {
                        delta_line: if tokens.is_empty() { line_idx as u32 } else { 0 },
                        delta_start: start as u32,
                        length: (char_idx - start) as u32,
                        token_type: 3, // COMMENT
                        token_modifiers_bitset: 0,
                    });
                    continue;
                }

                // Check for identifiers and keywords
                if chars[char_idx].is_alphabetic() || chars[char_idx] == '_' {
                    let start = char_idx;
                    while char_idx < chars.len() && (chars[char_idx].is_alphanumeric() || chars[char_idx] == '_') {
                        char_idx += 1;
                    }

                    let word: String = chars[start..char_idx].iter().collect();
                    let token_type = if keywords.contains(&word.as_str()) {
                        0 // KEYWORD
                    } else if types.contains(&word.as_str()) {
                        6 // TYPE
                    } else if let Some(symbols) = &document.symbols {
                        if let Some(symbol) = symbols.lookup(&word) {
                            match symbol.symbol_type() {
                                crate::semantic::symbol_table::SymbolType::Function => 4, // FUNCTION
                                _ => 5, // VARIABLE
                            }
                        } else {
                            5 // VARIABLE (default for unknown identifiers)
                        }
                    } else {
                        5 // VARIABLE
                    };

                    tokens.push(SemanticToken {
                        delta_line: if tokens.is_empty() { line_idx as u32 } else { 0 },
                        delta_start: start as u32,
                        length: (char_idx - start) as u32,
                        token_type,
                        token_modifiers_bitset: 0,
                    });
                    continue;
                }

                // Check for operators
                let mut found_operator = false;
                for op in &operators {
                    if remaining.starts_with(op) {
                        tokens.push(SemanticToken {
                            delta_line: if tokens.is_empty() { line_idx as u32 } else { 0 },
                            delta_start: char_idx as u32,
                            length: op.len() as u32,
                            token_type: 7, // OPERATOR
                            token_modifiers_bitset: 0,
                        });
                        char_idx += op.len();
                        found_operator = true;
                        break;
                    }
                }

                if !found_operator {
                    char_idx += 1; // Skip unknown character
                }
            }
        }

        tokens
    }

    /// Find the symbol at a given position in the document
    async fn find_symbol_at_position(&self, document: &super::DocumentState, position: Position) -> Option<String> {
        let lines: Vec<&str> = document.content.lines().collect();
        let line_idx = position.line as usize;
        let char_idx = position.character as usize;

        if line_idx >= lines.len() {
            return None;
        }

        let line = lines[line_idx];
        let chars: Vec<char> = line.chars().collect();

        if char_idx >= chars.len() {
            return None;
        }

        // Find the start and end of the identifier at the cursor position
        let mut start = char_idx;
        let mut end = char_idx;

        // If we're not on an identifier character, return None
        if !chars[char_idx].is_alphabetic() && chars[char_idx] != '_' {
            return None;
        }

        // Find start of identifier
        while start > 0 && (chars[start - 1].is_alphanumeric() || chars[start - 1] == '_') {
            start -= 1;
        }

        // Find end of identifier
        while end < chars.len() && (chars[end].is_alphanumeric() || chars[end] == '_') {
            end += 1;
        }

        if start < end {
            Some(chars[start..end].iter().collect())
        } else {
            None
        }
    }
}

/// Apply a text edit to content
fn apply_text_edit(content: &str, new_text: &str, range: Range) -> std::result::Result<String, String> {
    let lines: Vec<&str> = content.lines().collect();
    let start_line = range.start.line as usize;
    let start_char = range.start.character as usize;
    let end_line = range.end.line as usize;
    let end_char = range.end.character as usize;
    
    if start_line >= lines.len() || end_line >= lines.len() {
        return Err("Range out of bounds".to_string());
    }
    
    let mut result = String::new();
    
    // Add lines before the change
    for i in 0..start_line {
        result.push_str(lines[i]);
        result.push('\n');
    }
    
    // Add the start of the line up to the change
    if start_line < lines.len() {
        let start_line_content = lines[start_line];
        if start_char <= start_line_content.len() {
            result.push_str(&start_line_content[..start_char]);
        }
    }
    
    // Add the new text
    result.push_str(new_text);
    
    // Add the rest of the end line after the change
    if end_line < lines.len() {
        let end_line_content = lines[end_line];
        if end_char <= end_line_content.len() {
            result.push_str(&end_line_content[end_char..]);
        }
    }
    
    // Add lines after the change
    for i in (end_line + 1)..lines.len() {
        result.push('\n');
        result.push_str(lines[i]);
    }
    
    Ok(result)
}

/// Create and start the LSP server
pub async fn start_lsp_server() -> std::result::Result<(), Box<dyn std::error::Error>> {
    let stdin = tokio::io::stdin();
    let stdout = tokio::io::stdout();
    
    let (service, socket) = LspService::new(|_client| UmbraLanguageServer::new());
    Server::new(stdin, stdout, socket).serve(service).await;
    
    Ok(())
}
