use tower_lsp::lsp_types::{
    DocumentFormattingParams, DocumentRangeFormattingParams, Position, Range, TextEdit,
    FormattingOptions,
};
use crate::parser::ast::Program;
use crate::error::UmbraResult;

/// Code formatting provider for Umbra language
pub struct FormattingProvider {
    /// Default indentation size
    indent_size: usize,
    /// Use spaces instead of tabs
    use_spaces: bool,
    /// Maximum line length
    max_line_length: usize,
    /// Insert final newline
    insert_final_newline: bool,
    /// Trim trailing whitespace
    trim_trailing_whitespace: bool,
}

impl FormattingProvider {
    /// Create a new formatting provider
    pub fn new() -> Self {
        Self {
            indent_size: 4,
            use_spaces: true,
            max_line_length: 100,
            insert_final_newline: true,
            trim_trailing_whitespace: true,
        }
    }

    /// Format entire document
    pub fn format_document(
        &self,
        content: &str,
        params: DocumentFormattingParams,
        ast: Option<&Program>,
    ) -> UmbraResult<Vec<TextEdit>> {
        let options = &params.options;
        let mut formatted_content = self.format_content(content, options, ast)?;
        
        // Apply final formatting rules
        if self.insert_final_newline && !formatted_content.ends_with('\n') {
            formatted_content.push('\n');
        }
        
        if self.trim_trailing_whitespace {
            formatted_content = self.trim_trailing_whitespace_from_content(&formatted_content);
        }
        
        // Create a single text edit that replaces the entire document
        let lines: Vec<&str> = content.lines().collect();
        let end_line = lines.len().saturating_sub(1) as u32;
        let end_character = lines.last().map(|line| line.len()).unwrap_or(0) as u32;
        
        Ok(vec![TextEdit {
            range: Range::new(
                Position::new(0, 0),
                Position::new(end_line, end_character),
            ),
            new_text: formatted_content,
        }])
    }

    /// Format document range
    pub fn format_range(
        &self,
        content: &str,
        params: DocumentRangeFormattingParams,
        ast: Option<&Program>,
    ) -> UmbraResult<Vec<TextEdit>> {
        let range = params.range;
        let options = &params.options;
        
        // Extract the range content
        let lines: Vec<&str> = content.lines().collect();
        let start_line = range.start.line as usize;
        let end_line = range.end.line as usize;
        
        if start_line >= lines.len() || end_line >= lines.len() {
            return Ok(vec![]);
        }
        
        // Get the content within the range
        let range_lines = &lines[start_line..=end_line];
        let range_content = range_lines.join("\n");
        
        // Format the range content
        let formatted_content = self.format_content(&range_content, options, ast)?;
        
        Ok(vec![TextEdit {
            range,
            new_text: formatted_content,
        }])
    }

    /// Format content with given options
    fn format_content(
        &self,
        content: &str,
        options: &FormattingOptions,
        _ast: Option<&Program>,
    ) -> UmbraResult<String> {
        let mut formatted = String::new();
        let lines: Vec<&str> = content.lines().collect();
        let mut indent_level = 0;
        let in_string = false;
        let mut in_comment = false;
        
        // Use formatting options directly (they are not Option types in this version)
        let use_spaces = self.use_spaces; // Use default for now
        let tab_size = self.indent_size; // Use default for now

        let indent_str = if use_spaces {
            " ".repeat(tab_size)
        } else {
            "\t".to_string()
        };
        
        for line in lines {
            let trimmed = line.trim();
            
            // Skip empty lines but preserve them
            if trimmed.is_empty() {
                formatted.push('\n');
                continue;
            }
            
            // Handle comments
            if trimmed.starts_with("//") {
                formatted.push_str(&indent_str.repeat(indent_level));
                formatted.push_str(trimmed);
                formatted.push('\n');
                continue;
            }
            
            // Handle block comments
            if trimmed.starts_with("/*") {
                in_comment = true;
            }
            if in_comment {
                formatted.push_str(&indent_str.repeat(indent_level));
                formatted.push_str(trimmed);
                formatted.push('\n');
                if trimmed.ends_with("*/") {
                    in_comment = false;
                }
                continue;
            }
            
            // Adjust indentation based on content
            let new_indent_level = self.calculate_indent_level(trimmed, indent_level);
            
            // Apply indentation
            formatted.push_str(&indent_str.repeat(new_indent_level));
            
            // Format the line content
            let formatted_line = self.format_line(trimmed);
            formatted.push_str(&formatted_line);
            formatted.push('\n');
            
            // Update indent level for next line
            indent_level = self.update_indent_level(trimmed, new_indent_level);
        }
        
        Ok(formatted)
    }

    /// Calculate indent level for a line
    fn calculate_indent_level(&self, line: &str, current_level: usize) -> usize {
        // Decrease indent for closing braces, brackets, etc.
        if line.starts_with('}') || line.starts_with(']') || line.starts_with(')') {
            current_level.saturating_sub(1)
        } else if line.starts_with("otherwise") || line.starts_with("else") {
            current_level.saturating_sub(1)
        } else {
            current_level
        }
    }

    /// Update indent level after processing a line
    fn update_indent_level(&self, line: &str, current_level: usize) -> usize {
        let mut level = current_level;
        
        // Increase indent for opening braces, control structures, etc.
        if line.ends_with('{') || line.ends_with('[') || line.ends_with('(') {
            level += 1;
        } else if line.starts_with("define ") || line.starts_with("when ") || 
                  line.starts_with("repeat ") || line.starts_with("structure ") {
            level += 1;
        }
        
        level
    }

    /// Format a single line
    fn format_line(&self, line: &str) -> String {
        let mut formatted = String::new();
        let chars: Vec<char> = line.chars().collect();
        let mut i = 0;
        
        while i < chars.len() {
            let ch = chars[i];
            
            match ch {
                // Add spaces around operators
                '+' | '-' | '*' | '/' | '=' | '<' | '>' | '!' => {
                    // Check if it's part of a compound operator
                    if i + 1 < chars.len() {
                        let next_ch = chars[i + 1];
                        if (ch == '=' && next_ch == '=') || 
                           (ch == '!' && next_ch == '=') ||
                           (ch == '<' && next_ch == '=') ||
                           (ch == '>' && next_ch == '=') {
                            // Compound operator
                            if !formatted.ends_with(' ') && !formatted.is_empty() {
                                formatted.push(' ');
                            }
                            formatted.push(ch);
                            formatted.push(next_ch);
                            formatted.push(' ');
                            i += 2;
                            continue;
                        }
                    }
                    
                    // Single operator
                    if !formatted.ends_with(' ') && !formatted.is_empty() {
                        formatted.push(' ');
                    }
                    formatted.push(ch);
                    formatted.push(' ');
                }
                // Add space after commas and semicolons
                ',' | ';' => {
                    formatted.push(ch);
                    formatted.push(' ');
                }
                // Handle parentheses and braces
                '(' | '[' | '{' => {
                    formatted.push(ch);
                    // Don't add space after opening brackets
                }
                ')' | ']' | '}' => {
                    formatted.push(ch);
                }
                // Regular characters
                _ => {
                    formatted.push(ch);
                }
            }
            
            i += 1;
        }
        
        formatted.trim_end().to_string()
    }

    /// Trim trailing whitespace from content
    fn trim_trailing_whitespace_from_content(&self, content: &str) -> String {
        content
            .lines()
            .map(|line| line.trim_end())
            .collect::<Vec<_>>()
            .join("\n")
    }

    /// Update formatting options
    pub fn update_options(&mut self, _options: &FormattingOptions) {
        // TODO: Update options when we understand the FormattingOptions structure better
        // For now, use defaults
    }
}

impl Default for FormattingProvider {
    fn default() -> Self {
        Self::new()
    }
}
