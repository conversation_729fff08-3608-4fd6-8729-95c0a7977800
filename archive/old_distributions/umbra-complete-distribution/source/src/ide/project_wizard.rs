/// Project wizard for IDE integration
/// 
/// Provides guided project creation with step-by-step configuration
/// and intelligent defaults for different project types.

use crate::error::UmbraResult;
use std::collections::HashMap;
use std::path::Path;
use serde::{Deserialize, Serialize};

/// Project wizard for guided project creation
pub struct ProjectWizard {
    /// Available project types
    project_types: HashMap<String, ProjectType>,
    
    /// Wizard steps
    steps: Vec<WizardStep>,
    
    /// Current wizard session
    current_session: Option<WizardSession>,
}

/// Project type definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProjectType {
    /// Type ID
    pub id: String,
    
    /// Type name
    pub name: String,
    
    /// Type description
    pub description: String,
    
    /// Type category
    pub category: String,
    
    /// Required configuration
    pub configuration: Vec<ConfigurationField>,
    
    /// Default values
    pub defaults: HashMap<String, String>,
    
    /// Template ID to use
    pub template_id: String,
    
    /// Recommended dependencies
    pub recommended_deps: Vec<String>,
}

/// Configuration field for project setup
#[derive(Debug, C<PERSON>, Serialize, Deserialize)]
pub struct ConfigurationField {
    /// Field ID
    pub id: String,
    
    /// Field name
    pub name: String,
    
    /// Field description
    pub description: String,
    
    /// Field type
    pub field_type: FieldType,
    
    /// Whether field is required
    pub required: bool,
    
    /// Default value
    pub default: Option<String>,
    
    /// Validation rules
    pub validation: Option<ValidationRule>,
}

/// Field types for configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum FieldType {
    /// Text input
    Text,
    
    /// Number input
    Number,
    
    /// Boolean checkbox
    Boolean,
    
    /// Single choice from list
    Choice(Vec<String>),
    
    /// Multiple choices from list
    MultiChoice(Vec<String>),
    
    /// File path
    FilePath,
    
    /// Directory path
    DirectoryPath,
    
    /// Email address
    Email,
    
    /// URL
    Url,
}

/// Validation rule
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationRule {
    /// Validation type
    pub rule_type: ValidationType,
    
    /// Validation message
    pub message: String,
    
    /// Validation parameters
    pub parameters: HashMap<String, String>,
}

/// Validation types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ValidationType {
    /// Minimum length
    MinLength(usize),
    
    /// Maximum length
    MaxLength(usize),
    
    /// Regular expression pattern
    Pattern(String),
    
    /// Required field
    Required,
    
    /// Valid identifier
    Identifier,
    
    /// Valid email
    Email,
    
    /// Valid URL
    Url,
}

/// Wizard step
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WizardStep {
    /// Step ID
    pub id: String,
    
    /// Step title
    pub title: String,
    
    /// Step description
    pub description: String,
    
    /// Step fields
    pub fields: Vec<ConfigurationField>,
    
    /// Step conditions
    pub conditions: Vec<StepCondition>,
}

/// Step condition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StepCondition {
    /// Field to check
    pub field_id: String,
    
    /// Expected value
    pub value: String,
    
    /// Condition operator
    pub operator: ConditionOperator,
}

/// Condition operators
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ConditionOperator {
    Equals,
    NotEquals,
    Contains,
    NotContains,
}

/// Wizard session
#[derive(Debug, Clone)]
pub struct WizardSession {
    /// Session ID
    pub id: String,
    
    /// Project type
    pub project_type: String,
    
    /// Current step
    pub current_step: usize,
    
    /// Collected values
    pub values: HashMap<String, String>,
    
    /// Session status
    pub status: WizardStatus,
}

/// Wizard status
#[derive(Debug, Clone)]
pub enum WizardStatus {
    InProgress,
    Completed,
    Cancelled,
}

impl ProjectWizard {
    /// Create new project wizard
    pub fn new() -> UmbraResult<Self> {
        Ok(Self {
            project_types: HashMap::new(),
            steps: Vec::new(),
            current_session: None,
        })
    }
    
    /// Initialize project wizard
    pub fn initialize(&mut self) -> UmbraResult<()> {
        self.create_builtin_project_types()?;
        self.create_wizard_steps()?;
        println!("🧙 Project wizard initialized");
        Ok(())
    }
    
    /// Create built-in project types
    fn create_builtin_project_types(&mut self) -> UmbraResult<()> {
        // AI/ML Project Type
        let ai_ml_project = ProjectType {
            id: "ai_ml".to_string(),
            name: "AI/ML Project".to_string(),
            description: "Machine learning project with training and evaluation".to_string(),
            category: "AI/ML".to_string(),
            configuration: vec![
                ConfigurationField {
                    id: "project_name".to_string(),
                    name: "Project Name".to_string(),
                    description: "Name of your AI/ML project".to_string(),
                    field_type: FieldType::Text,
                    required: true,
                    default: None,
                    validation: Some(ValidationRule {
                        rule_type: ValidationType::Identifier,
                        message: "Project name must be a valid identifier".to_string(),
                        parameters: HashMap::new(),
                    }),
                },
                ConfigurationField {
                    id: "model_type".to_string(),
                    name: "Model Type".to_string(),
                    description: "Type of machine learning model".to_string(),
                    field_type: FieldType::Choice(vec![
                        "neural_network".to_string(),
                        "decision_tree".to_string(),
                        "random_forest".to_string(),
                        "svm".to_string(),
                        "linear_regression".to_string(),
                    ]),
                    required: true,
                    default: Some("neural_network".to_string()),
                    validation: None,
                },
                ConfigurationField {
                    id: "dataset_format".to_string(),
                    name: "Dataset Format".to_string(),
                    description: "Format of your training data".to_string(),
                    field_type: FieldType::Choice(vec![
                        "csv".to_string(),
                        "json".to_string(),
                        "parquet".to_string(),
                        "images".to_string(),
                    ]),
                    required: true,
                    default: Some("csv".to_string()),
                    validation: None,
                },
                ConfigurationField {
                    id: "enable_gpu".to_string(),
                    name: "Enable GPU Support".to_string(),
                    description: "Use GPU acceleration for training".to_string(),
                    field_type: FieldType::Boolean,
                    required: false,
                    default: Some("false".to_string()),
                    validation: None,
                },
            ],
            defaults: {
                let mut defaults = HashMap::new();
                defaults.insert("version".to_string(), "0.1.0".to_string());
                defaults.insert("author".to_string(), "Your Name <<EMAIL>>".to_string());
                defaults.insert("epochs".to_string(), "100".to_string());
                defaults.insert("learning_rate".to_string(), "0.001".to_string());
                defaults.insert("batch_size".to_string(), "32".to_string());
                defaults
            },
            template_id: "ai_ml_project".to_string(),
            recommended_deps: vec![
                "std::ai".to_string(),
                "std::data".to_string(),
                "std::math".to_string(),
            ],
        };
        
        // Web API Project Type
        let web_api_project = ProjectType {
            id: "web_api".to_string(),
            name: "Web API".to_string(),
            description: "RESTful web API with database integration".to_string(),
            category: "Web".to_string(),
            configuration: vec![
                ConfigurationField {
                    id: "api_name".to_string(),
                    name: "API Name".to_string(),
                    description: "Name of your web API".to_string(),
                    field_type: FieldType::Text,
                    required: true,
                    default: None,
                    validation: Some(ValidationRule {
                        rule_type: ValidationType::Identifier,
                        message: "API name must be a valid identifier".to_string(),
                        parameters: HashMap::new(),
                    }),
                },
                ConfigurationField {
                    id: "database_type".to_string(),
                    name: "Database Type".to_string(),
                    description: "Type of database to use".to_string(),
                    field_type: FieldType::Choice(vec![
                        "postgresql".to_string(),
                        "mysql".to_string(),
                        "sqlite".to_string(),
                        "mongodb".to_string(),
                    ]),
                    required: true,
                    default: Some("postgresql".to_string()),
                    validation: None,
                },
                ConfigurationField {
                    id: "auth_method".to_string(),
                    name: "Authentication Method".to_string(),
                    description: "Authentication method for the API".to_string(),
                    field_type: FieldType::Choice(vec![
                        "jwt".to_string(),
                        "oauth2".to_string(),
                        "api_key".to_string(),
                        "none".to_string(),
                    ]),
                    required: true,
                    default: Some("jwt".to_string()),
                    validation: None,
                },
            ],
            defaults: {
                let mut defaults = HashMap::new();
                defaults.insert("version".to_string(), "0.1.0".to_string());
                defaults.insert("port".to_string(), "8080".to_string());
                defaults
            },
            template_id: "web_api_project".to_string(),
            recommended_deps: vec![
                "std::http".to_string(),
                "std::json".to_string(),
                "std::db".to_string(),
            ],
        };
        
        // Library Project Type
        let library_project = ProjectType {
            id: "library".to_string(),
            name: "Library".to_string(),
            description: "Reusable library with comprehensive testing".to_string(),
            category: "Library".to_string(),
            configuration: vec![
                ConfigurationField {
                    id: "library_name".to_string(),
                    name: "Library Name".to_string(),
                    description: "Name of your library".to_string(),
                    field_type: FieldType::Text,
                    required: true,
                    default: None,
                    validation: Some(ValidationRule {
                        rule_type: ValidationType::Identifier,
                        message: "Library name must be a valid identifier".to_string(),
                        parameters: HashMap::new(),
                    }),
                },
                ConfigurationField {
                    id: "library_type".to_string(),
                    name: "Library Type".to_string(),
                    description: "Type of library functionality".to_string(),
                    field_type: FieldType::Choice(vec![
                        "utility".to_string(),
                        "data_processing".to_string(),
                        "ai_ml".to_string(),
                        "web".to_string(),
                        "system".to_string(),
                    ]),
                    required: true,
                    default: Some("utility".to_string()),
                    validation: None,
                },
            ],
            defaults: {
                let mut defaults = HashMap::new();
                defaults.insert("version".to_string(), "0.1.0".to_string());
                defaults
            },
            template_id: "library_project".to_string(),
            recommended_deps: vec!["std::testing".to_string()],
        };
        
        self.project_types.insert(ai_ml_project.id.clone(), ai_ml_project);
        self.project_types.insert(web_api_project.id.clone(), web_api_project);
        self.project_types.insert(library_project.id.clone(), library_project);
        
        Ok(())
    }
    
    /// Create wizard steps
    fn create_wizard_steps(&mut self) -> UmbraResult<()> {
        self.steps = vec![
            WizardStep {
                id: "project_type".to_string(),
                title: "Choose Project Type".to_string(),
                description: "Select the type of project you want to create".to_string(),
                fields: vec![
                    ConfigurationField {
                        id: "project_type".to_string(),
                        name: "Project Type".to_string(),
                        description: "Type of Umbra project".to_string(),
                        field_type: FieldType::Choice(vec![
                            "ai_ml".to_string(),
                            "web_api".to_string(),
                            "library".to_string(),
                        ]),
                        required: true,
                        default: Some("ai_ml".to_string()),
                        validation: None,
                    },
                ],
                conditions: vec![],
            },
            WizardStep {
                id: "basic_info".to_string(),
                title: "Basic Information".to_string(),
                description: "Provide basic information about your project".to_string(),
                fields: vec![
                    ConfigurationField {
                        id: "project_name".to_string(),
                        name: "Project Name".to_string(),
                        description: "Name of your project".to_string(),
                        field_type: FieldType::Text,
                        required: true,
                        default: None,
                        validation: Some(ValidationRule {
                            rule_type: ValidationType::Identifier,
                            message: "Project name must be a valid identifier".to_string(),
                            parameters: HashMap::new(),
                        }),
                    },
                    ConfigurationField {
                        id: "description".to_string(),
                        name: "Description".to_string(),
                        description: "Brief description of your project".to_string(),
                        field_type: FieldType::Text,
                        required: false,
                        default: None,
                        validation: None,
                    },
                    ConfigurationField {
                        id: "author".to_string(),
                        name: "Author".to_string(),
                        description: "Project author information".to_string(),
                        field_type: FieldType::Text,
                        required: true,
                        default: Some("Your Name <<EMAIL>>".to_string()),
                        validation: None,
                    },
                ],
                conditions: vec![],
            },
        ];
        
        Ok(())
    }
    
    /// Start new wizard session
    pub fn start_session(&mut self, project_type: String) -> UmbraResult<String> {
        let session_id = uuid::Uuid::new_v4().to_string();
        
        let session = WizardSession {
            id: session_id.clone(),
            project_type,
            current_step: 0,
            values: HashMap::new(),
            status: WizardStatus::InProgress,
        };
        
        self.current_session = Some(session);
        println!("🧙 Started project wizard session: {session_id}");
        
        Ok(session_id)
    }
    
    /// Get current step
    pub fn get_current_step(&self) -> Option<&WizardStep> {
        if let Some(session) = &self.current_session {
            self.steps.get(session.current_step)
        } else {
            None
        }
    }
    
    /// Submit step values
    pub fn submit_step(&mut self, values: HashMap<String, String>) -> UmbraResult<bool> {
        if let Some(session) = &mut self.current_session {
            // Validate values
            if let Some(step) = self.steps.get(session.current_step) {
                for field in &step.fields {
                    if field.required && !values.contains_key(&field.id) {
                        return Err(crate::error::UmbraError::CodeGen(
                            format!("Required field '{}' is missing", field.name)
                        ));
                    }
                }
            }
            
            // Store values
            session.values.extend(values);
            
            // Move to next step
            session.current_step += 1;
            
            // Check if wizard is complete
            if session.current_step >= self.steps.len() {
                session.status = WizardStatus::Completed;
                return Ok(true);
            }
        }
        
        Ok(false)
    }
    
    /// Get project types
    pub fn get_project_types(&self) -> Vec<&ProjectType> {
        self.project_types.values().collect()
    }
    
    /// Get project type by ID
    pub fn get_project_type(&self, id: &str) -> Option<&ProjectType> {
        self.project_types.get(id)
    }
    
    /// Complete wizard and generate project
    pub fn complete_wizard(&mut self, target_dir: &Path) -> UmbraResult<()> {
        if let Some(session) = &self.current_session {
            if let WizardStatus::Completed = session.status {
                // Generate project using collected values
                println!("✅ Wizard completed, generating project...");
                
                // Here we would integrate with the template manager
                // to generate the actual project files
                
                return Ok(());
            }
        }
        
        Err(crate::error::UmbraError::CodeGen("Wizard not completed".to_string()))
    }
    
    /// Cancel current session
    pub fn cancel_session(&mut self) {
        if let Some(mut session) = self.current_session.take() {
            session.status = WizardStatus::Cancelled;
            println!("❌ Wizard session cancelled");
        }
    }
    
    /// Get current session
    pub fn get_current_session(&self) -> Option<&WizardSession> {
        self.current_session.as_ref()
    }
}

impl Default for ProjectWizard {
    fn default() -> Self {
        Self::new().unwrap()
    }
}
