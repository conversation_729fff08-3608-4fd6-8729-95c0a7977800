/// Template management for IDE integration
/// 
/// Provides project templates, file templates, and code generation templates
/// for enhanced development workflows.

use crate::error::UmbraResult;
use std::collections::HashMap;
use std::path::{Path, PathBuf};
use serde::{Deserialize, Serialize};

/// Template manager for IDE integration
pub struct TemplateManager {
    /// Project templates
    project_templates: HashMap<String, ProjectTemplate>,
    
    /// File templates
    file_templates: HashMap<String, FileTemplate>,
    
    /// Code templates
    code_templates: HashMap<String, CodeTemplate>,
    
    /// Template directory
    template_dir: PathBuf,
}

/// Project template definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProjectTemplate {
    /// Template ID
    pub id: String,
    
    /// Template name
    pub name: String,
    
    /// Template description
    pub description: String,
    
    /// Template category
    pub category: String,
    
    /// Template files
    pub files: Vec<TemplateFile>,
    
    /// Template variables
    pub variables: HashMap<String, TemplateVariable>,
    
    /// Post-creation scripts
    pub post_scripts: Vec<String>,
    
    /// Required dependencies
    pub dependencies: Vec<String>,
}

/// File template definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileTemplate {
    /// Template ID
    pub id: String,
    
    /// Template name
    pub name: String,
    
    /// File extension
    pub extension: String,
    
    /// Template content
    pub content: String,
    
    /// Template variables
    pub variables: HashMap<String, TemplateVariable>,
    
    /// Template description
    pub description: String,
}

/// Code template definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CodeTemplate {
    /// Template ID
    pub id: String,
    
    /// Template name
    pub name: String,
    
    /// Template code
    pub code: String,
    
    /// Template variables
    pub variables: HashMap<String, TemplateVariable>,
    
    /// Template context (where it can be used)
    pub context: Vec<String>,
    
    /// Template description
    pub description: String,
}

/// Template file definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TemplateFile {
    /// Relative path within project
    pub path: String,
    
    /// File content template
    pub content: String,
    
    /// Whether file is executable
    pub executable: bool,
}

/// Template variable definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TemplateVariable {
    /// Variable name
    pub name: String,
    
    /// Variable description
    pub description: String,
    
    /// Variable type
    pub var_type: TemplateVariableType,
    
    /// Default value
    pub default: Option<String>,
    
    /// Whether variable is required
    pub required: bool,
    
    /// Validation pattern
    pub pattern: Option<String>,
}

/// Template variable types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TemplateVariableType {
    /// String value
    String,
    /// Integer value
    Integer,
    /// Boolean value
    Boolean,
    /// Choice from list
    Choice(Vec<String>),
    /// File path
    Path,
    /// Directory path
    Directory,
}

impl TemplateManager {
    /// Create new template manager
    pub fn new() -> UmbraResult<Self> {
        let template_dir = PathBuf::from("templates");
        
        Ok(Self {
            project_templates: HashMap::new(),
            file_templates: HashMap::new(),
            code_templates: HashMap::new(),
            template_dir,
        })
    }
    
    /// Initialize template manager
    pub fn initialize(&mut self) -> UmbraResult<()> {
        self.load_builtin_templates()?;
        self.load_user_templates()?;
        println!("📋 Template manager initialized");
        Ok(())
    }
    
    /// Load built-in templates
    fn load_builtin_templates(&mut self) -> UmbraResult<()> {
        // AI/ML Project Template
        self.add_project_template(ProjectTemplate {
            id: "ai_ml_project".to_string(),
            name: "AI/ML Project".to_string(),
            description: "Complete AI/ML project with training, evaluation, and deployment".to_string(),
            category: "AI/ML".to_string(),
            files: vec![
                TemplateFile {
                    path: "src/main.umbra".to_string(),
                    content: r#"// {{project_name}} - AI/ML Application
// Generated from Umbra AI/ML template

import std::ai
import std::data
import std::io

define main() -> void:
    // Load and prepare data
    dataset := Dataset::load("data/{{dataset_file}}")
    
    // Create and configure model
    model := Model::new("{{model_type}}")
    model.configure({{model_config}})
    
    // Train the model
    train model using dataset with:
        epochs: {{epochs}}
        learning_rate: {{learning_rate}}
        batch_size: {{batch_size}}
    
    // Evaluate model performance
    accuracy := evaluate model on dataset
    show("Model accuracy: {}", accuracy)
    
    // Save trained model
    model.save("models/{{project_name}}_model.umbra")
    
    show("Training completed successfully!")
"#.to_string(),
                    executable: false,
                },
                TemplateFile {
                    path: "Umbra.toml".to_string(),
                    content: r#"[package]
name = "{{project_name}}"
version = "{{version}}"
description = "{{description}}"
authors = ["{{author}}"]

[dependencies]
std = "1.0"

[build]
target = "binary"
optimization = 2

[ai]
enable_gpu = {{enable_gpu}}
memory_limit = "{{memory_limit}}"
"#.to_string(),
                    executable: false,
                },
                TemplateFile {
                    path: "README.md".to_string(),
                    content: r#"# {{project_name}}

{{description}}

## Getting Started

1. Install dependencies:
   ```bash
   umbra project build
   ```

2. Prepare your data:
   - Place training data in `data/{{dataset_file}}`
   - Ensure data is in the correct format

3. Train the model:
   ```bash
   umbra project run
   ```

4. Evaluate results:
   - Check the output for accuracy metrics
   - Trained model will be saved in `models/`

## Configuration

Edit the model parameters in `src/main.umbra`:
- `epochs`: Number of training epochs
- `learning_rate`: Learning rate for optimization
- `batch_size`: Batch size for training

## Project Structure

- `src/` - Source code
- `data/` - Training and test data
- `models/` - Saved models
- `tests/` - Unit tests
"#.to_string(),
                    executable: false,
                },
            ],
            variables: {
                let mut vars = HashMap::new();
                vars.insert("project_name".to_string(), TemplateVariable {
                    name: "project_name".to_string(),
                    description: "Name of the project".to_string(),
                    var_type: TemplateVariableType::String,
                    default: Some("my_ai_project".to_string()),
                    required: true,
                    pattern: Some(r"^[a-zA-Z][a-zA-Z0-9_]*$".to_string()),
                });
                vars.insert("description".to_string(), TemplateVariable {
                    name: "description".to_string(),
                    description: "Project description".to_string(),
                    var_type: TemplateVariableType::String,
                    default: Some("An AI/ML project built with Umbra".to_string()),
                    required: false,
                    pattern: None,
                });
                vars.insert("author".to_string(), TemplateVariable {
                    name: "author".to_string(),
                    description: "Project author".to_string(),
                    var_type: TemplateVariableType::String,
                    default: Some("Your Name <<EMAIL>>".to_string()),
                    required: true,
                    pattern: None,
                });
                vars.insert("model_type".to_string(), TemplateVariable {
                    name: "model_type".to_string(),
                    description: "Type of ML model".to_string(),
                    var_type: TemplateVariableType::Choice(vec![
                        "neural_network".to_string(),
                        "decision_tree".to_string(),
                        "random_forest".to_string(),
                        "svm".to_string(),
                    ]),
                    default: Some("neural_network".to_string()),
                    required: true,
                    pattern: None,
                });
                vars.insert("dataset_file".to_string(), TemplateVariable {
                    name: "dataset_file".to_string(),
                    description: "Training dataset filename".to_string(),
                    var_type: TemplateVariableType::String,
                    default: Some("training.csv".to_string()),
                    required: true,
                    pattern: None,
                });
                vars
            },
            post_scripts: vec![
                "mkdir -p data models tests".to_string(),
                "echo 'Project created successfully!' > .project_status".to_string(),
            ],
            dependencies: vec!["std::ai".to_string(), "std::data".to_string()],
        })?;
        
        // Library Template
        self.add_project_template(ProjectTemplate {
            id: "library_project".to_string(),
            name: "Library Project".to_string(),
            description: "Reusable library with comprehensive testing".to_string(),
            category: "Library".to_string(),
            files: vec![
                TemplateFile {
                    path: "src/lib.umbra".to_string(),
                    content: r#"// {{project_name}} - Library
// {{description}}

// Public API functions
export define add(a: i32, b: i32) -> i32:
    return a + b

export define multiply(a: i32, b: i32) -> i32:
    return a * b

export define {{main_function}}(input: {{input_type}}) -> {{output_type}}:
    // TODO: Implement main library function
    return input
"#.to_string(),
                    executable: false,
                },
            ],
            variables: {
                let mut vars = HashMap::new();
                vars.insert("main_function".to_string(), TemplateVariable {
                    name: "main_function".to_string(),
                    description: "Main library function name".to_string(),
                    var_type: TemplateVariableType::String,
                    default: Some("process".to_string()),
                    required: true,
                    pattern: Some(r"^[a-zA-Z][a-zA-Z0-9_]*$".to_string()),
                });
                vars.insert("input_type".to_string(), TemplateVariable {
                    name: "input_type".to_string(),
                    description: "Input parameter type".to_string(),
                    var_type: TemplateVariableType::Choice(vec![
                        "i32".to_string(),
                        "f64".to_string(),
                        "string".to_string(),
                        "Dataset".to_string(),
                    ]),
                    default: Some("i32".to_string()),
                    required: true,
                    pattern: None,
                });
                vars.insert("output_type".to_string(), TemplateVariable {
                    name: "output_type".to_string(),
                    description: "Output return type".to_string(),
                    var_type: TemplateVariableType::Choice(vec![
                        "i32".to_string(),
                        "f64".to_string(),
                        "string".to_string(),
                        "Dataset".to_string(),
                    ]),
                    default: Some("i32".to_string()),
                    required: true,
                    pattern: None,
                });
                vars
            },
            post_scripts: vec![],
            dependencies: vec![],
        })?;
        
        // File Templates
        self.add_file_template(FileTemplate {
            id: "umbra_module".to_string(),
            name: "Umbra Module".to_string(),
            extension: "umbra".to_string(),
            content: r#"// {{module_name}} - {{description}}
// Created: {{date}}

{{#if has_imports}}
{{#each imports}}
import {{this}}
{{/each}}
{{/if}}

{{#if is_public}}
// Public API
{{/if}}
define {{main_function}}({{parameters}}) -> {{return_type}}:
    // TODO: Implement function
    {{#if return_type != "void"}}
    return {{default_return}}
    {{/if}}

{{#if include_tests}}
// Tests
define test_{{main_function}}() -> void:
    // TODO: Add tests
    assert(true, "Test placeholder")
{{/if}}
"#.to_string(),
            variables: {
                let mut vars = HashMap::new();
                vars.insert("module_name".to_string(), TemplateVariable {
                    name: "module_name".to_string(),
                    description: "Module name".to_string(),
                    var_type: TemplateVariableType::String,
                    default: None,
                    required: true,
                    pattern: Some(r"^[a-zA-Z][a-zA-Z0-9_]*$".to_string()),
                });
                vars.insert("main_function".to_string(), TemplateVariable {
                    name: "main_function".to_string(),
                    description: "Main function name".to_string(),
                    var_type: TemplateVariableType::String,
                    default: Some("process".to_string()),
                    required: true,
                    pattern: Some(r"^[a-zA-Z][a-zA-Z0-9_]*$".to_string()),
                });
                vars
            },
            description: "Create a new Umbra module with standard structure".to_string(),
        })?;
        
        Ok(())
    }
    
    /// Load user-defined templates
    fn load_user_templates(&mut self) -> UmbraResult<()> {
        if self.template_dir.exists() {
            // Load templates from user directory
            // Implementation would scan for template files
        }
        Ok(())
    }
    
    /// Add project template
    pub fn add_project_template(&mut self, template: ProjectTemplate) -> UmbraResult<()> {
        self.project_templates.insert(template.id.clone(), template);
        Ok(())
    }
    
    /// Add file template
    pub fn add_file_template(&mut self, template: FileTemplate) -> UmbraResult<()> {
        self.file_templates.insert(template.id.clone(), template);
        Ok(())
    }
    
    /// Add code template
    pub fn add_code_template(&mut self, template: CodeTemplate) -> UmbraResult<()> {
        self.code_templates.insert(template.id.clone(), template);
        Ok(())
    }
    
    /// Get project templates
    pub fn get_project_templates(&self) -> Vec<&ProjectTemplate> {
        self.project_templates.values().collect()
    }
    
    /// Get file templates
    pub fn get_file_templates(&self) -> Vec<&FileTemplate> {
        self.file_templates.values().collect()
    }
    
    /// Get code templates
    pub fn get_code_templates(&self) -> Vec<&CodeTemplate> {
        self.code_templates.values().collect()
    }
    
    /// Get project template by ID
    pub fn get_project_template(&self, id: &str) -> Option<&ProjectTemplate> {
        self.project_templates.get(id)
    }
    
    /// Get file template by ID
    pub fn get_file_template(&self, id: &str) -> Option<&FileTemplate> {
        self.file_templates.get(id)
    }
    
    /// Get code template by ID
    pub fn get_code_template(&self, id: &str) -> Option<&CodeTemplate> {
        self.code_templates.get(id)
    }
    
    /// Generate project from template
    pub fn generate_project(
        &self,
        template_id: &str,
        target_dir: &Path,
        variables: HashMap<String, String>,
    ) -> UmbraResult<()> {
        let template = self.get_project_template(template_id)
            .ok_or_else(|| crate::error::UmbraError::CodeGen(format!("Template not found: {template_id}")))?;
        
        // Create target directory
        std::fs::create_dir_all(target_dir)?;
        
        // Generate files
        for file in &template.files {
            let file_path = target_dir.join(&file.path);
            
            // Create parent directories
            if let Some(parent) = file_path.parent() {
                std::fs::create_dir_all(parent)?;
            }
            
            // Process template content
            let content = self.process_template_content(&file.content, &variables)?;
            
            // Write file
            std::fs::write(&file_path, content)?;
            
            // Set executable if needed
            if file.executable {
                #[cfg(unix)]
                {
                    use std::os::unix::fs::PermissionsExt;
                    let mut perms = std::fs::metadata(&file_path)?.permissions();
                    perms.set_mode(0o755);
                    std::fs::set_permissions(&file_path, perms)?;
                }
            }
        }
        
        // Run post-creation scripts
        for script in &template.post_scripts {
            let processed_script = self.process_template_content(script, &variables)?;
            // Execute script (simplified - would use proper shell execution)
            println!("📜 Running: {processed_script}");
        }
        
        println!("✅ Project generated from template '{}'", template.name);
        Ok(())
    }
    
    /// Process template content with variables
    fn process_template_content(&self, content: &str, variables: &HashMap<String, String>) -> UmbraResult<String> {
        let mut result = content.to_string();
        
        // Simple variable substitution ({{variable_name}})
        for (key, value) in variables {
            let placeholder = format!("{{{{{key}}}}}");
            result = result.replace(&placeholder, value);
        }
        
        Ok(result)
    }
}

impl Default for TemplateManager {
    fn default() -> Self {
        Self::new().unwrap()
    }
}
