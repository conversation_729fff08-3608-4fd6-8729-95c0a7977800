/// Extension management for IDE integration
/// 
/// Provides IDE-specific extensions and configurations for popular editors
/// like VS Code, IntelliJ, Vim, Emacs, and others.

use crate::error::UmbraResult;
use std::collections::HashMap;
use std::path::Path;
use serde::{Deserialize, Serialize};

/// Extension manager for IDE integration
pub struct ExtensionManager {
    /// Available extensions
    extensions: HashMap<String, IDEExtension>,
    
    /// Extension configurations
    configurations: HashMap<String, ExtensionConfig>,
    
    /// Installation status
    installation_status: HashMap<String, bool>,
}

/// IDE extension definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IDEExtension {
    /// Extension ID
    pub id: String,
    
    /// Extension name
    pub name: String,
    
    /// Target IDE
    pub target_ide: SupportedIDE,
    
    /// Extension version
    pub version: String,
    
    /// Extension description
    pub description: String,
    
    /// Extension files
    pub files: Vec<ExtensionFile>,
    
    /// Extension configuration
    pub config: ExtensionConfig,
    
    /// Installation instructions
    pub installation: InstallationInstructions,
    
    /// Required dependencies
    pub dependencies: Vec<String>,
}

/// Extension file definition
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ExtensionFile {
    /// File path relative to extension root
    pub path: String,
    
    /// File content
    pub content: String,
    
    /// File type
    pub file_type: ExtensionFileType,
}

/// Extension file types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ExtensionFileType {
    /// Configuration file
    Config,
    /// Syntax definition
    Syntax,
    /// Theme file
    Theme,
    /// Snippet file
    Snippets,
    /// Language definition
    Language,
    /// Icon file
    Icon,
    /// Documentation
    Documentation,
}

/// Supported IDEs
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SupportedIDE {
    /// Visual Studio Code
    VSCode,
    /// IntelliJ IDEA
    IntelliJ,
    /// Vim/Neovim
    Vim,
    /// Emacs
    Emacs,
    /// Sublime Text
    SublimeText,
    /// Atom
    Atom,
    /// Eclipse
    Eclipse,
}

/// Extension configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExtensionConfig {
    /// Language configuration
    pub language: LanguageConfig,
    
    /// Syntax highlighting
    pub syntax_highlighting: SyntaxConfig,
    
    /// Code completion
    pub completion: CompletionConfig,
    
    /// Build integration
    pub build: BuildConfig,
    
    /// Debug integration
    pub debug: DebugConfig,
    
    /// Custom commands
    pub commands: Vec<CustomCommand>,
}

/// Language configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LanguageConfig {
    /// Language ID
    pub id: String,
    
    /// Language name
    pub name: String,
    
    /// File extensions
    pub extensions: Vec<String>,
    
    /// MIME types
    pub mime_types: Vec<String>,
    
    /// Comment patterns
    pub comments: CommentConfig,
    
    /// Bracket pairs
    pub brackets: Vec<(String, String)>,
    
    /// Auto-closing pairs
    pub auto_closing_pairs: Vec<(String, String)>,
}

/// Comment configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CommentConfig {
    /// Line comment prefix
    pub line_comment: String,
    
    /// Block comment start/end
    pub block_comment: Option<(String, String)>,
}

/// Syntax highlighting configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SyntaxConfig {
    /// Grammar file path
    pub grammar_file: String,
    
    /// Scope name
    pub scope_name: String,
    
    /// Token patterns
    pub patterns: Vec<TokenPattern>,
    
    /// Color theme
    pub theme: HashMap<String, String>,
}

/// Token pattern for syntax highlighting
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TokenPattern {
    /// Pattern name
    pub name: String,
    
    /// Regular expression pattern
    pub pattern: String,
    
    /// Token scope
    pub scope: String,
}

/// Code completion configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CompletionConfig {
    /// Enable auto-completion
    pub enabled: bool,
    
    /// Trigger characters
    pub trigger_characters: Vec<String>,
    
    /// Completion items
    pub items: Vec<CompletionItem>,
}

/// Completion item
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CompletionItem {
    /// Item label
    pub label: String,
    
    /// Item kind
    pub kind: String,
    
    /// Insert text
    pub insert_text: String,
    
    /// Documentation
    pub documentation: Option<String>,
}

/// Build configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BuildConfig {
    /// Build command
    pub command: String,
    
    /// Build arguments
    pub args: Vec<String>,
    
    /// Working directory
    pub working_dir: Option<String>,
    
    /// Environment variables
    pub env: HashMap<String, String>,
}

/// Debug configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DebugConfig {
    /// Debug adapter type
    pub adapter_type: String,
    
    /// Debug command
    pub command: String,
    
    /// Debug arguments
    pub args: Vec<String>,
    
    /// Port for debug adapter
    pub port: Option<u16>,
}

/// Custom command
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CustomCommand {
    /// Command ID
    pub id: String,
    
    /// Command title
    pub title: String,
    
    /// Command to execute
    pub command: String,
    
    /// Command arguments
    pub args: Vec<String>,
}

/// Installation instructions
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InstallationInstructions {
    /// Installation method
    pub method: InstallationMethod,
    
    /// Installation steps
    pub steps: Vec<String>,
    
    /// Post-installation steps
    pub post_install: Vec<String>,
    
    /// Verification steps
    pub verification: Vec<String>,
}

/// Installation methods
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum InstallationMethod {
    /// Manual installation
    Manual,
    /// Package manager
    PackageManager(String),
    /// Extension marketplace
    Marketplace(String),
    /// Git repository
    Git(String),
}

impl ExtensionManager {
    /// Create new extension manager
    pub fn new() -> UmbraResult<Self> {
        Ok(Self {
            extensions: HashMap::new(),
            configurations: HashMap::new(),
            installation_status: HashMap::new(),
        })
    }
    
    /// Initialize extension manager
    pub fn initialize(&mut self) -> UmbraResult<()> {
        self.create_builtin_extensions()?;
        println!("🔌 Extension manager initialized");
        Ok(())
    }
    
    /// Create built-in extensions
    fn create_builtin_extensions(&mut self) -> UmbraResult<()> {
        // VS Code Extension
        self.create_vscode_extension()?;
        
        // Vim Extension
        self.create_vim_extension()?;
        
        // IntelliJ Extension
        self.create_intellij_extension()?;
        
        Ok(())
    }
    
    /// Create VS Code extension
    fn create_vscode_extension(&mut self) -> UmbraResult<()> {
        let extension = IDEExtension {
            id: "umbra-vscode".to_string(),
            name: "Umbra Language Support".to_string(),
            target_ide: SupportedIDE::VSCode,
            version: "1.0.0".to_string(),
            description: "Comprehensive Umbra language support for Visual Studio Code".to_string(),
            files: vec![
                ExtensionFile {
                    path: "package.json".to_string(),
                    content: r#"{
    "name": "umbra-language-support",
    "displayName": "Umbra Language Support",
    "description": "Language support for Umbra programming language",
    "version": "1.0.0",
    "engines": {
        "vscode": "^1.60.0"
    },
    "categories": ["Programming Languages"],
    "contributes": {
        "languages": [{
            "id": "umbra",
            "aliases": ["Umbra", "umbra"],
            "extensions": [".umbra"],
            "configuration": "./language-configuration.json"
        }],
        "grammars": [{
            "language": "umbra",
            "scopeName": "source.umbra",
            "path": "./syntaxes/umbra.tmLanguage.json"
        }],
        "commands": [
            {
                "command": "umbra.build",
                "title": "Build Project",
                "category": "Umbra"
            },
            {
                "command": "umbra.test",
                "title": "Run Tests",
                "category": "Umbra"
            },
            {
                "command": "umbra.debug",
                "title": "Start Debugging",
                "category": "Umbra"
            }
        ],
        "keybindings": [
            {
                "command": "umbra.build",
                "key": "ctrl+shift+b",
                "when": "resourceExtname == .umbra"
            }
        ],
        "snippets": [
            {
                "language": "umbra",
                "path": "./snippets/umbra.json"
            }
        ]
    }
}"#.to_string(),
                    file_type: ExtensionFileType::Config,
                },
                ExtensionFile {
                    path: "syntaxes/umbra.tmLanguage.json".to_string(),
                    content: r##"{
  "scopeName": "source.umbra",
  "patterns": [
    { "include": "#keywords" },
    { "include": "#ai_keywords" },
    { "include": "#strings" },
    { "include": "#numbers" },
    { "include": "#comments" }
  ],
  "repository": {
    "keywords": {
      "patterns": [{
        "name": "keyword.control.umbra",
        "match": "\\b(define|when|otherwise|repeat|for|in|return|import|export|try|catch|throw)\\b"
      }]
    },
    "ai_keywords": {
      "patterns": [{
        "name": "keyword.ai.umbra",
        "match": "\\b(train|evaluate|predict|Dataset|Model|Tensor)\\b"
      }]
    },
    "strings": {
      "patterns": [{
        "name": "string.quoted.double.umbra",
        "begin": "\"",
        "end": "\"",
        "patterns": [{
          "name": "constant.character.escape.umbra",
          "match": "\\\\."
        }]
      }]
    },
    "numbers": {
      "patterns": [{
        "name": "constant.numeric.umbra",
        "match": "\\b\\d+(\\.\\d+)?\\b"
      }]
    },
    "comments": {
      "patterns": [{
        "name": "comment.line.double-slash.umbra",
        "match": "//.*$"
      }]
    }
  }
}"##.to_string(),
                    file_type: ExtensionFileType::Syntax,
                },
            ],
            config: ExtensionConfig {
                language: LanguageConfig {
                    id: "umbra".to_string(),
                    name: "Umbra".to_string(),
                    extensions: vec![".umbra".to_string()],
                    mime_types: vec!["text/x-umbra".to_string()],
                    comments: CommentConfig {
                        line_comment: "//".to_string(),
                        block_comment: Some(("/*".to_string(), "*/".to_string())),
                    },
                    brackets: vec![
                        ("[".to_string(), "]".to_string()),
                        ("(".to_string(), ")".to_string()),
                        ("{".to_string(), "}".to_string()),
                    ],
                    auto_closing_pairs: vec![
                        ("\"".to_string(), "\"".to_string()),
                        ("'".to_string(), "'".to_string()),
                        ("(".to_string(), ")".to_string()),
                        ("[".to_string(), "]".to_string()),
                        ("{".to_string(), "}".to_string()),
                    ],
                },
                syntax_highlighting: SyntaxConfig {
                    grammar_file: "syntaxes/umbra.tmLanguage.json".to_string(),
                    scope_name: "source.umbra".to_string(),
                    patterns: vec![],
                    theme: HashMap::new(),
                },
                completion: CompletionConfig {
                    enabled: true,
                    trigger_characters: vec![".".to_string(), ":".to_string()],
                    items: vec![],
                },
                build: BuildConfig {
                    command: "umbra".to_string(),
                    args: vec!["project".to_string(), "build".to_string()],
                    working_dir: None,
                    env: HashMap::new(),
                },
                debug: DebugConfig {
                    adapter_type: "umbra".to_string(),
                    command: "umbra".to_string(),
                    args: vec!["debug".to_string()],
                    port: Some(8080),
                },
                commands: vec![
                    CustomCommand {
                        id: "umbra.build".to_string(),
                        title: "Build Project".to_string(),
                        command: "umbra".to_string(),
                        args: vec!["project".to_string(), "build".to_string()],
                    },
                ],
            },
            installation: InstallationInstructions {
                method: InstallationMethod::Marketplace("vscode".to_string()),
                steps: vec![
                    "Open VS Code".to_string(),
                    "Go to Extensions (Ctrl+Shift+X)".to_string(),
                    "Search for 'Umbra Language Support'".to_string(),
                    "Click Install".to_string(),
                ],
                post_install: vec![
                    "Restart VS Code".to_string(),
                    "Open an .umbra file to activate the extension".to_string(),
                ],
                verification: vec![
                    "Open a .umbra file".to_string(),
                    "Verify syntax highlighting is working".to_string(),
                    "Try code completion with Ctrl+Space".to_string(),
                ],
            },
            dependencies: vec!["umbra-lsp".to_string()],
        };
        
        self.extensions.insert(extension.id.clone(), extension);
        Ok(())
    }
    
    /// Create Vim extension
    fn create_vim_extension(&mut self) -> UmbraResult<()> {
        let extension = IDEExtension {
            id: "umbra-vim".to_string(),
            name: "Umbra Vim Plugin".to_string(),
            target_ide: SupportedIDE::Vim,
            version: "1.0.0".to_string(),
            description: "Vim plugin for Umbra language support".to_string(),
            files: vec![
                ExtensionFile {
                    path: "ftdetect/umbra.vim".to_string(),
                    content: "au BufRead,BufNewFile *.umbra set filetype=umbra\n".to_string(),
                    file_type: ExtensionFileType::Config,
                },
                ExtensionFile {
                    path: "syntax/umbra.vim".to_string(),
                    content: r#"" Vim syntax file for Umbra
if exists("b:current_syntax")
  finish
endif

" Keywords
syn keyword umbraKeyword define when otherwise repeat for in return import export try catch throw
syn keyword umbraAIKeyword train evaluate predict Dataset Model Tensor

" Types
syn keyword umbraType i32 i64 f32 f64 string bool void

" Strings
syn region umbraString start='"' end='"' contains=umbraEscape
syn match umbraEscape '\\.' contained

" Numbers
syn match umbraNumber '\v<\d+>'
syn match umbraFloat '\v<\d+\.\d+>'

" Comments
syn match umbraComment '//.*$'

" Highlighting
hi def link umbraKeyword Keyword
hi def link umbraAIKeyword Special
hi def link umbraType Type
hi def link umbraString String
hi def link umbraNumber Number
hi def link umbraFloat Float
hi def link umbraComment Comment

let b:current_syntax = "umbra"
"#.to_string(),
                    file_type: ExtensionFileType::Syntax,
                },
            ],
            config: ExtensionConfig::default(),
            installation: InstallationInstructions {
                method: InstallationMethod::Manual,
                steps: vec![
                    "Copy files to ~/.vim/ directory".to_string(),
                    "Or use a plugin manager like Vundle or Pathogen".to_string(),
                ],
                post_install: vec![
                    "Restart Vim".to_string(),
                    "Open an .umbra file".to_string(),
                ],
                verification: vec![
                    "Open a .umbra file in Vim".to_string(),
                    "Verify syntax highlighting is active".to_string(),
                ],
            },
            dependencies: vec![],
        };
        
        self.extensions.insert(extension.id.clone(), extension);
        Ok(())
    }
    
    /// Create IntelliJ extension
    fn create_intellij_extension(&mut self) -> UmbraResult<()> {
        // Simplified IntelliJ extension
        let extension = IDEExtension {
            id: "umbra-intellij".to_string(),
            name: "Umbra IntelliJ Plugin".to_string(),
            target_ide: SupportedIDE::IntelliJ,
            version: "1.0.0".to_string(),
            description: "IntelliJ IDEA plugin for Umbra language support".to_string(),
            files: vec![],
            config: ExtensionConfig::default(),
            installation: InstallationInstructions {
                method: InstallationMethod::Marketplace("intellij".to_string()),
                steps: vec![
                    "Open IntelliJ IDEA".to_string(),
                    "Go to File > Settings > Plugins".to_string(),
                    "Search for 'Umbra Language Support'".to_string(),
                    "Install the plugin".to_string(),
                ],
                post_install: vec!["Restart IntelliJ IDEA".to_string()],
                verification: vec!["Open a .umbra file and verify syntax highlighting".to_string()],
            },
            dependencies: vec![],
        };
        
        self.extensions.insert(extension.id.clone(), extension);
        Ok(())
    }
    
    /// Get extension by ID
    pub fn get_extension(&self, id: &str) -> Option<&IDEExtension> {
        self.extensions.get(id)
    }
    
    /// Get extensions for IDE
    pub fn get_extensions_for_ide(&self, ide: &SupportedIDE) -> Vec<&IDEExtension> {
        self.extensions.values()
            .filter(|ext| std::mem::discriminant(&ext.target_ide) == std::mem::discriminant(ide))
            .collect()
    }
    
    /// Get all extensions
    pub fn get_all_extensions(&self) -> Vec<&IDEExtension> {
        self.extensions.values().collect()
    }
    
    /// Generate extension files
    pub fn generate_extension(&self, extension_id: &str, output_dir: &Path) -> UmbraResult<()> {
        let extension = self.get_extension(extension_id)
            .ok_or_else(|| crate::error::UmbraError::CodeGen(format!("Extension not found: {extension_id}")))?;
        
        // Create output directory
        std::fs::create_dir_all(output_dir)?;
        
        // Generate files
        for file in &extension.files {
            let file_path = output_dir.join(&file.path);
            
            // Create parent directories
            if let Some(parent) = file_path.parent() {
                std::fs::create_dir_all(parent)?;
            }
            
            // Write file
            std::fs::write(&file_path, &file.content)?;
        }
        
        println!("✅ Extension '{}' generated in {}", extension.name, output_dir.display());
        Ok(())
    }
    
    /// Check if extension is installed
    pub fn is_installed(&self, extension_id: &str) -> bool {
        self.installation_status.get(extension_id).copied().unwrap_or(false)
    }
    
    /// Mark extension as installed
    pub fn mark_installed(&mut self, extension_id: &str) {
        self.installation_status.insert(extension_id.to_string(), true);
    }
    
    /// Mark extension as uninstalled
    pub fn mark_uninstalled(&mut self, extension_id: &str) {
        self.installation_status.insert(extension_id.to_string(), false);
    }
}

impl Default for ExtensionConfig {
    fn default() -> Self {
        Self {
            language: LanguageConfig {
                id: "umbra".to_string(),
                name: "Umbra".to_string(),
                extensions: vec![".umbra".to_string()],
                mime_types: vec!["text/x-umbra".to_string()],
                comments: CommentConfig {
                    line_comment: "//".to_string(),
                    block_comment: None,
                },
                brackets: vec![],
                auto_closing_pairs: vec![],
            },
            syntax_highlighting: SyntaxConfig {
                grammar_file: "".to_string(),
                scope_name: "source.umbra".to_string(),
                patterns: vec![],
                theme: HashMap::new(),
            },
            completion: CompletionConfig {
                enabled: true,
                trigger_characters: vec![],
                items: vec![],
            },
            build: BuildConfig {
                command: "umbra".to_string(),
                args: vec!["build".to_string()],
                working_dir: None,
                env: HashMap::new(),
            },
            debug: DebugConfig {
                adapter_type: "umbra".to_string(),
                command: "umbra".to_string(),
                args: vec!["debug".to_string()],
                port: None,
            },
            commands: vec![],
        }
    }
}

impl Default for ExtensionManager {
    fn default() -> Self {
        Self::new().unwrap()
    }
}
