/// Code snippet management for IDE integration
/// 
/// Provides intelligent code snippets for common Umbra patterns,
/// AI/ML workflows, and development tasks.

use crate::error::UmbraResult;
use std::collections::HashMap;
use serde::{Deserialize, Serialize};

/// Snippet manager for IDE integration
pub struct SnippetManager {
    /// Code snippets by category
    snippets: HashMap<String, Vec<CodeSnippet>>,
    
    /// Snippet triggers (shortcuts)
    triggers: HashMap<String, String>,
    
    /// Context-aware snippets
    context_snippets: HashMap<String, Vec<String>>,
}

/// Code snippet definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CodeSnippet {
    /// Snippet ID
    pub id: String,
    
    /// Snippet name
    pub name: String,
    
    /// Snippet description
    pub description: String,
    
    /// Trigger prefix
    pub prefix: String,
    
    /// Snippet body (with placeholders)
    pub body: Vec<String>,
    
    /// Snippet category
    pub category: String,
    
    /// Context where snippet is applicable
    pub context: Vec<SnippetContext>,
    
    /// Snippet variables
    pub variables: HashMap<String, SnippetVariable>,
}

/// Snippet variable definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SnippetVariable {
    /// Variable name
    pub name: String,
    
    /// Default value
    pub default: String,
    
    /// Possible choices
    pub choices: Option<Vec<String>>,
    
    /// Variable description
    pub description: String,
}

/// Snippet context types
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum SnippetContext {
    /// Global context (anywhere)
    Global,
    
    /// Function body
    Function,
    
    /// Class/structure definition
    Structure,
    
    /// Module level
    Module,
    
    /// AI/ML specific context
    AiMl,
    
    /// Test context
    Test,
    
    /// Import section
    Import,
}

impl SnippetManager {
    /// Create new snippet manager
    pub fn new() -> UmbraResult<Self> {
        Ok(Self {
            snippets: HashMap::new(),
            triggers: HashMap::new(),
            context_snippets: HashMap::new(),
        })
    }
    
    /// Initialize snippet manager with built-in snippets
    pub fn initialize(&mut self) -> UmbraResult<()> {
        self.load_builtin_snippets()?;
        self.build_trigger_map();
        println!("✂️ Snippet manager initialized");
        Ok(())
    }
    
    /// Load built-in code snippets
    fn load_builtin_snippets(&mut self) -> UmbraResult<()> {
        // AI/ML Snippets
        let ai_ml_snippets = vec![
            CodeSnippet {
                id: "train_model".to_string(),
                name: "Train Model".to_string(),
                description: "Train an AI/ML model with dataset".to_string(),
                prefix: "train".to_string(),
                body: vec![
                    "train ${1:model} using ${2:dataset} with:".to_string(),
                    "    epochs: ${3:100}".to_string(),
                    "    learning_rate: ${4:0.001}".to_string(),
                    "    batch_size: ${5:32}".to_string(),
                ],
                category: "AI/ML".to_string(),
                context: vec![SnippetContext::Function, SnippetContext::AiMl],
                variables: HashMap::new(),
            },
            CodeSnippet {
                id: "evaluate_model".to_string(),
                name: "Evaluate Model".to_string(),
                description: "Evaluate model performance".to_string(),
                prefix: "eval".to_string(),
                body: vec![
                    "${1:accuracy} := evaluate ${2:model} on ${3:test_dataset}".to_string(),
                    "show(\"${4:Model accuracy}: {}\", ${1:accuracy})".to_string(),
                ],
                category: "AI/ML".to_string(),
                context: vec![SnippetContext::Function, SnippetContext::AiMl],
                variables: HashMap::new(),
            },
            CodeSnippet {
                id: "load_dataset".to_string(),
                name: "Load Dataset".to_string(),
                description: "Load dataset from file".to_string(),
                prefix: "dataset".to_string(),
                body: vec![
                    "${1:dataset} := Dataset::load(\"${2:data/training.csv}\")".to_string(),
                    "${1:dataset}.preprocess(${3:normalize: true})".to_string(),
                ],
                category: "AI/ML".to_string(),
                context: vec![SnippetContext::Function, SnippetContext::AiMl],
                variables: HashMap::new(),
            },
            CodeSnippet {
                id: "create_model".to_string(),
                name: "Create Model".to_string(),
                description: "Create and configure a new model".to_string(),
                prefix: "model".to_string(),
                body: vec![
                    "${1:model} := Model::new(\"${2:neural_network}\")".to_string(),
                    "${1:model}.configure({".to_string(),
                    "    layers: [${3:128, 64, 32}],".to_string(),
                    "    activation: \"${4:relu}\",".to_string(),
                    "    optimizer: \"${5:adam}\"".to_string(),
                    "})".to_string(),
                ],
                category: "AI/ML".to_string(),
                context: vec![SnippetContext::Function, SnippetContext::AiMl],
                variables: HashMap::new(),
            },
            CodeSnippet {
                id: "predict".to_string(),
                name: "Make Prediction".to_string(),
                description: "Use model to make predictions".to_string(),
                prefix: "predict".to_string(),
                body: vec![
                    "${1:predictions} := predict ${2:model} on ${3:input_data}".to_string(),
                    "show(\"Predictions: {}\", ${1:predictions})".to_string(),
                ],
                category: "AI/ML".to_string(),
                context: vec![SnippetContext::Function, SnippetContext::AiMl],
                variables: HashMap::new(),
            },
        ];
        
        // Function Snippets
        let function_snippets = vec![
            CodeSnippet {
                id: "function_def".to_string(),
                name: "Function Definition".to_string(),
                description: "Define a new function".to_string(),
                prefix: "def".to_string(),
                body: vec![
                    "define ${1:function_name}(${2:parameters}) -> ${3:return_type}:".to_string(),
                    "    ${4:// TODO: Implement function}".to_string(),
                    "    ${0}".to_string(),
                ],
                category: "Functions".to_string(),
                context: vec![SnippetContext::Module, SnippetContext::Structure],
                variables: HashMap::new(),
            },
            CodeSnippet {
                id: "main_function".to_string(),
                name: "Main Function".to_string(),
                description: "Create main entry point".to_string(),
                prefix: "main".to_string(),
                body: vec![
                    "define main() -> void:".to_string(),
                    "    ${0}".to_string(),
                ],
                category: "Functions".to_string(),
                context: vec![SnippetContext::Module],
                variables: HashMap::new(),
            },
        ];
        
        // Control Flow Snippets
        let control_flow_snippets = vec![
            CodeSnippet {
                id: "when_statement".to_string(),
                name: "When Statement".to_string(),
                description: "Conditional when statement".to_string(),
                prefix: "when".to_string(),
                body: vec![
                    "when ${1:condition}:".to_string(),
                    "    ${2:// TODO: Add code}".to_string(),
                    "otherwise:".to_string(),
                    "    ${0}".to_string(),
                ],
                category: "Control Flow".to_string(),
                context: vec![SnippetContext::Function],
                variables: HashMap::new(),
            },
            CodeSnippet {
                id: "repeat_loop".to_string(),
                name: "Repeat Loop".to_string(),
                description: "Repeat loop with condition".to_string(),
                prefix: "repeat".to_string(),
                body: vec![
                    "repeat ${1:times}:".to_string(),
                    "    ${0}".to_string(),
                ],
                category: "Control Flow".to_string(),
                context: vec![SnippetContext::Function],
                variables: HashMap::new(),
            },
            CodeSnippet {
                id: "for_each".to_string(),
                name: "For Each Loop".to_string(),
                description: "Iterate over collection".to_string(),
                prefix: "foreach".to_string(),
                body: vec![
                    "for ${1:item} in ${2:collection}:".to_string(),
                    "    ${0}".to_string(),
                ],
                category: "Control Flow".to_string(),
                context: vec![SnippetContext::Function],
                variables: HashMap::new(),
            },
        ];
        
        // Test Snippets
        let test_snippets = vec![
            CodeSnippet {
                id: "test_function".to_string(),
                name: "Test Function".to_string(),
                description: "Create a test function".to_string(),
                prefix: "test".to_string(),
                body: vec![
                    "define test_${1:function_name}() -> void:".to_string(),
                    "    // Arrange".to_string(),
                    "    ${2:// Setup test data}".to_string(),
                    "    ".to_string(),
                    "    // Act".to_string(),
                    "    ${3:// Call function under test}".to_string(),
                    "    ".to_string(),
                    "    // Assert".to_string(),
                    "    assert(${4:condition}, \"${5:Test description}\")".to_string(),
                ],
                category: "Testing".to_string(),
                context: vec![SnippetContext::Module, SnippetContext::Test],
                variables: HashMap::new(),
            },
            CodeSnippet {
                id: "assert_equal".to_string(),
                name: "Assert Equal".to_string(),
                description: "Assert two values are equal".to_string(),
                prefix: "assert_eq".to_string(),
                body: vec![
                    "assert(${1:actual} == ${2:expected}, \"${3:Expected ${2:expected}, got ${1:actual}}\")".to_string(),
                ],
                category: "Testing".to_string(),
                context: vec![SnippetContext::Function, SnippetContext::Test],
                variables: HashMap::new(),
            },
        ];
        
        // Error Handling Snippets
        let error_snippets = vec![
            CodeSnippet {
                id: "try_catch".to_string(),
                name: "Try-Catch Block".to_string(),
                description: "Error handling with try-catch".to_string(),
                prefix: "try".to_string(),
                body: vec![
                    "try:".to_string(),
                    "    ${1:// Code that might fail}".to_string(),
                    "catch ${2:error}:".to_string(),
                    "    ${3:// Handle error}".to_string(),
                    "    show(\"Error: {}\", ${2:error})".to_string(),
                ],
                category: "Error Handling".to_string(),
                context: vec![SnippetContext::Function],
                variables: HashMap::new(),
            },
        ];
        
        // Store snippets by category
        self.snippets.insert("AI/ML".to_string(), ai_ml_snippets);
        self.snippets.insert("Functions".to_string(), function_snippets);
        self.snippets.insert("Control Flow".to_string(), control_flow_snippets);
        self.snippets.insert("Testing".to_string(), test_snippets);
        self.snippets.insert("Error Handling".to_string(), error_snippets);
        
        Ok(())
    }
    
    /// Build trigger map for quick lookup
    fn build_trigger_map(&mut self) {
        self.triggers.clear();
        
        for snippets in self.snippets.values() {
            for snippet in snippets {
                self.triggers.insert(snippet.prefix.clone(), snippet.id.clone());
            }
        }
    }
    
    /// Get snippets by category
    pub fn get_snippets_by_category(&self, category: &str) -> Option<&Vec<CodeSnippet>> {
        self.snippets.get(category)
    }
    
    /// Get all categories
    pub fn get_categories(&self) -> Vec<&String> {
        self.snippets.keys().collect()
    }
    
    /// Get snippet by trigger
    pub fn get_snippet_by_trigger(&self, trigger: &str) -> Option<&CodeSnippet> {
        if let Some(snippet_id) = self.triggers.get(trigger) {
            self.get_snippet_by_id(snippet_id)
        } else {
            None
        }
    }
    
    /// Get snippet by ID
    pub fn get_snippet_by_id(&self, id: &str) -> Option<&CodeSnippet> {
        for snippets in self.snippets.values() {
            for snippet in snippets {
                if snippet.id == id {
                    return Some(snippet);
                }
            }
        }
        None
    }
    
    /// Get snippets for context
    pub fn get_snippets_for_context(&self, context: &SnippetContext) -> Vec<&CodeSnippet> {
        let mut result = Vec::new();
        
        for snippets in self.snippets.values() {
            for snippet in snippets {
                if snippet.context.contains(context) || snippet.context.contains(&SnippetContext::Global) {
                    result.push(snippet);
                }
            }
        }
        
        result
    }
    
    /// Expand snippet with variables
    pub fn expand_snippet(&self, snippet: &CodeSnippet, variables: &HashMap<String, String>) -> String {
        let mut result = snippet.body.join("\n");
        
        // Replace variables
        for (key, value) in variables {
            let placeholder = format!("${{{key}}}");
            result = result.replace(&placeholder, value);
        }
        
        // Handle numbered placeholders (${1}, ${2}, etc.)
        for i in 0..10 {
            let placeholder = format!("${{{i}}}");
            if result.contains(&placeholder) {
                result = result.replace(&placeholder, "");
            }
        }
        
        result
    }
    
    /// Add custom snippet
    pub fn add_snippet(&mut self, category: String, snippet: CodeSnippet) {
        self.snippets.entry(category).or_default().push(snippet.clone());
        self.triggers.insert(snippet.prefix.clone(), snippet.id);
    }
    
    /// Remove snippet
    pub fn remove_snippet(&mut self, id: &str) -> bool {
        for snippets in self.snippets.values_mut() {
            if let Some(pos) = snippets.iter().position(|s| s.id == id) {
                let snippet = snippets.remove(pos);
                self.triggers.remove(&snippet.prefix);
                return true;
            }
        }
        false
    }
    
    /// Get all snippets
    pub fn get_all_snippets(&self) -> Vec<&CodeSnippet> {
        let mut result = Vec::new();
        for snippets in self.snippets.values() {
            result.extend(snippets.iter());
        }
        result
    }
    
    /// Search snippets by name or description
    pub fn search_snippets(&self, query: &str) -> Vec<&CodeSnippet> {
        let query_lower = query.to_lowercase();
        let mut result = Vec::new();
        
        for snippets in self.snippets.values() {
            for snippet in snippets {
                if snippet.name.to_lowercase().contains(&query_lower) ||
                   snippet.description.to_lowercase().contains(&query_lower) ||
                   snippet.prefix.to_lowercase().contains(&query_lower) {
                    result.push(snippet);
                }
            }
        }
        
        result
    }
}

impl Default for SnippetManager {
    fn default() -> Self {
        Self::new().unwrap()
    }
}
