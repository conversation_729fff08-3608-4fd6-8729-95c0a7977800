/// Package Installer for Umbra
/// 
/// Handles package installation, uninstallation, and management
/// of installed packages in the local environment.

use crate::error::UmbraResult;
use std::collections::HashMap;
use std::path::{Path, PathBuf};
use std::fs;
use serde::{Deserialize, Serialize};

/// Package installer
pub struct PackageInstaller {
    /// Installation directory
    install_dir: PathBuf,
    
    /// Package database
    package_db: PackageDatabase,
    
    /// Installation cache
    cache: InstallationCache,
}

/// Package database for tracking installed packages
pub struct PackageDatabase {
    /// Database file path
    db_path: PathBuf,
    
    /// Installed packages
    packages: HashMap<String, InstalledPackage>,
}

/// Installation cache
pub struct InstallationCache {
    /// Cache directory
    cache_dir: PathBuf,
    
    /// Cached package files
    cached_packages: HashMap<String, CachedPackage>,
}

/// Installed package information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InstalledPackage {
    /// Package name
    pub name: String,
    
    /// Package version
    pub version: String,
    
    /// Installation path
    pub install_path: PathBuf,
    
    /// Installation timestamp
    pub installed_at: String,
    
    /// Package dependencies
    pub dependencies: Vec<String>,
    
    /// Package files
    pub files: Vec<String>,
    
    /// Package metadata
    pub metadata: PackageMetadata,
}

/// Cached package
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CachedPackage {
    /// Package name and version
    pub name_version: String,
    
    /// Cache file path
    pub cache_path: PathBuf,
    
    /// Package checksum
    pub checksum: String,
    
    /// Cache timestamp
    pub cached_at: String,
}

/// Package metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PackageMetadata {
    /// Package description
    pub description: String,
    
    /// Package authors
    pub authors: Vec<String>,
    
    /// Package license
    pub license: String,
    
    /// Package homepage
    pub homepage: Option<String>,
}

/// Installation result
#[derive(Debug)]
pub struct InstallationResult {
    /// Installation success
    pub success: bool,
    
    /// Installed package info
    pub package: Option<InstalledPackage>,
    
    /// Installation errors
    pub errors: Vec<String>,
    
    /// Installation warnings
    pub warnings: Vec<String>,
}

impl PackageInstaller {
    /// Create new package installer
    pub fn new(install_dir: &Path) -> UmbraResult<Self> {
        let install_dir = install_dir.to_path_buf();
        fs::create_dir_all(&install_dir)?;
        
        let db_path = install_dir.join("packages.db");
        let cache_dir = install_dir.join("cache");
        fs::create_dir_all(&cache_dir)?;
        
        Ok(Self {
            install_dir: install_dir.clone(),
            package_db: PackageDatabase::new(db_path)?,
            cache: InstallationCache::new(cache_dir)?,
        })
    }
    
    /// Install a package
    pub fn install(&mut self, name: &str, version: &str) -> UmbraResult<InstallationResult> {
        println!("📦 Installing package '{name}' version '{version}'...");
        
        // Check if already installed
        if let Some(installed) = self.package_db.get_package(name) {
            if installed.version == version {
                println!("✅ Package '{name}' version '{version}' is already installed");
                return Ok(InstallationResult {
                    success: true,
                    package: Some(installed.clone()),
                    errors: Vec::new(),
                    warnings: vec!["Package already installed".to_string()],
                });
            }
        }
        
        // Download package if not cached
        let package_data = self.get_package_data(name, version)?;
        
        // Extract package
        let package_dir = self.install_dir.join(format!("{name}-{version}"));
        self.extract_package(&package_data, &package_dir)?;
        
        // Load package metadata
        let metadata = self.load_package_metadata(&package_dir)?;
        
        // Install package files
        let installed_files = self.install_package_files(&package_dir, name)?;
        
        // Create installed package record
        let installed_package = InstalledPackage {
            name: name.to_string(),
            version: version.to_string(),
            install_path: package_dir.clone(),
            installed_at: chrono::Utc::now().to_rfc3339(),
            dependencies: Vec::new(), // Would be populated from dependency resolution
            files: installed_files,
            metadata,
        };
        
        // Update package database
        self.package_db.add_package(installed_package.clone())?;
        
        println!("✅ Package '{name}' version '{version}' installed successfully");
        
        Ok(InstallationResult {
            success: true,
            package: Some(installed_package),
            errors: Vec::new(),
            warnings: Vec::new(),
        })
    }
    
    /// Uninstall a package
    pub fn uninstall(&mut self, name: &str) -> UmbraResult<()> {
        println!("🗑️ Uninstalling package '{name}'...");
        
        // Get installed package info
        let installed = self.package_db.get_package(name)
            .ok_or_else(|| crate::error::UmbraError::CodeGen(
                format!("Package '{name}' is not installed")
            ))?;
        
        // Remove package files
        if installed.install_path.exists() {
            fs::remove_dir_all(&installed.install_path)?;
        }
        
        // Remove from package database
        self.package_db.remove_package(name)?;
        
        println!("✅ Package '{name}' uninstalled successfully");
        Ok(())
    }
    
    /// List installed packages
    pub fn list_installed(&self) -> UmbraResult<Vec<(String, String)>> {
        let packages = self.package_db.list_packages();
        Ok(packages.into_iter()
            .map(|pkg| (pkg.name.clone(), pkg.version.clone()))
            .collect())
    }
    
    /// Check if package is installed
    pub fn is_installed(&self, name: &str) -> bool {
        self.package_db.get_package(name).is_some()
    }
    
    /// Get installed package info
    pub fn get_installed_package(&self, name: &str) -> Option<&InstalledPackage> {
        self.package_db.get_package(name)
    }
    
    /// Update package database
    pub fn update_database(&mut self) -> UmbraResult<()> {
        self.package_db.save()?;
        Ok(())
    }
    
    /// Get package data (download or from cache)
    fn get_package_data(&mut self, name: &str, version: &str) -> UmbraResult<Vec<u8>> {
        let cache_key = format!("{name}-{version}");
        
        // Check cache first
        if let Some(cached) = self.cache.get_cached_package(&cache_key) {
            println!("📋 Using cached package data");
            return Ok(fs::read(&cached.cache_path)?);
        }
        
        // Simulate package download (in real implementation, use registry client)
        println!("📥 Downloading package data...");
        let package_data = format!("UMBRA_PACKAGE:{name}:{version}").into_bytes();
        
        // Cache the package
        self.cache.cache_package(&cache_key, &package_data)?;
        
        Ok(package_data)
    }
    
    /// Extract package to directory
    fn extract_package(&self, package_data: &[u8], target_dir: &Path) -> UmbraResult<()> {
        println!("📂 Extracting package to {}", target_dir.display());
        
        // Create target directory
        fs::create_dir_all(target_dir)?;
        
        // For simplicity, just create some dummy files
        // In real implementation, extract tar.gz/zip archive
        let src_dir = target_dir.join("src");
        fs::create_dir_all(&src_dir)?;
        
        // Create a dummy source file
        let main_file = src_dir.join("lib.umbra");
        fs::write(main_file, format!("// Package extracted from {} bytes", package_data.len()))?;
        
        // Create package manifest
        let manifest_content = format!(r#"{{
    "extracted_from": "{} bytes",
    "extraction_time": "{}"
}}"#, package_data.len(), chrono::Utc::now().to_rfc3339());
        
        fs::write(target_dir.join("PACKAGE_MANIFEST.json"), manifest_content)?;
        
        Ok(())
    }
    
    /// Load package metadata from extracted package
    fn load_package_metadata(&self, package_dir: &Path) -> UmbraResult<PackageMetadata> {
        let manifest_path = package_dir.join("PACKAGE_MANIFEST.json");
        
        if manifest_path.exists() {
            // Try to load from manifest
            let content = fs::read_to_string(manifest_path)?;
            if let Ok(manifest) = serde_json::from_str::<serde_json::Value>(&content) {
                return Ok(PackageMetadata {
                    description: "Extracted package".to_string(),
                    authors: vec!["Unknown".to_string()],
                    license: "Unknown".to_string(),
                    homepage: None,
                });
            }
        }
        
        // Default metadata
        Ok(PackageMetadata {
            description: "Umbra package".to_string(),
            authors: vec!["Unknown".to_string()],
            license: "MIT".to_string(),
            homepage: None,
        })
    }
    
    /// Install package files to appropriate locations
    fn install_package_files(&self, package_dir: &Path, package_name: &str) -> UmbraResult<Vec<String>> {
        let mut installed_files = Vec::new();
        
        // Install source files
        let src_dir = package_dir.join("src");
        if src_dir.exists() {
            for entry in walkdir::WalkDir::new(&src_dir) {
                let entry = entry?;
                if entry.path().is_file() {
                    let relative_path = entry.path().strip_prefix(package_dir)?;
                    installed_files.push(relative_path.to_string_lossy().to_string());
                }
            }
        }
        
        println!("📁 Installed {} files for package '{}'", installed_files.len(), package_name);
        Ok(installed_files)
    }
}

impl PackageDatabase {
    /// Create new package database
    pub fn new(db_path: PathBuf) -> UmbraResult<Self> {
        let mut db = Self {
            db_path,
            packages: HashMap::new(),
        };
        
        // Load existing database
        db.load()?;
        
        Ok(db)
    }
    
    /// Add package to database
    pub fn add_package(&mut self, package: InstalledPackage) -> UmbraResult<()> {
        self.packages.insert(package.name.clone(), package);
        self.save()
    }
    
    /// Remove package from database
    pub fn remove_package(&mut self, name: &str) -> UmbraResult<()> {
        self.packages.remove(name);
        self.save()
    }
    
    /// Get package from database
    pub fn get_package(&self, name: &str) -> Option<&InstalledPackage> {
        self.packages.get(name)
    }
    
    /// List all packages
    pub fn list_packages(&self) -> Vec<&InstalledPackage> {
        self.packages.values().collect()
    }
    
    /// Load database from file
    fn load(&mut self) -> UmbraResult<()> {
        if self.db_path.exists() {
            let content = fs::read_to_string(&self.db_path)?;
            if let Ok(packages) = serde_json::from_str(&content) {
                self.packages = packages;
            }
        }
        Ok(())
    }
    
    /// Save database to file
    fn save(&self) -> UmbraResult<()> {
        let content = serde_json::to_string_pretty(&self.packages)?;
        fs::write(&self.db_path, content)?;
        Ok(())
    }
}

impl InstallationCache {
    /// Create new installation cache
    pub fn new(cache_dir: PathBuf) -> UmbraResult<Self> {
        fs::create_dir_all(&cache_dir)?;
        
        Ok(Self {
            cache_dir,
            cached_packages: HashMap::new(),
        })
    }
    
    /// Get cached package
    pub fn get_cached_package(&self, key: &str) -> Option<&CachedPackage> {
        self.cached_packages.get(key)
    }
    
    /// Cache package data
    pub fn cache_package(&mut self, key: &str, data: &[u8]) -> UmbraResult<()> {
        let cache_path = self.cache_dir.join(format!("{key}.pkg"));
        fs::write(&cache_path, data)?;
        
        let cached_package = CachedPackage {
            name_version: key.to_string(),
            cache_path,
            checksum: format!("{:x}", md5::compute(data)),
            cached_at: chrono::Utc::now().to_rfc3339(),
        };
        
        self.cached_packages.insert(key.to_string(), cached_package);
        Ok(())
    }
    
    /// Clear cache
    pub fn clear_cache(&mut self) -> UmbraResult<()> {
        for cached in self.cached_packages.values() {
            if cached.cache_path.exists() {
                fs::remove_file(&cached.cache_path)?;
            }
        }
        self.cached_packages.clear();
        Ok(())
    }
}

impl Default for PackageInstaller {
    fn default() -> Self {
        Self::new(&PathBuf::from(".umbra/packages")).unwrap()
    }
}
