/// Package Publishing System for Umbra
/// 
/// Provides comprehensive package management including registry, publishing,
/// version management, and distribution for Umbra packages.

pub mod registry;
pub mod publisher;
pub mod version;
pub mod installer;
pub mod resolver;
pub mod security;
pub mod integrity;
pub mod advanced_registry;

use crate::error::UmbraResult;
use std::collections::HashMap;
use std::path::{Path, PathBuf};
use serde::{Deserialize, Serialize};
use registry::CacheStats;
use security::{PackageSecurityManager, PackageSignature};
use integrity::{IntegrityManager, IntegrityInfo, HashAlgorithm};

/// Package publishing manager
pub struct PublishingManager {
    /// Package registry client
    registry: registry::PackageRegistry,

    /// Package publisher
    publisher: publisher::PackagePublisher,

    /// Version manager
    version_manager: version::VersionManager,

    /// Package installer
    installer: installer::PackageInstaller,

    /// Dependency resolver
    resolver: resolver::DependencyResolver,

    /// Security manager
    security_manager: Option<PackageSecurityManager>,

    /// Integrity manager
    integrity_manager: IntegrityManager,

    /// Configuration
    config: PublishingConfig,
}

/// Publishing configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PublishingConfig {
    /// Default registry URL
    pub registry_url: String,
    
    /// Authentication token
    pub auth_token: Option<String>,
    
    /// Local cache directory
    pub cache_dir: PathBuf,
    
    /// Package installation directory
    pub install_dir: PathBuf,
    
    /// Enable pre-release packages
    pub allow_prerelease: bool,
    
    /// Timeout for network operations
    pub timeout_seconds: u64,
    
    /// Maximum package size (in bytes)
    pub max_package_size: u64,
}

/// Package version information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PackageVersion {
    /// Version string
    pub version: String,
    /// Publication timestamp
    pub published_at: String,
    /// Download count for this version
    pub downloads: u64,
    /// Whether this version is yanked
    pub yanked: bool,
}

/// Security information for packages
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityInfo {
    /// Package signature
    pub signature: Option<String>,
    /// Checksum
    pub checksum: String,
    /// Hash algorithm used
    pub hash_algorithm: String,
    /// Security scan results
    pub scan_results: Option<SecurityScanResults>,
}

/// Security scan results
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityScanResults {
    /// Vulnerabilities found
    pub vulnerabilities: Vec<Vulnerability>,
    /// Scan timestamp
    pub scanned_at: String,
    /// Scanner version
    pub scanner_version: String,
}

/// Vulnerability information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Vulnerability {
    /// Vulnerability ID
    pub id: String,
    /// Severity level
    pub severity: String,
    /// Description
    pub description: String,
    /// Affected versions
    pub affected_versions: Vec<String>,
}

/// Package metadata for publishing
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PackageMetadata {
    /// Package name
    pub name: String,
    
    /// Package version
    pub version: String,
    
    /// Package description
    pub description: String,
    
    /// Package authors
    pub authors: Vec<String>,
    
    /// Package license
    pub license: String,
    
    /// Package homepage
    pub homepage: Option<String>,
    
    /// Package repository
    pub repository: Option<String>,
    
    /// Package documentation
    pub documentation: Option<String>,
    
    /// Package keywords
    pub keywords: Vec<String>,
    
    /// Package categories
    pub categories: Vec<String>,
    
    /// Package dependencies
    pub dependencies: HashMap<String, String>,
    
    /// Development dependencies
    pub dev_dependencies: HashMap<String, String>,
    
    /// Build dependencies
    pub build_dependencies: HashMap<String, String>,
    
    /// Minimum Umbra version required
    pub umbra_version: String,
    
    /// Package files to include
    pub include: Vec<String>,
    
    /// Package files to exclude
    pub exclude: Vec<String>,
    
    /// Package build configuration
    pub build: Option<BuildConfig>,
}

/// Build configuration for packages
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BuildConfig {
    /// Build script path
    pub script: Option<String>,
    
    /// Build targets
    pub targets: Vec<String>,
    
    /// Build features
    pub features: Vec<String>,
    
    /// Default features
    pub default_features: bool,
    
    /// Build environment variables
    pub env: HashMap<String, String>,
}

/// Package information from registry
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PackageInfo {
    /// Package metadata
    pub metadata: PackageMetadata,

    /// Package download URL
    pub download_url: String,

    /// Package checksum
    pub checksum: String,

    /// Package size in bytes
    pub size: u64,

    /// Publication timestamp
    pub published_at: String,

    /// Download count
    pub downloads: u64,

    /// Package status
    pub status: PackageStatus,
}

/// Search result item for CLI display
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchResultItem {
    /// Package name
    pub name: String,

    /// Package version
    pub version: String,

    /// Package description
    pub description: Option<String>,

    /// Package author
    pub author: Option<String>,

    /// Package keywords
    pub keywords: Option<Vec<String>>,

    /// Download count
    pub download_count: Option<u64>,

    /// Publication timestamp
    pub published_at: Option<String>,
}

/// Package status
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PackageStatus {
    /// Package is active and available
    Active,

    /// Package is deprecated
    Deprecated {
        reason: String,
        replacement: Option<String>,
    },

    /// Package is yanked (unavailable for new installs)
    Yanked {
        reason: String,
    },

    /// Package is under review
    UnderReview,
}

impl std::fmt::Display for PackageStatus {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            PackageStatus::Active => write!(f, "Active"),
            PackageStatus::Deprecated { reason, replacement } => {
                if let Some(repl) = replacement {
                    write!(f, "Deprecated ({}), use {} instead", reason, repl)
                } else {
                    write!(f, "Deprecated ({})", reason)
                }
            },
            PackageStatus::Yanked { reason } => write!(f, "Yanked ({})", reason),
            PackageStatus::UnderReview => write!(f, "Under Review"),
        }
    }
}

/// Search result from package registry
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchResult {
    /// Matching packages
    pub packages: Vec<PackageInfo>,
    
    /// Total number of results
    pub total: usize,
    
    /// Current page
    pub page: usize,
    
    /// Results per page
    pub per_page: usize,
}

impl PublishingManager {
    /// Create new publishing manager
    pub fn new() -> UmbraResult<Self> {
        let config = PublishingConfig::default();

        // Initialize security manager if keys directory exists
        let keys_dir = config.cache_dir.join("keys");
        let security_manager = if keys_dir.exists() || std::env::var("UMBRA_ENABLE_SIGNING").is_ok() {
            Some(PackageSecurityManager::new(keys_dir)?)
        } else {
            None
        };

        Ok(Self {
            registry: registry::PackageRegistry::new(&config.registry_url)?,
            publisher: publisher::PackagePublisher::new()?,
            version_manager: version::VersionManager::new()?,
            installer: installer::PackageInstaller::new(&config.install_dir)?,
            resolver: resolver::DependencyResolver::new()?,
            security_manager,
            integrity_manager: IntegrityManager::new(),
            config,
        })
    }
    
    /// Initialize publishing manager with custom config
    pub fn with_config(config: PublishingConfig) -> UmbraResult<Self> {
        // Initialize security manager if keys directory exists
        let keys_dir = config.cache_dir.join("keys");
        let security_manager = if keys_dir.exists() || std::env::var("UMBRA_ENABLE_SIGNING").is_ok() {
            Some(PackageSecurityManager::new(keys_dir)?)
        } else {
            None
        };

        Ok(Self {
            registry: registry::PackageRegistry::new(&config.registry_url)?,
            publisher: publisher::PackagePublisher::new()?,
            version_manager: version::VersionManager::new()?,
            installer: installer::PackageInstaller::new(&config.install_dir)?,
            resolver: resolver::DependencyResolver::new()?,
            security_manager,
            integrity_manager: IntegrityManager::new(),
            config,
        })
    }
    
    /// Publish a package
    pub async fn publish_package(&mut self, package_dir: &Path) -> UmbraResult<String> {
        println!("📦 Publishing package from {}", package_dir.display());
        
        // Load package metadata
        let metadata = self.load_package_metadata(package_dir)?;
        
        // Validate package
        self.validate_package(&metadata, package_dir)?;
        
        // Build package
        let package_path = self.publisher.build_package(&metadata, package_dir)?;
        
        // Upload to registry
        let package_id = self.registry.upload_package(&package_path, &metadata).await?;
        
        println!("✅ Package '{}' version '{}' published successfully!", 
                 metadata.name, metadata.version);
        
        Ok(package_id)
    }
    
    /// Install a package
    pub fn install_package(&mut self, name: &str, version: Option<&str>) -> UmbraResult<()> {
        println!("📥 Installing package '{name}'");
        
        // Resolve dependencies
        let resolved = self.resolver.resolve_package(name, version)?;
        
        // Install package and dependencies
        for (pkg_name, pkg_version) in resolved {
            self.installer.install(&pkg_name, &pkg_version)?;
        }
        
        println!("✅ Package '{name}' installed successfully!");
        Ok(())
    }
    
    /// Search for packages
    pub async fn search_packages(&mut self, query: &str, limit: usize) -> UmbraResult<Vec<SearchResultItem>> {
        self.registry.search_packages(query, limit).await
    }

    /// Get package information
    pub async fn get_package_info(&mut self, name: &str, version: Option<&str>) -> UmbraResult<PackageInfo> {
        self.registry.get_package_info(name, version).await
    }

    /// Clean up expired cache entries
    pub fn cleanup_cache(&mut self) -> UmbraResult<()> {
        println!("🧹 Cleaning up package cache...");
        self.registry.cleanup_cache()?;
        Ok(())
    }

    /// Get cache statistics
    pub fn get_cache_stats(&self) -> UmbraResult<CacheStats> {
        self.registry.get_cache_stats()
    }
    
    /// List installed packages
    pub fn list_installed(&self) -> UmbraResult<Vec<(String, String)>> {
        self.installer.list_installed()
    }
    
    /// Uninstall a package
    pub fn uninstall_package(&mut self, name: &str) -> UmbraResult<()> {
        println!("🗑️ Uninstalling package '{name}'");
        self.installer.uninstall(name)?;
        println!("✅ Package '{name}' uninstalled successfully!");
        Ok(())
    }
    
    /// Update all packages
    pub async fn update_packages(&mut self) -> UmbraResult<Vec<String>> {
        println!("🔄 Updating packages...");
        
        let installed = self.list_installed()?;
        let mut updated = Vec::new();
        
        for (name, current_version) in installed {
            if let Ok(latest_info) = self.get_package_info(&name, None).await {
                if latest_info.metadata.version != current_version {
                    self.install_package(&name, Some(&latest_info.metadata.version))?;
                    updated.push(format!("{}: {} -> {}", name, current_version, latest_info.metadata.version));
                }
            }
        }
        
        if updated.is_empty() {
            println!("✅ All packages are up to date!");
        } else {
            println!("✅ Updated {} packages", updated.len());
        }
        
        Ok(updated)
    }
    
    /// Load package metadata from directory
    fn load_package_metadata(&self, package_dir: &Path) -> UmbraResult<PackageMetadata> {
        let manifest_path = package_dir.join("Umbra.toml");
        
        if !manifest_path.exists() {
            return Err(crate::error::UmbraError::CodeGen(
                "Package manifest (Umbra.toml) not found".to_string()
            ));
        }
        
        let content = std::fs::read_to_string(&manifest_path)?;
        let manifest: toml::Value = toml::from_str(&content)?;
        
        // Extract package metadata from TOML
        self.extract_metadata_from_toml(&manifest)
    }
    
    /// Extract metadata from TOML manifest
    fn extract_metadata_from_toml(&self, manifest: &toml::Value) -> UmbraResult<PackageMetadata> {
        let package = manifest.get("package")
            .ok_or_else(|| crate::error::UmbraError::CodeGen("Missing [package] section".to_string()))?;
        
        let name = package.get("name")
            .and_then(|v| v.as_str())
            .ok_or_else(|| crate::error::UmbraError::CodeGen("Missing package name".to_string()))?
            .to_string();
        
        let version = package.get("version")
            .and_then(|v| v.as_str())
            .ok_or_else(|| crate::error::UmbraError::CodeGen("Missing package version".to_string()))?
            .to_string();
        
        let description = package.get("description")
            .and_then(|v| v.as_str())
            .unwrap_or("")
            .to_string();
        
        let authors = package.get("authors")
            .and_then(|v| v.as_array())
            .map(|arr| arr.iter().filter_map(|v| v.as_str()).map(String::from).collect())
            .unwrap_or_else(Vec::new);
        
        let license = package.get("license")
            .and_then(|v| v.as_str())
            .unwrap_or("MIT")
            .to_string();
        
        // Extract dependencies
        let dependencies = manifest.get("dependencies")
            .and_then(|v| v.as_table())
            .map(|table| {
                table.iter()
                    .filter_map(|(k, v)| v.as_str().map(|s| (k.clone(), s.to_string())))
                    .collect()
            })
            .unwrap_or_default();
        
        Ok(PackageMetadata {
            name,
            version,
            description,
            authors,
            license,
            homepage: None,
            repository: None,
            documentation: None,
            keywords: Vec::new(),
            categories: Vec::new(),
            dependencies,
            dev_dependencies: HashMap::new(),
            build_dependencies: HashMap::new(),
            umbra_version: "1.0.0".to_string(),
            include: vec!["src/**/*.umbra".to_string()],
            exclude: vec!["target/**".to_string(), "tests/**".to_string()],
            build: None,
        })
    }
    
    /// Validate package before publishing
    fn validate_package(&self, metadata: &PackageMetadata, package_dir: &Path) -> UmbraResult<()> {
        // Validate package name
        if !self.is_valid_package_name(&metadata.name) {
            return Err(crate::error::UmbraError::CodeGen(
                format!("Invalid package name: {}", metadata.name)
            ));
        }
        
        // Validate version
        if !self.version_manager.is_valid_version(&metadata.version) {
            return Err(crate::error::UmbraError::CodeGen(
                format!("Invalid version: {}", metadata.version)
            ));
        }
        
        // Check if source files exist
        let src_dir = package_dir.join("src");
        if !src_dir.exists() {
            return Err(crate::error::UmbraError::CodeGen(
                "Package must contain a 'src' directory".to_string()
            ));
        }
        
        println!("✅ Package validation passed");
        Ok(())
    }
    
    /// Check if package name is valid
    fn is_valid_package_name(&self, name: &str) -> bool {
        // Package names must be lowercase, alphanumeric with hyphens
        name.chars().all(|c| c.is_ascii_lowercase() || c.is_ascii_digit() || c == '-') &&
        !name.starts_with('-') &&
        !name.ends_with('-') &&
        name.len() >= 2 &&
        name.len() <= 64
    }
    
    /// Get configuration
    pub fn config(&self) -> &PublishingConfig {
        &self.config
    }
    
    /// Update configuration
    pub fn update_config(&mut self, config: PublishingConfig) {
        self.config = config;
    }

    /// Generate a new signing key
    pub fn generate_signing_key(&mut self, name: &str, email: &str, organization: Option<String>) -> UmbraResult<String> {
        let security_manager = self.security_manager.as_mut()
            .ok_or_else(|| crate::error::UmbraError::Security(
                "Package signing is not enabled".to_string()
            ))?;

        let signer = security::SignerInfo {
            name: name.to_string(),
            email: email.to_string(),
            organization,
        };

        security_manager.generate_key_pair(signer)
    }

    /// Load a signing key for use
    pub fn load_signing_key(&mut self, fingerprint: &str) -> UmbraResult<()> {
        let security_manager = self.security_manager.as_mut()
            .ok_or_else(|| crate::error::UmbraError::Security(
                "Package signing is not enabled".to_string()
            ))?;

        security_manager.load_signing_key(fingerprint)
    }

    /// Sign a package
    pub fn sign_package(&self, package_path: &Path) -> UmbraResult<PackageSignature> {
        let security_manager = self.security_manager.as_ref()
            .ok_or_else(|| crate::error::UmbraError::Security(
                "Package signing is not enabled".to_string()
            ))?;

        security_manager.sign_package(package_path)
    }

    /// Verify a package signature
    pub fn verify_package(&self, package_path: &Path, signature: &PackageSignature) -> UmbraResult<bool> {
        let security_manager = self.security_manager.as_ref()
            .ok_or_else(|| crate::error::UmbraError::Security(
                "Package signing is not enabled".to_string()
            ))?;

        security_manager.verify_signature(package_path, signature)
    }

    /// List trusted keys
    pub fn list_trusted_keys(&self) -> UmbraResult<Vec<String>> {
        let security_manager = self.security_manager.as_ref()
            .ok_or_else(|| crate::error::UmbraError::Security(
                "Package signing is not enabled".to_string()
            ))?;

        let keys = security_manager.list_trusted_keys()
            .iter()
            .map(|key| key.fingerprint.clone())
            .collect();

        Ok(keys)
    }

    /// Remove a trusted key
    pub fn remove_trusted_key(&mut self, fingerprint: &str) -> UmbraResult<()> {
        let security_manager = self.security_manager.as_mut()
            .ok_or_else(|| crate::error::UmbraError::Security(
                "Package signing is not enabled".to_string()
            ))?;

        security_manager.remove_trusted_key(fingerprint)
    }

    /// Calculate integrity hash for a package
    pub fn calculate_integrity(&self, package_path: &Path, algorithm: Option<HashAlgorithm>) -> UmbraResult<IntegrityInfo> {
        self.integrity_manager.calculate_hash(package_path, algorithm)
    }

    /// Verify package integrity
    pub fn verify_integrity(&self, package_path: &Path, info: &IntegrityInfo) -> UmbraResult<bool> {
        self.integrity_manager.verify_integrity(package_path, info)
    }

    /// Save integrity information to file
    pub fn save_integrity_info(&self, info: &IntegrityInfo, output_path: &Path) -> UmbraResult<()> {
        self.integrity_manager.save_integrity_info(info, output_path)
    }

    /// Load integrity information from file
    pub fn load_integrity_info(&self, file_path: &Path) -> UmbraResult<IntegrityInfo> {
        self.integrity_manager.load_integrity_info(file_path)
    }


}

impl Default for PublishingConfig {
    fn default() -> Self {
        Self {
            registry_url: "https://registry.umbra-lang.org".to_string(),
            auth_token: None,
            cache_dir: PathBuf::from(".umbra/cache"),
            install_dir: PathBuf::from(".umbra/packages"),
            allow_prerelease: false,
            timeout_seconds: 30,
            max_package_size: 100 * 1024 * 1024, // 100MB
        }
    }
}

impl Default for PublishingManager {
    fn default() -> Self {
        Self::new().unwrap()
    }
}
