/// Package Registry for Umbra
/// 
/// Handles communication with the Umbra package registry,
/// including package upload, download, search, and metadata retrieval.

use crate::error::UmbraResult;
use super::{PackageMetadata, PackageInfo, SearchResult, SearchResultItem};
use std::collections::HashMap;
use std::path::Path;
use serde::{Deserialize, Serialize};

/// Package registry client
pub struct PackageRegistry {
    /// Registry base URL
    base_url: String,
    
    /// HTTP client for API requests
    client: RegistryClient,
    
    /// Authentication token
    auth_token: Option<String>,
    
    /// Local cache
    cache: RegistryCache,
}

/// HTTP client for registry communication
pub struct RegistryClient {
    /// Base URL for API requests
    api_base: String,

    /// Request timeout in seconds
    timeout: u64,

    /// User agent string
    user_agent: String,

    /// HTTP client instance
    http_client: reqwest::Client,
}

/// Local cache for registry data
pub struct RegistryCache {
    /// Cache directory
    cache_dir: std::path::PathBuf,
    
    /// Cached package information
    package_cache: HashMap<String, CachedPackageInfo>,
    
    /// Cache expiry time in seconds
    cache_ttl: u64,
}

/// Cached package information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CachedPackageInfo {
    /// Package information
    pub info: PackageInfo,

    /// Cache timestamp
    pub cached_at: u64,
}

/// Cached search result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CachedSearchResult {
    /// Search results
    pub results: SearchResult,

    /// Cache timestamp
    pub cached_at: u64,
}

/// Cache statistics
#[derive(Debug, Clone)]
pub struct CacheStats {
    /// Total number of entries in cache
    pub total_entries: usize,

    /// Number of valid (non-expired) entries
    pub valid_entries: usize,

    /// Number of expired entries
    pub expired_entries: usize,

    /// Estimated cache size in bytes
    pub cache_size_bytes: usize,
}

/// Registry API request
#[derive(Debug, Serialize)]
pub struct RegistryRequest {
    /// Request method
    pub method: String,
    
    /// Request path
    pub path: String,
    
    /// Request headers
    pub headers: HashMap<String, String>,
    
    /// Request body
    pub body: Option<String>,
}

/// Registry API response
#[derive(Debug, Deserialize)]
pub struct RegistryResponse {
    /// Response status code
    pub status: u16,
    
    /// Response headers
    pub headers: HashMap<String, String>,
    
    /// Response body
    pub body: String,
}

/// Package upload request
#[derive(Debug, Serialize)]
pub struct UploadRequest {
    /// Package metadata
    pub metadata: PackageMetadata,
    
    /// Package file data (base64 encoded)
    pub package_data: String,
    
    /// Package checksum
    pub checksum: String,
}

/// Package upload response
#[derive(Debug, Serialize, Deserialize)]
pub struct UploadResponse {
    /// Package ID
    pub package_id: String,
    
    /// Upload status
    pub status: String,
    
    /// Upload message
    pub message: String,
    
    /// Package URL
    pub package_url: String,
}

/// Search request parameters
#[derive(Debug, Serialize)]
pub struct SearchRequest {
    /// Search query
    pub query: String,
    
    /// Page number
    pub page: usize,
    
    /// Results per page
    pub per_page: usize,
    
    /// Sort order
    pub sort: String,
    
    /// Filter by category
    pub category: Option<String>,
}

impl PackageRegistry {
    /// Create new package registry client
    pub fn new(registry_url: &str) -> UmbraResult<Self> {
        let cache_dir = std::path::PathBuf::from(".umbra/registry_cache");
        std::fs::create_dir_all(&cache_dir)?;
        
        Ok(Self {
            base_url: registry_url.to_string(),
            client: RegistryClient::new(registry_url)?,
            auth_token: None,
            cache: RegistryCache::new(cache_dir)?,
        })
    }
    
    /// Set authentication token
    pub fn set_auth_token(&mut self, token: String) {
        self.auth_token = Some(token);
    }
    
    /// Upload package to registry
    pub async fn upload_package(&self, package_path: &Path, metadata: &PackageMetadata) -> UmbraResult<String> {
        println!("📤 Uploading package to registry...");
        
        // Read package file
        let package_data = std::fs::read(package_path)?;
        let package_base64 = base64_encode(&package_data);
        
        // Calculate checksum
        let checksum = calculate_checksum(&package_data);
        
        // Create upload request
        let upload_request = UploadRequest {
            metadata: metadata.clone(),
            package_data: package_base64,
            checksum,
        };
        
        // Send upload request
        let response = self.client.post("/packages/upload", &upload_request, &self.auth_token).await?;
        
        if response.status == 201 {
            let upload_response: UploadResponse = serde_json::from_str(&response.body)?;
            println!("✅ Package uploaded successfully: {}", upload_response.package_url);
            Ok(upload_response.package_id)
        } else {
            Err(crate::error::UmbraError::CodeGen(
                format!("Upload failed with status {}: {}", response.status, response.body)
            ))
        }
    }
    
    /// Search for packages
    pub async fn search(&mut self, query: &str, page: usize) -> UmbraResult<SearchResult> {
        // Check cache first
        if let Some(cached) = self.cache.get_search_results(query, page) {
            return Ok(cached);
        }

        let search_request = SearchRequest {
            query: query.to_string(),
            page,
            per_page: 20,
            sort: "relevance".to_string(),
            category: None,
        };

        let response = self.client.get(&format!("/packages/search?{}", encode_search_params(&search_request)), None).await?;

        if response.status == 200 {
            let search_result: SearchResult = serde_json::from_str(&response.body)?;

            // Cache results
            self.cache.cache_search_results(query, page, &search_result);

            Ok(search_result)
        } else {
            Err(crate::error::UmbraError::CodeGen(
                format!("Search failed with status {}: {}", response.status, response.body)
            ))
        }
    }

    /// Search for packages and return search result items
    pub async fn search_packages(&self, query: &str, limit: usize) -> UmbraResult<Vec<SearchResultItem>> {
        // For now, return mock data since we don't have a real registry
        // In a real implementation, this would call the search API
        let mock_packages = vec![
            SearchResultItem {
                name: "example-package".to_string(),
                version: "1.0.0".to_string(),
                description: Some("An example Umbra package for demonstration".to_string()),
                author: Some("Eclipse Softworks".to_string()),
                keywords: Some(vec!["example".to_string(), "demo".to_string()]),
                download_count: Some(42),
                published_at: Some("2025-07-15T10:00:00Z".to_string()),
            },
            SearchResultItem {
                name: "ai-utils".to_string(),
                version: "2.1.0".to_string(),
                description: Some("AI/ML utilities for Umbra".to_string()),
                author: Some("AI Team".to_string()),
                keywords: Some(vec!["ai".to_string(), "ml".to_string(), "utils".to_string()]),
                download_count: Some(156),
                published_at: Some("2025-07-10T14:30:00Z".to_string()),
            },
            SearchResultItem {
                name: "web-framework".to_string(),
                version: "0.5.2".to_string(),
                description: Some("Lightweight web framework for Umbra".to_string()),
                author: Some("Web Team".to_string()),
                keywords: Some(vec!["web".to_string(), "http".to_string(), "framework".to_string()]),
                download_count: Some(89),
                published_at: Some("2025-07-12T09:15:00Z".to_string()),
            },
        ];

        // Filter packages based on query
        let filtered_packages: Vec<SearchResultItem> = mock_packages
            .into_iter()
            .filter(|pkg| {
                let query_lower = query.to_lowercase();
                pkg.name.to_lowercase().contains(&query_lower) ||
                pkg.description.as_ref().map_or(false, |desc| desc.to_lowercase().contains(&query_lower)) ||
                pkg.keywords.as_ref().map_or(false, |keywords| {
                    keywords.iter().any(|keyword| keyword.to_lowercase().contains(&query_lower))
                })
            })
            .take(limit)
            .collect();

        Ok(filtered_packages)
    }
    
    /// Clean up cache
    pub fn cleanup_cache(&mut self) -> UmbraResult<()> {
        self.cache.cleanup_expired()
    }

    /// Get cache statistics
    pub fn get_cache_stats(&self) -> UmbraResult<CacheStats> {
        Ok(self.cache.get_stats())
    }

    /// Get package information
    pub async fn get_package_info(&mut self, name: &str, version: Option<&str>) -> UmbraResult<PackageInfo> {
        let cache_key = format!("{}:{}", name, version.unwrap_or("latest"));

        // Check cache first
        if let Some(cached) = self.cache.get_package_info(&cache_key) {
            return Ok(cached.info.clone());
        }
        
        let path = if let Some(v) = version {
            format!("/packages/{name}/{v}")
        } else {
            format!("/packages/{name}")
        };
        
        let response = self.client.get(&path, None).await?;
        
        if response.status == 200 {
            let package_info: PackageInfo = serde_json::from_str(&response.body)?;
            
            // Cache the result
            self.cache.cache_package_info(&cache_key, &package_info);
            
            Ok(package_info)
        } else if response.status == 404 {
            Err(crate::error::UmbraError::CodeGen(
                format!("Package '{name}' not found")
            ))
        } else {
            Err(crate::error::UmbraError::CodeGen(
                format!("Failed to get package info: status {}", response.status)
            ))
        }
    }
    
    /// Download package
    pub async fn download_package(&self, name: &str, version: &str) -> UmbraResult<Vec<u8>> {
        println!("📥 Downloading package '{name}' version '{version}'...");
        
        let path = format!("/packages/{name}/{version}/download");
        let response = self.client.get(&path, None).await?;
        
        if response.status == 200 {
            // For simplicity, assume response body contains base64 encoded package data
            let package_data = base64_decode(&response.body)?;
            println!("✅ Package downloaded successfully ({} bytes)", package_data.len());
            Ok(package_data)
        } else {
            Err(crate::error::UmbraError::CodeGen(
                format!("Download failed with status {}: {}", response.status, response.body)
            ))
        }
    }
    
    /// List package versions
    pub async fn list_versions(&self, name: &str) -> UmbraResult<Vec<String>> {
        let path = format!("/packages/{name}/versions");
        let response = self.client.get(&path, None).await?;
        
        if response.status == 200 {
            let versions: Vec<String> = serde_json::from_str(&response.body)?;
            Ok(versions)
        } else {
            Err(crate::error::UmbraError::CodeGen(
                format!("Failed to list versions: status {}", response.status)
            ))
        }
    }
    
    /// Check if package exists
    pub fn package_exists(&self, name: &str, version: Option<&str>) -> bool {
        // For now, assume package exists (in real implementation, this would be async)
        true
    }
    
    /// Get registry statistics
    pub async fn get_stats(&self) -> UmbraResult<RegistryStats> {
        let response = self.client.get("/stats", None).await?;
        
        if response.status == 200 {
            let stats: RegistryStats = serde_json::from_str(&response.body)?;
            Ok(stats)
        } else {
            Err(crate::error::UmbraError::CodeGen(
                "Failed to get registry statistics".to_string()
            ))
        }
    }
}

/// Registry statistics
#[derive(Debug, Deserialize)]
pub struct RegistryStats {
    /// Total number of packages
    pub total_packages: usize,
    
    /// Total number of versions
    pub total_versions: usize,
    
    /// Total downloads
    pub total_downloads: u64,
    
    /// Recent packages
    pub recent_packages: Vec<String>,
}

impl RegistryClient {
    /// Create new registry client
    pub fn new(base_url: &str) -> UmbraResult<Self> {
        let http_client = reqwest::Client::builder()
            .timeout(std::time::Duration::from_secs(30))
            .user_agent(format!("umbra-cli/{}", env!("CARGO_PKG_VERSION")))
            .build()
            .map_err(|e| crate::error::UmbraError::Runtime(format!("Failed to create HTTP client: {}", e)))?;

        Ok(Self {
            api_base: format!("{base_url}/api/v1"),
            timeout: 30,
            user_agent: format!("umbra-cli/{}", env!("CARGO_PKG_VERSION")),
            http_client,
        })
    }
    
    /// Send GET request
    pub async fn get(&self, path: &str, auth_token: Option<&String>) -> UmbraResult<RegistryResponse> {
        let url = format!("{}{}", self.api_base, path);

        let mut request = self.http_client.get(&url);

        if let Some(token) = auth_token {
            request = request.header("Authorization", format!("Bearer {}", token));
        }

        match request.send().await {
            Ok(response) => {
                let status = response.status();
                let body = response.text().await
                    .map_err(|e| crate::error::UmbraError::Runtime(format!("Failed to read response: {}", e)))?;

                if status.is_success() {
                    Ok(RegistryResponse {
                        status: status.as_u16(),
                        body,
                        headers: std::collections::HashMap::new(),
                    })
                } else {
                    Err(crate::error::UmbraError::Runtime(format!("HTTP {} {}: {}", status.as_u16(), status.canonical_reason().unwrap_or("Unknown"), body)))
                }
            }
            Err(e) => {
                // Fallback to mock response for development
                println!("🌐 GET {} (using mock - network error: {})", url, e);
                self.mock_response(path, "GET")
            }
        }
    }
    
    /// Send POST request
    pub async fn post<T: Serialize>(&self, path: &str, body: &T, auth_token: &Option<String>) -> UmbraResult<RegistryResponse> {
        let url = format!("{}{}", self.api_base, path);
        let body_json = serde_json::to_string(body)
            .map_err(|e| crate::error::UmbraError::Runtime(format!("Failed to serialize request body: {}", e)))?;

        let mut request = self.http_client.post(&url)
            .header("Content-Type", "application/json")
            .body(body_json.clone());

        if let Some(token) = auth_token {
            request = request.header("Authorization", format!("Bearer {}", token));
        }

        match request.send().await {
            Ok(response) => {
                let status = response.status();
                let response_body = response.text().await
                    .map_err(|e| crate::error::UmbraError::Runtime(format!("Failed to read response: {}", e)))?;

                if status.is_success() {
                    Ok(RegistryResponse {
                        status: status.as_u16(),
                        body: response_body,
                        headers: std::collections::HashMap::new(),
                    })
                } else {
                    Err(crate::error::UmbraError::Runtime(format!("HTTP {} {}: {}", status.as_u16(), status.canonical_reason().unwrap_or("Unknown"), response_body)))
                }
            }
            Err(e) => {
                // Fallback to mock response for development
                println!("🌐 POST {} (using mock - network error: {})", url, e);
                self.mock_response(path, "POST")
            }
        }
    }
    
    /// Mock HTTP response for development
    fn mock_response(&self, path: &str, method: &str) -> UmbraResult<RegistryResponse> {
        // Simulate different responses based on path
        match (method, path) {
            ("POST", "/packages/upload") => Ok(RegistryResponse {
                status: 201,
                headers: HashMap::new(),
                body: serde_json::to_string(&UploadResponse {
                    package_id: uuid::Uuid::new_v4().to_string(),
                    status: "success".to_string(),
                    message: "Package uploaded successfully".to_string(),
                    package_url: "https://registry.umbra-lang.org/packages/example/1.0.0".to_string(),
                })?,
            }),
            ("GET", path) if path.starts_with("/packages/search") => Ok(RegistryResponse {
                status: 200,
                headers: HashMap::new(),
                body: serde_json::to_string(&SearchResult {
                    packages: vec![],
                    total: 0,
                    page: 1,
                    per_page: 20,
                })?,
            }),
            ("GET", path) if path.starts_with("/packages/") => Ok(RegistryResponse {
                status: 404,
                headers: HashMap::new(),
                body: "Package not found".to_string(),
            }),
            _ => Ok(RegistryResponse {
                status: 200,
                headers: HashMap::new(),
                body: "{}".to_string(),
            }),
        }
    }
}

impl RegistryCache {
    /// Create new registry cache
    pub fn new(cache_dir: std::path::PathBuf) -> UmbraResult<Self> {
        std::fs::create_dir_all(&cache_dir)?;

        let mut cache = Self {
            cache_dir,
            package_cache: HashMap::new(),
            cache_ttl: 3600, // 1 hour
        };

        // Load existing cache from disk
        cache.load_cache_from_disk()?;

        Ok(cache)
    }

    /// Load cache from disk
    fn load_cache_from_disk(&mut self) -> UmbraResult<()> {
        let cache_file = self.cache_dir.join("package_cache.json");

        if cache_file.exists() {
            let content = std::fs::read_to_string(&cache_file)?;
            if let Ok(cached_data) = serde_json::from_str::<HashMap<String, CachedPackageInfo>>(&content) {
                self.package_cache = cached_data;
                println!("💾 Loaded {} cached packages from disk", self.package_cache.len());
            }
        }

        Ok(())
    }

    /// Save cache to disk
    fn save_cache_to_disk(&self) -> UmbraResult<()> {
        let cache_file = self.cache_dir.join("package_cache.json");
        let content = serde_json::to_string_pretty(&self.package_cache)?;
        std::fs::write(&cache_file, content)?;
        Ok(())
    }

    /// Get cached package info
    pub fn get_package_info(&self, key: &str) -> Option<&CachedPackageInfo> {
        if let Some(cached) = self.package_cache.get(key) {
            let now = std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs();

            if now - cached.cached_at < self.cache_ttl {
                return Some(cached);
            }
        }
        None
    }

    /// Cache package info
    pub fn cache_package_info(&mut self, key: &str, info: &PackageInfo) {
        let now = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs();

        let cached_info = CachedPackageInfo {
            info: info.clone(),
            cached_at: now,
        };

        self.package_cache.insert(key.to_string(), cached_info);

        // Save to disk
        if let Err(e) = self.save_cache_to_disk() {
            eprintln!("Warning: Failed to save cache to disk: {e}");
        }

        println!("💾 Cached package info for '{key}'");
    }

    /// Get cached search results
    pub fn get_search_results(&self, query: &str, page: usize) -> Option<SearchResult> {
        let cache_key = format!("search:{}:{}", query, page);
        let cache_file = self.cache_dir.join(format!("{}.json", cache_key));

        if cache_file.exists() {
            if let Ok(content) = std::fs::read_to_string(&cache_file) {
                if let Ok(cached_search) = serde_json::from_str::<CachedSearchResult>(&content) {
                    let now = std::time::SystemTime::now()
                        .duration_since(std::time::UNIX_EPOCH)
                        .unwrap()
                        .as_secs();

                    if now - cached_search.cached_at < self.cache_ttl {
                        return Some(cached_search.results);
                    }
                }
            }
        }

        None
    }

    /// Cache search results
    pub fn cache_search_results(&self, query: &str, page: usize, results: &SearchResult) {
        let cache_key = format!("search:{}:{}", query, page);
        let cache_file = self.cache_dir.join(format!("{}.json", cache_key));

        let now = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs();

        let cached_search = CachedSearchResult {
            results: results.clone(),
            cached_at: now,
        };

        if let Ok(content) = serde_json::to_string_pretty(&cached_search) {
            if let Err(e) = std::fs::write(&cache_file, content) {
                eprintln!("Warning: Failed to cache search results: {e}");
            } else {
                println!("💾 Cached search results for '{query}' page {page}");
            }
        }
    }

    /// Clear expired cache entries
    pub fn cleanup_expired(&mut self) -> UmbraResult<()> {
        let now = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs();

        let initial_count = self.package_cache.len();
        self.package_cache.retain(|_, cached| now - cached.cached_at < self.cache_ttl);
        let removed_count = initial_count - self.package_cache.len();

        if removed_count > 0 {
            self.save_cache_to_disk()?;
            println!("🧹 Cleaned up {removed_count} expired cache entries");
        }

        // Clean up search cache files
        if let Ok(entries) = std::fs::read_dir(&self.cache_dir) {
            for entry in entries.flatten() {
                if let Some(name) = entry.file_name().to_str() {
                    if name.starts_with("search:") && name.ends_with(".json") {
                        if let Ok(metadata) = entry.metadata() {
                            if let Ok(modified) = metadata.modified() {
                                if let Ok(duration) = modified.duration_since(std::time::UNIX_EPOCH) {
                                    if now - duration.as_secs() > self.cache_ttl {
                                        let _ = std::fs::remove_file(entry.path());
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        Ok(())
    }

    /// Get cache statistics
    pub fn get_stats(&self) -> CacheStats {
        let now = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs();

        let valid_entries = self.package_cache.values()
            .filter(|cached| now - cached.cached_at < self.cache_ttl)
            .count();

        let expired_entries = self.package_cache.len() - valid_entries;

        CacheStats {
            total_entries: self.package_cache.len(),
            valid_entries,
            expired_entries,
            cache_size_bytes: self.estimate_cache_size(),
        }
    }

    /// Estimate cache size in bytes
    fn estimate_cache_size(&self) -> usize {
        // Rough estimate based on serialized JSON size
        if let Ok(serialized) = serde_json::to_string(&self.package_cache) {
            serialized.len()
        } else {
            0
        }
    }
}

// Utility functions

/// Encode data as base64
fn base64_encode(data: &[u8]) -> String {
    // Simplified base64 encoding (use base64 crate in real implementation)
    format!("base64:{}", data.len())
}

/// Decode base64 data
fn base64_decode(encoded: &str) -> UmbraResult<Vec<u8>> {
    // Simplified base64 decoding
    if let Some(size_str) = encoded.strip_prefix("base64:") {
        let size: usize = size_str.parse().unwrap_or(0);
        Ok(vec![0; size])
    } else {
        Ok(Vec::new())
    }
}

/// Calculate checksum for data
fn calculate_checksum(data: &[u8]) -> String {
    // Use MD5 for simplicity (use SHA256 in production)
    format!("{:x}", md5::compute(data))
}

/// Encode search parameters
fn encode_search_params(request: &SearchRequest) -> String {
    format!("q={}&page={}&per_page={}&sort={}", 
            request.query, request.page, request.per_page, request.sort)
}
