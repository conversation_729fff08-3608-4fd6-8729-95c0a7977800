/// Dependency Resolver for Umbra Packages
/// 
/// Handles dependency resolution, conflict detection, and installation
/// order determination for Umbra packages.

use crate::error::UmbraResult;
use super::version::{Version, VersionConstraint, VersionManager};
use std::collections::{HashMap, HashSet, VecDeque};

/// Dependency resolver
pub struct DependencyResolver {
    /// Version manager for constraint resolution
    version_manager: VersionManager,
    
    /// Resolution cache
    cache: ResolutionCache,
    
    /// Resolver configuration
    config: ResolverConfig,
}

/// Resolver configuration
#[derive(Debug, Clone)]
pub struct ResolverConfig {
    /// Maximum resolution depth
    pub max_depth: usize,
    
    /// Allow pre-release versions
    pub allow_prerelease: bool,
    
    /// Prefer latest versions
    pub prefer_latest: bool,
    
    /// Maximum resolution time in seconds
    pub timeout_seconds: u64,
}

/// Resolution cache
pub struct ResolutionCache {
    /// Cached resolutions
    cache: HashMap<String, CachedResolution>,
    
    /// Cache TTL in seconds
    ttl: u64,
}

/// Cached resolution
#[derive(Debug, Clone)]
pub struct CachedResolution {
    /// Resolved packages
    pub packages: Vec<(String, String)>,
    
    /// Cache timestamp
    pub cached_at: u64,
}

/// Dependency graph node
#[derive(Debug, Clone)]
pub struct DependencyNode {
    /// Package name
    pub name: String,
    
    /// Package version
    pub version: String,
    
    /// Package dependencies
    pub dependencies: HashMap<String, VersionConstraint>,
    
    /// Dependency depth
    pub depth: usize,
}

/// Resolution result
#[derive(Debug)]
pub struct ResolutionResult {
    /// Resolved packages in installation order
    pub packages: Vec<(String, String)>,
    
    /// Resolution conflicts
    pub conflicts: Vec<DependencyConflict>,
    
    /// Resolution warnings
    pub warnings: Vec<String>,
    
    /// Resolution statistics
    pub stats: ResolutionStats,
}

/// Dependency conflict
#[derive(Debug, Clone)]
pub struct DependencyConflict {
    /// Package name
    pub package: String,
    
    /// Conflicting versions
    pub versions: Vec<String>,
    
    /// Packages that require these versions
    pub required_by: Vec<String>,
    
    /// Conflict type
    pub conflict_type: ConflictType,
}

/// Conflict type
#[derive(Debug, Clone)]
pub enum ConflictType {
    /// Version constraint conflict
    VersionConstraint,
    
    /// Circular dependency
    CircularDependency,
    
    /// Missing package
    MissingPackage,
    
    /// Incompatible versions
    IncompatibleVersions,
}

/// Resolution statistics
#[derive(Debug)]
pub struct ResolutionStats {
    /// Total packages resolved
    pub total_packages: usize,
    
    /// Resolution time in milliseconds
    pub resolution_time_ms: u64,
    
    /// Maximum depth reached
    pub max_depth: usize,
    
    /// Cache hits
    pub cache_hits: usize,
}

impl DependencyResolver {
    /// Create new dependency resolver
    pub fn new() -> UmbraResult<Self> {
        Ok(Self {
            version_manager: VersionManager::new()?,
            cache: ResolutionCache::new(),
            config: ResolverConfig::default(),
        })
    }
    
    /// Resolve package dependencies
    pub fn resolve_package(&mut self, name: &str, version: Option<&str>) -> UmbraResult<Vec<(String, String)>> {
        let start_time = std::time::Instant::now();
        
        println!("🔍 Resolving dependencies for package '{name}'...");
        
        // Check cache first
        let cache_key = format!("{}:{}", name, version.unwrap_or("latest"));
        if let Some(cached) = self.cache.get_cached_resolution(&cache_key) {
            println!("💾 Using cached resolution");
            return Ok(cached.packages.clone());
        }
        
        // Build dependency graph
        let mut graph = self.build_dependency_graph(name, version)?;
        
        // Detect conflicts
        let conflicts = self.detect_conflicts(&graph)?;
        if !conflicts.is_empty() {
            return Err(crate::error::UmbraError::CodeGen(
                format!("Dependency conflicts detected: {conflicts:?}")
            ));
        }
        
        // Resolve versions
        let resolved = self.resolve_versions(&mut graph)?;
        
        // Determine installation order
        let installation_order = self.determine_installation_order(&resolved)?;
        
        // Cache result
        self.cache.cache_resolution(&cache_key, &installation_order);
        
        let resolution_time = start_time.elapsed().as_millis() as u64;
        println!("✅ Dependencies resolved in {}ms ({} packages)", 
                 resolution_time, installation_order.len());
        
        Ok(installation_order)
    }
    
    /// Build dependency graph
    fn build_dependency_graph(&self, root_name: &str, root_version: Option<&str>) -> UmbraResult<Vec<DependencyNode>> {
        let mut graph = Vec::new();
        let mut visited = HashSet::new();
        let mut queue = VecDeque::new();
        
        // Add root package
        queue.push_back((root_name.to_string(), root_version.map(String::from), 0));
        
        while let Some((name, version, depth)) = queue.pop_front() {
            if depth > self.config.max_depth {
                continue;
            }
            
            let node_key = format!("{}:{}", name, version.as_deref().unwrap_or("latest"));
            if visited.contains(&node_key) {
                continue;
            }
            visited.insert(node_key);
            
            // Get package information (mock for now)
            let dependencies = self.get_package_dependencies(&name, version.as_deref())?;
            
            let node = DependencyNode {
                name: name.clone(),
                version: version.unwrap_or_else(|| "latest".to_string()),
                dependencies: dependencies.clone(),
                depth,
            };
            
            graph.push(node);
            
            // Add dependencies to queue
            for (dep_name, _constraint) in dependencies {
                queue.push_back((dep_name, None, depth + 1));
            }
        }
        
        Ok(graph)
    }
    
    /// Get package dependencies (mock implementation)
    fn get_package_dependencies(&self, name: &str, _version: Option<&str>) -> UmbraResult<HashMap<String, VersionConstraint>> {
        // Mock dependencies for common packages
        let dependencies = match name {
            "std" => HashMap::new(), // Standard library has no dependencies
            "ai" => {
                let mut deps = HashMap::new();
                deps.insert("std".to_string(), VersionConstraint::Compatible(Version::new(1, 0, 0)));
                deps.insert("math".to_string(), VersionConstraint::Compatible(Version::new(1, 0, 0)));
                deps
            }
            "data" => {
                let mut deps = HashMap::new();
                deps.insert("std".to_string(), VersionConstraint::Compatible(Version::new(1, 0, 0)));
                deps
            }
            "math" => {
                let mut deps = HashMap::new();
                deps.insert("std".to_string(), VersionConstraint::Compatible(Version::new(1, 0, 0)));
                deps
            }
            _ => HashMap::new(),
        };
        
        Ok(dependencies)
    }
    
    /// Detect dependency conflicts
    fn detect_conflicts(&self, graph: &[DependencyNode]) -> UmbraResult<Vec<DependencyConflict>> {
        let mut conflicts = Vec::new();
        let mut package_versions: HashMap<String, Vec<String>> = HashMap::new();
        
        // Collect all versions for each package
        for node in graph {
            package_versions
                .entry(node.name.clone())
                .or_default()
                .push(node.version.clone());
        }
        
        // Check for version conflicts
        for (package, versions) in package_versions {
            let unique_versions: HashSet<_> = versions.iter().collect();
            if unique_versions.len() > 1 {
                conflicts.push(DependencyConflict {
                    package: package.clone(),
                    versions: unique_versions.into_iter().cloned().collect(),
                    required_by: vec!["multiple".to_string()],
                    conflict_type: ConflictType::VersionConstraint,
                });
            }
        }
        
        // Check for circular dependencies
        if let Some(cycle) = self.detect_circular_dependencies(graph) {
            conflicts.push(DependencyConflict {
                package: cycle.join(" -> "),
                versions: vec!["N/A".to_string()],
                required_by: vec!["circular".to_string()],
                conflict_type: ConflictType::CircularDependency,
            });
        }
        
        Ok(conflicts)
    }
    
    /// Detect circular dependencies
    fn detect_circular_dependencies(&self, graph: &[DependencyNode]) -> Option<Vec<String>> {
        // Simple cycle detection using DFS
        let mut visited = HashSet::new();
        let mut rec_stack = HashSet::new();
        
        for node in graph {
            if !visited.contains(&node.name) {
                if let Some(cycle) = self.dfs_cycle_detection(graph, &node.name, &mut visited, &mut rec_stack) {
                    return Some(cycle);
                }
            }
        }
        
        None
    }
    
    /// DFS-based cycle detection
    fn dfs_cycle_detection(
        &self,
        graph: &[DependencyNode],
        current: &str,
        visited: &mut HashSet<String>,
        rec_stack: &mut HashSet<String>,
    ) -> Option<Vec<String>> {
        visited.insert(current.to_string());
        rec_stack.insert(current.to_string());
        
        // Find current node
        if let Some(node) = graph.iter().find(|n| n.name == current) {
            for dep_name in node.dependencies.keys() {
                if !visited.contains(dep_name) {
                    if let Some(cycle) = self.dfs_cycle_detection(graph, dep_name, visited, rec_stack) {
                        return Some(cycle);
                    }
                } else if rec_stack.contains(dep_name) {
                    return Some(vec![current.to_string(), dep_name.clone()]);
                }
            }
        }
        
        rec_stack.remove(current);
        None
    }
    
    /// Resolve versions for all packages
    fn resolve_versions(&self, graph: &mut [DependencyNode]) -> UmbraResult<Vec<DependencyNode>> {
        // For simplicity, just return the graph as-is
        // In a real implementation, this would resolve version constraints
        Ok(graph.to_vec())
    }
    
    /// Determine installation order using topological sort
    fn determine_installation_order(&self, graph: &[DependencyNode]) -> UmbraResult<Vec<(String, String)>> {
        let mut result = Vec::new();
        let mut visited = HashSet::new();
        let mut temp_visited = HashSet::new();
        
        // Topological sort using DFS
        for node in graph {
            if !visited.contains(&node.name) {
                self.topological_sort_visit(
                    graph,
                    &node.name,
                    &mut visited,
                    &mut temp_visited,
                    &mut result,
                )?;
            }
        }
        
        // Reverse to get correct installation order
        result.reverse();
        
        Ok(result)
    }
    
    /// Topological sort visit
    fn topological_sort_visit(
        &self,
        graph: &[DependencyNode],
        current: &str,
        visited: &mut HashSet<String>,
        temp_visited: &mut HashSet<String>,
        result: &mut Vec<(String, String)>,
    ) -> UmbraResult<()> {
        if temp_visited.contains(current) {
            return Err(crate::error::UmbraError::CodeGen(
                "Circular dependency detected".to_string()
            ));
        }
        
        if visited.contains(current) {
            return Ok(());
        }
        
        temp_visited.insert(current.to_string());
        
        // Find current node and visit dependencies first
        if let Some(node) = graph.iter().find(|n| n.name == current) {
            for dep_name in node.dependencies.keys() {
                self.topological_sort_visit(graph, dep_name, visited, temp_visited, result)?;
            }
            
            result.push((node.name.clone(), node.version.clone()));
        }
        
        temp_visited.remove(current);
        visited.insert(current.to_string());
        
        Ok(())
    }
}

impl ResolutionCache {
    /// Create new resolution cache
    pub fn new() -> Self {
        Self {
            cache: HashMap::new(),
            ttl: 3600, // 1 hour
        }
    }
    
    /// Get cached resolution
    pub fn get_cached_resolution(&self, key: &str) -> Option<&CachedResolution> {
        if let Some(cached) = self.cache.get(key) {
            let now = std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs();
            
            if now - cached.cached_at < self.ttl {
                return Some(cached);
            }
        }
        None
    }
    
    /// Cache resolution
    pub fn cache_resolution(&mut self, key: &str, packages: &[(String, String)]) {
        let cached = CachedResolution {
            packages: packages.to_vec(),
            cached_at: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs(),
        };
        
        self.cache.insert(key.to_string(), cached);
    }
    
    /// Clear cache
    pub fn clear_cache(&mut self) {
        self.cache.clear();
    }
}

impl Default for ResolverConfig {
    fn default() -> Self {
        Self {
            max_depth: 10,
            allow_prerelease: false,
            prefer_latest: true,
            timeout_seconds: 30,
        }
    }
}

impl Default for DependencyResolver {
    fn default() -> Self {
        Self::new().unwrap()
    }
}
