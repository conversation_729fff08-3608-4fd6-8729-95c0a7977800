/// Package Publisher for Umbra
/// 
/// Handles package building, validation, and preparation for publishing
/// to the Umbra package registry.

use crate::error::UmbraResult;
use super::PackageMetadata;
use std::collections::HashSet;
use std::path::{Path, PathBuf};
use std::fs;
use serde::{Deserialize, Serialize};

/// Package publisher
pub struct PackagePublisher {
    /// Temporary build directory
    build_dir: PathBuf,
    
    /// Package validation rules
    validator: PackageValidator,
    
    /// Package archiver
    archiver: PackageArchiver,
}

/// Package validator
pub struct PackageValidator {
    /// Maximum package size in bytes
    max_size: u64,
    
    /// Allowed file extensions
    allowed_extensions: HashSet<String>,
    
    /// Forbidden file patterns
    forbidden_patterns: Vec<String>,
}

/// Package archiver
pub struct PackageArchiver {
    /// Compression level (0-9)
    compression_level: u8,
    
    /// Archive format
    format: ArchiveFormat,
}

/// Archive format
#[derive(Debug, Clone)]
pub enum ArchiveFormat {
    /// Tar.gz format
    TarGz,
    
    /// Zip format
    Zip,
    
    /// Tar.xz format
    TarXz,
}

/// Package build result
#[derive(Debug)]
pub struct BuildResult {
    /// Path to built package
    pub package_path: PathBuf,
    
    /// Package size in bytes
    pub size: u64,
    
    /// Package checksum
    pub checksum: String,
    
    /// Build warnings
    pub warnings: Vec<String>,
    
    /// Included files
    pub included_files: Vec<String>,
}

/// Package validation result
#[derive(Debug)]
pub struct ValidationResult {
    /// Validation success
    pub success: bool,
    
    /// Validation errors
    pub errors: Vec<String>,
    
    /// Validation warnings
    pub warnings: Vec<String>,
    
    /// Package statistics
    pub stats: PackageStats,
}

/// Package statistics
#[derive(Debug)]
pub struct PackageStats {
    /// Total files
    pub total_files: usize,
    
    /// Source files
    pub source_files: usize,
    
    /// Test files
    pub test_files: usize,
    
    /// Documentation files
    pub doc_files: usize,
    
    /// Total size in bytes
    pub total_size: u64,
}

impl PackagePublisher {
    /// Create new package publisher
    pub fn new() -> UmbraResult<Self> {
        let build_dir = PathBuf::from(".umbra/build");
        fs::create_dir_all(&build_dir)?;
        
        Ok(Self {
            build_dir,
            validator: PackageValidator::new(),
            archiver: PackageArchiver::new(),
        })
    }
    
    /// Build package for publishing
    pub fn build_package(&self, metadata: &PackageMetadata, source_dir: &Path) -> UmbraResult<PathBuf> {
        println!("🔨 Building package '{}'...", metadata.name);
        
        // Validate package
        let validation = self.validator.validate_package(metadata, source_dir)?;
        if !validation.success {
            return Err(crate::error::UmbraError::CodeGen(
                format!("Package validation failed: {}", validation.errors.join(", "))
            ));
        }
        
        // Print warnings
        for warning in &validation.warnings {
            println!("⚠️  Warning: {warning}");
        }
        
        // Prepare build directory
        let package_build_dir = self.build_dir.join(&metadata.name);
        if package_build_dir.exists() {
            fs::remove_dir_all(&package_build_dir)?;
        }
        fs::create_dir_all(&package_build_dir)?;
        
        // Copy source files
        let included_files = self.copy_package_files(metadata, source_dir, &package_build_dir)?;
        
        // Generate package manifest
        self.generate_package_manifest(metadata, &package_build_dir)?;
        
        // Create package archive
        let package_path = self.archiver.create_archive(
            &package_build_dir,
            &self.build_dir.join(format!("{}-{}.tar.gz", metadata.name, metadata.version))
        )?;
        
        println!("✅ Package built successfully: {}", package_path.display());
        println!("📊 Package statistics:");
        println!("   Files: {}", included_files.len());
        println!("   Size: {} bytes", fs::metadata(&package_path)?.len());
        
        Ok(package_path)
    }
    
    /// Validate package before building
    pub fn validate_package(&self, metadata: &PackageMetadata, source_dir: &Path) -> UmbraResult<ValidationResult> {
        self.validator.validate_package(metadata, source_dir)
    }
    
    /// Copy package files according to include/exclude patterns
    fn copy_package_files(
        &self,
        metadata: &PackageMetadata,
        source_dir: &Path,
        target_dir: &Path,
    ) -> UmbraResult<Vec<String>> {
        let mut included_files = Vec::new();
        
        // Default include patterns if none specified
        let include_patterns = if metadata.include.is_empty() {
            vec!["src/**/*.umbra".to_string(), "README.md".to_string(), "LICENSE".to_string()]
        } else {
            metadata.include.clone()
        };
        
        // Walk through source directory
        for entry in walkdir::WalkDir::new(source_dir) {
            let entry = entry?;
            let path = entry.path();

            if path.is_file() {
                let relative_path = path.strip_prefix(source_dir)?;
                let relative_str = relative_path.to_string_lossy();

                // Check if file should be included
                if self.should_include_file(&relative_str, &include_patterns, &metadata.exclude) {
                    // Copy file to target directory
                    let target_path = target_dir.join(relative_path);

                    // Create parent directories
                    if let Some(parent) = target_path.parent() {
                        fs::create_dir_all(parent)?;
                    }

                    fs::copy(path, &target_path)?;
                    included_files.push(relative_str.to_string());
                }
            }
        }
        
        if included_files.is_empty() {
            return Err(crate::error::UmbraError::CodeGen(
                "No files to include in package".to_string()
            ));
        }
        
        Ok(included_files)
    }
    
    /// Check if file should be included based on patterns
    fn should_include_file(&self, file_path: &str, include: &[String], exclude: &[String]) -> bool {
        // Check exclude patterns first
        for pattern in exclude {
            if self.matches_pattern(file_path, pattern) {
                return false;
            }
        }

        // Check include patterns
        for pattern in include {
            if self.matches_pattern(file_path, pattern) {
                return true;
            }
        }

        false
    }
    
    /// Simple pattern matching (supports * and ** wildcards)
    fn matches_pattern(&self, path: &str, pattern: &str) -> bool {
        if pattern.contains("**") {
            // Handle recursive directory patterns like "src/**/*.umbra"
            // Split pattern into parts around **
            if let Some(star_pos) = pattern.find("**") {
                let prefix = &pattern[..star_pos];
                let suffix = &pattern[star_pos + 2..];

                // Remove leading slash from suffix if present
                let suffix = suffix.strip_prefix('/').unwrap_or(suffix);

                return path.starts_with(prefix) && self.matches_simple_pattern(path, suffix);
            }
        }

        if pattern.contains('*') {
            self.matches_simple_pattern(path, pattern)
        } else {
            path == pattern
        }
    }

    /// Simple glob pattern matching
    fn matches_simple_pattern(&self, path: &str, pattern: &str) -> bool {
        if pattern.contains('*') {
            let parts: Vec<&str> = pattern.split('*').collect();
            if parts.len() == 2 {
                path.starts_with(parts[0]) && path.ends_with(parts[1])
            } else if parts.len() == 3 && parts[1].is_empty() {
                // Handle patterns like "src/**/*.umbra" which becomes ["src/", "", ".umbra"]
                let prefix = parts[0];
                let suffix = parts[2];
                path.starts_with(prefix) && path.ends_with(suffix)
            } else {
                // Multiple wildcards - simplified matching
                let mut current_pos = 0;
                for (i, part) in parts.iter().enumerate() {
                    if part.is_empty() {
                        continue;
                    }

                    if i == 0 {
                        // First part must match from start
                        if !path[current_pos..].starts_with(part) {
                            return false;
                        }
                        current_pos += part.len();
                    } else if i == parts.len() - 1 {
                        // Last part must match at end
                        return path[current_pos..].ends_with(part);
                    } else {
                        // Middle parts must be found
                        if let Some(pos) = path[current_pos..].find(part) {
                            current_pos += pos + part.len();
                        } else {
                            return false;
                        }
                    }
                }
                true
            }
        } else {
            path == pattern
        }
    }
    
    /// Generate package manifest file
    fn generate_package_manifest(&self, metadata: &PackageMetadata, target_dir: &Path) -> UmbraResult<()> {
        let manifest_path = target_dir.join("PACKAGE_MANIFEST.json");
        
        let manifest = PackageManifest {
            name: metadata.name.clone(),
            version: metadata.version.clone(),
            description: metadata.description.clone(),
            authors: metadata.authors.clone(),
            license: metadata.license.clone(),
            dependencies: metadata.dependencies.clone(),
            umbra_version: metadata.umbra_version.clone(),
            build_time: chrono::Utc::now().to_rfc3339(),
            checksum: "".to_string(), // Will be calculated later
        };
        
        let manifest_json = serde_json::to_string_pretty(&manifest)?;
        fs::write(manifest_path, manifest_json)?;
        
        Ok(())
    }
}

/// Package manifest for distribution
#[derive(Debug, Serialize, Deserialize)]
pub struct PackageManifest {
    /// Package name
    pub name: String,
    
    /// Package version
    pub version: String,
    
    /// Package description
    pub description: String,
    
    /// Package authors
    pub authors: Vec<String>,
    
    /// Package license
    pub license: String,
    
    /// Package dependencies
    pub dependencies: std::collections::HashMap<String, String>,
    
    /// Required Umbra version
    pub umbra_version: String,
    
    /// Build timestamp
    pub build_time: String,
    
    /// Package checksum
    pub checksum: String,
}

impl PackageValidator {
    /// Create new package validator
    pub fn new() -> Self {
        let mut allowed_extensions = HashSet::new();
        allowed_extensions.insert("umbra".to_string());
        allowed_extensions.insert("md".to_string());
        allowed_extensions.insert("txt".to_string());
        allowed_extensions.insert("toml".to_string());
        allowed_extensions.insert("json".to_string());
        allowed_extensions.insert("yaml".to_string());
        allowed_extensions.insert("yml".to_string());
        
        Self {
            max_size: 100 * 1024 * 1024, // 100MB
            allowed_extensions,
            forbidden_patterns: vec![
                "target/**".to_string(),
                ".git/**".to_string(),
                "*.tmp".to_string(),
                "*.log".to_string(),
            ],
        }
    }
    
    /// Validate package
    pub fn validate_package(&self, metadata: &PackageMetadata, source_dir: &Path) -> UmbraResult<ValidationResult> {
        let mut errors = Vec::new();
        let mut warnings = Vec::new();
        let mut stats = PackageStats {
            total_files: 0,
            source_files: 0,
            test_files: 0,
            doc_files: 0,
            total_size: 0,
        };
        
        // Validate metadata
        if metadata.name.is_empty() {
            errors.push("Package name cannot be empty".to_string());
        }
        
        if metadata.version.is_empty() {
            errors.push("Package version cannot be empty".to_string());
        }
        
        if metadata.description.is_empty() {
            warnings.push("Package description is empty".to_string());
        }
        
        if metadata.authors.is_empty() {
            warnings.push("No authors specified".to_string());
        }
        
        // Validate source directory
        if !source_dir.exists() {
            errors.push("Source directory does not exist".to_string());
            return Ok(ValidationResult {
                success: false,
                errors,
                warnings,
                stats,
            });
        }
        
        let src_dir = source_dir.join("src");
        if !src_dir.exists() {
            errors.push("Package must contain a 'src' directory".to_string());
        }
        
        // Validate files
        for entry in walkdir::WalkDir::new(source_dir) {
            let entry = entry?;
            let path = entry.path();
            
            if path.is_file() {
                stats.total_files += 1;
                
                let file_size = fs::metadata(path)?.len();
                stats.total_size += file_size;
                
                let relative_path = path.strip_prefix(source_dir)?;
                let path_str = relative_path.to_string_lossy();
                
                // Check file extension
                if let Some(extension) = path.extension() {
                    let ext_str = extension.to_string_lossy().to_lowercase();
                    
                    if ext_str == "umbra" {
                        stats.source_files += 1;
                    } else if path_str.contains("test") {
                        stats.test_files += 1;
                    } else if ext_str == "md" || ext_str == "txt" {
                        stats.doc_files += 1;
                    }
                    
                    if !self.allowed_extensions.contains(&ext_str) {
                        warnings.push(format!("Unusual file extension: {path_str}"));
                    }
                }
                
                // Check forbidden patterns
                for pattern in &self.forbidden_patterns {
                    if path_str.contains(&pattern.replace("**", "")) {
                        warnings.push(format!("File matches forbidden pattern '{pattern}': {path_str}"));
                    }
                }
            }
        }
        
        // Check package size
        if stats.total_size > self.max_size {
            errors.push(format!("Package size ({} bytes) exceeds maximum allowed size ({} bytes)", 
                               stats.total_size, self.max_size));
        }
        
        // Check for required files
        let readme_exists = source_dir.join("README.md").exists() || source_dir.join("README.txt").exists();
        if !readme_exists {
            warnings.push("No README file found".to_string());
        }
        
        let license_exists = source_dir.join("LICENSE").exists() || source_dir.join("LICENSE.txt").exists();
        if !license_exists {
            warnings.push("No LICENSE file found".to_string());
        }
        
        Ok(ValidationResult {
            success: errors.is_empty(),
            errors,
            warnings,
            stats,
        })
    }
}

impl PackageArchiver {
    /// Create new package archiver
    pub fn new() -> Self {
        Self {
            compression_level: 6,
            format: ArchiveFormat::TarGz,
        }
    }
    
    /// Create package archive
    pub fn create_archive(&self, source_dir: &Path, output_path: &Path) -> UmbraResult<PathBuf> {
        println!("📦 Creating package archive...");
        
        match self.format {
            ArchiveFormat::TarGz => self.create_tar_gz(source_dir, output_path),
            ArchiveFormat::Zip => self.create_zip(source_dir, output_path),
            ArchiveFormat::TarXz => self.create_tar_xz(source_dir, output_path),
        }
    }
    
    /// Create tar.gz archive
    fn create_tar_gz(&self, source_dir: &Path, output_path: &Path) -> UmbraResult<PathBuf> {
        // For simplicity, just copy the directory structure
        // In a real implementation, use tar crate
        
        let mut total_size = 0u64;
        let mut file_count = 0usize;
        
        for entry in walkdir::WalkDir::new(source_dir) {
            let entry = entry?;
            if entry.path().is_file() {
                total_size += fs::metadata(entry.path())?.len();
                file_count += 1;
            }
        }
        
        // Create a dummy archive file
        fs::write(output_path, format!("UMBRA_PACKAGE:{file_count}:{total_size}"))?;
        
        println!("✅ Archive created: {file_count} files, {total_size} bytes");
        Ok(output_path.to_path_buf())
    }
    
    /// Create zip archive
    fn create_zip(&self, source_dir: &Path, output_path: &Path) -> UmbraResult<PathBuf> {
        // Similar to tar.gz but with zip format
        self.create_tar_gz(source_dir, output_path)
    }
    
    /// Create tar.xz archive
    fn create_tar_xz(&self, source_dir: &Path, output_path: &Path) -> UmbraResult<PathBuf> {
        // Similar to tar.gz but with xz compression
        self.create_tar_gz(source_dir, output_path)
    }
}

impl Default for PackagePublisher {
    fn default() -> Self {
        Self::new().unwrap()
    }
}
