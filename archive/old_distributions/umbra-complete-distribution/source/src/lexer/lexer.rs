use logos::<PERSON><PERSON>;

use super::token::{Token, TokenType};
use crate::error::{SourceLocation, UmbraError, UmbraResult};

/// Lexer for the Umbra programming language
pub struct Lexer {
    input: String,
    tokens: Vec<Token>,
    current_line: usize,
    current_column: usize,
    indent_stack: Vec<usize>,
    brace_depth: usize, // Track brace nesting depth
}

impl Lexer {
    pub fn new(input: String) -> Self {
        Self {
            input,
            tokens: Vec::new(),
            current_line: 1,
            current_column: 1,
            indent_stack: vec![0], // Start with zero indentation
            brace_depth: 0,
        }
    }

    pub fn tokenize(&mut self) -> UmbraResult<Vec<Token>> {
        let input_copy = self.input.clone();
        let lines: Vec<&str> = input_copy.lines().collect();

        for (line_idx, line) in lines.iter().enumerate() {
            self.current_line = line_idx + 1;
            self.current_column = 1;

            // Handle indentation at the beginning of non-empty lines (but not inside braces)
            if !line.trim().is_empty() && !line.trim_start().starts_with("//") && self.brace_depth == 0 {
                self.handle_indentation(line)?;
            }

            // Tokenize the line content
            self.tokenize_line(line)?;

            // Add newline token for non-empty lines
            if !line.trim().is_empty() {
                self.add_token(TokenType::Newline, "\n".to_string());
            }
        }

        // Close any remaining indentation levels
        while self.indent_stack.len() > 1 {
            self.indent_stack.pop();
            self.add_token(TokenType::Dedent, String::new());
        }

        // Add EOF token
        self.add_token(TokenType::Eof, String::new());

        Ok(std::mem::take(&mut self.tokens))
    }

    fn handle_indentation(&mut self, line: &str) -> UmbraResult<()> {
        let indent_level = line.len() - line.trim_start().len();
        let current_indent = *self.indent_stack.last().unwrap();

        if indent_level > current_indent {
            // Increased indentation
            self.indent_stack.push(indent_level);
            self.add_token(TokenType::Indent, String::new());
        } else if indent_level < current_indent {
            // Decreased indentation - may need multiple dedents
            while let Some(&stack_indent) = self.indent_stack.last() {
                if stack_indent <= indent_level {
                    break;
                }
                self.indent_stack.pop();
                self.add_token(TokenType::Dedent, String::new());
            }

            // Check for indentation error
            if self.indent_stack.last() != Some(&indent_level) {
                return Err(UmbraError::Lexical {
                    message: "Inconsistent indentation".to_string(),
                    line: self.current_line,
                    column: 1,
                });
            }
        }

        Ok(())
    }

    fn tokenize_line(&mut self, line: &str) -> UmbraResult<()> {
        let mut lexer = TokenType::lexer(line);
        let mut char_indices: Vec<_> = line.char_indices().collect();
        char_indices.push((line.len(), '\0')); // Add sentinel

        while let Some(token_type) = lexer.next() {
            let span = lexer.span();
            let lexeme = lexer.slice().to_string();

            // Calculate column position
            let start_byte = span.start;
            let column = char_indices
                .iter()
                .position(|(byte_idx, _)| *byte_idx >= start_byte)
                .unwrap_or(0)
                + 1;

            self.current_column = column;

            match token_type {
                Ok(token_type) => {
                    // Skip comments
                    if matches!(token_type, TokenType::Comment) {
                        break; // Rest of line is comment
                    }

                    // Handle special tokens that need the lexeme
                    let final_token_type = match token_type {
                        TokenType::Identifier => {
                            // Check if it's actually a keyword that wasn't caught
                            match lexeme.as_str() {
                                "define" => TokenType::Define,
                                "let" => TokenType::Let,
                                "bring" => TokenType::Bring,
                                "structure" => TokenType::Structure,
                                "when" => TokenType::When,
                                "otherwise" => TokenType::Otherwise,
                                "repeat" => TokenType::Repeat,
                                "in" => TokenType::In,
                                "return" => TokenType::Return,
                                "train" => TokenType::Train,
                                "using" => TokenType::Using,
                                "evaluate" => TokenType::Evaluate,
                                "on" => TokenType::On,
                                "visualize" => TokenType::Visualize,
                                "over" => TokenType::Over,
                                "export" => TokenType::Export,
                                "to" => TokenType::To,
                                "predict" => TokenType::Predict,
                                "and" => TokenType::And,
                                "or" => TokenType::Or,
                                "not" => TokenType::Not,
                                "true" => TokenType::True,
                                "false" => TokenType::False,
                                "Integer" => TokenType::IntegerType,
                                "Float" => TokenType::FloatType,
                                "String" => TokenType::StringType,
                                "Boolean" => TokenType::BooleanType,
                                "Void" => TokenType::VoidType,
                                "List" => TokenType::ListType,
                                "Dataset" => TokenType::DatasetType,
                                "Model" => TokenType::ModelType,
                                "Tensor" => TokenType::TensorType,
                                "auto" => TokenType::AutoType,
                                _ => TokenType::Identifier,
                            }
                        }
                        _ => token_type,
                    };

                    self.add_token(final_token_type, lexeme);
                }
                Err(_) => {
                    return Err(UmbraError::Lexical {
                        message: format!("Unexpected character: '{lexeme}'"),
                        line: self.current_line,
                        column,
                    });
                }
            }
        }

        Ok(())
    }

    fn add_token(&mut self, token_type: TokenType, lexeme: String) {
        // Track brace depth for indentation handling
        match token_type {
            TokenType::LeftBrace => self.brace_depth += 1,
            TokenType::RightBrace => {
                if self.brace_depth > 0 {
                    self.brace_depth -= 1;
                }
            }
            _ => {}
        }

        let location = SourceLocation::new(self.current_line, self.current_column);
        self.tokens.push(Token::new(token_type, lexeme, location));
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_basic_tokens() {
        let input = "let x: Integer := 42".to_string();
        let mut lexer = Lexer::new(input);
        let tokens = lexer.tokenize().unwrap();

        // Expected tokens: let, x, :, Integer, :=, 42, newline, eof = 8 tokens
        assert_eq!(tokens.len(), 8);
        assert_eq!(tokens[0].token_type, TokenType::Let);
        assert_eq!(tokens[1].token_type, TokenType::Identifier);
        assert_eq!(tokens[2].token_type, TokenType::Colon);
        assert_eq!(tokens[3].token_type, TokenType::IntegerType);
        assert_eq!(tokens[4].token_type, TokenType::Assign);
        assert!(matches!(tokens[5].token_type, TokenType::Integer(42)));
        // tokens[6] should be newline and tokens[7] should be EOF
    }

    #[test]
    fn test_indentation() {
        let input = "define main() -> Void:\n    show(\"hello\")".to_string();
        let mut lexer = Lexer::new(input);
        let tokens = lexer.tokenize().unwrap();

        // Should contain INDENT and DEDENT tokens
        let indent_count = tokens
            .iter()
            .filter(|t| t.token_type == TokenType::Indent)
            .count();
        let dedent_count = tokens
            .iter()
            .filter(|t| t.token_type == TokenType::Dedent)
            .count();

        assert_eq!(indent_count, 1);
        assert_eq!(dedent_count, 1);
    }

    #[test]
    fn test_ai_keywords() {
        let input = "train model using dataset".to_string();
        let mut lexer = Lexer::new(input);
        let tokens = lexer.tokenize().unwrap();

        assert_eq!(tokens[0].token_type, TokenType::Train);
        assert_eq!(tokens[1].token_type, TokenType::Identifier); // model
        assert_eq!(tokens[2].token_type, TokenType::Using);
        assert_eq!(tokens[3].token_type, TokenType::Identifier); // dataset
    }
}
