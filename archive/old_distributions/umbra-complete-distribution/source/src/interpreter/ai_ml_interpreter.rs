use crate::runtime::{
    umbra_load_dataset, umbra_create_model, umbra_train_model,
    umbra_evaluate_model, umbra_predict_sample, umbra_visualize_metric
};
use crate::runtime::RuntimeValue;
use crate::error::{UmbraError, UmbraResult};
use std::collections::HashMap;

/// Handle load_dataset function call
pub fn handle_load_dataset(args: &[RuntimeValue]) -> UmbraResult<RuntimeValue> {
    if args.len() != 1 {
        return Err(UmbraError::Runtime("load_dataset expects 1 argument".to_string()));
    }
    
    match &args[0] {
        RuntimeValue::String(file_path) => umbra_load_dataset(file_path),
        _ => Err(UmbraError::Runtime("load_dataset expects string argument".to_string()))
    }
}

/// Handle create_model function call
pub fn handle_create_model(args: &[RuntimeValue]) -> UmbraResult<RuntimeValue> {
    if args.len() != 1 {
        return Err(UmbraError::Runtime("create_model expects 1 argument".to_string()));
    }
    
    match &args[0] {
        RuntimeValue::String(model_type) => umbra_create_model(model_type),
        _ => Err(UmbraError::Runtime("create_model expects string argument".to_string()))
    }
}

/// Handle train statement
pub fn handle_train_statement(
    model: &RuntimeValue,
    dataset: &RuntimeValue,
    config: &HashMap<String, RuntimeValue>
) -> UmbraResult<RuntimeValue> {
    umbra_train_model(model, dataset, config)
}

/// Handle evaluate statement
pub fn handle_evaluate_statement(
    model: &RuntimeValue,
    dataset: &RuntimeValue
) -> UmbraResult<RuntimeValue> {
    umbra_evaluate_model(model, dataset)
}

/// Handle predict statement
pub fn handle_predict_statement(
    sample: &RuntimeValue,
    model: &RuntimeValue
) -> UmbraResult<RuntimeValue> {
    match sample {
        RuntimeValue::String(sample_str) => umbra_predict_sample(sample_str, model),
        _ => Err(UmbraError::Runtime("predict expects string sample".to_string()))
    }
}

/// Handle visualize statement
pub fn handle_visualize_statement(
    metric: &RuntimeValue,
    dimension: &RuntimeValue
) -> UmbraResult<RuntimeValue> {
    match (metric, dimension) {
        (RuntimeValue::String(metric_str), RuntimeValue::String(dim_str)) => {
            umbra_visualize_metric(metric_str, dim_str)
        },
        _ => Err(UmbraError::Runtime("visualize expects string arguments".to_string()))
    }
}