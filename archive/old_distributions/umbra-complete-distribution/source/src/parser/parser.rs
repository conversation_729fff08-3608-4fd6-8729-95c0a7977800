use super::ast::*;
use crate::error::{SourceLocation, UmbraError, UmbraResult};
use crate::lexer::{Token, TokenType};
use crate::semantic::symbol_table::StructureField;

/// Recursive descent parser for Umbra
pub struct Parser {
    tokens: Vec<Token>,
    current: usize,
    current_type_parameters: Vec<String>, // Track current generic type parameters
}

impl Parser {
    pub fn new(tokens: Vec<Token>) -> Self {
        Self {
            tokens,
            current: 0,
            current_type_parameters: Vec::new(),
        }
    }

    pub fn parse(&mut self) -> UmbraResult<Program> {
        let mut statements = Vec::new();
        let start_location = self.current_location();

        while !self.is_at_end() {
            // Skip newlines at the top level
            if self.check(&TokenType::Newline) {
                self.advance();
                continue;
            }

            statements.push(self.statement()?);
        }

        Ok(Program {
            statements,
            location: start_location,
        })
    }

    fn statement(&mut self) -> UmbraResult<Statement> {
        match &self.peek().token_type {
            TokenType::Module => self.module_declaration(),
            TokenType::Import => self.new_import_statement(),
            TokenType::Export => self.new_export_statement(),
            TokenType::Bring => self.import_statement(), // Legacy support
            TokenType::Public | TokenType::Private => self.visibility_statement(),
            // Error handling statements
            TokenType::Try => self.try_statement(),
            TokenType::Throw => self.throw_statement(),
            TokenType::Panic => self.panic_statement(),
            TokenType::Error => self.error_definition(),
            TokenType::Define => self.function_definition(),
            TokenType::Fn => self.function_definition(),
            TokenType::Structure => self.structure_definition(),
            TokenType::Struct => self.structure_definition(),
            TokenType::Trait => self.trait_definition(),
            TokenType::Impl => self.impl_block(),
            TokenType::Let => self.variable_declaration(),
            TokenType::When => self.when_statement(),
            TokenType::Match => self.match_statement(),
            TokenType::Repeat => self.repeat_statement(),
            TokenType::Return => self.return_statement(),
            TokenType::Train => self.train_statement(),
            TokenType::Evaluate => self.evaluate_statement(),
            TokenType::Visualize => self.visualize_statement(),
            TokenType::Predict => self.predict_statement(),
            TokenType::Identifier => {
                // Could be assignment or expression statement
                if self.check_ahead(&TokenType::Assign)
                    || self.check_ahead(&TokenType::PlusAssign)
                    || self.check_ahead(&TokenType::MinusAssign)
                    || self.check_ahead(&TokenType::MultiplyAssign)
                    || self.check_ahead(&TokenType::DivideAssign)
                {
                    self.assignment()
                } else {
                    self.expression_statement()
                }
            }
            _ => self.expression_statement(),
        }
    }

    fn import_statement(&mut self) -> UmbraResult<Statement> {
        let location = self.current_location();
        self.consume(&TokenType::Bring, "Expected 'bring'")?;

        // Parse module path with dots (e.g., std.collections)
        let mut module_parts = Vec::new();
        module_parts.push(self.consume_identifier("Expected module name")?);

        while self.match_token(&TokenType::Dot) {
            module_parts.push(self.consume_identifier("Expected module name after '.'")?);
        }

        // Optional semicolon
        if self.check(&TokenType::Semicolon) {
            self.advance();
        }

        self.consume_newline()?;

        let module_path = module_parts.join(".");
        Ok(Statement::Import(ImportStatement {
            import_type: ImportType::Module(ModulePath::from_string(&module_path)),
            location,
        }))
    }

    fn function_definition(&mut self) -> UmbraResult<Statement> {
        let location = self.current_location();

        // Accept either 'define' or 'fn'
        if self.check(&TokenType::Define) {
            self.consume(&TokenType::Define, "Expected 'define'")?;
        } else if self.check(&TokenType::Fn) {
            self.consume(&TokenType::Fn, "Expected 'fn'")?;
        } else {
            return Err(UmbraError::Parse {
                message: "Expected 'define' or 'fn'".to_string(),
                line: self.current_location().line,
                column: self.current_location().column,
            });
        }
        let name = self.consume_identifier("Expected function name")?;

        // Parse optional type parameters <T: Bound, U: Bound + Clone, ...>
        let type_params = if self.match_token(&TokenType::Less) {
            self.parse_type_parameters()?
        } else {
            Vec::new()
        };

        // Set current type parameters for parsing parameter and return types
        self.current_type_parameters = type_params.iter().map(|p| p.name.clone()).collect();

        self.consume(&TokenType::LeftParen, "Expected '(' after function name")?;
        let parameters = self.parameter_list()?;
        self.consume(&TokenType::RightParen, "Expected ')' after parameters")?;

        // Handle optional return type
        let return_type = if self.match_token(&TokenType::Arrow) {
            self.type_annotation()?
        } else {
            Type::Basic(BasicType::Void)
        };

        // Parse optional where clauses
        let constraints = if self.match_token(&TokenType::Where) {
            self.parse_where_clauses()?
        } else {
            Vec::new()
        };

        // Handle both Umbra syntax (with :) and Rust-like syntax (without :)
        if self.check(&TokenType::Colon) {
            self.consume(&TokenType::Colon, "Expected ':' after return type")?;
        }

        let body = self.block()?;

        // Clear type parameters after parsing function
        self.current_type_parameters.clear();

        Ok(Statement::Function(FunctionDef {
            visibility: Visibility::default(),
            name,
            type_params,
            parameters,
            return_type,
            body,
            constraints,
            location,
        }))
    }

    fn function_definition_with_visibility(&mut self, visibility: Visibility) -> UmbraResult<Statement> {
        let location = self.current_location();
        self.consume(&TokenType::Define, "Expected 'define'")?;
        let name = self.consume_identifier("Expected function name")?;

        // Parse optional type parameters <T: Bound, U: Bound + Clone, ...>
        let type_params = if self.match_token(&TokenType::Less) {
            self.parse_type_parameters()?
        } else {
            Vec::new()
        };

        // Set current type parameters for parsing parameter and return types
        self.current_type_parameters = type_params.iter().map(|p| p.name.clone()).collect();

        self.consume(&TokenType::LeftParen, "Expected '(' after function name")?;
        let parameters = self.parameter_list()?;
        self.consume(&TokenType::RightParen, "Expected ')' after parameters")?;

        // Handle optional return type
        let return_type = if self.match_token(&TokenType::Arrow) {
            self.type_annotation()?
        } else {
            Type::Basic(BasicType::Void)
        };

        // Parse optional where clauses
        let constraints = if self.match_token(&TokenType::Where) {
            self.parse_where_clauses()?
        } else {
            Vec::new()
        };

        // Handle both Umbra syntax (with :) and Rust-like syntax (without :)
        if self.check(&TokenType::Colon) {
            self.consume(&TokenType::Colon, "Expected ':' after return type")?;
        }

        let body = self.block()?;

        // Clear type parameters after parsing function
        self.current_type_parameters.clear();

        Ok(Statement::Function(FunctionDef {
            visibility,
            name,
            type_params,
            parameters,
            return_type,
            body,
            constraints,
            location,
        }))
    }

    fn parameter_list(&mut self) -> UmbraResult<Vec<Parameter>> {
        let mut parameters = Vec::new();

        if !self.check(&TokenType::RightParen) {
            loop {
                let location = self.current_location();

                // Handle 'self' parameter
                let (name, type_annotation) = if self.check(&TokenType::SelfKeyword) {
                    self.advance();
                    let name = "self".to_string();

                    // Check if there's a type annotation for self
                    let type_annotation = if self.match_token(&TokenType::Colon) {
                        self.type_annotation()?
                    } else {
                        // Default self type for trait methods
                        Type::SelfType
                    };

                    (name, type_annotation)
                } else {
                    let name = self.consume_identifier("Expected parameter name")?;
                    self.consume(&TokenType::Colon, "Expected ':' after parameter name")?;
                    let type_annotation = self.type_annotation()?;
                    (name, type_annotation)
                };

                parameters.push(Parameter {
                    name,
                    type_annotation,
                    location,
                });

                if !self.match_token(&TokenType::Comma) {
                    break;
                }
            }
        }

        Ok(parameters)
    }

    fn module_declaration(&mut self) -> UmbraResult<Statement> {
        let location = self.current_location();
        self.consume(&TokenType::Module, "Expected 'module'")?;
        let module_path = self.parse_module_path()?;

        Ok(Statement::Module(ModuleDeclaration {
            name: module_path,
            location,
        }))
    }

    fn new_import_statement(&mut self) -> UmbraResult<Statement> {
        let location = self.current_location();
        self.consume(&TokenType::Import, "Expected 'import'")?;

        let import_type = if self.match_token(&TokenType::LeftBrace) {
            // import { symbol1, symbol2 } from module_path
            let mut items = Vec::new();
            items.push(self.parse_import_item()?);
            while self.match_token(&TokenType::Comma) {
                items.push(self.parse_import_item()?);
            }
            self.consume(&TokenType::RightBrace, "Expected '}'")?;
            self.consume(&TokenType::From, "Expected 'from'")?;
            let module_path = self.parse_module_path()?;
            ImportType::Multiple(module_path, items)
        } else {
            let module_path = self.parse_module_path()?;
            if self.match_token(&TokenType::As) {
                // import module_path as alias
                let alias = self.consume_identifier("Expected alias name")?;
                ImportType::ModuleAs(module_path, alias)
            } else {
                // import module_path
                ImportType::Module(module_path)
            }
        };

        Ok(Statement::Import(ImportStatement {
            import_type,
            location,
        }))
    }

    fn new_export_statement(&mut self) -> UmbraResult<Statement> {
        let location = self.current_location();
        self.consume(&TokenType::Export, "Expected 'export'")?;

        let export_type = if self.match_token(&TokenType::LeftBrace) {
            // export { symbol1, symbol2 }
            let mut symbols = Vec::new();
            symbols.push(self.consume_identifier("Expected symbol name")?);
            while self.match_token(&TokenType::Comma) {
                symbols.push(self.consume_identifier("Expected symbol name")?);
            }
            self.consume(&TokenType::RightBrace, "Expected '}'")?;

            if self.match_token(&TokenType::From) {
                // export { symbol } from module_path
                let module_path = self.parse_module_path()?;
                if symbols.len() == 1 {
                    ExportType::ReExportSymbol(module_path, symbols[0].clone())
                } else {
                    return Err(UmbraError::Parse {
                        message: "Multiple symbol re-export not yet supported".to_string(),
                        line: location.line,
                        column: location.column,
                    });
                }
            } else {
                ExportType::Multiple(symbols)
            }
        } else if self.match_token(&TokenType::Multiply) {
            // export * from module_path
            self.consume(&TokenType::From, "Expected 'from'")?;
            let module_path = self.parse_module_path()?;
            ExportType::ReExportWildcard(module_path)
        } else {
            // export symbol_name
            let symbol = self.consume_identifier("Expected symbol name")?;
            // Determine if it's a function, struct, or variable based on context
            // For now, we'll default to function
            ExportType::Function(symbol)
        };

        Ok(Statement::Export(ExportStatement {
            export_type,
            location,
        }))
    }

    fn visibility_statement(&mut self) -> UmbraResult<Statement> {
        let visibility = if self.match_token(&TokenType::Public) {
            Visibility::Public
        } else if self.match_token(&TokenType::Private) {
            Visibility::Private
        } else {
            return Err(UmbraError::Parse {
                message: "Expected 'public' or 'private'".to_string(),
                line: self.current_location().line,
                column: self.current_location().column,
            });
        };

        // Parse the following statement with the visibility modifier
        match &self.peek().token_type {
            TokenType::Define => self.function_definition_with_visibility(visibility),
            TokenType::Structure => self.structure_definition_with_visibility(visibility),
            TokenType::Trait => self.trait_definition_with_visibility(visibility),
            TokenType::Impl => self.impl_block_with_visibility(visibility),
            TokenType::Error => self.error_definition_with_visibility(visibility),
            _ => Err(UmbraError::Parse {
                message: "Visibility modifiers can only be applied to functions, structures, traits, implementations, and errors".to_string(),
                line: self.current_location().line,
                column: self.current_location().column,
            }),
        }
    }

    fn parse_module_path(&mut self) -> UmbraResult<ModulePath> {
        let mut segments = Vec::new();
        segments.push(self.consume_identifier("Expected module name")?);

        while self.match_token(&TokenType::DoubleColon) {
            segments.push(self.consume_identifier("Expected module name")?);
        }

        Ok(ModulePath::new(segments))
    }

    fn parse_import_item(&mut self) -> UmbraResult<ImportItem> {
        let name = self.consume_identifier("Expected symbol name")?;
        let alias = if self.match_token(&TokenType::As) {
            Some(self.consume_identifier("Expected alias name")?)
        } else {
            None
        };
        Ok(ImportItem { name, alias })
    }

    fn structure_definition(&mut self) -> UmbraResult<Statement> {
        let location = self.current_location();
        self.consume(&TokenType::Structure, "Expected 'structure'")?;
        let name = self.consume_identifier("Expected structure name")?;

        // Parse optional type parameters <T, U, ...>
        let type_parameters = if self.match_token(&TokenType::Less) {
            let mut params = Vec::new();
            params.push(self.consume_identifier("Expected type parameter name")?);
            while self.match_token(&TokenType::Comma) {
                params.push(self.consume_identifier("Expected type parameter name")?);
            }
            self.consume(&TokenType::Greater, "Expected '>' after type parameters")?;
            params
        } else {
            Vec::new()
        };

        self.consume(&TokenType::Colon, "Expected ':' after structure name")?;
        self.consume(&TokenType::Newline, "Expected newline after ':'")?;

        let fields = self.field_list()?;

        Ok(Statement::Structure(StructureDef {
            visibility: Visibility::default(),
            name,
            type_parameters,
            fields,
            annotations: vec![], // TODO: Parse annotations
            location,
        }))
    }

    fn structure_definition_with_visibility(&mut self, visibility: Visibility) -> UmbraResult<Statement> {
        let location = self.current_location();
        self.consume(&TokenType::Structure, "Expected 'structure'")?;
        let name = self.consume_identifier("Expected structure name")?;

        // Parse optional type parameters <T, U, ...>
        let type_parameters = if self.match_token(&TokenType::Less) {
            let mut params = Vec::new();
            params.push(self.consume_identifier("Expected type parameter name")?);
            while self.match_token(&TokenType::Comma) {
                params.push(self.consume_identifier("Expected type parameter name")?);
            }
            self.consume(&TokenType::Greater, "Expected '>' after type parameters")?;
            params
        } else {
            Vec::new()
        };

        self.consume(&TokenType::Colon, "Expected ':' after structure name")?;
        self.consume(&TokenType::Newline, "Expected newline after ':'")?;

        let fields = self.field_list()?;

        Ok(Statement::Structure(StructureDef {
            visibility,
            name,
            type_parameters,
            fields,
            annotations: vec![], // TODO: Parse annotations
            location,
        }))
    }

    fn field_list(&mut self) -> UmbraResult<Vec<Field>> {
        let mut fields = Vec::new();

        self.consume(&TokenType::Indent, "Expected indentation")?;

        while !self.check(&TokenType::Dedent) && !self.is_at_end() {
            if self.check(&TokenType::Newline) {
                self.advance();
                continue;
            }

            let location = self.current_location();
            let name = self.consume_identifier("Expected field name")?;
            self.consume(&TokenType::Colon, "Expected ':' after field name")?;
            let type_annotation = self.type_annotation()?;

            fields.push(Field {
                name,
                field_type: type_annotation,
                annotations: vec![], // TODO: Parse field annotations
                location,
            });

            if !self.check(&TokenType::Dedent) {
                self.consume_newline()?;
            }
        }

        self.consume(&TokenType::Dedent, "Expected dedent after fields")?;

        Ok(fields)
    }

    fn trait_definition(&mut self) -> UmbraResult<Statement> {
        self.trait_definition_with_visibility(Visibility::default())
    }

    fn trait_definition_with_visibility(&mut self, visibility: Visibility) -> UmbraResult<Statement> {
        let location = self.current_location();
        self.consume(&TokenType::Trait, "Expected 'trait'")?;
        let name = self.consume_identifier("Expected trait name")?;

        // Parse optional type parameters <T: Bound, U: Bound + Clone, ...>
        let type_params = if self.match_token(&TokenType::Less) {
            self.parse_type_parameters()?
        } else {
            Vec::new()
        };

        // Parse optional supertraits: trait MyTrait: Clone + Send + Sync
        let supertraits = if self.check(&TokenType::Colon) && !self.check_ahead_for_newline() {
            self.advance(); // consume ':'
            self.parse_supertrait_list()?
        } else {
            Vec::new()
        };

        self.consume(&TokenType::Colon, "Expected ':' after trait declaration")?;
        self.consume(&TokenType::Newline, "Expected newline after ':'")?;

        // Parse trait body
        let (methods, associated_types) = self.parse_trait_body()?;

        Ok(Statement::Trait(TraitDef {
            visibility,
            name,
            type_params,
            supertraits,
            methods,
            associated_types,
            location,
        }))
    }

    fn parse_supertrait_list(&mut self) -> UmbraResult<Vec<String>> {
        let mut supertraits = Vec::new();

        supertraits.push(self.consume_identifier("Expected supertrait name")?);

        while self.match_token(&TokenType::Plus) {
            supertraits.push(self.consume_identifier("Expected supertrait name")?);
        }

        Ok(supertraits)
    }

    fn parse_trait_body(&mut self) -> UmbraResult<(Vec<TraitMethod>, Vec<AssociatedType>)> {
        let mut methods = Vec::new();
        let mut associated_types = Vec::new();

        self.consume(&TokenType::Indent, "Expected indentation")?;

        while !self.check(&TokenType::Dedent) && !self.is_at_end() {
            if self.check(&TokenType::Newline) {
                self.advance();
                continue;
            }

            if self.check(&TokenType::Type) {
                // Associated type
                associated_types.push(self.parse_associated_type()?);
            } else if self.check(&TokenType::Define) || self.check(&TokenType::Fn) {
                // Trait method
                methods.push(self.parse_trait_method()?);
            } else {
                return Err(UmbraError::Parse {
                    message: "Expected method or associated type in trait body".to_string(),
                    line: self.current_location().line,
                    column: self.current_location().column,
                });
            }

            if !self.check(&TokenType::Dedent) {
                self.consume_newline()?;
            }
        }

        self.consume(&TokenType::Dedent, "Expected dedent after trait body")?;

        Ok((methods, associated_types))
    }

    fn parse_associated_type(&mut self) -> UmbraResult<AssociatedType> {
        let location = self.current_location();
        self.consume(&TokenType::Type, "Expected 'type'")?;
        let name = self.consume_identifier("Expected associated type name")?;

        // Parse optional bounds
        let bounds = if self.match_token(&TokenType::Colon) {
            self.parse_type_bounds()?
        } else {
            Vec::new()
        };

        // Parse optional default
        let default = if self.match_token(&TokenType::Equal) {
            Some(self.type_annotation()?)
        } else {
            None
        };

        Ok(AssociatedType {
            name,
            bounds,
            default,
            location,
        })
    }

    fn parse_trait_method(&mut self) -> UmbraResult<TraitMethod> {
        let location = self.current_location();
        self.advance(); // consume 'define' or 'fn'

        let name = self.consume_identifier("Expected method name")?;

        // Parse optional type parameters
        let type_params = if self.match_token(&TokenType::Less) {
            self.parse_type_parameters()?
        } else {
            Vec::new()
        };

        self.consume(&TokenType::LeftParen, "Expected '(' after method name")?;
        let parameters = self.parameter_list()?;
        self.consume(&TokenType::RightParen, "Expected ')' after parameters")?;

        // Handle optional return type
        let return_type = if self.match_token(&TokenType::Arrow) {
            self.type_annotation()?
        } else {
            Type::Basic(BasicType::Void)
        };

        // Parse optional default implementation
        let default_body = if self.match_token(&TokenType::Colon) {
            self.consume(&TokenType::Newline, "Expected newline after ':'")?;
            Some(self.block()?)
        } else {
            None
        };

        Ok(TraitMethod {
            name,
            type_params,
            parameters,
            return_type,
            default_body,
            location,
        })
    }

    fn impl_block(&mut self) -> UmbraResult<Statement> {
        self.impl_block_with_visibility(Visibility::default())
    }

    fn impl_block_with_visibility(&mut self, visibility: Visibility) -> UmbraResult<Statement> {
        let location = self.current_location();
        self.consume(&TokenType::Impl, "Expected 'impl'")?;

        // Parse optional generic parameters
        let type_params = if self.match_token(&TokenType::Less) {
            self.parse_type_parameters()?
        } else {
            Vec::new()
        };

        // Parse trait name (optional) and implementing type
        let (trait_name, implementing_type) = if self.check_ahead_for_for_keyword() {
            // impl<T> TraitName for TypeName
            let trait_name = self.consume_identifier("Expected trait name")?;
            self.consume(&TokenType::For, "Expected 'for' after trait name")?;
            let implementing_type = self.type_annotation()?;
            (Some(trait_name), implementing_type)
        } else {
            // impl<T> TypeName (inherent impl)
            let implementing_type = self.type_annotation()?;
            (None, implementing_type)
        };

        // Parse optional where clauses
        let where_clauses = if self.match_token(&TokenType::Where) {
            self.parse_where_clauses()?
        } else {
            Vec::new()
        };

        self.consume(&TokenType::Colon, "Expected ':' after impl declaration")?;
        self.consume(&TokenType::Newline, "Expected newline after ':'")?;

        // Parse impl body
        let (methods, associated_types) = self.parse_impl_body()?;

        Ok(Statement::Implementation(ImplDef {
            visibility,
            type_params,
            trait_name,
            implementing_type,
            where_clauses,
            methods,
            associated_types,
            location,
        }))
    }

    fn check_ahead_for_for_keyword(&self) -> bool {
        // Look ahead to see if there's a 'for' keyword after the next identifier
        let mut lookahead = 1;
        while lookahead < self.tokens.len() - self.current {
            match &self.tokens[self.current + lookahead].token_type {
                TokenType::For => return true,
                TokenType::Identifier => lookahead += 1,
                TokenType::Less | TokenType::Greater | TokenType::Comma => lookahead += 1,
                _ => return false,
            }
        }
        false
    }

    fn check_ahead_for_newline(&self) -> bool {
        // Look ahead to see if there's a newline after the current token
        if self.current + 1 < self.tokens.len() {
            matches!(self.tokens[self.current + 1].token_type, TokenType::Newline)
        } else {
            false
        }
    }

    fn parse_impl_body(&mut self) -> UmbraResult<(Vec<FunctionDef>, Vec<TypeAssociation>)> {
        let mut methods = Vec::new();
        let mut associated_types = Vec::new();

        self.consume(&TokenType::Indent, "Expected indentation")?;

        while !self.check(&TokenType::Dedent) && !self.is_at_end() {
            if self.check(&TokenType::Newline) {
                self.advance();
                continue;
            }

            if self.check(&TokenType::Type) {
                // Associated type implementation
                associated_types.push(self.parse_type_association()?);
            } else if self.check(&TokenType::Define) || self.check(&TokenType::Fn) {
                // Method implementation
                if let Statement::Function(func) = self.function_definition()? {
                    methods.push(func);
                } else {
                    return Err(UmbraError::Parse {
                        message: "Expected function definition in impl block".to_string(),
                        line: self.current_location().line,
                        column: self.current_location().column,
                    });
                }
            } else {
                return Err(UmbraError::Parse {
                    message: "Expected method or associated type in impl body".to_string(),
                    line: self.current_location().line,
                    column: self.current_location().column,
                });
            }

            if !self.check(&TokenType::Dedent) {
                self.consume_newline()?;
            }
        }

        self.consume(&TokenType::Dedent, "Expected dedent after impl body")?;

        Ok((methods, associated_types))
    }

    fn parse_type_association(&mut self) -> UmbraResult<TypeAssociation> {
        let location = self.current_location();
        self.consume(&TokenType::Type, "Expected 'type'")?;
        let name = self.consume_identifier("Expected associated type name")?;
        self.consume(&TokenType::Equal, "Expected '=' after associated type name")?;
        let type_value = self.type_annotation()?;

        Ok(TypeAssociation {
            name,
            type_value,
            location,
        })
    }

    fn variable_declaration(&mut self) -> UmbraResult<Statement> {
        let location = self.current_location();
        self.consume(&TokenType::Let, "Expected 'let'")?;

        // Check for optional 'mut' keyword
        let _is_mutable = if self.match_token(&TokenType::Mut) {
            true
        } else {
            false
        };

        let name = self.consume_identifier("Expected variable name")?;
        self.consume(&TokenType::Colon, "Expected ':' after variable name")?;
        let type_annotation = self.type_annotation()?;
        self.consume(&TokenType::Assign, "Expected ':=' after type")?;
        let value = self.expression()?;
        self.consume_newline()?;

        Ok(Statement::Variable(VariableDecl {
            name,
            type_annotation,
            value,
            location,
        }))
    }

    fn assignment(&mut self) -> UmbraResult<Statement> {
        let location = self.current_location();
        let name = self.consume_identifier("Expected variable name")?;

        // Parse assignment operator
        let operator = if self.match_token(&TokenType::Assign) {
            AssignmentOperator::Assign
        } else if self.match_token(&TokenType::PlusAssign) {
            AssignmentOperator::AddAssign
        } else if self.match_token(&TokenType::MinusAssign) {
            AssignmentOperator::SubAssign
        } else if self.match_token(&TokenType::MultiplyAssign) {
            AssignmentOperator::MulAssign
        } else if self.match_token(&TokenType::DivideAssign) {
            AssignmentOperator::DivAssign
        } else {
            return Err(UmbraError::Parse {
                message: "Expected assignment operator (:=, +=, -=, *=, /=)".to_string(),
                line: self.current_location().line,
                column: self.current_location().column,
            });
        };

        let value = self.expression()?;
        self.consume_newline()?;

        Ok(Statement::Assignment(Assignment {
            name,
            operator,
            value,
            location,
        }))
    }

    fn when_statement(&mut self) -> UmbraResult<Statement> {
        let location = self.current_location();
        self.consume(&TokenType::When, "Expected 'when'")?;
        let condition = self.expression()?;
        self.consume(&TokenType::Colon, "Expected ':' after condition")?;

        let then_body = self.block()?;

        let else_body = if self.match_token(&TokenType::Otherwise) {
            self.consume(&TokenType::Colon, "Expected ':' after 'otherwise'")?;
            Some(self.block()?)
        } else {
            None
        };

        Ok(Statement::When(WhenStatement {
            condition,
            then_body,
            else_body,
            location,
        }))
    }

    fn match_statement(&mut self) -> UmbraResult<Statement> {
        let location = self.current_location();
        self.consume(&TokenType::Match, "Expected 'match'")?;
        let expression = self.expression()?;
        self.consume(&TokenType::Colon, "Expected ':' after match expression")?;
        self.consume(&TokenType::Newline, "Expected newline after ':'")?;

        let arms = self.match_arms()?;

        Ok(Statement::Expression(ExpressionStatement {
            expression: Expression::Match(MatchExpression {
                expression: Box::new(expression),
                arms,
                location,
            }),
            location,
        }))
    }

    fn match_arms(&mut self) -> UmbraResult<Vec<MatchArm>> {
        let mut arms = Vec::new();

        self.consume(&TokenType::Indent, "Expected indentation")?;

        while !self.check(&TokenType::Dedent) && !self.is_at_end() {
            if self.check(&TokenType::Newline) {
                self.advance();
                continue;
            }

            let location = self.current_location();
            self.consume(&TokenType::Case, "Expected 'case'")?;
            let pattern = self.parse_pattern()?;
            self.consume(&TokenType::Colon, "Expected ':' after pattern")?;
            self.consume(&TokenType::Newline, "Expected newline after ':'")?;

            let body_statements = self.block()?;
            let body = if body_statements.len() == 1 {
                match &body_statements[0] {
                    Statement::Expression(expr_stmt) => expr_stmt.expression.clone(),
                    _ => Expression::Literal(Literal::Integer(0)), // Placeholder
                }
            } else {
                Expression::Literal(Literal::Integer(0)) // Placeholder for multiple statements
            };

            arms.push(MatchArm {
                pattern,
                guard: None,
                body,
                location,
            });
        }

        self.consume(&TokenType::Dedent, "Expected dedent after match arms")?;

        Ok(arms)
    }

    fn parse_pattern(&mut self) -> UmbraResult<Pattern> {
        match &self.peek().token_type {
            TokenType::Some => {
                self.advance(); // consume 'Some'
                self.consume(&TokenType::LeftParen, "Expected '(' after 'Some'")?;
                let inner_pattern = self.parse_pattern()?;
                self.consume(&TokenType::RightParen, "Expected ')' after Some pattern")?;
                Ok(Pattern::Some(Box::new(inner_pattern)))
            }
            TokenType::None => {
                self.advance(); // consume 'None'
                Ok(Pattern::None)
            }
            TokenType::Ok => {
                self.advance(); // consume 'Ok'
                self.consume(&TokenType::LeftParen, "Expected '(' after 'Ok'")?;
                let inner_pattern = self.parse_pattern()?;
                self.consume(&TokenType::RightParen, "Expected ')' after Ok pattern")?;
                Ok(Pattern::Ok(Box::new(inner_pattern)))
            }
            TokenType::Err => {
                self.advance(); // consume 'Err'
                self.consume(&TokenType::LeftParen, "Expected '(' after 'Err'")?;
                let inner_pattern = self.parse_pattern()?;
                self.consume(&TokenType::RightParen, "Expected ')' after Err pattern")?;
                Ok(Pattern::Err(Box::new(inner_pattern)))
            }
            TokenType::Identifier => {
                let name = self.consume_identifier("Expected identifier")?;
                Ok(Pattern::Identifier(name))
            }
            TokenType::Integer(n) => {
                let n = *n;
                self.advance();
                Ok(Pattern::Literal(Literal::Integer(n)))
            }
            TokenType::String(s) => {
                let s = s.clone();
                self.advance();
                Ok(Pattern::Literal(Literal::String(s)))
            }
            TokenType::True => {
                self.advance();
                Ok(Pattern::Literal(Literal::Boolean(true)))
            }
            TokenType::False => {
                self.advance();
                Ok(Pattern::Literal(Literal::Boolean(false)))
            }
            _ => {
                Err(UmbraError::Parse {
                    message: format!("Unexpected token in pattern: {}", self.peek().token_type),
                    line: self.peek().location.line,
                    column: self.peek().location.column,
                })
            }
        }
    }

    fn repeat_statement(&mut self) -> UmbraResult<Statement> {
        let location = self.current_location();
        self.consume(&TokenType::Repeat, "Expected 'repeat'")?;
        let variable = self.consume_identifier("Expected variable name")?;
        self.consume(&TokenType::In, "Expected 'in' after variable")?;
        let iterable = self.expression()?;
        self.consume(&TokenType::Colon, "Expected ':' after iterable")?;
        self.consume(&TokenType::Newline, "Expected newline after ':'")?;

        let body = self.block()?;

        Ok(Statement::Repeat(RepeatStatement {
            variable,
            iterable,
            body,
            location,
        }))
    }

    fn return_statement(&mut self) -> UmbraResult<Statement> {
        let location = self.current_location();
        self.consume(&TokenType::Return, "Expected 'return'")?;

        let value = if self.check(&TokenType::Newline) {
            None
        } else {
            Some(self.expression()?)
        };

        self.consume_newline()?;

        Ok(Statement::Return(ReturnStatement { value, location }))
    }

    fn expression_statement(&mut self) -> UmbraResult<Statement> {
        let location = self.current_location();
        let expression = self.expression()?;
        self.consume_newline()?;

        Ok(Statement::Expression(ExpressionStatement {
            expression,
            location,
        }))
    }

    // AI/ML specific statements
    fn train_statement(&mut self) -> UmbraResult<Statement> {
        let location = self.current_location();
        self.consume(&TokenType::Train, "Expected 'train'")?;
        let model = self.consume_identifier("Expected model name")?;
        self.consume(&TokenType::Using, "Expected 'using' after model")?;
        let dataset = self.consume_identifier("Expected dataset name")?;
        self.consume(&TokenType::Colon, "Expected ':' after dataset")?;
        self.consume(&TokenType::Newline, "Expected newline after ':'")?;

        let config = self.train_config()?;

        Ok(Statement::Train(TrainStatement {
            model,
            dataset,
            config,
            location,
        }))
    }

    fn train_config(&mut self) -> UmbraResult<Vec<TrainParameter>> {
        let mut config = Vec::new();

        self.consume(&TokenType::Indent, "Expected indentation")?;

        while !self.check(&TokenType::Dedent) && !self.is_at_end() {
            if self.check(&TokenType::Newline) {
                self.advance();
                continue;
            }

            let location = self.current_location();
            let name = self.consume_identifier("Expected parameter name")?;
            self.consume(&TokenType::Assign, "Expected ':=' after parameter name")?;
            let value = self.expression()?;

            config.push(TrainParameter {
                name,
                value,
                location,
            });

            if !self.check(&TokenType::Dedent) {
                self.consume_newline()?;
            }
        }

        self.consume(&TokenType::Dedent, "Expected dedent after config")?;

        Ok(config)
    }

    fn evaluate_statement(&mut self) -> UmbraResult<Statement> {
        let location = self.current_location();
        self.consume(&TokenType::Evaluate, "Expected 'evaluate'")?;
        let model = self.consume_identifier("Expected model name")?;
        self.consume(&TokenType::On, "Expected 'on' after model")?;
        let dataset = self.consume_identifier("Expected dataset name")?;
        self.consume_newline()?;

        Ok(Statement::Evaluate(EvaluateStatement {
            model,
            dataset,
            location,
        }))
    }

    fn visualize_statement(&mut self) -> UmbraResult<Statement> {
        let location = self.current_location();
        self.consume(&TokenType::Visualize, "Expected 'visualize'")?;
        let metric = self.consume_identifier("Expected metric name")?;
        self.consume(&TokenType::Over, "Expected 'over' after metric")?;
        let dimension = self.consume_identifier("Expected dimension name")?;
        self.consume_newline()?;

        Ok(Statement::Visualize(VisualizeStatement {
            metric,
            dimension,
            location,
        }))
    }

    fn export_statement(&mut self) -> UmbraResult<Statement> {
        let location = self.current_location();
        self.consume(&TokenType::Export, "Expected 'export'")?;
        let model = self.consume_identifier("Expected model name")?;
        self.consume(&TokenType::To, "Expected 'to' after model")?;
        let _path = self.consume_string("Expected file path")?;
        self.consume_newline()?;

        // Legacy export statement - convert to new format
        Ok(Statement::Export(ExportStatement {
            export_type: ExportType::Function(model),
            location,
        }))
    }

    fn predict_statement(&mut self) -> UmbraResult<Statement> {
        let location = self.current_location();
        self.consume(&TokenType::Predict, "Expected 'predict'")?;
        let sample = if self.check(&TokenType::Identifier) {
            self.consume_identifier("Expected sample name")?
        } else {
            self.consume_string("Expected sample")?
        };
        self.consume(&TokenType::Using, "Expected 'using' after sample")?;
        let model = self.consume_identifier("Expected model name")?;
        self.consume_newline()?;

        Ok(Statement::Predict(PredictStatement {
            sample,
            model,
            location,
        }))
    }

    // Expression parsing with operator precedence
    fn expression(&mut self) -> UmbraResult<Expression> {
        self.logical_or()
    }

    fn logical_or(&mut self) -> UmbraResult<Expression> {
        let mut expr = self.logical_and()?;

        while self.match_token(&TokenType::Or) {
            let location = self.previous().location;
            let right = self.logical_and()?;
            expr = Expression::Binary(BinaryOp {
                left: Box::new(expr),
                operator: BinaryOperator::Or,
                right: Box::new(right),
                location,
            });
        }

        Ok(expr)
    }

    fn logical_and(&mut self) -> UmbraResult<Expression> {
        let mut expr = self.equality()?;

        while self.match_token(&TokenType::And) {
            let location = self.previous().location;
            let right = self.equality()?;
            expr = Expression::Binary(BinaryOp {
                left: Box::new(expr),
                operator: BinaryOperator::And,
                right: Box::new(right),
                location,
            });
        }

        Ok(expr)
    }

    fn equality(&mut self) -> UmbraResult<Expression> {
        let mut expr = self.comparison()?;

        while self.match_tokens(&[TokenType::Equal, TokenType::NotEqual]) {
            let operator = match self.previous().token_type {
                TokenType::Equal => BinaryOperator::Equal,
                TokenType::NotEqual => BinaryOperator::NotEqual,
                _ => unreachable!(),
            };
            let location = self.previous().location;
            let right = self.comparison()?;
            expr = Expression::Binary(BinaryOp {
                left: Box::new(expr),
                operator,
                right: Box::new(right),
                location,
            });
        }

        Ok(expr)
    }

    fn comparison(&mut self) -> UmbraResult<Expression> {
        let mut expr = self.addition()?;

        while self.match_tokens(&[
            TokenType::Greater,
            TokenType::GreaterEqual,
            TokenType::Less,
            TokenType::LessEqual,
        ]) {
            let operator = match self.previous().token_type {
                TokenType::Greater => BinaryOperator::Greater,
                TokenType::GreaterEqual => BinaryOperator::GreaterEqual,
                TokenType::Less => BinaryOperator::Less,
                TokenType::LessEqual => BinaryOperator::LessEqual,
                _ => unreachable!(),
            };
            let location = self.previous().location;
            let right = self.addition()?;
            expr = Expression::Binary(BinaryOp {
                left: Box::new(expr),
                operator,
                right: Box::new(right),
                location,
            });
        }

        Ok(expr)
    }

    fn addition(&mut self) -> UmbraResult<Expression> {
        let mut expr = self.multiplication()?;

        while self.match_tokens(&[TokenType::Plus, TokenType::Minus]) {
            let operator = match self.previous().token_type {
                TokenType::Plus => BinaryOperator::Add,
                TokenType::Minus => BinaryOperator::Subtract,
                _ => unreachable!(),
            };
            let location = self.previous().location;
            let right = self.multiplication()?;
            expr = Expression::Binary(BinaryOp {
                left: Box::new(expr),
                operator,
                right: Box::new(right),
                location,
            });
        }

        Ok(expr)
    }

    fn multiplication(&mut self) -> UmbraResult<Expression> {
        let mut expr = self.unary()?;

        while self.match_tokens(&[TokenType::Multiply, TokenType::Divide, TokenType::Modulo]) {
            let operator = match self.previous().token_type {
                TokenType::Multiply => BinaryOperator::Multiply,
                TokenType::Divide => BinaryOperator::Divide,
                TokenType::Modulo => BinaryOperator::Modulo,
                _ => unreachable!(),
            };
            let location = self.previous().location;
            let right = self.unary()?;
            expr = Expression::Binary(BinaryOp {
                left: Box::new(expr),
                operator,
                right: Box::new(right),
                location,
            });
        }

        Ok(expr)
    }

    fn unary(&mut self) -> UmbraResult<Expression> {
        if self.match_tokens(&[TokenType::Not, TokenType::Minus]) {
            let operator = match self.previous().token_type {
                TokenType::Not => UnaryOperator::Not,
                TokenType::Minus => UnaryOperator::Minus,
                _ => unreachable!(),
            };
            let location = self.previous().location;
            let right = self.unary()?;
            return Ok(Expression::Unary(UnaryOp {
                operator,
                operand: Box::new(right),
                location,
            }));
        }

        self.primary()
    }

    fn primary(&mut self) -> UmbraResult<Expression> {
        let mut expr = self.atom()?;

        // Handle field access (object.field) and index access (object[index])
        loop {
            if self.match_token(&TokenType::Dot) {
                let location = self.previous().location;
                let name = self.consume_identifier("Expected method or field name after '.'")?;

                // Check if this is a method call (has parentheses) or field access
                if self.check(&TokenType::LeftParen) {
                    // Method call: object.method(args)
                    self.advance(); // consume '('
                    let arguments = self.argument_list()?;
                    self.consume(&TokenType::RightParen, "Expected ')' after method arguments")?;
                    expr = Expression::MethodCall(MethodCall {
                        object: Box::new(expr),
                        method_name: name,
                        arguments,
                        location,
                    });
                } else {
                    // Field access: object.field
                    expr = Expression::FieldAccess(FieldAccess {
                        object: Box::new(expr),
                        field: name,
                        location,
                    });
                }
            } else if self.match_token(&TokenType::LeftBracket) {
                let location = self.previous().location;
                let index = self.expression()?;
                self.consume(&TokenType::RightBracket, "Expected ']' after index")?;
                expr = Expression::IndexAccess(IndexAccess {
                    object: Box::new(expr),
                    index: Box::new(index),
                    location,
                });
            } else if self.match_token(&TokenType::Question) {
                // Error propagation operator (?)
                let location = self.previous().location;
                expr = Expression::ErrorPropagation(ErrorPropagationExpression {
                    expression: Box::new(expr),
                    location,
                });
            } else {
                break;
            }
        }

        Ok(expr)
    }

    fn atom(&mut self) -> UmbraResult<Expression> {
        match &self.peek().token_type {
            TokenType::True => {
                self.advance();
                Ok(Expression::Literal(Literal::Boolean(true)))
            }
            TokenType::False => {
                self.advance();
                Ok(Expression::Literal(Literal::Boolean(false)))
            }
            TokenType::Integer(n) => {
                let n = *n;
                self.advance();
                Ok(Expression::Literal(Literal::Integer(n)))
            }
            TokenType::Float(n) => {
                let n = *n;
                self.advance();
                Ok(Expression::Literal(Literal::Float(n)))
            }
            TokenType::String(s) => {
                let s = s.clone();
                self.advance();
                Ok(Expression::Literal(Literal::String(s)))
            }
            TokenType::Some => {
                let location = self.current_location();
                self.advance(); // consume 'Some'
                self.consume(&TokenType::LeftParen, "Expected '(' after 'Some'")?;
                let value = self.expression()?;
                self.consume(&TokenType::RightParen, "Expected ')' after Some value")?;
                Ok(Expression::Some(SomeExpression {
                    value: Box::new(value),
                    location,
                }))
            }
            TokenType::None => {
                let location = self.current_location();
                self.advance(); // consume 'None'
                Ok(Expression::None(NoneExpression { location }))
            }
            TokenType::Ok => {
                let location = self.current_location();
                self.advance(); // consume 'Ok'
                self.consume(&TokenType::LeftParen, "Expected '(' after 'Ok'")?;
                let value = self.expression()?;
                self.consume(&TokenType::RightParen, "Expected ')' after Ok value")?;
                Ok(Expression::Ok(OkExpression {
                    value: Box::new(value),
                    location,
                }))
            }
            TokenType::Err => {
                let location = self.current_location();
                self.advance(); // consume 'Err'
                self.consume(&TokenType::LeftParen, "Expected '(' after 'Err'")?;
                let error = self.expression()?;
                self.consume(&TokenType::RightParen, "Expected ')' after Err value")?;
                Ok(Expression::Err(ErrExpression {
                    error: Box::new(error),
                    location,
                }))
            }
            TokenType::Try => {
                let location = self.current_location();
                self.advance(); // consume 'try'
                let expression = self.expression()?;
                Ok(Expression::TryExpression(TryExpression {
                    expression: Box::new(expression),
                    location,
                }))
            }
            TokenType::Identifier => {
                let location = self.current_location();
                let name = self.consume_identifier("Expected identifier")?;

                // Check if this is a qualified identifier (has ::)
                if self.check(&TokenType::DoubleColon) {
                    let mut segments = vec![name];
                    while self.match_token(&TokenType::DoubleColon) {
                        segments.push(self.consume_identifier("Expected identifier after '::'")?);
                    }
                    let path = ModulePath::new(segments);

                    // Check for function call
                    if self.match_token(&TokenType::LeftParen) {
                        let arguments = self.argument_list()?;
                        self.consume(&TokenType::RightParen, "Expected ')' after arguments")?;
                        Ok(Expression::Call(FunctionCall {
                            name: path.to_string(),
                            arguments,
                            location,
                        }))
                    } else {
                        Ok(Expression::QualifiedIdentifier(QualifiedIdentifier {
                            path,
                            location,
                        }))
                    }
                } else {
                    // Regular identifier
                    // Check for macro call (identifier!)
                    if self.match_token(&TokenType::Exclamation) {
                        self.consume(&TokenType::LeftParen, "Expected '(' after '!' in macro call")?;
                        let arguments = self.argument_list()?;
                        self.consume(&TokenType::RightParen, "Expected ')' after macro arguments")?;
                        Ok(Expression::Call(FunctionCall {
                            name: format!("{}!", name), // Add ! to the name to distinguish macros
                            arguments,
                            location,
                        }))
                    }
                    // Check for function call
                    else if self.match_token(&TokenType::LeftParen) {
                        let arguments = self.argument_list()?;
                        self.consume(&TokenType::RightParen, "Expected ')' after arguments")?;
                        Ok(Expression::Call(FunctionCall {
                            name,
                            arguments,
                            location,
                        }))
                    }
                    // Check for struct literal
                    else if self.match_token(&TokenType::LeftBrace) {
                        let fields = self.struct_field_list()?;
                        self.consume(&TokenType::RightBrace, "Expected '}' after struct fields")?;
                        Ok(Expression::Struct(StructLiteral {
                            struct_name: name,
                            fields,
                            location,
                        }))
                    } else {
                        Ok(Expression::Identifier(Identifier { name, location }))
                    }
                }
            }
            TokenType::LeftBracket => {
                let location = self.current_location();
                self.advance(); // consume '['
                let mut elements = Vec::new();

                if !self.check(&TokenType::RightBracket) {
                    elements.push(self.expression()?);
                    while self.match_token(&TokenType::Comma) {
                        elements.push(self.expression()?);
                    }
                }

                self.consume(&TokenType::RightBracket, "Expected ']' after list elements")?;
                Ok(Expression::List(ListLiteral { elements, location }))
            }
            TokenType::LeftParen => {
                self.advance(); // consume '('
                let expr = self.expression()?;
                self.consume(&TokenType::RightParen, "Expected ')' after expression")?;
                Ok(expr)
            }
            TokenType::Pipe => {
                // Lambda expression: |param1, param2| -> body
                self.parse_lambda()
            }
            TokenType::SelfType => {
                let location = self.current_location();
                let name = self.advance().lexeme.clone();

                // Check for struct literal
                if self.match_token(&TokenType::LeftBrace) {
                    let fields = self.struct_field_list()?;
                    self.consume(&TokenType::RightBrace, "Expected '}' after struct fields")?;
                    Ok(Expression::Struct(StructLiteral {
                        struct_name: name,
                        fields,
                        location,
                    }))
                } else {
                    Ok(Expression::Identifier(Identifier { name, location }))
                }
            }
            TokenType::SelfKeyword => {
                let location = self.current_location();
                let name = self.advance().lexeme.clone();

                // self (lowercase) is always treated as an identifier
                Ok(Expression::Identifier(Identifier { name, location }))
            }
            _ => Err(UmbraError::Parse {
                message: format!("Unexpected token: {}", self.peek().token_type),
                line: self.peek().location.line,
                column: self.peek().location.column,
            }),
        }
    }

    fn parse_lambda(&mut self) -> UmbraResult<Expression> {
        let location = self.current_location();
        self.consume(&TokenType::Pipe, "Expected '|' to start lambda")?;

        // Parse parameters
        let mut parameters = Vec::new();
        if !self.check(&TokenType::Pipe) {
            loop {
                let param_location = self.current_location();
                let name = self.consume_identifier("Expected parameter name")?;

                // Optional type annotation
                let type_annotation = if self.match_token(&TokenType::Colon) {
                    Some(self.parse_type()?)
                } else {
                    None
                };

                parameters.push(LambdaParameter {
                    name,
                    type_annotation,
                    location: param_location,
                });

                if !self.match_token(&TokenType::Comma) {
                    break;
                }
            }
        }

        self.consume(&TokenType::Pipe, "Expected '|' after lambda parameters")?;

        // Expect arrow before body
        self.consume(&TokenType::Arrow, "Expected '->' after lambda parameters")?;

        // Parse body expression
        let body = Box::new(self.expression()?);

        Ok(Expression::Lambda(LambdaExpression {
            parameters,
            return_type: None, // Type inference will handle this
            body,
            location,
        }))
    }

    fn argument_list(&mut self) -> UmbraResult<Vec<Expression>> {
        let mut arguments = Vec::new();

        if !self.check(&TokenType::RightParen) {
            arguments.push(self.expression()?);
            while self.match_token(&TokenType::Comma) {
                arguments.push(self.expression()?);
            }
        }

        Ok(arguments)
    }

    fn struct_field_list(&mut self) -> UmbraResult<Vec<StructFieldInit>> {
        let mut fields = Vec::new();

        if !self.check(&TokenType::RightBrace) {
            loop {
                let location = self.current_location();
                let name = self.consume_identifier("Expected field name")?;
                self.consume(&TokenType::Colon, "Expected ':' after field name")?;
                let value = self.expression()?;

                fields.push(StructFieldInit {
                    name,
                    value,
                    location,
                });

                if !self.match_token(&TokenType::Comma) {
                    break;
                }
            }
        }

        Ok(fields)
    }

    fn type_annotation(&mut self) -> UmbraResult<Type> {
        match &self.peek().token_type {
            TokenType::AutoType => {
                self.advance();
                Ok(Type::Auto)
            }
            TokenType::ListType => {
                self.advance();
                self.consume(&TokenType::LeftBracket, "Expected '[' after List")?;
                let element_type = self.type_annotation()?;
                self.consume(&TokenType::RightBracket, "Expected ']' after element type")?;
                Ok(Type::List(Box::new(element_type)))
            }
            TokenType::Optional => {
                self.advance();
                self.consume(&TokenType::LeftBracket, "Expected '[' after Optional")?;
                let inner_type = self.type_annotation()?;
                self.consume(&TokenType::RightBracket, "Expected ']' after inner type")?;
                Ok(Type::Optional(Box::new(inner_type)))
            }
            TokenType::Result => {
                self.advance();
                self.consume(&TokenType::LeftBracket, "Expected '[' after Result")?;
                let ok_type = self.type_annotation()?;
                self.consume(&TokenType::Comma, "Expected ',' after Ok type")?;
                let err_type = self.type_annotation()?;
                self.consume(&TokenType::RightBracket, "Expected ']' after Err type")?;
                Ok(Type::Result(Box::new(ok_type), Box::new(err_type)))
            }
            TokenType::Union => {
                self.advance();
                self.consume(&TokenType::LeftBracket, "Expected '[' after Union")?;
                let mut types = Vec::new();
                types.push(self.type_annotation()?);
                while self.match_token(&TokenType::Comma) {
                    types.push(self.type_annotation()?);
                }
                self.consume(&TokenType::RightBracket, "Expected ']' after union types")?;
                Ok(Type::Union(types))
            }
            TokenType::IntegerType => {
                self.advance();
                Ok(Type::Basic(BasicType::Integer))
            }
            TokenType::FloatType => {
                self.advance();
                Ok(Type::Basic(BasicType::Float))
            }
            TokenType::StringType => {
                self.advance();
                Ok(Type::Basic(BasicType::String))
            }
            TokenType::BooleanType => {
                self.advance();
                Ok(Type::Basic(BasicType::Boolean))
            }
            TokenType::VoidType => {
                self.advance();
                Ok(Type::Basic(BasicType::Void))
            }
            TokenType::DatasetType => {
                self.advance();
                Ok(Type::Basic(BasicType::Dataset))
            }
            TokenType::ModelType => {
                self.advance();
                Ok(Type::Basic(BasicType::Model))
            }
            TokenType::TensorType => {
                self.advance();
                Ok(Type::Basic(BasicType::Tensor))
            }
            TokenType::VecType => {
                self.advance();
                self.consume(&TokenType::Less, "Expected '<' after Vec")?;
                let element_type = self.type_annotation()?;
                self.consume(&TokenType::Greater, "Expected '>' after element type")?;
                Ok(Type::List(Box::new(element_type)))
            }
            TokenType::HashMapType => {
                self.advance();
                self.consume(&TokenType::Less, "Expected '<' after HashMap")?;
                let key_type = self.type_annotation()?;
                self.consume(&TokenType::Comma, "Expected ',' after key type")?;
                let value_type = self.type_annotation()?;
                self.consume(&TokenType::Greater, "Expected '>' after value type")?;
                Ok(Type::HashMap(Box::new(key_type), Box::new(value_type)))
            }
            TokenType::HashSetType => {
                self.advance();
                self.consume(&TokenType::Less, "Expected '<' after HashSet")?;
                let element_type = self.type_annotation()?;
                self.consume(&TokenType::Greater, "Expected '>' after element type")?;
                Ok(Type::HashSet(Box::new(element_type)))
            }
            TokenType::OptionType => {
                self.advance();
                self.consume(&TokenType::Less, "Expected '<' after Option")?;
                let inner_type = self.type_annotation()?;
                self.consume(&TokenType::Greater, "Expected '>' after inner type")?;
                Ok(Type::Optional(Box::new(inner_type)))
            }
            TokenType::BoxType => {
                self.advance();
                self.consume(&TokenType::Less, "Expected '<' after Box")?;
                let inner_type = self.type_annotation()?;
                self.consume(&TokenType::Greater, "Expected '>' after inner type")?;
                Ok(Type::Box(Box::new(inner_type)))
            }
            TokenType::RcType => {
                self.advance();
                self.consume(&TokenType::Less, "Expected '<' after Rc")?;
                let inner_type = self.type_annotation()?;
                self.consume(&TokenType::Greater, "Expected '>' after inner type")?;
                Ok(Type::Rc(Box::new(inner_type)))
            }
            TokenType::ArcType => {
                self.advance();
                self.consume(&TokenType::Less, "Expected '<' after Arc")?;
                let inner_type = self.type_annotation()?;
                self.consume(&TokenType::Greater, "Expected '>' after inner type")?;
                Ok(Type::Arc(Box::new(inner_type)))
            }
            TokenType::WeakType => {
                self.advance();
                self.consume(&TokenType::Less, "Expected '<' after Weak")?;
                let inner_type = self.type_annotation()?;
                self.consume(&TokenType::Greater, "Expected '>' after inner type")?;
                Ok(Type::Weak(Box::new(inner_type)))
            }
            TokenType::RefCellType => {
                self.advance();
                self.consume(&TokenType::Less, "Expected '<' after RefCell")?;
                let inner_type = self.type_annotation()?;
                self.consume(&TokenType::Greater, "Expected '>' after inner type")?;
                Ok(Type::RefCell(Box::new(inner_type)))
            }
            TokenType::MutexType => {
                self.advance();
                self.consume(&TokenType::Less, "Expected '<' after Mutex")?;
                let inner_type = self.type_annotation()?;
                self.consume(&TokenType::Greater, "Expected '>' after inner type")?;
                Ok(Type::Mutex(Box::new(inner_type)))
            }
            TokenType::VoidLowerType => {
                self.advance();
                Ok(Type::Basic(BasicType::Void))
            }
            TokenType::SelfType => {
                self.advance();
                Ok(Type::SelfType)
            }
            TokenType::FunctionType => {
                self.advance();
                // Generic function type - can accept any function
                Ok(Type::Function(vec![Type::Auto], Box::new(Type::Auto)))
            }
            TokenType::Identifier => {
                // User-defined struct type, generic type, or type parameter
                let name = self.consume_identifier("Expected type name")?;

                // Check if this is a type parameter
                if self.current_type_parameters.contains(&name) {
                    Ok(Type::TypeParameter {
                        name,
                        bounds: vec![], // Bounds will be resolved during semantic analysis
                    })
                } else if self.match_token(&TokenType::LeftBracket) {
                    // Generic type with parameters
                    let mut params = Vec::new();
                    params.push(self.type_annotation()?);
                    while self.match_token(&TokenType::Comma) {
                        params.push(self.type_annotation()?);
                    }
                    self.consume(&TokenType::RightBracket, "Expected ']' after generic parameters")?;
                    Ok(Type::Generic(name, params))
                } else {
                    // Regular struct type
                    Ok(Type::Struct(name))
                }
            }
            _ => Err(UmbraError::Parse {
                message: format!("Expected type, got: {}", self.peek().token_type),
                line: self.peek().location.line,
                column: self.peek().location.column,
            }),
        }
    }

    fn block(&mut self) -> UmbraResult<Vec<Statement>> {
        let mut statements = Vec::new();

        // Handle both brace-style blocks and indent-style blocks
        if self.check(&TokenType::LeftBrace) {
            // Brace-style block (Rust-like syntax)
            self.consume(&TokenType::LeftBrace, "Expected '{'")?;

            while !self.check(&TokenType::RightBrace) && !self.is_at_end() {
                if self.check(&TokenType::Newline) {
                    self.advance();
                    continue;
                }
                statements.push(self.statement()?);
            }

            self.consume(&TokenType::RightBrace, "Expected '}'")?;
        } else {
            // Indent-style block (Umbra syntax)
            // Consume newline if present, but don't require it
            if self.check(&TokenType::Newline) {
                self.advance();
            }
            self.consume(&TokenType::Indent, "Expected indentation")?;

            while !self.check(&TokenType::Dedent) && !self.is_at_end() {
                if self.check(&TokenType::Newline) {
                    self.advance();
                    continue;
                }
                statements.push(self.statement()?);
            }

            self.consume(&TokenType::Dedent, "Expected dedent")?;
        }

        Ok(statements)
    }

    fn parse_type(&mut self) -> UmbraResult<Type> {
        self.type_annotation()
    }

    // Utility methods
    fn match_token(&mut self, token_type: &TokenType) -> bool {
        if self.check(token_type) {
            self.advance();
            true
        } else {
            false
        }
    }

    fn match_tokens(&mut self, token_types: &[TokenType]) -> bool {
        for token_type in token_types {
            if self.check(token_type) {
                self.advance();
                return true;
            }
        }
        false
    }

    fn check(&self, token_type: &TokenType) -> bool {
        if self.is_at_end() {
            false
        } else {
            std::mem::discriminant(&self.peek().token_type) == std::mem::discriminant(token_type)
        }
    }

    fn check_ahead(&self, token_type: &TokenType) -> bool {
        if self.current + 1 >= self.tokens.len() {
            false
        } else {
            std::mem::discriminant(&self.tokens[self.current + 1].token_type)
                == std::mem::discriminant(token_type)
        }
    }

    fn advance(&mut self) -> &Token {
        if !self.is_at_end() {
            self.current += 1;
        }
        self.previous()
    }

    fn is_at_end(&self) -> bool {
        matches!(self.peek().token_type, TokenType::Eof)
    }

    fn peek(&self) -> &Token {
        &self.tokens[self.current]
    }

    fn previous(&self) -> &Token {
        &self.tokens[self.current - 1]
    }

    fn current_location(&self) -> SourceLocation {
        self.peek().location
    }

    fn consume(&mut self, token_type: &TokenType, message: &str) -> UmbraResult<&Token> {
        if self.check(token_type) {
            Ok(self.advance())
        } else {
            Err(UmbraError::Parse {
                message: format!("{}. Got: {}", message, self.peek().token_type),
                line: self.peek().location.line,
                column: self.peek().location.column,
            })
        }
    }

    fn consume_identifier(&mut self, message: &str) -> UmbraResult<String> {
        if matches!(self.peek().token_type, TokenType::Identifier) {
            Ok(self.advance().lexeme.clone())
        } else {
            Err(UmbraError::Parse {
                message: format!("{}. Got: {}", message, self.peek().token_type),
                line: self.peek().location.line,
                column: self.peek().location.column,
            })
        }
    }

    fn consume_string(&mut self, message: &str) -> UmbraResult<String> {
        if let TokenType::String(s) = &self.peek().token_type {
            let s = s.clone();
            self.advance();
            Ok(s)
        } else {
            Err(UmbraError::Parse {
                message: format!("{}. Got: {}", message, self.peek().token_type),
                line: self.peek().location.line,
                column: self.peek().location.column,
            })
        }
    }

    fn consume_newline(&mut self) -> UmbraResult<()> {
        if self.check(&TokenType::Newline) {
            self.advance();
        }
        Ok(())
    }

    // Error handling parsing methods

    fn try_statement(&mut self) -> UmbraResult<Statement> {
        let location = self.current_location();
        self.consume(&TokenType::Try, "Expected 'try'")?;
        self.consume(&TokenType::Colon, "Expected ':' after 'try'")?;
        self.consume(&TokenType::Newline, "Expected newline after ':'")?;

        let try_block = self.block()?;
        let mut catch_clauses = Vec::new();
        let mut finally_block = None;

        // Parse catch clauses
        while self.match_token(&TokenType::Catch) {
            let catch_location = self.current_location();
            let mut error_type = None;
            let mut error_variable = None;

            // Optional error type and variable binding
            if self.match_token(&TokenType::LeftParen) {
                if !self.check(&TokenType::RightParen) {
                    error_type = Some(self.type_annotation()?);
                    if self.match_token(&TokenType::Identifier) {
                        error_variable = Some(self.previous().lexeme.clone());
                    }
                }
                self.consume(&TokenType::RightParen, "Expected ')' after catch parameters")?;
            }

            self.consume(&TokenType::Colon, "Expected ':' after 'catch'")?;
            self.consume(&TokenType::Newline, "Expected newline after ':'")?;
            let catch_block = self.block()?;

            catch_clauses.push(CatchClause {
                error_type,
                error_variable,
                catch_block,
                location: catch_location,
            });
        }

        // Parse optional finally block
        if self.match_token(&TokenType::Finally) {
            self.consume(&TokenType::Colon, "Expected ':' after 'finally'")?;
            self.consume(&TokenType::Newline, "Expected newline after ':'")?;
            finally_block = Some(self.block()?);
        }

        Ok(Statement::Try(TryStatement {
            try_block,
            catch_clauses,
            finally_block,
            location,
        }))
    }

    fn throw_statement(&mut self) -> UmbraResult<Statement> {
        let location = self.current_location();
        self.consume(&TokenType::Throw, "Expected 'throw'")?;
        let error_expression = self.expression()?;
        self.consume_newline()?;

        Ok(Statement::Throw(ThrowStatement {
            error_expression,
            location,
        }))
    }

    fn panic_statement(&mut self) -> UmbraResult<Statement> {
        let location = self.current_location();
        self.consume(&TokenType::Panic, "Expected 'panic'")?;

        let message = if self.match_token(&TokenType::LeftParen) {
            let msg = if !self.check(&TokenType::RightParen) {
                Some(self.expression()?)
            } else {
                None
            };
            self.consume(&TokenType::RightParen, "Expected ')' after panic message")?;
            msg
        } else {
            None
        };

        self.consume_newline()?;

        Ok(Statement::Panic(PanicStatement {
            message,
            location,
        }))
    }

    fn error_definition(&mut self) -> UmbraResult<Statement> {
        let location = self.current_location();

        // Error definitions don't need explicit visibility handling here
        // since they're handled by the visibility_statement method
        let visibility = Visibility::Private; // Default visibility

        self.consume(&TokenType::Error, "Expected 'error'")?;
        let name = self.consume_identifier("Expected error name")?;

        // Optional parent error (inheritance)
        let parent_error = if self.match_token(&TokenType::LeftParen) {
            let parent = self.consume_identifier("Expected parent error name")?;
            self.consume(&TokenType::RightParen, "Expected ')' after parent error")?;
            Some(parent)
        } else {
            None
        };

        self.consume(&TokenType::Colon, "Expected ':' after error name")?;
        self.consume(&TokenType::Newline, "Expected newline after ':'")?;

        // Parse error fields (similar to structure fields)
        let mut fields = Vec::new();
        if self.check(&TokenType::Indent) {
            self.advance(); // consume indent
            while !self.check(&TokenType::Dedent) && !self.is_at_end() {
                if self.check(&TokenType::Newline) {
                    self.advance(); // skip empty lines
                    continue;
                }

                let field_location = self.current_location();
                let field_name = self.consume_identifier("Expected field name")?;
                self.consume(&TokenType::Colon, "Expected ':' after field name")?;
                let field_type = self.type_annotation()?;
                self.consume_newline()?;

                fields.push(StructureField {
                    name: field_name,
                    field_type,
                });
            }
            if self.check(&TokenType::Dedent) {
                self.advance(); // consume dedent
            }
        }

        Ok(Statement::Error(ErrorDefinition {
            name,
            fields,
            parent_error,
            visibility,
            location,
        }))
    }

    fn error_definition_with_visibility(&mut self, visibility: Visibility) -> UmbraResult<Statement> {
        let location = self.current_location();
        self.consume(&TokenType::Error, "Expected 'error'")?;
        let name = self.consume_identifier("Expected error name")?;

        // Optional parent error (inheritance)
        let parent_error = if self.match_token(&TokenType::LeftParen) {
            let parent = self.consume_identifier("Expected parent error name")?;
            self.consume(&TokenType::RightParen, "Expected ')' after parent error")?;
            Some(parent)
        } else {
            None
        };

        self.consume(&TokenType::Colon, "Expected ':' after error name")?;
        self.consume(&TokenType::Newline, "Expected newline after ':'")?;

        // Parse error fields (similar to structure fields)
        let mut fields = Vec::new();
        if self.check(&TokenType::Indent) {
            self.advance(); // consume indent
            while !self.check(&TokenType::Dedent) && !self.is_at_end() {
                if self.check(&TokenType::Newline) {
                    self.advance(); // skip empty lines
                    continue;
                }

                let _field_location = self.current_location();
                let field_name = self.consume_identifier("Expected field name")?;
                self.consume(&TokenType::Colon, "Expected ':' after field name")?;
                let field_type = self.type_annotation()?;
                self.consume_newline()?;

                fields.push(StructureField {
                    name: field_name,
                    field_type,
                });
            }
            if self.check(&TokenType::Dedent) {
                self.advance(); // consume dedent
            }
        }

        Ok(Statement::Error(ErrorDefinition {
            name,
            fields,
            parent_error,
            visibility,
            location,
        }))
    }

    /// Parse type parameters with bounds: <T: Clone, U: Send + Sync>
    fn parse_type_parameters(&mut self) -> UmbraResult<Vec<TypeParameter>> {
        let mut params = Vec::new();

        loop {
            let location = self.current_location();

            // Parse optional variance annotation
            let variance = if self.match_token(&TokenType::Plus) {
                Some(Variance::Covariant)
            } else if self.match_token(&TokenType::Minus) {
                Some(Variance::Contravariant)
            } else if self.match_token(&TokenType::Multiply) {
                Some(Variance::Bivariant)
            } else {
                None
            };

            let name = self.consume_identifier("Expected type parameter name")?;

            // Parse optional bounds: T: Clone + Send
            let bounds = if self.match_token(&TokenType::Colon) {
                self.parse_type_bounds()?
            } else {
                Vec::new()
            };

            // Parse optional default: T = String
            let default = if self.match_token(&TokenType::Equal_) {
                Some(self.parse_type()?)
            } else {
                None
            };

            params.push(TypeParameter {
                name,
                bounds,
                default,
                variance,
                location,
            });

            if !self.match_token(&TokenType::Comma) {
                break;
            }
        }

        self.consume(&TokenType::Greater, "Expected '>' after type parameters")?;
        Ok(params)
    }

    /// Parse type bounds: Clone + Send + 'static
    fn parse_type_bounds(&mut self) -> UmbraResult<Vec<TypeBound>> {
        let mut bounds = Vec::new();

        loop {
            if self.check(&TokenType::Identifier) {
                let identifier = self.consume_identifier("Expected trait name")?;
                if identifier.starts_with('\'') {
                    // Lifetime bound
                    bounds.push(TypeBound::Lifetime(identifier));
                } else {
                    // Trait bound
                    bounds.push(TypeBound::Trait(identifier));
                }
            } else {
                return Err(UmbraError::Parse {
                    message: "Expected trait name or lifetime in type bound".to_string(),
                    line: self.current_location().line,
                    column: self.current_location().column,
                });
            }

            if !self.match_token(&TokenType::Plus) {
                break;
            }
        }

        Ok(bounds)
    }

    /// Parse where clauses: where T: Clone, U: Send + Sync
    fn parse_where_clauses(&mut self) -> UmbraResult<Vec<WhereClause>> {
        let mut clauses = Vec::new();

        loop {
            let location = self.current_location();
            let type_param = self.consume_identifier("Expected type parameter name in where clause")?;

            self.consume(&TokenType::Colon, "Expected ':' after type parameter in where clause")?;
            let bounds = self.parse_type_bounds()?;

            clauses.push(WhereClause {
                type_param,
                bounds,
                location,
            });

            if !self.match_token(&TokenType::Comma) {
                break;
            }
        }

        Ok(clauses)
    }

    /// Parse generic type instantiation: Vec<T>, Map<K, V>
    fn parse_generic_type(&mut self, base_name: String) -> UmbraResult<Type> {
        self.consume(&TokenType::Less, "Expected '<' for generic type")?;

        let mut type_args = Vec::new();
        loop {
            type_args.push(self.parse_type()?);

            if !self.match_token(&TokenType::Comma) {
                break;
            }
        }

        self.consume(&TokenType::Greater, "Expected '>' after generic type arguments")?;
        Ok(Type::Generic(base_name, type_args))
    }

    /// Parse function type with generics: <T>(T) -> T
    fn parse_function_type(&mut self) -> UmbraResult<Type> {
        // Parse optional type parameters
        let _type_params = if self.match_token(&TokenType::Less) {
            self.parse_type_parameters()?
        } else {
            Vec::new()
        };

        self.consume(&TokenType::LeftParen, "Expected '(' in function type")?;

        let mut param_types = Vec::new();
        if !self.check(&TokenType::RightParen) {
            loop {
                param_types.push(self.parse_type()?);
                if !self.match_token(&TokenType::Comma) {
                    break;
                }
            }
        }

        self.consume(&TokenType::RightParen, "Expected ')' in function type")?;
        self.consume(&TokenType::Arrow, "Expected '->' in function type")?;
        let return_type = self.parse_type()?;

        Ok(Type::Function(param_types, Box::new(return_type)))
    }
}
