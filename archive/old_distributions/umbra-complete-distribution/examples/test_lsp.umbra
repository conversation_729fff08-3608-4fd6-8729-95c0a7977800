// Test file for LSP functionality
bring math

fn calculate_area(width: Float, height: Float) -> Float {
    let area: Float := width * height
    return area
}

fn main() -> Void {
    let x: Integer := 42
    let y: Float := 3.14
    let name: String := "Umbra"
    
    show("Hello, ", name)
    show("Number:", x)
    show("Pi:", y)
    
    let rect_area: Float := calculate_area(10.0, 5.0)
    show("Rectangle area:", rect_area)
    
    // AI/ML example
    let dataset : Dataset := load_dataset("data.csv")
    let m1 : model:= train linear_regression Using dataset
    let predictions : prediction := Predict m1 Using dataset
    
    when x > 40: {
        show("x is greater than 40")
    } otherwise: {
        show("x is not greater than 40")
    }
}