# Umbra Programming Language

Umbra is a modern, high-performance programming language designed for AI/ML applications, data science, and general-purpose programming. It combines the simplicity of Python with the performance of Rust, featuring strong type safety, memory safety, and seamless interoperability with existing ecosystems.

## 🚀 Features

- **High Performance**: Compiled to native code with zero-cost abstractions
- **Memory Safety**: Automatic memory management without garbage collection overhead
- **Type Safety**: Strong static typing with type inference
- **AI/ML Integration**: Built-in support for NumPy, PyTorch, TensorFlow, and more
- **Interoperability**: Seamless integration with Python, C, and Rust ecosystems
- **Modern Syntax**: Clean, readable syntax inspired by Python and Rust
- **Concurrent Programming**: Built-in support for async/await and parallel processing
- **Package Management**: Integrated package manager and build system
- **Performance Optimizations**: Automatic optimization for large programs

## 📦 Installation

### Manual Installation

1. Download the latest release from the distribution packages
2. Extract the archive
3. Run the installer:
   - **Linux/macOS**: `./distribution/install.sh`
   - **Windows**: `./distribution/install.bat`

### Build from Source

```bash
git clone https://github.com/umbra-lang/umbra.git
cd umbra/umbra-compiler
cargo build --release
```

## 🏃‍♂️ Quick Start

### Hello World

```umbra
fn main() -> void {
    println!("Hello, Umbra!")
}
```

### Variables and Types

```umbra
fn main() -> void {
    let name: String := "Umbra"
    let version: Float := 1.0
    let is_awesome: Boolean := true
    
    println!("Welcome to {} v{}", name, version)
}
```

### Functions

```umbra
fn add(a: Integer, b: Integer) -> Integer {
    return a + b
}

fn main() -> void {
    let result: Integer := add(5, 3)
    println!("5 + 3 = {}", result)
}
```

### AI/ML Example

```umbra
bring numpy as np
bring torch

fn train_model() -> void {
    let data: np.Array := np.random.randn(100, 10)
    let model: torch.Module := torch.nn.Linear(10, 1)
    
    // Training loop
    for epoch in range(100) {
        let output: torch.Tensor := model(data)
        let loss: torch.Tensor := torch.nn.mse_loss(output, target)
        loss.backward()
    }
}
```

## 📚 Documentation

- [Getting Started Guide](docs/GETTING_STARTED.md)
- [Language Reference](docs/LANGUAGE_REFERENCE.md)
- [AI/ML Ecosystem Integration](docs/AI_ML_ECOSYSTEM.md)
- [Testing Framework](docs/testing-framework.md)
- [Performance Results](PERFORMANCE_RESULTS.md)

## 🛠️ Development

### Project Structure

```
umbra/
├── umbra-compiler/          # Main compiler source code
│   ├── src/                # Compiler implementation
│   ├── tests/              # Unit tests
│   ├── Cargo.toml         # Rust project configuration
│   └── build.rs           # Build script
├── docs/                    # Documentation
│   ├── GETTING_STARTED.md
│   ├── LANGUAGE_REFERENCE.md
│   ├── AI_ML_ECOSYSTEM.md
│   └── testing-framework.md
├── examples/                # Example programs
│   ├── basic/              # Basic language examples
│   ├── advanced/           # Advanced features
│   ├── ai_ml/              # AI/ML examples
│   └── stdlib/             # Standard library examples
├── tools/                   # Development tools
│   ├── scripts/            # Build and test scripts
│   ├── benchmarks/         # Performance benchmarks
│   └── integration-tests/  # Integration test suite
├── distribution/           # Distribution packages
│   ├── installers/        # Platform-specific installers
│   ├── icons/             # Application icons
│   ├── logos/             # Project logos
│   └── signing/           # Code signing certificates
├── LICENSE                 # MIT License
├── README.md              # This file
└── PERFORMANCE_RESULTS.md # Performance optimization results
```

### Building

```bash
cd umbra-compiler
cargo build --release
```

The optimized compiler automatically detects large programs (100+ functions) and uses enhanced performance optimizations.

### Testing

```bash
# Run unit tests
cd umbra-compiler
cargo test

# Run integration tests
./tools/scripts/run-all-tests.sh

# Run benchmarks
./tools/scripts/benchmark.sh

# Validate testing framework
./tools/scripts/validate-framework.sh
```

### Performance

The Umbra compiler includes automatic performance optimizations:
- Pre-allocated symbol tables for large programs
- Function signature caching
- Optimized memory usage patterns
- Automatic detection of large codebases

See [PERFORMANCE_RESULTS.md](PERFORMANCE_RESULTS.md) for detailed performance analysis.

## 🤝 Contributing

We welcome contributions! Please follow these steps:

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

### Code Style

- Follow Rust conventions for the compiler
- Use clear, descriptive variable names
- Add documentation for public APIs
- Include tests for new features

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🌟 Acknowledgments

- The Rust community for inspiration and tools
- The Python community for ecosystem integration ideas
- All contributors and early adopters

## 📞 Support

- **Issues**: Report bugs and request features via GitHub Issues
- **Documentation**: Comprehensive guides in the `docs/` directory
- **Examples**: Working examples in the `examples/` directory
- **Performance**: See optimization results in `PERFORMANCE_RESULTS.md`
