// Test Umbra program for Windows
fn main() {
    show("Hello from Umbra on Windows!");
    
    let x: Integer = 42;
    let y: Integer = 58;
    let result: Integer = x + y;
    
    show("Calculation: ", x, " + ", y, " = ", result);
    
    // Test AI/ML features
    let data: Array<Integer> = [1, 2, 3, 4, 5];
    show("Data array: ", data);
    
    // Test string operations
    let message: String = "Umbra works on Windows!";
    show("Message: ", message);
}
