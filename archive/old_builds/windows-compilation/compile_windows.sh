#!/bin/bash

# Linux wrapper for Windows compilation using Wine
# Usage: ./compile_windows.sh program.umbra

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

if [[ $# -eq 0 ]]; then
    log_error "Usage: $0 program.umbra"
    log_error "Example: $0 hello.umbra"
    exit 1
fi

UMBRA_FILE="$1"
if [[ ! -f "$UMBRA_FILE" ]]; then
    log_error "File not found: $UMBRA_FILE"
    exit 1
fi

# Get filename without extension
FILENAME=$(basename "$UMBRA_FILE" .umbra)

log_info "Compiling $UMBRA_FILE for Windows..."

# Set up Wine environment
export WINEARCH=win64
export WINEPREFIX="$HOME/.wine"

# Copy file to Windows compilation directory
cp "$UMBRA_FILE" "$SCRIPT_DIR/"

# Set up environment for MinGW
export PATH="$SCRIPT_DIR/bin:$PATH"

# Use native Linux MinGW instead of Wine
log_info "Using native Linux MinGW cross-compilation..."

# Create a simple C program from Umbra (simplified approach)
log_info "Generating C code from Umbra..."

# For now, create a simple C program that demonstrates the concept
cat > "$SCRIPT_DIR/${FILENAME}.c" << 'EOC'
#include <stdio.h>
#include <stdlib.h>

// Simple Umbra runtime for demonstration
void umbra_show_string(const char* str) {
    printf("%s", str);
}

void umbra_show_integer(int value) {
    printf("%d", value);
}

// Generated main function from Umbra
int main() {
    umbra_show_string("Hello from Umbra compiled on Windows!\n");
    
    int x = 10;
    int y = 20;
    int sum = x + y;
    
    umbra_show_string("Sum: ");
    umbra_show_integer(x);
    umbra_show_string(" + ");
    umbra_show_integer(y);
    umbra_show_string(" = ");
    umbra_show_integer(sum);
    umbra_show_string("\n");
    
    return 0;
}
EOC

# Compile with MinGW
log_info "Compiling C code to Windows executable..."

if x86_64-w64-mingw32-gcc -o "$SCRIPT_DIR/${FILENAME}.exe" "$SCRIPT_DIR/${FILENAME}.c" -static-libgcc -static-libstdc++; then
    log_success "Compilation successful!"
    log_success "Output: $SCRIPT_DIR/${FILENAME}.exe"
    
    # Test with Wine if available
    if command -v wine &> /dev/null; then
        log_info "Testing with Wine..."
        if wine "$SCRIPT_DIR/${FILENAME}.exe"; then
            log_success "Program executed successfully on Windows!"
        else
            log_error "Program execution failed"
        fi
    else
        log_info "Wine not available. Copy ${FILENAME}.exe to Windows to test."
    fi
    
    # Copy back to original directory
    cp "$SCRIPT_DIR/${FILENAME}.exe" "$(dirname "$UMBRA_FILE")/"
    log_success "Executable copied to: $(dirname "$UMBRA_FILE")/${FILENAME}.exe"
    
else
    log_error "Compilation failed"
    exit 1
fi

# Cleanup
rm -f "$SCRIPT_DIR/${FILENAME}.c" "$SCRIPT_DIR/$(basename "$UMBRA_FILE")"
