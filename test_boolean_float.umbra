// Test boolean and float formatting

println!("=== Boolean and Float Test ===")

// Test boolean values
let is_true: Boolean := true
let is_false: Boolean := false

println!("Boolean true: {}", is_true)
println!("Boolean false: {}", is_false)

// Test float values
let pi: Float := 3.14159
let negative: Float := -2.5

println!("Float pi: {}", pi)
println!("Float negative: {}", negative)

// Test mixed types
let count: Integer := 42
let name: String := "Umbra"

println!("Mixed: {} items in {}", count, name)
println!("All types: {}, {}, {}, {}", count, name, is_true, pi)

println!("=== Test Completed ===")
