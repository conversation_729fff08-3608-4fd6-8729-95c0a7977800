#!/usr/bin/env python3
"""
Simple LSP client test for Umbra Language Server
"""
import json
import subprocess
import sys
import time

def test_lsp_server():
    # Start the LSP server
    process = subprocess.Popen(
        ['./umbra-compiler/target/release/umbra', 'lsp'],
        stdin=subprocess.PIPE,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True,
        bufsize=0
    )
    
    # Initialize request
    init_request = {
        "jsonrpc": "2.0",
        "id": 1,
        "method": "initialize",
        "params": {
            "processId": None,
            "rootUri": "file:///home/<USER>/Desktop/Umbra",
            "capabilities": {
                "textDocument": {
                    "completion": {
                        "completionItem": {
                            "snippetSupport": True
                        }
                    },
                    "hover": {},
                    "definition": {},
                    "references": {}
                }
            }
        }
    }
    
    # Send initialize request
    message = json.dumps(init_request)
    content_length = len(message.encode('utf-8'))
    
    request = f"Content-Length: {content_length}\r\n\r\n{message}"
    
    print("Sending initialize request...")
    print(f"Request: {request}")
    
    try:
        process.stdin.write(request)
        process.stdin.flush()
        
        # Read response
        print("Waiting for response...")
        time.sleep(1)
        
        # Try to read the response
        response = process.stdout.read(1024)
        print(f"Response: {response}")
        
        # Check stderr for any errors
        stderr_output = process.stderr.read(1024)
        if stderr_output:
            print(f"Stderr: {stderr_output}")
            
    except Exception as e:
        print(f"Error: {e}")
    finally:
        process.terminate()
        process.wait()

if __name__ == "__main__":
    test_lsp_server()
