#!/usr/bin/env python3
"""
Debug Python Execution - Test if Python ML operations work
"""

import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
from sklearn.model_selection import train_test_split
import sys

def test_real_ml_operations():
    print("🧪 Testing Real ML Operations")
    print("=============================")
    
    try:
        # Load the customer churn dataset
        print("📊 Loading customer churn dataset...")
        data = pd.read_csv('data/customer_churn.csv')
        
        print(f"✅ Dataset loaded: {len(data)} samples, {len(data.columns)} features")
        print(f"📋 Features: {', '.join(data.columns.tolist())}")
        
        # Prepare data
        if 'churn' in data.columns:
            X = data.drop('churn', axis=1)
            y = data['churn']
        else:
            X = data.iloc[:, :-1]
            y = data.iloc[:, -1]
        
        print(f"🎯 Target distribution: {y.value_counts().to_dict()}")
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
        
        # Train model
        print("🚀 Training Random Forest model...")
        model = RandomForestClassifier(n_estimators=50, max_depth=5, random_state=42)
        model.fit(X_train, y_train)
        
        # Make predictions
        print("🔮 Making predictions...")
        predictions = model.predict(X_test)
        
        # Calculate real metrics
        print("📈 Computing real performance metrics...")
        accuracy = accuracy_score(y_test, predictions)
        precision = precision_score(y_test, predictions, average='weighted', zero_division=0)
        recall = recall_score(y_test, predictions, average='weighted', zero_division=0)
        f1 = f1_score(y_test, predictions, average='weighted', zero_division=0)
        
        print("🎯 REAL PERFORMANCE METRICS:")
        print("============================")
        print(f"  Accuracy: {accuracy:.4f}")
        print(f"  Precision: {precision:.4f}")
        print(f"  Recall: {recall:.4f}")
        print(f"  F1-Score: {f1:.4f}")
        
        print("✅ Real ML operations completed successfully!")
        print("These are GENUINE metrics computed from actual model evaluation!")
        
        return {
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'f1_score': f1
        }
        
    except Exception as e:
        print(f"❌ Error in ML operations: {e}")
        return None

if __name__ == "__main__":
    results = test_real_ml_operations()
    if results:
        print(f"\n🏆 SUCCESS: Real ML metrics computed!")
        print(f"Accuracy: {results['accuracy']:.1%}")
    else:
        print(f"\n💥 FAILED: Could not compute real metrics")
        sys.exit(1)
