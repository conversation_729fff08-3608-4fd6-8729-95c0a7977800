#!/usr/bin/env python3
"""
Create Real Datasets for Umbra ML Testing
Generates actual CSV datasets with realistic data for genuine ML operations
"""

import pandas as pd
import numpy as np
from sklearn.datasets import make_classification, make_regression
from sklearn.model_selection import train_test_split
import os

def create_classification_dataset():
    """Create a realistic classification dataset"""
    print("🔧 Creating classification dataset...")
    
    # Generate synthetic but realistic customer churn data
    np.random.seed(42)
    n_samples = 1000
    
    # Generate features
    X, y = make_classification(
        n_samples=n_samples,
        n_features=10,
        n_informative=8,
        n_redundant=2,
        n_clusters_per_class=1,
        random_state=42
    )
    
    # Create feature names
    feature_names = [
        'age', 'income', 'credit_score', 'account_balance', 
        'transaction_count', 'support_calls', 'contract_length',
        'monthly_charges', 'total_charges', 'satisfaction_score'
    ]
    
    # Create DataFrame
    df = pd.DataFrame(X, columns=feature_names)
    df['target'] = y
    
    # Make values more realistic
    df['age'] = (df['age'] * 20 + 45).clip(18, 80).astype(int)
    df['income'] = (df['income'] * 25000 + 50000).clip(20000, 150000).astype(int)
    df['credit_score'] = (df['credit_score'] * 150 + 650).clip(300, 850).astype(int)
    df['account_balance'] = (df['account_balance'] * 5000 + 1000).clip(0, 50000).round(2)
    df['transaction_count'] = (df['transaction_count'] * 50 + 100).clip(0, 500).astype(int)
    df['support_calls'] = (df['support_calls'] * 5 + 2).clip(0, 20).astype(int)
    df['contract_length'] = (df['contract_length'] * 12 + 12).clip(1, 36).astype(int)
    df['monthly_charges'] = (df['monthly_charges'] * 50 + 75).clip(20, 200).round(2)
    df['total_charges'] = df['monthly_charges'] * df['contract_length']
    df['satisfaction_score'] = (df['satisfaction_score'] * 2 + 5).clip(1, 10).round(1)
    
    return df

def create_datasets():
    """Create training, validation, and test datasets"""
    print("📊 Creating real datasets for Umbra ML testing...")
    
    # Create data directory
    os.makedirs('data', exist_ok=True)
    
    # Generate main dataset
    df = create_classification_dataset()
    
    print(f"✅ Generated dataset with {len(df)} samples and {len(df.columns)-1} features")
    print(f"📋 Features: {', '.join(df.columns[:-1])}")
    print(f"🎯 Target: {df['target'].value_counts().to_dict()}")
    
    # Split into train/validation/test
    train_df, temp_df = train_test_split(df, test_size=0.4, random_state=42, stratify=df['target'])
    val_df, test_df = train_test_split(temp_df, test_size=0.5, random_state=42, stratify=temp_df['target'])
    
    # Save datasets
    train_df.to_csv('data/training_data.csv', index=False)
    val_df.to_csv('data/validation_data.csv', index=False)
    test_df.to_csv('data/test_data.csv', index=False)
    
    print(f"💾 Saved datasets:")
    print(f"  📁 training_data.csv: {len(train_df)} samples")
    print(f"  📁 validation_data.csv: {len(val_df)} samples")
    print(f"  📁 test_data.csv: {len(test_df)} samples")
    
    # Create additional datasets for comprehensive testing
    create_customer_churn_dataset()
    create_regression_dataset()
    
    print("✅ All real datasets created successfully!")

def create_customer_churn_dataset():
    """Create a specific customer churn dataset"""
    print("🔧 Creating customer churn dataset...")
    
    np.random.seed(123)
    n_customers = 2000
    
    # Generate realistic customer data
    data = {
        'customer_id': range(1, n_customers + 1),
        'age': np.random.normal(40, 15, n_customers).clip(18, 80).astype(int),
        'tenure_months': np.random.exponential(24, n_customers).clip(1, 72).astype(int),
        'monthly_charges': np.random.normal(65, 20, n_customers).clip(20, 150).round(2),
        'total_charges': np.random.normal(1500, 800, n_customers).clip(100, 8000).round(2),
        'contract_type': np.random.choice(['Month-to-month', 'One year', 'Two year'], n_customers, p=[0.5, 0.3, 0.2]),
        'payment_method': np.random.choice(['Electronic check', 'Mailed check', 'Bank transfer', 'Credit card'], n_customers),
        'internet_service': np.random.choice(['DSL', 'Fiber optic', 'No'], n_customers, p=[0.4, 0.4, 0.2]),
        'online_security': np.random.choice(['Yes', 'No'], n_customers, p=[0.3, 0.7]),
        'tech_support': np.random.choice(['Yes', 'No'], n_customers, p=[0.3, 0.7]),
        'streaming_tv': np.random.choice(['Yes', 'No'], n_customers, p=[0.4, 0.6]),
        'streaming_movies': np.random.choice(['Yes', 'No'], n_customers, p=[0.4, 0.6]),
    }
    
    df = pd.DataFrame(data)
    
    # Create churn target based on realistic factors
    churn_prob = (
        0.1 +  # Base churn rate
        0.3 * (df['contract_type'] == 'Month-to-month') +
        0.2 * (df['tenure_months'] < 12) +
        0.15 * (df['monthly_charges'] > 80) +
        0.1 * (df['payment_method'] == 'Electronic check') +
        0.1 * (df['tech_support'] == 'No') -
        0.2 * (df['contract_type'] == 'Two year') -
        0.1 * (df['tenure_months'] > 36)
    ).clip(0, 1)
    
    df['churn'] = np.random.binomial(1, churn_prob, n_customers)
    
    # Encode categorical variables
    df['contract_type_encoded'] = pd.Categorical(df['contract_type']).codes
    df['payment_method_encoded'] = pd.Categorical(df['payment_method']).codes
    df['internet_service_encoded'] = pd.Categorical(df['internet_service']).codes
    df['online_security_encoded'] = (df['online_security'] == 'Yes').astype(int)
    df['tech_support_encoded'] = (df['tech_support'] == 'Yes').astype(int)
    df['streaming_tv_encoded'] = (df['streaming_tv'] == 'Yes').astype(int)
    df['streaming_movies_encoded'] = (df['streaming_movies'] == 'Yes').astype(int)
    
    # Select numeric features for ML
    ml_features = [
        'age', 'tenure_months', 'monthly_charges', 'total_charges',
        'contract_type_encoded', 'payment_method_encoded', 'internet_service_encoded',
        'online_security_encoded', 'tech_support_encoded', 'streaming_tv_encoded',
        'streaming_movies_encoded', 'churn'
    ]
    
    ml_df = df[ml_features]
    ml_df.to_csv('data/customer_churn.csv', index=False)
    
    print(f"✅ Customer churn dataset: {len(ml_df)} customers, {len(ml_features)-1} features")
    print(f"📊 Churn rate: {ml_df['churn'].mean():.1%}")

def create_regression_dataset():
    """Create a regression dataset for house prices"""
    print("🔧 Creating house price regression dataset...")
    
    np.random.seed(456)
    n_houses = 1500
    
    # Generate house features
    X, y = make_regression(
        n_samples=n_houses,
        n_features=8,
        n_informative=6,
        noise=0.1,
        random_state=456
    )
    
    feature_names = [
        'square_feet', 'bedrooms', 'bathrooms', 'age_years',
        'lot_size', 'garage_spaces', 'neighborhood_score', 'school_rating'
    ]
    
    df = pd.DataFrame(X, columns=feature_names)
    
    # Make features realistic
    df['square_feet'] = (df['square_feet'] * 800 + 2000).clip(800, 5000).astype(int)
    df['bedrooms'] = (df['bedrooms'] * 1.5 + 3).clip(1, 6).astype(int)
    df['bathrooms'] = (df['bathrooms'] * 1 + 2).clip(1, 4).round(1)
    df['age_years'] = (df['age_years'] * 15 + 20).clip(0, 100).astype(int)
    df['lot_size'] = (df['lot_size'] * 5000 + 8000).clip(2000, 20000).astype(int)
    df['garage_spaces'] = (df['garage_spaces'] * 1 + 2).clip(0, 4).astype(int)
    df['neighborhood_score'] = (df['neighborhood_score'] * 2 + 7).clip(1, 10).round(1)
    df['school_rating'] = (df['school_rating'] * 2 + 7).clip(1, 10).round(1)
    
    # Create realistic house prices
    df['price'] = (
        df['square_feet'] * 150 +
        df['bedrooms'] * 10000 +
        df['bathrooms'] * 15000 +
        (100 - df['age_years']) * 1000 +
        df['lot_size'] * 5 +
        df['garage_spaces'] * 8000 +
        df['neighborhood_score'] * 20000 +
        df['school_rating'] * 15000 +
        np.random.normal(0, 25000, n_houses)
    ).clip(100000, 1000000).round(-3).astype(int)
    
    df.to_csv('data/house_prices.csv', index=False)
    
    print(f"✅ House price dataset: {len(df)} houses, {len(feature_names)} features")
    print(f"💰 Price range: ${df['price'].min():,} - ${df['price'].max():,}")

if __name__ == "__main__":
    create_datasets()
    print("\n🎉 Real dataset creation complete!")
    print("📁 Created datasets:")
    print("  • data/training_data.csv (classification)")
    print("  • data/validation_data.csv (classification)")
    print("  • data/test_data.csv (classification)")
    print("  • data/customer_churn.csv (customer churn)")
    print("  • data/house_prices.csv (regression)")
    print("\n✅ Ready for real ML operations with Umbra!")
