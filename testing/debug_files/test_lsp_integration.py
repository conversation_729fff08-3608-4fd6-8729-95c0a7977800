#!/usr/bin/env python3
"""
LSP Integration Test for Umbra Language Server
Tests the complete LSP functionality including initialization, completion, hover, etc.
"""
import json
import subprocess
import sys
import time
import threading
import queue

class LSPClient:
    def __init__(self, server_command):
        self.process = subprocess.Popen(
            server_command,
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=0
        )
        self.request_id = 0
        self.response_queue = queue.Queue()
        
        # Start response reader thread
        self.reader_thread = threading.Thread(target=self._read_responses)
        self.reader_thread.daemon = True
        self.reader_thread.start()
    
    def _read_responses(self):
        """Read responses from the LSP server"""
        while True:
            try:
                # Read Content-Length header
                line = self.process.stdout.readline()
                if not line:
                    break
                
                if line.startswith('Content-Length:'):
                    length = int(line.split(':')[1].strip())
                    
                    # Read empty line
                    self.process.stdout.readline()
                    
                    # Read JSON content
                    content = self.process.stdout.read(length)
                    response = json.loads(content)
                    self.response_queue.put(response)
                    
            except Exception as e:
                print(f"Error reading response: {e}")
                break
    
    def send_request(self, method, params=None):
        """Send a JSON-RPC request to the server"""
        self.request_id += 1
        request = {
            "jsonrpc": "2.0",
            "id": self.request_id,
            "method": method,
            "params": params or {}
        }
        
        message = json.dumps(request)
        content_length = len(message.encode('utf-8'))
        
        full_message = f"Content-Length: {content_length}\r\n\r\n{message}"
        
        try:
            self.process.stdin.write(full_message)
            self.process.stdin.flush()
            return self.request_id
        except Exception as e:
            print(f"Error sending request: {e}")
            return None
    
    def send_notification(self, method, params=None):
        """Send a JSON-RPC notification to the server"""
        notification = {
            "jsonrpc": "2.0",
            "method": method,
            "params": params or {}
        }
        
        message = json.dumps(notification)
        content_length = len(message.encode('utf-8'))
        
        full_message = f"Content-Length: {content_length}\r\n\r\n{message}"
        
        try:
            self.process.stdin.write(full_message)
            self.process.stdin.flush()
        except Exception as e:
            print(f"Error sending notification: {e}")
    
    def get_response(self, timeout=5):
        """Get a response from the server"""
        try:
            return self.response_queue.get(timeout=timeout)
        except queue.Empty:
            return None
    
    def close(self):
        """Close the LSP client"""
        if self.process:
            self.process.terminate()
            self.process.wait()

def test_lsp_server():
    """Test the Umbra LSP server functionality"""
    print("🚀 Starting Umbra LSP Integration Test...")
    
    # Start LSP client
    server_command = ['./umbra-compiler/target/release/umbra', 'lsp']
    client = LSPClient(server_command)
    
    try:
        # Test 1: Initialize
        print("\n📋 Test 1: Server Initialization")
        init_params = {
            "processId": None,
            "rootUri": "file:///home/<USER>/Desktop/Umbra",
            "capabilities": {
                "textDocument": {
                    "completion": {
                        "completionItem": {
                            "snippetSupport": True
                        }
                    },
                    "hover": {},
                    "definition": {},
                    "references": {}
                }
            }
        }
        
        request_id = client.send_request("initialize", init_params)
        if request_id:
            response = client.get_response(timeout=10)
            if response and response.get("id") == request_id:
                print("✅ Server initialized successfully")
                print(f"   Server: {response.get('result', {}).get('serverInfo', {}).get('name', 'Unknown')}")
            else:
                print("❌ Server initialization failed")
                print(f"   Response: {response}")
                return False
        
        # Send initialized notification
        client.send_notification("initialized", {})
        
        # Test 2: Document Open
        print("\n📋 Test 2: Document Operations")
        doc_uri = "file:///home/<USER>/Desktop/Umbra/test_lsp.umbra"
        doc_content = """// Test Umbra file
fn main() -> void {
    let x: Integer := 42
    show("Hello, Umbra!")
}"""
        
        client.send_notification("textDocument/didOpen", {
            "textDocument": {
                "uri": doc_uri,
                "languageId": "umbra",
                "version": 1,
                "text": doc_content
            }
        })
        print("✅ Document opened successfully")
        
        # Test 3: Completion
        print("\n📋 Test 3: Code Completion")
        completion_request_id = client.send_request("textDocument/completion", {
            "textDocument": {"uri": doc_uri},
            "position": {"line": 2, "character": 8}
        })
        
        if completion_request_id:
            response = client.get_response(timeout=5)
            if response and response.get("id") == completion_request_id:
                items = response.get("result", [])
                if items:
                    print(f"✅ Completion working - {len(items)} items found")
                    # Show first few completions
                    for item in items[:3]:
                        print(f"   - {item.get('label', 'Unknown')}: {item.get('detail', 'No detail')}")
                else:
                    print("⚠️  Completion returned no items")
            else:
                print("❌ Completion request failed")
        
        # Test 4: Hover
        print("\n📋 Test 4: Hover Information")
        hover_request_id = client.send_request("textDocument/hover", {
            "textDocument": {"uri": doc_uri},
            "position": {"line": 3, "character": 4}  # On "show"
        })
        
        if hover_request_id:
            response = client.get_response(timeout=5)
            if response and response.get("id") == hover_request_id:
                hover_result = response.get("result")
                if hover_result:
                    print("✅ Hover working")
                    contents = hover_result.get("contents", "")
                    if isinstance(contents, dict):
                        contents = contents.get("value", "")
                    print(f"   Info: {contents[:100]}...")
                else:
                    print("⚠️  Hover returned no information")
            else:
                print("❌ Hover request failed")
        
        print("\n🎉 LSP Integration Test Completed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False
    finally:
        client.close()

if __name__ == "__main__":
    success = test_lsp_server()
    sys.exit(0 if success else 1)
