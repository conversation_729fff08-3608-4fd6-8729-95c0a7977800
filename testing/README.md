# Testing Directory

This directory contains all test files, scripts, and debugging utilities for the Umbra programming language.

## Structure

- `test_files/` - Umbra test programs and demo files
- `scripts/` - Shell scripts for building, testing, and deployment
- `debug_files/` - Python debugging scripts and utilities
- `data_files/` - Test data files (CSV, JSON, etc.)
- `temp_files/` - Temporary files and executables

## Usage

To run tests from this directory:
```bash
cd ../umbra-compiler
cargo run run ../testing/test_files/test_basic_functionality.umbra
```

To execute scripts:
```bash
cd testing/scripts
./build_installers.sh
```
