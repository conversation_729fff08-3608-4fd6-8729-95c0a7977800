// Test Module System - Constants Only
// Focus on importing and using constants from std.math

show("Module Constants Test")
show("=====================")

// Import the math module
show("Importing std.math module...")
bring std.math

// Test using the imported constants
show("Testing imported constants...")

let pi_value: Float := PI
show("PI constant: ", pi_value)

let e_value: Float := E  
show("E constant: ", e_value)

// Test basic math with constants
let circle_area: Float := PI * 5.0 * 5.0
show("Area of circle with radius 5: ", circle_area)

show("Module constants test completed successfully!")
