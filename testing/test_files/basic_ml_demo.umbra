// Basic Umbra ML Demo: Create, Train, Evaluate, and Visualize
// Demonstrates core ML concepts with proper Umbra syntax

structure DataPoint:
    x: Float
    y: Float

define main() -> Void:
    show("🚀 Umbra ML Demo: Machine Learning Pipeline")
    show("=" * 50)
    
    // Step 1: Create dataset
    show("📊 Step 1: Creating dataset...")
    let dataset: Array[DataPoint] := create_dataset()
    show("✅ Dataset created with ", dataset.length(), " points")
    
    // Step 2: Create model
    show("🧠 Step 2: Creating linear model...")
    let model: LinearModel := create_model()
    show("✅ Linear regression model created")
    
    // Step 3: Train model
    show("🚀 Step 3: Training model...")
    let training_result: TrainingResult := train_model(model, dataset)
    show("✅ Training completed in ", training_result.iterations, " iterations")
    show("📈 Final loss: ", training_result.final_loss)
    
    // Step 4: Evaluate model
    show("📊 Step 4: Evaluating model...")
    let metrics: ModelMetrics := evaluate_model(model, dataset)
    show("✅ Evaluation completed")
    show("📈 R² Score: ", metrics.r2_score)
    show("📈 Mean Squared Error: ", metrics.mse)
    
    // Step 5: Make predictions
    show("🎯 Step 5: Making predictions...")
    make_predictions(model)
    
    // Step 6: Visualize results
    show("📊 Step 6: Visualizing results...")
    visualize_model(model, dataset, metrics)
    
    show("")
    show("✅ ML Pipeline completed successfully!")
    show("🎉 Umbra ML Demo finished!")

define create_dataset() -> Array[DataPoint]:
    let data: Array[DataPoint] := []
    
    // Generate synthetic linear data: y = 2x + 3 + noise
    repeat i in range(0, 50):
        let x_val: Float := i.to_float() / 10.0
        let y_val: Float := 2.0 * x_val + 3.0 + random_noise()
        
        let point: DataPoint := DataPoint(x_val, y_val)
        data.push(point)
    
    return data

define create_model() -> LinearModel:
    // Create a simple linear regression model
    let model: LinearModel := LinearModel()
    model.set_learning_rate(0.01)
    model.set_max_iterations(1000)
    return model

define train_model(model: LinearModel, data: Array[DataPoint]) -> TrainingResult:
    // Extract features and labels
    let features: Array[Float] := []
    let labels: Array[Float] := []
    
    repeat point in data:
        features.push(point.x)
        labels.push(point.y)
    
    // Train using Umbra's native ML syntax
    let result: TrainingResult := train model using features, labels:
        learning_rate := 0.01
        max_iterations := 1000
        optimizer := "sgd"
    
    return result

define evaluate_model(model: LinearModel, data: Array[DataPoint]) -> ModelMetrics:
    let features: Array[Float] := []
    let labels: Array[Float] := []
    
    repeat point in data:
        features.push(point.x)
        labels.push(point.y)
    
    // Evaluate using Umbra's native evaluate syntax
    let metrics: ModelMetrics := evaluate model using features, labels:
        metrics := ["r2", "mse", "mae"]
    
    return metrics

define make_predictions(model: LinearModel) -> Void:
    let test_inputs: Array[Float] := [1.0, 2.0, 3.0, 4.0, 5.0]
    
    repeat input in test_inputs:
        let prediction: Float := predict model using input
        let expected: Float := 2.0 * input + 3.0  // True function
        let error: Float := abs_diff(prediction, expected)
        
        show("   Input: ", input, " → Predicted: ", prediction, 
             " (Expected: ", expected, ", Error: ", error, ")")

define visualize_model(model: LinearModel, data: Array[DataPoint], metrics: ModelMetrics) -> Void:
    show("📊 Model Visualization:")
    
    // Show model parameters
    let coefficients: Array[Float] := model.get_coefficients()
    show("   Model equation: y = ", coefficients[1], " * x + ", coefficients[0])
    
    // Show performance metrics
    show("   R² Score: ", metrics.r2_score * 100.0, "%")
    show("   Mean Squared Error: ", metrics.mse)
    show("   Mean Absolute Error: ", metrics.mae)
    
    // Show sample predictions vs actual
    show("   Sample predictions:")
    let sample_size: Integer := min_val(5, data.length())
    
    repeat i in range(0, sample_size):
        let point: DataPoint := data[i]
        let prediction: Float := predict model using point.x
        show("     x=", point.x, " actual=", point.y, " predicted=", prediction)
    
    // Cross-validation
    show("   Cross-validation (5-fold):")
    let cv_scores: Array[Float] := cross_validate model using data:
        folds := 5
        metric := "r2"
    
    let cv_mean: Float := calculate_mean(cv_scores)
    show("     Mean CV Score: ", cv_mean)

// Helper functions
define random_noise() -> Float:
    // Simple noise generator for demo
    return (random_seed() % 100 - 50) / 100.0

define random_seed() -> Integer:
    // Simple pseudo-random for demo
    return 42  // In real implementation, would be truly random

define abs_diff(a: Float, b: Float) -> Float:
    when a > b:
        return a - b
    otherwise:
        return b - a

define min_val(a: Integer, b: Integer) -> Integer:
    when a < b:
        return a
    otherwise:
        return b

define calculate_mean(values: Array[Float]) -> Float:
    let sum: Float := 0.0
    repeat value in values:
        sum := sum + value
    return sum / values.length().to_float()

// ML Type definitions for demonstration
structure LinearModel:
    learning_rate: Float
    max_iterations: Integer
    coefficients: Array[Float]

structure TrainingResult:
    final_loss: Float
    iterations: Integer
    training_time: Float

structure ModelMetrics:
    r2_score: Float
    mse: Float
    mae: Float
