// Working Umbra ML Demo: Create, Train, Evaluate, and Visualize
// Demonstrates core ML concepts with proper Umbra syntax

define main() -> Void:
    show("🚀 Umbra ML Demo: Machine Learning Pipeline")
    show("=" * 50)
    
    // Step 1: Create dataset
    show("📊 Step 1: Creating synthetic dataset...")
    let dataset: Array[Float] := create_linear_dataset()
    show("✅ Dataset created with ", dataset.length(), " points")
    
    // Step 2: Create and train model
    show("🧠 Step 2: Creating and training linear model...")
    let model: LinearModel := train_linear_model(dataset)
    show("✅ Model trained successfully")
    
    // Step 3: Evaluate model
    show("📊 Step 3: Evaluating model performance...")
    let accuracy: Float := evaluate_model(model, dataset)
    show("✅ Model accuracy: ", accuracy * 100.0, "%")
    
    // Step 4: Make predictions
    show("🎯 Step 4: Making sample predictions...")
    make_sample_predictions(model)
    
    // Step 5: Visualize results
    show("📊 Step 5: Visualizing model performance...")
    visualize_model_performance(model, dataset, accuracy)
    
    show("")
    show("✅ ML Pipeline completed successfully!")
    show("🎉 Umbra ML Demo finished!")

// Create synthetic linear dataset: y = 2x + 3 + noise
define create_linear_dataset() -> Array[Float]:
    let data: Array[Float] := []
    
    // Generate 20 data points
    repeat i in range(0, 20):
        let x: Float := i.to_float()
        let y: Float := 2.0 * x + 3.0 + random_noise()
        
        // Store as pairs: [x1, y1, x2, y2, ...]
        data.push(x)
        data.push(y)
    
    return data

// Train a simple linear model using Umbra's ML capabilities
define train_linear_model(data: Array[Float]) -> LinearModel:
    // Extract features and labels
    let features: Array[Float] := []
    let labels: Array[Float] := []
    
    let i: Integer := 0
    repeat i in range(0, data.length() / 2):
        let idx: Integer := i * 2
        features.push(data[idx])      // x values
        labels.push(data[idx + 1])    // y values
    
    // Create and train model using Umbra's native ML syntax
    let model: LinearModel := LinearModel()
    
    // Train the model (conceptual - shows Umbra ML syntax)
    train model using features, labels:
        learning_rate := 0.01
        max_iterations := 1000
        optimizer := "sgd"
        verbose := true
    
    show("   Model trained with ", features.length(), " samples")
    show("   Learning rate: 0.01, Iterations: 1000")
    
    return model

// Evaluate model performance
define evaluate_model(model: LinearModel, data: Array[Float]) -> Float:
    let total_error: Float := 0.0
    let num_samples: Integer := data.length() / 2
    
    repeat i in range(0, num_samples):
        let idx: Integer := i * 2
        let x: Float := data[idx]
        let actual_y: Float := data[idx + 1]
        
        // Make prediction using Umbra's predict syntax
        let predicted_y: Float := predict model using x
        
        // Calculate error
        let error: Float := abs_difference(actual_y, predicted_y)
        total_error := total_error + error
    
    // Calculate accuracy (1 - normalized error)
    let mean_error: Float := total_error / num_samples.to_float()
    let accuracy: Float := max_float(0.0, 1.0 - (mean_error / 10.0))  // Normalize
    
    show("   Mean absolute error: ", mean_error)
    show("   Model accuracy: ", accuracy * 100.0, "%")
    
    return accuracy

// Make sample predictions
define make_sample_predictions(model: LinearModel) -> Void:
    let test_values: Array[Float] := [1.0, 5.0, 10.0, 15.0, 20.0]
    
    show("   Sample predictions:")
    repeat x in test_values:
        let prediction: Float := predict model using x
        let expected: Float := 2.0 * x + 3.0  // True function
        let error: Float := abs_difference(prediction, expected)
        
        show("     x=", x, " → predicted=", prediction, 
             " (expected=", expected, ", error=", error, ")")

// Visualize model performance
define visualize_model_performance(model: LinearModel, data: Array[Float], accuracy: Float) -> Void:
    show("   📈 Model Performance Visualization:")
    
    // Show model equation (conceptual)
    let slope: Float := model.get_slope()
    let intercept: Float := model.get_intercept()
    show("     Model equation: y = ", slope, " * x + ", intercept)
    
    // Show performance metrics
    show("     Overall accuracy: ", accuracy * 100.0, "%")
    
    // Show sample fit visualization (text-based)
    show("     Sample data vs predictions:")
    let num_samples: Integer := min_integer(5, data.length() / 2)
    
    repeat i in range(0, num_samples):
        let idx: Integer := i * 2
        let x: Float := data[idx]
        let actual: Float := data[idx + 1]
        let predicted: Float := predict model using x
        
        // Simple text visualization
        let actual_bar: String := create_bar(actual, 20.0)
        let pred_bar: String := create_bar(predicted, 20.0)
        
        show("     x=", x, " actual:", actual_bar, " pred:", pred_bar)
    
    // Cross-validation simulation
    show("     Cross-validation (5-fold simulation):")
    let cv_scores: Array[Float] := simulate_cross_validation(model, data)
    let cv_mean: Float := calculate_mean(cv_scores)
    show("       Mean CV score: ", cv_mean * 100.0, "%")

// Helper functions
define random_noise() -> Float:
    // Simple noise generator (deterministic for demo)
    return (random_seed() % 200 - 100) / 100.0  // -1.0 to 1.0

define random_seed() -> Integer:
    // Simple pseudo-random for demo consistency
    return 42

define abs_difference(a: Float, b: Float) -> Float:
    when a > b:
        return a - b
    otherwise:
        return b - a

define max_float(a: Float, b: Float) -> Float:
    when a > b:
        return a
    otherwise:
        return b

define min_integer(a: Integer, b: Integer) -> Integer:
    when a < b:
        return a
    otherwise:
        return b

define calculate_mean(values: Array[Float]) -> Float:
    let sum: Float := 0.0
    repeat value in values:
        sum := sum + value
    return sum / values.length().to_float()

define create_bar(value: Float, max_val: Float) -> String:
    let normalized: Float := value / max_val
    let bar_length: Integer := (normalized * 10.0).to_integer()
    let bar: String := ""
    
    repeat i in range(0, bar_length):
        bar := bar + "█"
    
    return bar + " (" + value.to_string() + ")"

define simulate_cross_validation(model: LinearModel, data: Array[Float]) -> Array[Float]:
    // Simulate 5-fold cross-validation results
    let scores: Array[Float] := [0.85, 0.88, 0.82, 0.90, 0.87]
    return scores

// ML Type definitions (conceptual structures for demo)
structure LinearModel:
    slope: Float
    intercept: Float
    learning_rate: Float
    trained: Boolean

// Conceptual ML operations (these would be implemented in the actual ML library)
define train(model: LinearModel, features: Array[Float], labels: Array[Float]) -> Void:
    // This would contain the actual training algorithm
    // For demo, we'll simulate training
    model.slope := 2.1      // Close to true slope of 2.0
    model.intercept := 2.8  // Close to true intercept of 3.0
    model.trained := true

define predict(model: LinearModel, x: Float) -> Float:
    // Simple linear prediction: y = slope * x + intercept
    return model.slope * x + model.intercept

define evaluate(model: LinearModel, features: Array[Float], labels: Array[Float]) -> Float:
    // This would contain actual evaluation metrics
    // For demo, return simulated accuracy
    return 0.85

define cross_validate(model: LinearModel, data: Array[Float]) -> Array[Float]:
    // This would perform actual cross-validation
    // For demo, return simulated scores
    return [0.85, 0.88, 0.82, 0.90, 0.87]
