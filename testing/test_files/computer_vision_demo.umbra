// Umbra Computer Vision Demo: CNN Image Classification
// Demonstrates advanced computer vision capabilities

bring std.ml.vision;
bring std.data.image;
bring std.viz;
bring std.math;

// CNN Architecture for image classification
struct CNNConfig {
    input_shape: Array<Integer>,  // [height, width, channels]
    num_classes: Integer,
    learning_rate: Float,
    epochs: Integer,
    batch_size: Integer
}

// Image dataset structure
struct ImageDataset {
    images: Tensor<Float>,        // 4D tensor: [batch, height, width, channels]
    labels: Array<Integer>,
    class_names: Array<String>,
    image_paths: Array<String>
}

// Generate synthetic image dataset (CIFAR-10 style)
fn generate_image_dataset(samples_per_class: Integer, classes: Integer) -> ImageDataset {
    show("📸 Generating synthetic image dataset...");
    show("   Image size: 32x32x3, Classes: ", classes, ", Samples per class: ", samples_per_class);
    
    let total_samples: Integer = samples_per_class * classes;
    let images: Tensor<Float> = Tensor::new([total_samples, 32, 32, 3]);
    let labels: Array<Integer> = [];
    let class_names: Array<String> = [
        "airplane", "automobile", "bird", "cat", "deer",
        "dog", "frog", "horse", "ship", "truck"
    ];
    let image_paths: Array<String> = [];
    
    let sample_idx: Integer = 0;
    
    for class_id in 0..classes {
        show("   Generating class: ", class_names[class_id]);
        
        for i in 0..samples_per_class {
            // Generate synthetic image with class-specific patterns
            for h in 0..32 {
                for w in 0..32 {
                    // Create class-specific color patterns
                    let base_color: Array<Float> = get_class_color(class_id);
                    
                    // Add spatial patterns
                    let pattern: Float = sin((h + w) * 0.2 + class_id * 1.5);
                    let noise: Float = random_float(-0.1, 0.1);
                    
                    images[sample_idx][h][w][0] = clamp(base_color[0] + pattern * 0.3 + noise, 0.0, 1.0);
                    images[sample_idx][h][w][1] = clamp(base_color[1] + pattern * 0.3 + noise, 0.0, 1.0);
                    images[sample_idx][h][w][2] = clamp(base_color[2] + pattern * 0.3 + noise, 0.0, 1.0);
                }
            }
            
            labels.push(class_id);
            image_paths.push("synthetic_" + class_id.to_string() + "_" + i.to_string() + ".png");
            sample_idx += 1;
        }
    }
    
    show("✅ Image dataset generated: ", total_samples, " images");
    
    return ImageDataset {
        images: images,
        labels: labels,
        class_names: class_names[0..classes],
        image_paths: image_paths
    };
}

// Create CNN model
fn create_cnn_model(config: CNNConfig) -> ConvolutionalNeuralNetwork {
    show("🧠 Creating Convolutional Neural Network...");
    
    let cnn: ConvolutionalNeuralNetwork = ConvolutionalNeuralNetwork::new() {
        // Input layer
        input_layer: Input(shape: config.input_shape),
        
        // Convolutional blocks
        conv_blocks: [
            // Block 1
            Conv2D(filters: 32, kernel_size: [3, 3], activation: "relu", padding: "same"),
            BatchNormalization(),
            Conv2D(filters: 32, kernel_size: [3, 3], activation: "relu", padding: "same"),
            MaxPooling2D(pool_size: [2, 2]),
            Dropout(0.25),
            
            // Block 2
            Conv2D(filters: 64, kernel_size: [3, 3], activation: "relu", padding: "same"),
            BatchNormalization(),
            Conv2D(filters: 64, kernel_size: [3, 3], activation: "relu", padding: "same"),
            MaxPooling2D(pool_size: [2, 2]),
            Dropout(0.25),
            
            // Block 3
            Conv2D(filters: 128, kernel_size: [3, 3], activation: "relu", padding: "same"),
            BatchNormalization(),
            Conv2D(filters: 128, kernel_size: [3, 3], activation: "relu", padding: "same"),
            MaxPooling2D(pool_size: [2, 2]),
            Dropout(0.25)
        ],
        
        // Classifier head
        classifier: [
            Flatten(),
            Dense(512, activation: "relu"),
            BatchNormalization(),
            Dropout(0.5),
            Dense(256, activation: "relu"),
            Dropout(0.5),
            Dense(config.num_classes, activation: "softmax")
        ],
        
        // Optimizer and loss
        optimizer: Adam(learning_rate: config.learning_rate, beta1: 0.9, beta2: 0.999),
        loss: "categorical_crossentropy",
        metrics: ["accuracy", "top_5_accuracy"]
    };
    
    show("✅ CNN created successfully!");
    cnn.summary();
    
    return cnn;
}

// Train CNN with data augmentation
fn train_cnn_model(cnn: &mut ConvolutionalNeuralNetwork, data: ImageDataset, 
                   config: CNNConfig) -> TrainingHistory {
    show("🚀 Training CNN with data augmentation...");
    
    // Data augmentation pipeline
    let augmentation: DataAugmentation = DataAugmentation {
        rotation_range: 15.0,
        width_shift_range: 0.1,
        height_shift_range: 0.1,
        horizontal_flip: true,
        zoom_range: 0.1,
        brightness_range: [0.8, 1.2],
        fill_mode: "nearest"
    };
    
    // Prepare training data
    let X_train: Tensor<Float> = data.images;
    let y_train: Matrix<Float> = one_hot_encode_images(data.labels, config.num_classes);
    
    // Advanced callbacks
    let callbacks: Array<Callback> = [
        EarlyStopping {
            monitor: "val_accuracy",
            patience: 15,
            restore_best_weights: true
        },
        ReduceLROnPlateau {
            monitor: "val_loss",
            factor: 0.2,
            patience: 8,
            min_lr: 1e-7
        },
        ModelCheckpoint {
            filepath: "best_cnn_model.umbra",
            monitor: "val_accuracy",
            save_best_only: true
        },
        CSVLogger {
            filename: "training_log.csv"
        },
        TensorBoard {
            log_dir: "./logs"
        }
    ];
    
    // Train with augmentation
    let history: TrainingHistory = train cnn with X_train, y_train using {
        epochs: config.epochs,
        batch_size: config.batch_size,
        validation_split: 0.2,
        data_augmentation: augmentation,
        callbacks: callbacks,
        verbose: true,
        shuffle: true,
        class_weight: "balanced"
    };
    
    show("✅ CNN training completed!");
    show("📈 Best validation accuracy: ", history.best_val_accuracy * 100.0, "%");
    show("⏱️  Total training time: ", history.total_time, " seconds");
    
    return history;
}

// Evaluate CNN with comprehensive metrics
fn evaluate_cnn_model(cnn: &ConvolutionalNeuralNetwork, test_data: ImageDataset) -> VisionEvaluationReport {
    show("📊 Evaluating CNN model...");
    
    let X_test: Tensor<Float> = test_data.images;
    let y_test: Matrix<Float> = one_hot_encode_images(test_data.labels, test_data.class_names.length());
    
    // Make predictions
    let predictions: Matrix<Float> = predict cnn with X_test;
    let predicted_classes: Array<Integer> = argmax(predictions);
    
    // Comprehensive evaluation
    let report: VisionEvaluationReport = evaluate cnn with X_test, y_test using {
        metrics: ["accuracy", "precision", "recall", "f1_score", "top_5_accuracy"],
        class_names: test_data.class_names,
        generate_cam: true,  // Class Activation Maps
        generate_gradcam: true,  // Gradient-weighted CAM
        detailed: true
    };
    
    show("📈 CNN Evaluation Results:");
    show("   Accuracy: ", report.accuracy * 100.0, "%");
    show("   Top-5 Accuracy: ", report.top_5_accuracy * 100.0, "%");
    show("   Macro F1-Score: ", report.macro_f1);
    show("   Weighted F1-Score: ", report.weighted_f1);
    
    // Per-class metrics
    show("📊 Per-Class Performance:");
    for i in 0..test_data.class_names.length() {
        show("   ", test_data.class_names[i], ": Precision=", report.per_class_precision[i],
             ", Recall=", report.per_class_recall[i], ", F1=", report.per_class_f1[i]);
    }
    
    return report;
}

// Advanced computer vision visualization
fn visualize_cnn_results(cnn: &ConvolutionalNeuralNetwork, data: ImageDataset, 
                        history: TrainingHistory, report: VisionEvaluationReport) {
    show("📊 Creating computer vision visualizations...");
    
    // 1. Training curves with multiple metrics
    let training_plot: Plot = create_subplot(2, 3, "CNN Training Analysis");
    
    training_plot.subplot(0, 0) {
        plot(history.epochs, history.train_loss, "blue", "Training");
        plot(history.epochs, history.val_loss, "red", "Validation");
        title("Loss Curves");
        xlabel("Epoch");
        ylabel("Loss");
        legend();
    };
    
    training_plot.subplot(0, 1) {
        plot(history.epochs, history.train_accuracy, "blue", "Training");
        plot(history.epochs, history.val_accuracy, "red", "Validation");
        title("Accuracy Curves");
        xlabel("Epoch");
        ylabel("Accuracy");
        legend();
    };
    
    training_plot.subplot(0, 2) {
        plot(history.epochs, history.learning_rates, "green");
        title("Learning Rate Schedule");
        xlabel("Epoch");
        ylabel("Learning Rate");
        yscale("log");
    };
    
    training_plot.subplot(1, 0) {
        plot(history.epochs, history.train_top5_accuracy, "blue", "Training");
        plot(history.epochs, history.val_top5_accuracy, "red", "Validation");
        title("Top-5 Accuracy");
        xlabel("Epoch");
        ylabel("Top-5 Accuracy");
        legend();
    };
    
    training_plot.subplot(1, 1) {
        bar(data.class_names, report.per_class_f1, "purple");
        title("Per-Class F1 Scores");
        xlabel("Class");
        ylabel("F1 Score");
        xticks(rotation: 45);
    };
    
    training_plot.subplot(1, 2) {
        plot(history.epochs, history.gradient_norms, "orange");
        title("Gradient Norms");
        xlabel("Epoch");
        ylabel("Gradient Norm");
    };
    
    display_plot(training_plot);
    save_plot(training_plot, "cnn_training_analysis.png");
    
    // 2. Confusion Matrix Heatmap
    let cm_plot: Plot = create_heatmap(report.confusion_matrix) {
        title: "CNN Confusion Matrix",
        xlabel: "Predicted Class",
        ylabel: "True Class",
        class_names: data.class_names,
        colormap: "Blues",
        annotations: true,
        figsize: [10, 8]
    };
    
    display_plot(cm_plot);
    save_plot(cm_plot, "cnn_confusion_matrix.png");
    
    // 3. Sample predictions with confidence
    let samples_plot: Plot = visualize_sample_predictions(cnn, data, num_samples: 16) {
        title: "Sample Predictions with Confidence",
        show_confidence: true,
        show_true_labels: true
    };
    
    display_plot(samples_plot);
    save_plot(samples_plot, "sample_predictions.png");
    
    // 4. Feature maps visualization
    let feature_maps: Plot = visualize_feature_maps(cnn, data.images[0]) {
        layers: ["conv2d_1", "conv2d_3", "conv2d_5"],
        title: "Convolutional Feature Maps"
    };
    
    display_plot(feature_maps);
    save_plot(feature_maps, "feature_maps.png");
    
    // 5. Class Activation Maps (CAM)
    let cam_plot: Plot = visualize_class_activation_maps(cnn, data, num_samples: 8) {
        title: "Class Activation Maps (Grad-CAM)",
        overlay_alpha: 0.6
    };
    
    display_plot(cam_plot);
    save_plot(cam_plot, "class_activation_maps.png");
    
    // 6. Filter visualization
    let filters_plot: Plot = visualize_conv_filters(cnn, layer: "conv2d_1") {
        title: "First Layer Convolutional Filters",
        normalize: true
    };
    
    display_plot(filters_plot);
    save_plot(filters_plot, "conv_filters.png");
    
    show("💾 All computer vision visualizations saved!");
}

// Advanced CNN analysis
fn analyze_cnn_model(cnn: &ConvolutionalNeuralNetwork, data: ImageDataset) {
    show("🔍 Performing advanced CNN analysis...");
    
    // 1. Layer-wise feature analysis
    let layer_analysis: LayerAnalysis = analyze_conv_layers(cnn) {
        include_activations: true,
        include_gradients: true,
        sample_images: data.images[0..10]
    };
    
    show("🧠 Convolutional Layer Analysis:");
    for layer in layer_analysis.conv_layers {
        show("   ", layer.name, ":");
        show("     Filters: ", layer.num_filters);
        show("     Kernel size: ", layer.kernel_size);
        show("     Output shape: ", layer.output_shape);
        show("     Activation sparsity: ", layer.activation_sparsity);
        show("     Dead neurons: ", layer.dead_neurons, "%");
    }
    
    // 2. Adversarial robustness for images
    let robustness: ImageRobustnessReport = test_image_adversarial_robustness(cnn, data) {
        attack_methods: ["fgsm", "pgd", "c&w"],
        epsilon_values: [0.01, 0.03, 0.1],
        num_samples: 100
    };
    
    show("🛡️  Image Adversarial Robustness:");
    show("   Clean accuracy: ", robustness.clean_accuracy * 100.0, "%");
    show("   FGSM (ε=0.03): ", robustness.fgsm_accuracy * 100.0, "%");
    show("   PGD (ε=0.03): ", robustness.pgd_accuracy * 100.0, "%");
    show("   C&W attack: ", robustness.cw_accuracy * 100.0, "%");
    
    // 3. Data augmentation impact analysis
    let aug_analysis: AugmentationAnalysis = analyze_augmentation_impact(cnn, data) {
        augmentation_types: ["rotation", "flip", "brightness", "zoom"],
        num_samples: 50
    };
    
    show("🔄 Data Augmentation Impact:");
    for aug_type in aug_analysis.results {
        show("   ", aug_type.name, ": ", aug_type.accuracy_change * 100.0, "% change");
    }
    
    // 4. Model compression analysis
    let compression: CompressionAnalysis = analyze_model_compression(cnn) {
        methods: ["pruning", "quantization"],
        compression_ratios: [0.1, 0.3, 0.5, 0.7, 0.9]
    };
    
    show("📦 Model Compression Analysis:");
    show("   Original size: ", compression.original_size, " MB");
    show("   50% pruning: ", compression.pruned_accuracy[2] * 100.0, "% accuracy");
    show("   8-bit quantization: ", compression.quantized_accuracy * 100.0, "% accuracy");
}

// Utility functions
fn get_class_color(class_id: Integer) -> Array<Float> {
    let colors: Array<Array<Float>> = [
        [0.8, 0.2, 0.2],  // Red for airplanes
        [0.2, 0.8, 0.2],  // Green for automobiles
        [0.2, 0.2, 0.8],  // Blue for birds
        [0.8, 0.8, 0.2],  // Yellow for cats
        [0.8, 0.2, 0.8],  // Magenta for deer
        [0.2, 0.8, 0.8],  // Cyan for dogs
        [0.6, 0.4, 0.2],  // Brown for frogs
        [0.4, 0.6, 0.8],  // Light blue for horses
        [0.7, 0.3, 0.5],  // Pink for ships
        [0.3, 0.7, 0.4]   // Light green for trucks
    ];
    return colors[class_id % colors.length()];
}

fn one_hot_encode_images(labels: Array<Integer>, num_classes: Integer) -> Matrix<Float> {
    let encoded: Matrix<Float> = Matrix::zeros(labels.length(), num_classes);
    for i in 0..labels.length() {
        encoded[i][labels[i]] = 1.0;
    }
    return encoded;
}

fn clamp(value: Float, min_val: Float, max_val: Float) -> Float {
    return max(min_val, min(max_val, value));
}

// Main computer vision pipeline
fn main() {
    show("📸 Umbra Computer Vision Demo: CNN Image Classification");
    show("=" * 80);
    
    // Configuration
    let config: CNNConfig = CNNConfig {
        input_shape: [32, 32, 3],
        num_classes: 5,  // Using 5 classes for demo
        learning_rate: 0.001,
        epochs: 50,
        batch_size: 32
    };
    
    // Step 1: Generate image dataset
    let dataset: ImageDataset = generate_image_dataset(200, config.num_classes);
    
    // Split dataset
    let split_idx: Integer = (dataset.images.shape()[0] * 0.8) as Integer;
    let train_data: ImageDataset = ImageDataset {
        images: dataset.images[0..split_idx],
        labels: dataset.labels[0..split_idx],
        class_names: dataset.class_names,
        image_paths: dataset.image_paths[0..split_idx]
    };
    let test_data: ImageDataset = ImageDataset {
        images: dataset.images[split_idx..],
        labels: dataset.labels[split_idx..],
        class_names: dataset.class_names,
        image_paths: dataset.image_paths[split_idx..]
    };
    
    show("📊 Dataset: ", train_data.images.shape()[0], " train, ", 
         test_data.images.shape()[0], " test images");
    
    // Step 2: Create CNN
    let mut cnn: ConvolutionalNeuralNetwork = create_cnn_model(config);
    
    // Step 3: Train CNN
    let history: TrainingHistory = train_cnn_model(&mut cnn, train_data, config);
    
    // Step 4: Evaluate CNN
    let report: VisionEvaluationReport = evaluate_cnn_model(&cnn, test_data);
    
    // Step 5: Visualize results
    visualize_cnn_results(&cnn, dataset, history, report);
    
    // Step 6: Advanced analysis
    analyze_cnn_model(&cnn, dataset);
    
    // Step 7: Interactive image classification
    show("🎯 Interactive Image Classification Demo:");
    let test_indices: Array<Integer> = [0, 10, 20, 30, 40];
    
    for idx in test_indices {
        let image: Tensor<Float> = test_data.images[idx];
        let prediction: Array<Float> = predict cnn with [image];
        let predicted_class: Integer = argmax(prediction);
        let confidence: Float = max(prediction);
        let true_class: Integer = test_data.labels[idx];
        
        show("   Image ", idx, ": True=", test_data.class_names[true_class], 
             ", Predicted=", test_data.class_names[predicted_class],
             " (", confidence * 100.0, "% confidence)");
    }
    
    // Final summary
    show("");
    show("📋 CNN Model Summary:");
    show("=" * 60);
    show("Architecture: CNN with ", cnn.count_layers(), " layers");
    show("Total Parameters: ", cnn.count_parameters());
    show("Input Shape: ", config.input_shape);
    show("Number of Classes: ", config.num_classes);
    show("Training Images: ", train_data.images.shape()[0]);
    show("Test Accuracy: ", report.accuracy * 100.0, "%");
    show("Top-5 Accuracy: ", report.top_5_accuracy * 100.0, "%");
    show("Training Time: ", history.total_time, " seconds");
    show("Model Size: ", cnn.get_model_size(), " MB");
    
    show("");
    show("✅ Computer Vision Pipeline completed successfully!");
    show("📊 All visualizations and analysis saved!");
    show("🎉 Umbra CNN Demo finished!");
}
