// Advanced Umbra ML Demo: Neural Network Classification
// Demonstrates deep learning capabilities with visualization

bring std.ml.neural;
bring std.data;
bring std.viz;
bring std.math;

// Define neural network architecture
struct NetworkConfig {
    input_size: Integer,
    hidden_layers: Array<Integer>,
    output_size: Integer,
    activation: String,
    learning_rate: Float,
    epochs: Integer,
    batch_size: Integer
}

// Classification dataset
struct ClassificationData {
    features: Matrix<Float>,
    labels: Array<Integer>,
    class_names: Array<String>
}

// Generate spiral classification dataset
fn generate_spiral_dataset(samples_per_class: Integer, classes: Integer) -> ClassificationData {
    show("🌀 Generating spiral classification dataset...");
    show("   Classes: ", classes, ", Samples per class: ", samples_per_class);
    
    let total_samples: Integer = samples_per_class * classes;
    let features: Matrix<Float> = Matrix::new(total_samples, 2);
    let labels: Array<Integer> = [];
    let class_names: Array<String> = [];
    
    // Generate class names
    for i in 0..classes {
        class_names.push("Class_" + i.to_string());
    }
    
    let sample_idx: Integer = 0;
    
    for class_id in 0..classes {
        for i in 0..samples_per_class {
            // Generate spiral pattern
            let r: Float = (i as Float) / (samples_per_class as Float);
            let theta: Float = (class_id as Float) * 4.0 + (i as Float) * 4.0 / (samples_per_class as Float);
            
            // Add noise
            let noise_x: Float = random_float(-0.1, 0.1);
            let noise_y: Float = random_float(-0.1, 0.1);
            
            features[sample_idx][0] = r * cos(theta) + noise_x;
            features[sample_idx][1] = r * sin(theta) + noise_y;
            labels.push(class_id);
            
            sample_idx += 1;
        }
    }
    
    show("✅ Spiral dataset generated: ", total_samples, " samples");
    
    return ClassificationData {
        features: features,
        labels: labels,
        class_names: class_names
    };
}

// Create neural network
fn create_neural_network(config: NetworkConfig) -> NeuralNetwork {
    show("🧠 Creating Neural Network...");
    show("   Architecture: ", config.input_size, " → ", config.hidden_layers, " → ", config.output_size);
    
    let network: NeuralNetwork = NeuralNetwork::new() {
        // Input layer
        input_layer: Dense(config.input_size),
        
        // Hidden layers
        hidden_layers: [
            Dense(config.hidden_layers[0], activation: config.activation),
            Dropout(0.2),
            Dense(config.hidden_layers[1], activation: config.activation),
            Dropout(0.2),
            Dense(config.hidden_layers[2], activation: config.activation)
        ],
        
        // Output layer
        output_layer: Dense(config.output_size, activation: "softmax"),
        
        // Optimizer
        optimizer: Adam(learning_rate: config.learning_rate),
        
        // Loss function
        loss: "categorical_crossentropy",
        
        // Metrics
        metrics: ["accuracy", "precision", "recall", "f1_score"]
    };
    
    show("✅ Neural network created successfully!");
    network.summary();
    
    return network;
}

// Train neural network with advanced features
fn train_neural_network(network: &mut NeuralNetwork, data: ClassificationData, 
                       config: NetworkConfig) -> TrainingHistory {
    show("🚀 Training Neural Network...");
    
    // Prepare data
    let X_train: Matrix<Float> = data.features;
    let y_train: Matrix<Float> = one_hot_encode(data.labels, data.class_names.length());
    
    // Set up callbacks
    let callbacks: Array<Callback> = [
        EarlyStopping {
            monitor: "val_loss",
            patience: 10,
            restore_best_weights: true
        },
        ReduceLROnPlateau {
            monitor: "val_loss",
            factor: 0.5,
            patience: 5,
            min_lr: 0.0001
        },
        ModelCheckpoint {
            filepath: "best_model.umbra",
            monitor: "val_accuracy",
            save_best_only: true
        }
    ];
    
    // Train the network
    let history: TrainingHistory = train network with X_train, y_train using {
        epochs: config.epochs,
        batch_size: config.batch_size,
        validation_split: 0.2,
        callbacks: callbacks,
        verbose: true,
        shuffle: true
    };
    
    show("✅ Training completed!");
    show("📈 Best validation accuracy: ", history.best_val_accuracy);
    show("⏱️  Total training time: ", history.total_time, " seconds");
    
    return history;
}

// Evaluate neural network
fn evaluate_neural_network(network: &NeuralNetwork, test_data: ClassificationData) -> EvaluationReport {
    show("📊 Evaluating Neural Network...");
    
    let X_test: Matrix<Float> = test_data.features;
    let y_test: Matrix<Float> = one_hot_encode(test_data.labels, test_data.class_names.length());
    
    // Make predictions
    let predictions: Matrix<Float> = predict network with X_test;
    let predicted_classes: Array<Integer> = argmax(predictions);
    
    // Calculate comprehensive metrics
    let report: EvaluationReport = evaluate network with X_test, y_test using {
        metrics: ["accuracy", "precision", "recall", "f1_score", "confusion_matrix"],
        class_names: test_data.class_names,
        detailed: true
    };
    
    show("📈 Evaluation Results:");
    show("   Accuracy: ", report.accuracy * 100.0, "%");
    show("   Precision: ", report.precision);
    show("   Recall: ", report.recall);
    show("   F1-Score: ", report.f1_score);
    
    // Display confusion matrix
    show("🔢 Confusion Matrix:");
    display_confusion_matrix(report.confusion_matrix, test_data.class_names);
    
    return report;
}

// Advanced visualization suite
fn visualize_neural_network(network: &NeuralNetwork, data: ClassificationData, 
                           history: TrainingHistory, report: EvaluationReport) {
    show("📊 Creating comprehensive visualizations...");
    
    // 1. Training History
    let history_plot: Plot = create_subplot(2, 2, "Neural Network Training Analysis");
    
    // Loss curves
    history_plot.subplot(0, 0) {
        plot(history.epochs, history.train_loss, "blue", "Training Loss");
        plot(history.epochs, history.val_loss, "red", "Validation Loss");
        title("Loss Curves");
        xlabel("Epoch");
        ylabel("Loss");
        legend();
    };
    
    // Accuracy curves
    history_plot.subplot(0, 1) {
        plot(history.epochs, history.train_accuracy, "blue", "Training Accuracy");
        plot(history.epochs, history.val_accuracy, "red", "Validation Accuracy");
        title("Accuracy Curves");
        xlabel("Epoch");
        ylabel("Accuracy");
        legend();
    };
    
    // Learning rate schedule
    history_plot.subplot(1, 0) {
        plot(history.epochs, history.learning_rates, "green", "Learning Rate");
        title("Learning Rate Schedule");
        xlabel("Epoch");
        ylabel("Learning Rate");
        yscale("log");
    };
    
    // Gradient norms
    history_plot.subplot(1, 1) {
        plot(history.epochs, history.gradient_norms, "purple", "Gradient Norm");
        title("Gradient Norms");
        xlabel("Epoch");
        ylabel("Gradient Norm");
    };
    
    display_plot(history_plot);
    save_plot(history_plot, "training_history.png");
    
    // 2. Decision Boundary Visualization
    let boundary_plot: Plot = visualize_decision_boundary(network, data) {
        resolution: 100,
        alpha: 0.8,
        title: "Neural Network Decision Boundary",
        colormap: "viridis"
    };
    
    display_plot(boundary_plot);
    save_plot(boundary_plot, "decision_boundary.png");
    
    // 3. Feature Space Analysis
    let feature_plot: Plot = create_plot("Feature Space Analysis") {
        x_data: data.features.column(0),
        y_data: data.features.column(1),
        colors: data.labels,
        plot_type: "scatter",
        title: "Feature Space Distribution",
        xlabel: "Feature 1",
        ylabel: "Feature 2",
        colorbar: true
    };
    
    display_plot(feature_plot);
    save_plot(feature_plot, "feature_space.png");
    
    // 4. Network Architecture Visualization
    let arch_plot: Plot = visualize_network_architecture(network) {
        show_weights: true,
        show_activations: true,
        title: "Neural Network Architecture"
    };
    
    display_plot(arch_plot);
    save_plot(arch_plot, "network_architecture.png");
    
    // 5. Confusion Matrix Heatmap
    let cm_plot: Plot = create_heatmap(report.confusion_matrix) {
        title: "Confusion Matrix",
        xlabel: "Predicted Class",
        ylabel: "True Class",
        class_names: data.class_names,
        colormap: "Blues",
        annotations: true
    };
    
    display_plot(cm_plot);
    save_plot(cm_plot, "confusion_matrix.png");
    
    // 6. ROC Curves (for multi-class)
    let roc_plot: Plot = create_roc_curves(report.roc_data) {
        title: "ROC Curves - Multi-Class",
        class_names: data.class_names,
        show_auc: true
    };
    
    display_plot(roc_plot);
    save_plot(roc_plot, "roc_curves.png");
    
    show("💾 All visualizations saved!");
}

// Advanced model analysis
fn analyze_neural_network(network: &NeuralNetwork, data: ClassificationData) {
    show("🔍 Performing advanced neural network analysis...");
    
    // 1. Layer-wise analysis
    let layer_analysis: LayerAnalysis = analyze_layers(network) {
        include_weights: true,
        include_activations: true,
        include_gradients: true
    };
    
    show("🧠 Layer Analysis:");
    for layer in layer_analysis.layers {
        show("   ", layer.name, ": ", layer.parameters, " parameters");
        show("     Weight mean: ", layer.weight_stats.mean);
        show("     Weight std: ", layer.weight_stats.std);
        show("     Activation sparsity: ", layer.activation_sparsity);
    }
    
    // 2. Feature importance via gradients
    let importance: Array<Float> = calculate_feature_importance(network, data.features) {
        method: "integrated_gradients",
        baseline: "zero"
    };
    
    show("🎯 Feature Importance:");
    for i in 0..importance.length() {
        show("   Feature ", i, ": ", importance[i]);
    }
    
    // 3. Model interpretability
    let interpretation: ModelInterpretation = interpret_model(network, data) {
        method: "lime",
        num_samples: 1000
    };
    
    show("🔍 Model Interpretability (LIME):");
    show("   Local explanations generated for sample predictions");
    
    // 4. Adversarial robustness test
    let robustness: RobustnessReport = test_adversarial_robustness(network, data) {
        attack_methods: ["fgsm", "pgd"],
        epsilon_values: [0.01, 0.05, 0.1]
    };
    
    show("🛡️  Adversarial Robustness:");
    show("   Clean accuracy: ", robustness.clean_accuracy);
    show("   FGSM accuracy: ", robustness.fgsm_accuracy);
    show("   PGD accuracy: ", robustness.pgd_accuracy);
}

// Utility functions
fn one_hot_encode(labels: Array<Integer>, num_classes: Integer) -> Matrix<Float> {
    let encoded: Matrix<Float> = Matrix::zeros(labels.length(), num_classes);
    for i in 0..labels.length() {
        encoded[i][labels[i]] = 1.0;
    }
    return encoded;
}

fn display_confusion_matrix(matrix: Matrix<Integer>, class_names: Array<String>) {
    show("     ", class_names.join("  "));
    for i in 0..matrix.rows() {
        let row_str: String = class_names[i] + " ";
        for j in 0..matrix.cols() {
            row_str += matrix[i][j].to_string() + "  ";
        }
        show(row_str);
    }
}

// Main neural network pipeline
fn main() {
    show("🧠 Umbra Advanced ML Demo: Neural Network Classification");
    show("=" * 70);
    
    // Configuration
    let config: NetworkConfig = NetworkConfig {
        input_size: 2,
        hidden_layers: [64, 32, 16],
        output_size: 3,
        activation: "relu",
        learning_rate: 0.001,
        epochs: 100,
        batch_size: 32
    };
    
    // Step 1: Generate dataset
    let dataset: ClassificationData = generate_spiral_dataset(100, 3);
    
    // Split dataset
    let split_idx: Integer = (dataset.features.rows() * 0.8) as Integer;
    let train_data: ClassificationData = ClassificationData {
        features: dataset.features[0..split_idx],
        labels: dataset.labels[0..split_idx],
        class_names: dataset.class_names
    };
    let test_data: ClassificationData = ClassificationData {
        features: dataset.features[split_idx..],
        labels: dataset.labels[split_idx..],
        class_names: dataset.class_names
    };
    
    show("📊 Dataset: ", train_data.features.rows(), " train, ", 
         test_data.features.rows(), " test");
    
    // Step 2: Create neural network
    let mut network: NeuralNetwork = create_neural_network(config);
    
    // Step 3: Train network
    let history: TrainingHistory = train_neural_network(&mut network, train_data, config);
    
    // Step 4: Evaluate network
    let report: EvaluationReport = evaluate_neural_network(&network, test_data);
    
    // Step 5: Visualize results
    visualize_neural_network(&network, dataset, history, report);
    
    // Step 6: Advanced analysis
    analyze_neural_network(&network, dataset);
    
    // Step 7: Interactive predictions
    show("🎯 Interactive Prediction Demo:");
    let test_points: Array<Array<Float>> = [
        [0.5, 0.5],
        [-0.3, 0.8],
        [0.0, -0.5],
        [1.0, 0.0],
        [-0.8, -0.3]
    ];
    
    for point in test_points {
        let prediction: Array<Float> = predict network with [point];
        let predicted_class: Integer = argmax(prediction);
        let confidence: Float = max(prediction);
        
        show("   Point (", point[0], ", ", point[1], ") → Class: ", 
             dataset.class_names[predicted_class], " (", confidence * 100.0, "% confidence)");
    }
    
    // Final summary
    show("");
    show("📋 Neural Network Summary:");
    show("=" * 50);
    show("Architecture: ", config.input_size, " → ", config.hidden_layers, " → ", config.output_size);
    show("Total Parameters: ", network.count_parameters());
    show("Training Samples: ", train_data.features.rows());
    show("Test Accuracy: ", report.accuracy * 100.0, "%");
    show("Training Time: ", history.total_time, " seconds");
    show("Best Epoch: ", history.best_epoch);
    
    show("");
    show("✅ Advanced ML Pipeline completed successfully!");
    show("📊 All visualizations and analysis saved!");
    show("🎉 Umbra Neural Network Demo finished!");
}
