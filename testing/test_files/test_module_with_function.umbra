// Test Module System with Function
// Check if functions cause issues with imports

show("Module Test with Function")
show("==========================")

bring std.math

let pi_value: Float := PI
show("PI = ", pi_value)

fn calculate_area(radius: Float) -> Float:
    let area: Float := PI * radius * radius
    return area

let area: Float := calculate_area(5.0)
show("Area of circle with radius 5: ", area)
