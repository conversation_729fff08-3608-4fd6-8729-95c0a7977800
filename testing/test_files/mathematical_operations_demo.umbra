// Mathematical Operations Demonstration
// Showcases Umbra's mathematical capabilities and standard library functions

show("Mathematical Operations Demonstration")
show("====================================")

// Basic arithmetic operations
let a: Integer := 15
let b: Integer := 7
let c: Float := 3.14159
let d: Float := 2.71828

show("Basic Arithmetic:")
show("a = ", a)
show("b = ", b)
show("c = ", c)
show("d = ", d)

let sum: Integer := a + b
let difference: Integer := a - b
let product: Integer := a * b
let quotient: Float := 15.0 / 7.0

show("Addition: ", a, " + ", b, " = ", sum)
show("Subtraction: ", a, " - ", b, " = ", difference)
show("Multiplication: ", a, " * ", b, " = ", product)
show("Division: ", a, " / ", b, " = ", quotient)

// Advanced mathematical operations
show("Advanced Operations:")

let power_result: Float := c * c
show("c squared: ", power_result)

let mixed_calc: Float := (a + b) * c / d
show("Mixed calculation: (", a, " + ", b, ") * ", c, " / ", d, " = ", mixed_calc)

// Working with larger numbers
let large_num: Integer := 1000000
let small_num: Float := 0.001

show("Large number operations:")
show("Large number: ", large_num)
show("Small number: ", small_num)

let scaled_result: Float := large_num * small_num
show("Product: ", scaled_result)

// Mathematical sequences and patterns
show("Mathematical Sequences:")

let fibonacci_a: Integer := 1
let fibonacci_b: Integer := 1
let fibonacci_c: Integer := fibonacci_a + fibonacci_b
let fibonacci_d: Integer := fibonacci_b + fibonacci_c
let fibonacci_e: Integer := fibonacci_c + fibonacci_d

show("Fibonacci sequence: ", fibonacci_a, ", ", fibonacci_b, ", ", fibonacci_c, ", ", fibonacci_d, ", ", fibonacci_e)

// Percentage calculations
let total: Integer := 250
let part: Integer := 75
let percentage: Float := (75.0 * 100.0) / 250.0

show("Percentage calculation:")
show(part, " out of ", total, " = ", percentage, "%")

// Area and volume calculations
let radius: Float := 5.0
let pi: Float := 3.14159
let circle_area: Float := pi * radius * radius
let sphere_volume: Float := (4.0 * pi * radius * radius * radius) / 3.0

show("Geometric calculations:")
show("Circle area (r=", radius, "): ", circle_area)
show("Sphere volume (r=", radius, "): ", sphere_volume)

show("Mathematical operations demonstration complete.")
