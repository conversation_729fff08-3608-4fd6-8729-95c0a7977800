// Test Core Functionality - Basic language features
// Tests the core fixes without AI/ML dependencies

show("Core Functionality Test")
show("========================")

// Test 1: Function Definition and Calling
show("Testing Function Definition and Calling...")

fn multiply_numbers(a: Integer, b: Integer) -> Integer:
    let result: Integer := a * b
    return result

fn format_message(name: String) -> String:
    let message: String := name + " says hello"
    return message

let product: Integer := multiply_numbers(8, 7)
show("multiply_numbers(8, 7) = ", product)

let message: String := format_message("Bob")
show("format_message result: ", message)

// Test 2: Basic Mathematical Operations (using builtins)
show("Testing Basic Mathematical Operations...")

let test_number: Integer := -15
let absolute_value: Integer := abs(test_number)
show("abs(-15) = ", absolute_value)

// Test 3: Standard Library Functions (already available)
show("Testing Standard Library Functions...")

let test_string: String := "Hello, World!"
let string_length: Integer := str_len(test_string)
show("str_len('Hello, World!') = ", string_length)

// Test 4: Variable declarations and assignments
show("Testing Variable Operations...")

let x: Integer := 10
let y: Integer := 20
let sum: Integer := x + y
show("x + y = ", sum)

let greeting: String := "Welcome"
show("Greeting: ", greeting)

show("Core functionality test completed successfully!")
