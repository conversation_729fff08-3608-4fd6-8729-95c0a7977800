// Test functions with correct syntax

println!("=== Function Test ===")

// Simple function with no parameters
fn greet() -> String:
    return "Hello, Umbra!"

// Function with parameters
fn add(a: Integer, b: Integer) -> Integer:
    return a + b

// Test function calls
let greeting: String := greet()
println!("Greeting: {}", greeting)

let sum: Integer := add(10, 20)
println!("Sum: {}", sum)

println!("=== Test Completed ===")
