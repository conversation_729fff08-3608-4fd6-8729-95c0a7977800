// Umbra ML Demo: Create, Train, Evaluate, and Visualize a Model
// This demonstrates Umbra's native AI/ML capabilities

bring std.ml;
bring std.data;
bring std.viz;
bring std.math;

// Define our dataset structure
structure DataPoint:
    features: Array[Float]
    label: Float

// Generate synthetic dataset for regression
define generate_dataset(size: Integer) -> Array[DataPoint]:
    show("🔄 Generating synthetic dataset with ", size, " samples...")

    let dataset: Array[DataPoint] := []

    // Generate data points following y = 2x + 3 + noise
    repeat i in 0..size:
        let x: Float := (i as Float) / 10.0
        let noise: Float := random_float(-0.5, 0.5)
        let y: Float := 2.0 * x + 3.0 + noise

        let point: DataPoint := DataPoint(x, y)

        dataset.push(point)

    show("✅ Dataset generated successfully!")
    return dataset

// Create and configure our model
fn create_model() -> LinearRegression {
    show("🧠 Creating Linear Regression model...");
    
    let model: LinearRegression = LinearRegression {
        learning_rate: 0.01,
        max_iterations: 1000,
        tolerance: 0.001
    };
    
    show("✅ Model created with learning rate: ", model.learning_rate);
    return model;
}

// Train the model
fn train_model(model: &mut LinearRegression, data: Array<DataPoint>) -> TrainingResult {
    show("🚀 Starting model training...");
    show("📊 Training on ", data.length(), " samples");
    
    // Extract features and labels
    let features: Matrix<Float> = extract_features(data);
    let labels: Array<Float> = extract_labels(data);
    
    // Train the model
    let result: TrainingResult = train model with features, labels using {
        optimizer: "adam",
        batch_size: 32,
        validation_split: 0.2,
        early_stopping: true,
        verbose: true
    };
    
    show("✅ Training completed!");
    show("📈 Final loss: ", result.final_loss);
    show("⏱️  Training time: ", result.training_time, " seconds");
    show("🔄 Iterations: ", result.iterations);
    
    return result;
}

// Evaluate model performance
fn evaluate_model(model: &LinearRegression, test_data: Array<DataPoint>) -> EvaluationMetrics {
    show("📊 Evaluating model performance...");
    
    let test_features: Matrix<Float> = extract_features(test_data);
    let test_labels: Array<Float> = extract_labels(test_data);
    
    // Make predictions
    let predictions: Array<Float> = predict model with test_features;
    
    // Calculate metrics
    let metrics: EvaluationMetrics = evaluate model with test_features, test_labels using {
        metrics: ["mse", "mae", "r2", "rmse"],
        detailed: true
    };
    
    show("📈 Evaluation Results:");
    show("   MSE (Mean Squared Error): ", metrics.mse);
    show("   MAE (Mean Absolute Error): ", metrics.mae);
    show("   R² Score: ", metrics.r2);
    show("   RMSE: ", metrics.rmse);
    
    // Calculate accuracy percentage
    let accuracy: Float = metrics.r2 * 100.0;
    show("   Accuracy: ", accuracy, "%");
    
    return metrics;
}

// Visualize training progress and results
fn visualize_results(model: &LinearRegression, data: Array<DataPoint>, 
                    training_result: TrainingResult, metrics: EvaluationMetrics) {
    show("📊 Creating visualizations...");
    
    // 1. Training Loss Curve
    let loss_plot: Plot = create_plot("Training Loss Over Time") {
        x_data: training_result.loss_history.iterations,
        y_data: training_result.loss_history.losses,
        plot_type: "line",
        color: "blue",
        title: "Training Loss Curve",
        x_label: "Iteration",
        y_label: "Loss"
    };
    
    show("📈 Training loss curve created");
    
    // 2. Predictions vs Actual
    let features: Matrix<Float> = extract_features(data);
    let actual_labels: Array<Float> = extract_labels(data);
    let predictions: Array<Float> = predict model with features;
    
    let scatter_plot: Plot = create_plot("Predictions vs Actual") {
        x_data: actual_labels,
        y_data: predictions,
        plot_type: "scatter",
        color: "red",
        title: "Predictions vs Actual Values",
        x_label: "Actual Values",
        y_label: "Predicted Values"
    };
    
    // Add perfect prediction line
    scatter_plot.add_line(actual_labels, actual_labels, "green", "Perfect Prediction");
    
    show("🎯 Predictions vs Actual plot created");
    
    // 3. Residuals Plot
    let residuals: Array<Float> = [];
    for i in 0..predictions.length() {
        residuals.push(actual_labels[i] - predictions[i]);
    }
    
    let residuals_plot: Plot = create_plot("Residuals Analysis") {
        x_data: predictions,
        y_data: residuals,
        plot_type: "scatter",
        color: "orange",
        title: "Residuals vs Predicted Values",
        x_label: "Predicted Values",
        y_label: "Residuals"
    };
    
    show("📊 Residuals plot created");
    
    // 4. Model Coefficients Visualization
    let coefficients: Array<Float> = model.get_coefficients();
    let coef_names: Array<String> = ["Intercept", "Slope"];
    
    let coef_plot: Plot = create_plot("Model Coefficients") {
        x_data: coef_names,
        y_data: coefficients,
        plot_type: "bar",
        color: "purple",
        title: "Linear Regression Coefficients",
        x_label: "Coefficient",
        y_label: "Value"
    };
    
    show("📊 Coefficients plot created");
    
    // 5. Feature Importance (for demonstration)
    let importance: Array<Float> = [abs(coefficients[1])]; // Slope magnitude
    let feature_names: Array<String> = ["Feature_1"];
    
    let importance_plot: Plot = create_plot("Feature Importance") {
        x_data: feature_names,
        y_data: importance,
        plot_type: "bar",
        color: "green",
        title: "Feature Importance",
        x_label: "Features",
        y_label: "Importance"
    };
    
    show("🎯 Feature importance plot created");
    
    // Display all plots
    show("🖼️  Displaying visualizations...");
    display_plot(loss_plot);
    display_plot(scatter_plot);
    display_plot(residuals_plot);
    display_plot(coef_plot);
    display_plot(importance_plot);
    
    // Save plots to files
    save_plot(loss_plot, "training_loss.png");
    save_plot(scatter_plot, "predictions_vs_actual.png");
    save_plot(residuals_plot, "residuals_analysis.png");
    save_plot(coef_plot, "model_coefficients.png");
    save_plot(importance_plot, "feature_importance.png");
    
    show("💾 All plots saved to PNG files");
}

// Advanced model analysis
fn analyze_model(model: &LinearRegression, data: Array<DataPoint>) {
    show("🔍 Performing advanced model analysis...");
    
    // Cross-validation
    let cv_scores: Array<Float> = cross_validate model with data using {
        folds: 5,
        metric: "r2",
        shuffle: true
    };
    
    let cv_mean: Float = mean(cv_scores);
    let cv_std: Float = std(cv_scores);
    
    show("🔄 Cross-Validation Results:");
    show("   Mean R² Score: ", cv_mean);
    show("   Standard Deviation: ", cv_std);
    show("   Individual Scores: ", cv_scores);
    
    // Learning curves
    let learning_curves: LearningCurves = generate_learning_curves model with data using {
        train_sizes: [0.1, 0.2, 0.4, 0.6, 0.8, 1.0],
        cv_folds: 3
    };
    
    show("📈 Learning curves generated");
    
    // Model interpretation
    let interpretation: ModelInterpretation = interpret model using {
        method: "linear_coefficients",
        feature_names: ["x_value"]
    };
    
    show("🧠 Model Interpretation:");
    show("   Equation: y = ", interpretation.intercept, " + ", 
         interpretation.coefficients[0], " * x");
    show("   Feature Impact: ", interpretation.feature_impacts);
}

// Utility functions
fn extract_features(data: Array<DataPoint>) -> Matrix<Float> {
    let features: Matrix<Float> = Matrix::new(data.length(), 1);
    for i in 0..data.length() {
        features[i][0] = data[i].features[0];
    }
    return features;
}

fn extract_labels(data: Array<DataPoint>) -> Array<Float> {
    let labels: Array<Float> = [];
    for point in data {
        labels.push(point.label);
    }
    return labels;
}

fn random_float(min: Float, max: Float) -> Float {
    return min + (max - min) * random();
}

// Main ML pipeline
fn main() {
    show("🚀 Umbra ML Demo: Complete Machine Learning Pipeline");
    show("=" * 60);
    
    // Step 1: Generate dataset
    let dataset: Array<DataPoint> = generate_dataset(200);
    
    // Split into train/test
    let split_index: Integer = (dataset.length() * 0.8) as Integer;
    let train_data: Array<DataPoint> = dataset[0..split_index];
    let test_data: Array<DataPoint> = dataset[split_index..];
    
    show("📊 Dataset split: ", train_data.length(), " training, ", 
         test_data.length(), " testing");
    
    // Step 2: Create model
    let mut model: LinearRegression = create_model();
    
    // Step 3: Train model
    let training_result: TrainingResult = train_model(&mut model, train_data);
    
    // Step 4: Evaluate model
    let metrics: EvaluationMetrics = evaluate_model(&model, test_data);
    
    // Step 5: Visualize results
    visualize_results(&model, dataset, training_result, metrics);
    
    // Step 6: Advanced analysis
    analyze_model(&model, dataset);
    
    // Step 7: Make sample predictions
    show("🎯 Making sample predictions...");
    let sample_inputs: Array<Float> = [1.0, 2.5, 5.0, 7.5, 10.0];
    
    for input in sample_inputs {
        let prediction: Float = predict model with [[input]];
        let expected: Float = 2.0 * input + 3.0; // True function
        let error: Float = abs(prediction - expected);
        
        show("   Input: ", input, " → Prediction: ", prediction, 
             " (Expected: ", expected, ", Error: ", error, ")");
    }
    
    // Step 8: Model summary
    show("");
    show("📋 Model Summary:");
    show("=" * 40);
    show("Model Type: Linear Regression");
    show("Training Samples: ", train_data.length());
    show("Test Samples: ", test_data.length());
    show("Final Training Loss: ", training_result.final_loss);
    show("Test R² Score: ", metrics.r2);
    show("Test RMSE: ", metrics.rmse);
    show("Training Time: ", training_result.training_time, " seconds");
    show("Model Equation: y = ", model.get_coefficients()[0], " + ", 
         model.get_coefficients()[1], " * x");
    
    show("");
    show("✅ ML Pipeline completed successfully!");
    show("📊 Visualizations saved as PNG files");
    show("🎉 Umbra ML Demo finished!");
}
