// Simple Umbra ML Demo: Create, Train, Evaluate, and Visualize a Model
// This demonstrates Umbra's native AI/ML capabilities with correct syntax

// Define our dataset structure
structure DataPoint:
    x: Float
    y: Float

// Generate synthetic dataset for linear regression
define generate_dataset(size: Integer) -> Array[DataPoint]:
    show("🔄 Generating synthetic dataset with ", size, " samples...")
    
    let dataset: Array[DataPoint] := []
    
    // Generate data points following y = 2x + 3 + noise
    repeat i in 0..size:
        let x_val: Float := (i as Float) / 10.0
        let noise: Float := (random() - 0.5) * 1.0  // Simple noise
        let y_val: Float := 2.0 * x_val + 3.0 + noise
        
        let point: DataPoint := DataPoint(x_val, y_val)
        dataset.push(point)
    
    show("✅ Dataset generated successfully!")
    return dataset

// Create a simple linear model
define create_linear_model() -> LinearModel:
    show("🧠 Creating Linear Regression model...")
    
    let model: LinearModel := LinearModel(0.01, 1000)  // learning_rate, max_iterations
    
    show("✅ Model created with learning rate: 0.01")
    return model

// Train the model using Umbra's native ML syntax
define train_model(model: LinearModel, data: Array[DataPoint]) -> TrainingResult:
    show("🚀 Starting model training...")
    show("📊 Training on ", data.length(), " samples")
    
    // Extract features and labels for training
    let features: Array[Float] := []
    let labels: Array[Float] := []
    
    repeat point in data:
        features.push(point.x)
        labels.push(point.y)
    
    // Train the model using Umbra's native train keyword
    let result: TrainingResult := train model using features, labels:
        optimizer := "sgd"
        learning_rate := 0.01
        max_iterations := 1000
        tolerance := 0.001
        verbose := true
    
    show("✅ Training completed!")
    show("📈 Final loss: ", result.final_loss)
    show("⏱️  Training time: ", result.training_time, " seconds")
    show("🔄 Iterations: ", result.iterations)
    
    return result

// Evaluate model performance
define evaluate_model(model: LinearModel, test_data: Array[DataPoint]) -> EvaluationMetrics:
    show("📊 Evaluating model performance...")
    
    let test_features: Array[Float] := []
    let test_labels: Array[Float] := []
    
    repeat point in test_data:
        test_features.push(point.x)
        test_labels.push(point.y)
    
    // Make predictions using Umbra's native predict keyword
    let predictions: Array[Float] := predict model using test_features
    
    // Calculate metrics using Umbra's native evaluate keyword
    let metrics: EvaluationMetrics := evaluate model using test_features, test_labels:
        metrics := ["mse", "mae", "r2"]
        detailed := true
    
    show("📈 Evaluation Results:")
    show("   MSE (Mean Squared Error): ", metrics.mse)
    show("   MAE (Mean Absolute Error): ", metrics.mae)
    show("   R² Score: ", metrics.r2)
    
    // Calculate accuracy percentage
    let accuracy: Float := metrics.r2 * 100.0
    show("   Accuracy: ", accuracy, "%")
    
    return metrics

// Visualize training progress and results
define visualize_results(model: LinearModel, data: Array[DataPoint], 
                        training_result: TrainingResult, metrics: EvaluationMetrics) -> Void:
    show("📊 Creating visualizations...")
    
    // 1. Training Loss Curve
    show("📈 Training loss curve:")
    repeat i in 0..training_result.loss_history.length():
        when i % 100 == 0:
            show("   Iteration ", i, ": Loss = ", training_result.loss_history[i])
    
    // 2. Predictions vs Actual
    show("🎯 Sample predictions vs actual values:")
    let sample_size: Integer := min(10, data.length())
    
    repeat i in 0..sample_size:
        let point: DataPoint := data[i]
        let prediction: Float := predict model using [point.x]
        let error: Float := abs(prediction - point.y)
        
        show("   Input: ", point.x, " → Actual: ", point.y, 
             ", Predicted: ", prediction, ", Error: ", error)
    
    // 3. Model Coefficients
    let coefficients: Array[Float] := model.get_coefficients()
    show("📊 Model coefficients:")
    show("   Intercept: ", coefficients[0])
    show("   Slope: ", coefficients[1])
    
    show("💾 Visualization analysis completed")

// Make sample predictions
define make_sample_predictions(model: LinearModel) -> Void:
    show("🎯 Making sample predictions...")
    let sample_inputs: Array[Float] := [1.0, 2.5, 5.0, 7.5, 10.0]
    
    repeat input in sample_inputs:
        let prediction: Float := predict model using [input]
        let expected: Float := 2.0 * input + 3.0  // True function
        let error: Float := abs(prediction - expected)
        
        show("   Input: ", input, " → Prediction: ", prediction, 
             " (Expected: ", expected, ", Error: ", error, ")")

// Cross-validation analysis
define cross_validate_model(model: LinearModel, data: Array[DataPoint]) -> Array[Float]:
    show("🔄 Performing cross-validation...")
    
    let cv_scores: Array[Float] := cross_validate model using data:
        folds := 5
        metric := "r2"
        shuffle := true
    
    let cv_mean: Float := mean(cv_scores)
    let cv_std: Float := std(cv_scores)
    
    show("🔄 Cross-Validation Results:")
    show("   Mean R² Score: ", cv_mean)
    show("   Standard Deviation: ", cv_std)
    show("   Individual Scores: ", cv_scores)
    
    return cv_scores

// Main ML pipeline
define main() -> Void:
    show("🚀 Umbra ML Demo: Complete Machine Learning Pipeline")
    show("=" * 60)
    
    // Step 1: Generate dataset
    let dataset: Array[DataPoint] := generate_dataset(100)
    
    // Split into train/test (80/20 split)
    let split_index: Integer := (dataset.length() * 80) / 100
    let train_data: Array[DataPoint] := dataset[0..split_index]
    let test_data: Array[DataPoint] := dataset[split_index..]
    
    show("📊 Dataset split: ", train_data.length(), " training, ", 
         test_data.length(), " testing")
    
    // Step 2: Create model
    let model: LinearModel := create_linear_model()
    
    // Step 3: Train model
    let training_result: TrainingResult := train_model(model, train_data)
    
    // Step 4: Evaluate model
    let metrics: EvaluationMetrics := evaluate_model(model, test_data)
    
    // Step 5: Visualize results
    visualize_results(model, dataset, training_result, metrics)
    
    // Step 6: Cross-validation
    let cv_scores: Array[Float] := cross_validate_model(model, dataset)
    
    // Step 7: Make sample predictions
    make_sample_predictions(model)
    
    // Step 8: Model summary
    show("")
    show("📋 Model Summary:")
    show("=" * 40)
    show("Model Type: Linear Regression")
    show("Training Samples: ", train_data.length())
    show("Test Samples: ", test_data.length())
    show("Final Training Loss: ", training_result.final_loss)
    show("Test R² Score: ", metrics.r2)
    show("Training Time: ", training_result.training_time, " seconds")
    
    let coefficients: Array[Float] := model.get_coefficients()
    show("Model Equation: y = ", coefficients[0], " + ", coefficients[1], " * x")
    
    show("")
    show("✅ ML Pipeline completed successfully!")
    show("🎉 Umbra ML Demo finished!")

// Helper functions for demonstration
define random() -> Float:
    // Simple random number generator for demo
    return 0.5  // Placeholder - in real implementation would be random

define abs(value: Float) -> Float:
    when value < 0.0:
        return -value
    otherwise:
        return value

define min(a: Integer, b: Integer) -> Integer:
    when a < b:
        return a
    otherwise:
        return b

define mean(values: Array[Float]) -> Float:
    let sum: Float := 0.0
    repeat value in values:
        sum := sum + value
    return sum / (values.length() as Float)

define std(values: Array[Float]) -> Float:
    let mean_val: Float := mean(values)
    let sum_sq_diff: Float := 0.0
    
    repeat value in values:
        let diff: Float := value - mean_val
        sum_sq_diff := sum_sq_diff + (diff * diff)
    
    return sqrt(sum_sq_diff / (values.length() as Float))

define sqrt(value: Float) -> Float:
    // Placeholder for square root - in real implementation would use math library
    return value * 0.5  // Simplified for demo
