#!/bin/bash
# Create Umbra Compiler with Bundled LLVM Tools
# This creates a self-contained compiler that includes all LLVM tools

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
VERSION="1.0.1"
BUNDLED_DIR="$SCRIPT_DIR/bundled-llvm-compiler"
LLVM_BUNDLE_DIR="$BUNDLED_DIR/llvm-tools"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Main function
main() {
    log_info "🔧 Creating Umbra Compiler with Bundled LLVM Tools"
    log_info "=================================================="
    log_info "This will create a self-contained compiler with embedded LLVM"
    echo
    
    setup_bundled_directory
    bundle_llvm_tools
    create_enhanced_cargo_config
    create_bundled_build_script
    create_llvm_wrapper
    build_bundled_compiler
    test_bundled_compiler
    
    echo
    log_success "🎉 Bundled LLVM Compiler Created Successfully!"
    echo
    echo "📦 Self-Contained Compiler Details:"
    echo "  ✅ Location: $BUNDLED_DIR"
    echo "  ✅ LLVM Tools: Bundled and embedded"
    echo "  ✅ No External Dependencies: Completely self-contained"
    echo "  ✅ Ready for Distribution: Single binary with all tools"
}

# Setup bundled directory
setup_bundled_directory() {
    log_info "Setting up bundled compiler directory..."
    
    rm -rf "$BUNDLED_DIR"
    mkdir -p "$BUNDLED_DIR"
    mkdir -p "$LLVM_BUNDLE_DIR"
    
    # Copy the existing compiler source
    cp -r "$SCRIPT_DIR/umbra-compiler"/* "$BUNDLED_DIR/"
    
    log_success "Bundled directory prepared"
}

# Bundle LLVM tools
bundle_llvm_tools() {
    log_info "Bundling LLVM tools into compiler..."
    
    # Create LLVM tools directory
    mkdir -p "$LLVM_BUNDLE_DIR/bin"
    mkdir -p "$LLVM_BUNDLE_DIR/lib"
    mkdir -p "$LLVM_BUNDLE_DIR/include"
    
    # Copy essential LLVM tools
    local llvm_tools=(
        "llc"
        "opt" 
        "llvm-as"
        "llvm-dis"
        "llvm-link"
        "llvm-config"
        "clang"
    )
    
    log_info "Copying LLVM tools..."
    for tool in "${llvm_tools[@]}"; do
        if command -v "$tool" &> /dev/null; then
            cp "$(which $tool)" "$LLVM_BUNDLE_DIR/bin/"
            log_success "Bundled: $tool"
        else
            log_warning "Tool not found: $tool"
        fi
    done
    
    # Copy LLVM libraries
    log_info "Copying LLVM libraries..."
    if [[ -d "/usr/lib/x86_64-linux-gnu" ]]; then
        find /usr/lib/x86_64-linux-gnu -name "libLLVM*.so*" -exec cp {} "$LLVM_BUNDLE_DIR/lib/" \; 2>/dev/null || true
        find /usr/lib/x86_64-linux-gnu -name "libclang*.so*" -exec cp {} "$LLVM_BUNDLE_DIR/lib/" \; 2>/dev/null || true
    fi
    
    # Copy LLVM headers (essential ones)
    if [[ -d "/usr/include/llvm" ]]; then
        cp -r /usr/include/llvm "$LLVM_BUNDLE_DIR/include/" 2>/dev/null || true
    fi
    if [[ -d "/usr/include/llvm-c" ]]; then
        cp -r /usr/include/llvm-c "$LLVM_BUNDLE_DIR/include/" 2>/dev/null || true
    fi
    
    # Create LLVM configuration
    cat > "$LLVM_BUNDLE_DIR/llvm-config.txt" << 'EOF'
# Bundled LLVM Configuration
version=18.1.3
prefix=./llvm-tools
bindir=./llvm-tools/bin
libdir=./llvm-tools/lib
includedir=./llvm-tools/include
EOF
    
    log_success "LLVM tools bundled successfully"
}

# Create enhanced Cargo configuration
create_enhanced_cargo_config() {
    log_info "Creating enhanced Cargo configuration..."
    
    cat > "$BUNDLED_DIR/Cargo.toml" << 'EOF'
[package]
name = "umbra-compiler-bundled"
version = "1.0.1"
edition = "2021"
authors = ["Eclipse Softworks <<EMAIL>>"]
description = "Umbra programming language compiler with bundled LLVM tools"
license = "MIT"
homepage = "https://eclipse-softworks.com"
repository = "https://github.com/eclipse-softworks/umbra"
documentation = "https://eclipse-softworks.com/docs/umbra"
keywords = ["umbra", "ai", "ml", "programming-language", "compiler", "llvm", "bundled"]
categories = ["programming-languages", "artificial-intelligence", "machine-learning"]

[features]
default = ["bundled-llvm", "python-interop"]
bundled-llvm = ["inkwell"]
python-interop = ["pyo3"]
static-llvm = []

[lib]
name = "umbra_compiler"
path = "src/lib.rs"

[[bin]]
name = "umbra"
path = "src/main.rs"

[dependencies]
# LLVM bindings with bundled support (latest version)
inkwell = { version = "0.6", optional = true }

# CLI and argument parsing
clap = { version = "4.4", features = ["derive"] }

# Error handling and diagnostics
thiserror = "1.0"
anyhow = "1.0"

# Parsing and lexing utilities
logos = "0.14"

# Serialization for AST and metadata
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
toml = "0.8"

# HTTP client for package registry
reqwest = { version = "0.11", features = ["json", "multipart"] }
tokio = { version = "1.0", features = ["full"] }

# UUID generation for debug sessions
uuid = { version = "1.0", features = ["v4"] }

# Hashing for debug information and package integrity
md5 = "0.7"
sha2 = "0.10"

# File system and path utilities
walkdir = "2.4"

# String and text processing
unicode-segmentation = "1.10"
base64 = "0.21"
urlencoding = "2.1"

# Colored output for diagnostics
colored = "2.0"

# XML processing
quick-xml = "0.31"

# Compression
flate2 = "1.0"

# Terminal handling
crossterm = "0.27"

# Temporary files
tempfile = "3.8"

# Parallel processing
num_cpus = "1.16"

# Testing framework dependencies
glob = "0.3"
regex = "1.0"
rand = "0.8"

# Static initialization
lazy_static = "1.4"

# Database integration dependencies
url = "2.4"
chrono = { version = "0.4", features = ["serde"] }
fastrand = "2.0"
log = "0.4"

# Language Server Protocol
tower-lsp = "0.20"
lsp-types = "0.95"

# Python interoperability
pyo3 = { version = "0.20", features = ["auto-initialize", "abi3-py38"], optional = true }
which = "4.4"

# Archive handling
tar = "0.4"

# Embedded resources
include_dir = "0.7"

[build-dependencies]
chrono = "0.4"
include_dir = "0.7"

[profile.release]
# Optimize for size and performance with bundled tools
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"
strip = false  # Keep symbols for bundled tools

[profile.dev]
# Fast compilation for development
opt-level = 0
debug = true
EOF
    
    log_success "Enhanced Cargo configuration created"
}

# Create bundled build script
create_bundled_build_script() {
    log_info "Creating bundled build script..."
    
    cat > "$BUNDLED_DIR/build.rs" << 'EOF'
// Bundled LLVM Build Script for Umbra Compiler
// This embeds LLVM tools directly into the binary

use std::env;
use std::path::Path;
use include_dir::{include_dir, Dir};

fn main() {
    let target = env::var("TARGET").unwrap_or_default();
    
    // Set build information
    setup_build_info();
    
    // Configure bundled LLVM
    setup_bundled_llvm();
    
    // Platform-specific configuration
    if target.contains("windows") {
        setup_windows_bundled();
    } else {
        setup_unix_bundled();
    }
    
    // Embed LLVM tools as resources
    embed_llvm_tools();
}

fn setup_build_info() {
    let build_date = chrono::Utc::now().format("%Y-%m-%d %H:%M:%S UTC").to_string();
    println!("cargo:rustc-env=BUILD_DATE={}", build_date);

    let git_commit = std::process::Command::new("git")
        .args(&["rev-parse", "--short", "HEAD"])
        .output()
        .map(|output| String::from_utf8_lossy(&output.stdout).trim().to_string())
        .unwrap_or_else(|_| "bundled".to_string());
    println!("cargo:rustc-env=BUILD_COMMIT={}", git_commit);

    let target = env::var("TARGET").unwrap_or_else(|_| "unknown".to_string());
    println!("cargo:rustc-env=BUILD_TARGET={}", target);
    
    println!("cargo:rustc-env=LLVM_BUNDLED=true");
}

fn setup_bundled_llvm() {
    // Configure for bundled LLVM
    let llvm_dir = "./llvm-tools";
    
    if Path::new(llvm_dir).exists() {
        println!("cargo:rustc-env=LLVM_SYS_180_PREFIX={}", llvm_dir);
        println!("cargo:rustc-link-search=native={}/lib", llvm_dir);
        println!("cargo:rustc-cfg=bundled_llvm");
        
        // Add LLVM tools to PATH during build
        let current_path = env::var("PATH").unwrap_or_default();
        let new_path = format!("{}/bin:{}", llvm_dir, current_path);
        println!("cargo:rustc-env=PATH={}", new_path);
    }
}

fn setup_windows_bundled() {
    // Windows-specific bundled configuration
    println!("cargo:rustc-cfg=windows_bundled");
    
    // Link Windows system libraries
    println!("cargo:rustc-link-lib=kernel32");
    println!("cargo:rustc-link-lib=user32");
    println!("cargo:rustc-link-lib=advapi32");
    println!("cargo:rustc-link-lib=ws2_32");
}

fn setup_unix_bundled() {
    // Unix-specific bundled configuration
    println!("cargo:rustc-cfg=unix_bundled");
    
    // Link system libraries
    println!("cargo:rustc-link-lib=dl");
    println!("cargo:rustc-link-lib=pthread");
    println!("cargo:rustc-link-lib=m");
}

fn embed_llvm_tools() {
    // This will embed the LLVM tools directory into the binary
    println!("cargo:rerun-if-changed=llvm-tools/");
    
    // The actual embedding is handled by include_dir! macro in the source
    if Path::new("llvm-tools").exists() {
        println!("cargo:rustc-cfg=has_embedded_llvm");
    }
}
EOF
    
    log_success "Bundled build script created"
}

# Create LLVM wrapper
create_llvm_wrapper() {
    log_info "Creating LLVM wrapper module..."
    
    mkdir -p "$BUNDLED_DIR/src/llvm"
    
    cat > "$BUNDLED_DIR/src/llvm/bundled.rs" << 'EOF'
//! Bundled LLVM Tools Module
//! This module provides access to bundled LLVM tools

use std::path::{Path, PathBuf};
use std::process::Command;
use std::fs;
use std::io::Write;
use tempfile::TempDir;
use include_dir::{include_dir, Dir};

// Embed the LLVM tools directory
#[cfg(has_embedded_llvm)]
static LLVM_TOOLS: Dir = include_dir!("llvm-tools");

pub struct BundledLLVM {
    temp_dir: Option<TempDir>,
    tools_path: PathBuf,
}

impl BundledLLVM {
    pub fn new() -> Result<Self, Box<dyn std::error::Error>> {
        #[cfg(has_embedded_llvm)]
        {
            // Extract bundled tools to temporary directory
            let temp_dir = TempDir::new()?;
            let tools_path = temp_dir.path().join("llvm-tools");
            
            // Extract embedded LLVM tools
            extract_embedded_tools(&tools_path)?;
            
            Ok(BundledLLVM {
                temp_dir: Some(temp_dir),
                tools_path,
            })
        }
        
        #[cfg(not(has_embedded_llvm))]
        {
            // Fallback to system LLVM
            Ok(BundledLLVM {
                temp_dir: None,
                tools_path: PathBuf::from("/usr/bin"),
            })
        }
    }
    
    pub fn get_tool_path(&self, tool: &str) -> PathBuf {
        self.tools_path.join("bin").join(tool)
    }
    
    pub fn run_llc(&self, args: &[&str]) -> Result<std::process::Output, std::io::Error> {
        let llc_path = self.get_tool_path("llc");
        Command::new(llc_path).args(args).output()
    }
    
    pub fn run_opt(&self, args: &[&str]) -> Result<std::process::Output, std::io::Error> {
        let opt_path = self.get_tool_path("opt");
        Command::new(opt_path).args(args).output()
    }
    
    pub fn run_clang(&self, args: &[&str]) -> Result<std::process::Output, std::io::Error> {
        let clang_path = self.get_tool_path("clang");
        Command::new(clang_path).args(args).output()
    }
    
    pub fn is_available(&self) -> bool {
        self.get_tool_path("llc").exists() || 
        which::which("llc").is_ok()
    }
}

#[cfg(has_embedded_llvm)]
fn extract_embedded_tools(target_path: &Path) -> Result<(), Box<dyn std::error::Error>> {
    fs::create_dir_all(target_path)?;
    
    // Extract all embedded files
    extract_dir(&LLVM_TOOLS, target_path)?;
    
    // Make binaries executable
    let bin_dir = target_path.join("bin");
    if bin_dir.exists() {
        for entry in fs::read_dir(&bin_dir)? {
            let entry = entry?;
            let path = entry.path();
            if path.is_file() {
                #[cfg(unix)]
                {
                    use std::os::unix::fs::PermissionsExt;
                    let mut perms = fs::metadata(&path)?.permissions();
                    perms.set_mode(0o755);
                    fs::set_permissions(&path, perms)?;
                }
            }
        }
    }
    
    Ok(())
}

#[cfg(has_embedded_llvm)]
fn extract_dir(dir: &Dir, target_path: &Path) -> Result<(), Box<dyn std::error::Error>> {
    for file in dir.files() {
        let file_path = target_path.join(file.path());
        if let Some(parent) = file_path.parent() {
            fs::create_dir_all(parent)?;
        }
        let mut output_file = fs::File::create(&file_path)?;
        output_file.write_all(file.contents())?;
    }
    
    for subdir in dir.dirs() {
        let subdir_path = target_path.join(subdir.path());
        fs::create_dir_all(&subdir_path)?;
        extract_dir(subdir, &subdir_path)?;
    }
    
    Ok(())
}

impl Drop for BundledLLVM {
    fn drop(&mut self) {
        // Temporary directory is automatically cleaned up
    }
}
EOF
    
    # Update main lib.rs to include the bundled module
    if [[ -f "$BUNDLED_DIR/src/lib.rs" ]]; then
        echo "" >> "$BUNDLED_DIR/src/lib.rs"
        echo "// Bundled LLVM support" >> "$BUNDLED_DIR/src/lib.rs"
        echo "pub mod llvm;" >> "$BUNDLED_DIR/src/lib.rs"
    fi
    
    # Create llvm module file
    cat > "$BUNDLED_DIR/src/llvm/mod.rs" << 'EOF'
//! LLVM Integration Module

pub mod bundled;

pub use bundled::BundledLLVM;

// Re-export for convenience
pub fn create_bundled_llvm() -> Result<BundledLLVM, Box<dyn std::error::Error>> {
    BundledLLVM::new()
}
EOF
    
    log_success "LLVM wrapper module created"
}

# Build bundled compiler
build_bundled_compiler() {
    log_info "Building bundled compiler..."
    
    cd "$BUNDLED_DIR"
    
    # Build the bundled compiler
    if cargo build --release --features bundled-llvm; then
        local binary_size=$(du -h target/release/umbra | cut -f1)
        log_success "Bundled compiler built successfully ($binary_size)"
        
        # Copy to a convenient location
        cp target/release/umbra "$SCRIPT_DIR/umbra-bundled"
        log_success "Bundled compiler available at: $SCRIPT_DIR/umbra-bundled"
    else
        log_error "Failed to build bundled compiler"
        return 1
    fi
}

# Test bundled compiler
test_bundled_compiler() {
    log_info "Testing bundled compiler..."
    
    if [[ -f "$SCRIPT_DIR/umbra-bundled" ]]; then
        # Test version
        if "$SCRIPT_DIR/umbra-bundled" --version; then
            log_success "Bundled compiler version check passed"
        else
            log_warning "Version check failed, but binary exists"
        fi
        
        # Test LLVM availability
        if "$SCRIPT_DIR/umbra-bundled" version 2>&1 | grep -q "LLVM"; then
            log_success "LLVM integration detected in bundled compiler"
        else
            log_info "LLVM integration status unclear"
        fi
    else
        log_error "Bundled compiler binary not found"
        return 1
    fi
}

# Run main function
main "$@"
