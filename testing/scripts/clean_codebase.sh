#!/bin/bash

# Umbra Codebase Cleanup Script
# Organizes test files, scripts, and temporary files into dedicated folders

echo "🧹 Cleaning up Umbra codebase..."
echo "================================"

# Create directory structure
echo "📁 Creating directory structure..."
mkdir -p testing/{test_files,scripts,temp_files,debug_files,data_files}
mkdir -p archive/{old_docs,old_builds,old_distributions}

echo "✅ Directory structure created"

# Move test files (.umbra files that start with test_ or are clearly test files)
echo "📝 Moving test files..."
mv test_*.umbra testing/test_files/ 2>/dev/null
mv simple_test.umbra testing/test_files/ 2>/dev/null
mv *_test.umbra testing/test_files/ 2>/dev/null
mv *_demo.umbra testing/test_files/ 2>/dev/null
mv simple_*.umbra testing/test_files/ 2>/dev/null

# Move ML demo and showcase files
echo "🤖 Moving ML demo files..."
mv *ml*.umbra testing/test_files/ 2>/dev/null
mv *ML*.umbra testing/test_files/ 2>/dev/null
mv *showcase*.umbra testing/test_files/ 2>/dev/null
mv *demo*.umbra testing/test_files/ 2>/dev/null
mv working_*.umbra testing/test_files/ 2>/dev/null
mv final_*.umbra testing/test_files/ 2>/dev/null
mv complete_*.umbra testing/test_files/ 2>/dev/null
mv comprehensive_*.umbra testing/test_files/ 2>/dev/null
mv professional_*.umbra testing/test_files/ 2>/dev/null
mv reliable_*.umbra testing/test_files/ 2>/dev/null
mv genuine_*.umbra testing/test_files/ 2>/dev/null
mv clean_*.umbra testing/test_files/ 2>/dev/null
mv real_*.umbra testing/test_files/ 2>/dev/null
mv native_*.umbra testing/test_files/ 2>/dev/null
mv advanced_*.umbra testing/test_files/ 2>/dev/null
mv basic_*.umbra testing/test_files/ 2>/dev/null
mv end_to_end_*.umbra testing/test_files/ 2>/dev/null

# Move specific application files
echo "📊 Moving application demo files..."
mv *social*.umbra testing/test_files/ 2>/dev/null
mv *healthcare*.umbra testing/test_files/ 2>/dev/null
mv *economic*.umbra testing/test_files/ 2>/dev/null
mv *climate*.umbra testing/test_files/ 2>/dev/null
mv *house*.umbra testing/test_files/ 2>/dev/null
mv *prediction*.umbra testing/test_files/ 2>/dev/null
mv *pipeline*.umbra testing/test_files/ 2>/dev/null
mv *training*.umbra testing/test_files/ 2>/dev/null
mv *system*.umbra testing/test_files/ 2>/dev/null
mv *service*.umbra testing/test_files/ 2>/dev/null
mv *workflow*.umbra testing/test_files/ 2>/dev/null
mv *reference*.umbra testing/test_files/ 2>/dev/null
mv *persistence*.umbra testing/test_files/ 2>/dev/null
mv *vision*.umbra testing/test_files/ 2>/dev/null
mv *network*.umbra testing/test_files/ 2>/dev/null
mv *operations*.umbra testing/test_files/ 2>/dev/null
mv *comparison*.umbra testing/test_files/ 2>/dev/null

# Move AI 2025 project files
mv ai_2025_*.umbra testing/test_files/ 2>/dev/null
mv umbra_ai_*.umbra testing/test_files/ 2>/dev/null

# Move shell scripts
echo "🔧 Moving shell scripts..."
mv *.sh testing/scripts/ 2>/dev/null

# Move Python files (debug and test scripts)
echo "🐍 Moving Python files..."
mv *.py testing/debug_files/ 2>/dev/null

# Move data files
echo "📊 Moving data files..."
mv *.csv testing/data_files/ 2>/dev/null
mv *.json testing/data_files/ 2>/dev/null
mv *.joblib testing/data_files/ 2>/dev/null

# Move executable files
echo "💻 Moving executable files..."
mv *.exe testing/temp_files/ 2>/dev/null

# Move old documentation files
echo "📚 Moving old documentation..."
mv *SUCCESS*.md archive/old_docs/ 2>/dev/null
mv *COMPLETE*.md archive/old_docs/ 2>/dev/null
mv *SUMMARY*.md archive/old_docs/ 2>/dev/null
mv *ANALYSIS*.md archive/old_docs/ 2>/dev/null
mv *SOLUTION*.md archive/old_docs/ 2>/dev/null
mv *COMPARISON*.md archive/old_docs/ 2>/dev/null
mv *SHOWCASE*.md archive/old_docs/ 2>/dev/null
mv README_CLEAN.md archive/old_docs/ 2>/dev/null

# Move old distribution files
echo "📦 Moving old distributions..."
mv umbra-complete-distribution* archive/old_distributions/ 2>/dev/null
mv umbra-windows-only* archive/old_distributions/ 2>/dev/null
mv umbra-documentation.zip archive/old_distributions/ 2>/dev/null

# Move Windows compilation folder
echo "🪟 Moving Windows compilation files..."
mv windows-compilation archive/old_builds/ 2>/dev/null

# Create README files for the new structure
echo "📝 Creating README files..."

cat > testing/README.md << 'EOF'
# Testing Directory

This directory contains all test files, scripts, and debugging utilities for the Umbra programming language.

## Structure

- `test_files/` - Umbra test programs and demo files
- `scripts/` - Shell scripts for building, testing, and deployment
- `debug_files/` - Python debugging scripts and utilities
- `data_files/` - Test data files (CSV, JSON, etc.)
- `temp_files/` - Temporary files and executables

## Usage

To run tests from this directory:
```bash
cd ../umbra-compiler
cargo run run ../testing/test_files/test_basic_functionality.umbra
```

To execute scripts:
```bash
cd testing/scripts
./build_installers.sh
```
EOF

cat > archive/README.md << 'EOF'
# Archive Directory

This directory contains archived files that are no longer actively used but kept for reference.

## Structure

- `old_docs/` - Archived documentation and status files
- `old_builds/` - Old build artifacts and compilation files
- `old_distributions/` - Previous distribution packages

These files are kept for historical reference and can be safely removed if disk space is needed.
EOF

# Clean up any remaining scattered files
echo "🧽 Final cleanup..."

# Move any remaining .umbra files that might have been missed
find . -maxdepth 1 -name "*.umbra" -exec mv {} testing/test_files/ \; 2>/dev/null

# Create a summary
echo ""
echo "✅ Codebase cleanup complete!"
echo "=============================="
echo ""
echo "📁 New structure:"
echo "  testing/"
echo "    ├── test_files/     - All .umbra test and demo files"
echo "    ├── scripts/        - All shell scripts"
echo "    ├── debug_files/    - Python debugging scripts"
echo "    ├── data_files/     - Test data (CSV, JSON, etc.)"
echo "    └── temp_files/     - Temporary and executable files"
echo ""
echo "  archive/"
echo "    ├── old_docs/       - Archived documentation"
echo "    ├── old_builds/     - Old build artifacts"
echo "    └── old_distributions/ - Previous distributions"
echo ""
echo "📊 Files moved:"
echo "  Test files: $(find testing/test_files -name "*.umbra" 2>/dev/null | wc -l) .umbra files"
echo "  Scripts: $(find testing/scripts -name "*.sh" 2>/dev/null | wc -l) shell scripts"
echo "  Debug files: $(find testing/debug_files -name "*.py" 2>/dev/null | wc -l) Python files"
echo "  Data files: $(find testing/data_files \( -name "*.csv" -o -name "*.json" -o -name "*.joblib" \) 2>/dev/null | wc -l) data files"
echo ""
echo "🎯 Core directories preserved:"
echo "  ✅ umbra-compiler/     - Main compiler source"
echo "  ✅ umbra_showcase_programs/ - Official showcase programs"
echo "  ✅ examples/           - Example programs"
echo "  ✅ docs/               - Current documentation"
echo "  ✅ distribution/       - Current distribution files"
echo "  ✅ tools/              - Development tools"
echo ""
echo "🧹 Codebase is now clean and organized!"
