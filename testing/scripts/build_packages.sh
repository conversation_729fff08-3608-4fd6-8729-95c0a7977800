#!/bin/bash

# Umbra Language Installer Package Builder
# Builds installer packages from existing binaries

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
DIST_DIR="$SCRIPT_DIR/distribution"
VERSION="1.0.1"
MAIN_LICENSE="$SCRIPT_DIR/LICENSE"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🔧 Building Umbra Programming Language Installer Packages"
echo "=========================================================="
echo "Version: $VERSION"
echo "Distribution: $DIST_DIR"
echo ""

# Create package directories
mkdir -p "$DIST_DIR"/packages/{deb,rpm,exe,pkg}
mkdir -p "$DIST_DIR"/temp/{deb,rpm,nsis,pkg}

# Function to create Debian .deb package
create_debian_package() {
    log_info "Creating Debian .deb package..."

    local deb_dir="$DIST_DIR/temp/deb"
    local linux_dir="$DIST_DIR/linux"

    # Clean and create structure
    rm -rf "$deb_dir"
    mkdir -p "$deb_dir/DEBIAN"
    mkdir -p "$deb_dir/usr/bin"
    mkdir -p "$deb_dir/usr/share/umbra"
    mkdir -p "$deb_dir/usr/share/doc/umbra"
    mkdir -p "$deb_dir/usr/share/man/man1"

    # Check if we need to extract from tarball
    if [ ! -f "$linux_dir/umbra" ] && [ -f "$DIST_DIR/umbra-$VERSION-linux-x86_64.tar.gz" ]; then
        log_info "Extracting Linux distribution from tarball..."
        mkdir -p "$linux_dir"
        tar -xzf "$DIST_DIR/umbra-$VERSION-linux-x86_64.tar.gz" -C "$linux_dir"
    fi
    
    # Create control file
    cat > "$deb_dir/DEBIAN/control" << EOF
Package: umbra
Version: $VERSION
Section: devel
Priority: optional
Architecture: amd64
Depends: libc6 (>= 2.17)
Maintainer: Eclipse Softworks <<EMAIL>>
Description: Umbra Programming Language Compiler
 Umbra is a modern programming language designed for AI/ML applications,
 systems programming, and general-purpose development. It features:
 .
  * Native AI/ML support with built-in training and inference
  * Memory safety without garbage collection
  * High-performance compilation with LLVM backend
  * Comprehensive standard library
  * Python interoperability
  * Advanced type system with generics and traits
Homepage: https://github.com/umbra-lang
EOF

    # Create postinst script
    cat > "$deb_dir/DEBIAN/postinst" << 'EOF'
#!/bin/bash
set -e
echo "Umbra Programming Language installed successfully!"
echo "Run 'umbra --version' to verify installation"
exit 0
EOF

    chmod 755 "$deb_dir/DEBIAN/postinst"
    
    # Copy files
    cp "$linux_dir/umbra" "$deb_dir/usr/bin/"
    chmod 755 "$deb_dir/usr/bin/umbra"
    
    cp "$MAIN_LICENSE" "$deb_dir/usr/share/doc/umbra/"
    cp "$linux_dir/README.md" "$deb_dir/usr/share/doc/umbra/"
    
    # Copy examples
    cp -r "$linux_dir/examples" "$deb_dir/usr/share/umbra/" 2>/dev/null || true
    
    # Create man page
    cat > "$deb_dir/usr/share/man/man1/umbra.1" << 'EOF'
.TH UMBRA 1 "2025-01-18" "1.0.1" "Umbra Programming Language"
.SH NAME
umbra \- Umbra Programming Language Compiler
.SH SYNOPSIS
.B umbra
[\fIOPTION\fR]... [\fIFILE\fR]...
.SH DESCRIPTION
Umbra is a modern programming language designed for AI/ML applications,
systems programming, and general-purpose development.
.SH OPTIONS
.TP
.B \-\-version
Show version information
.TP
.B \-\-help
Show help information
.SH EXAMPLES
.TP
umbra hello.umbra
Compile hello.umbra
.TP
umbra --version
Show version
.SH AUTHOR
Eclipse Softworks
.SH SEE ALSO
Full documentation at: https://github.com/umbra-lang
EOF
    
    gzip "$deb_dir/usr/share/man/man1/umbra.1"
    
    # Build package
    fakeroot dpkg-deb --build "$deb_dir" "$DIST_DIR/packages/deb/umbra-$VERSION-amd64.deb"
    
    log_success "Debian .deb package created"
}

# Function to create RPM package
create_rpm_package() {
    log_info "Creating RPM package..."

    local rpm_dir="$DIST_DIR/temp/rpm"
    local linux_dir="$DIST_DIR/linux"

    # Clean and create structure
    rm -rf "$rpm_dir"
    mkdir -p "$rpm_dir"/{BUILD,RPMS,SOURCES,SPECS,SRPMS}
    mkdir -p "$rpm_dir/BUILD/umbra-$VERSION"

    # Check if we need to extract from tarball
    if [ ! -f "$linux_dir/umbra" ] && [ -f "$DIST_DIR/umbra-$VERSION-linux-x86_64.tar.gz" ]; then
        log_info "Extracting Linux distribution from tarball..."
        mkdir -p "$linux_dir"
        tar -xzf "$DIST_DIR/umbra-$VERSION-linux-x86_64.tar.gz" -C "$linux_dir"
    fi

    # Create spec file
    cat > "$rpm_dir/SPECS/umbra.spec" << EOF
Name:           umbra
Version:        $VERSION
Release:        1%{?dist}
Summary:        Umbra Programming Language Compiler
Group:          Development/Languages
License:        Proprietary
URL:            https://github.com/umbra-lang
Source0:        umbra-%{version}.tar.gz
BuildRoot:      %{_tmppath}/%{name}-%{version}-%{release}-root-%(%{__id_u} -n)
Requires:       glibc >= 2.17

%description
Umbra is a modern programming language designed for AI/ML applications,
systems programming, and general-purpose development.

%prep
%setup -q

%build
# Binary is pre-compiled

%install
rm -rf %{buildroot}
mkdir -p %{buildroot}%{_bindir}
mkdir -p %{buildroot}%{_datadir}/umbra
mkdir -p %{buildroot}%{_docdir}/umbra

install -m 755 umbra %{buildroot}%{_bindir}/
install -m 644 LICENSE %{buildroot}%{_docdir}/umbra/
install -m 644 README.md %{buildroot}%{_docdir}/umbra/
cp -r examples %{buildroot}%{_datadir}/umbra/ || true

%clean
rm -rf %{buildroot}

%post
echo "Umbra Programming Language installed successfully!"
echo "Run 'umbra --version' to verify installation"

%files
%defattr(-,root,root,-)
%{_bindir}/umbra
%{_datadir}/umbra/
%{_docdir}/umbra/

%changelog
* $(date +'%a %b %d %Y') Eclipse Softworks <<EMAIL>> - $VERSION-1
- Initial RPM package for Umbra Programming Language
EOF

    # Create source tarball
    cd "$rpm_dir/BUILD"
    cp "$linux_dir/umbra" "umbra-$VERSION/"
    cp "$MAIN_LICENSE" "umbra-$VERSION/"
    cp "$linux_dir/README.md" "umbra-$VERSION/"
    cp -r "$linux_dir/examples" "umbra-$VERSION/" 2>/dev/null || true

    tar -czf "../SOURCES/umbra-$VERSION.tar.gz" "umbra-$VERSION"

    # Build RPM
    cd "$rpm_dir"
    rpmbuild --define "_topdir $(pwd)" -ba SPECS/umbra.spec

    # Move RPM to packages directory
    find RPMS -name "*.rpm" -exec cp {} "$DIST_DIR/packages/rpm/" \; 2>/dev/null || true

    log_success "RPM package created"
}

# Function to create Windows installer
create_windows_installer() {
    log_info "Creating Windows .exe installer..."

    local nsis_dir="$DIST_DIR/temp/nsis"
    local windows_dir="$DIST_DIR/windows"

    # Check if we need to extract from zip
    if [ ! -f "$windows_dir/umbra.exe" ] && [ -f "$DIST_DIR/umbra-$VERSION-windows-x86_64.zip" ]; then
        log_info "Extracting Windows distribution from zip..."
        mkdir -p "$windows_dir"
        cd "$windows_dir"
        unzip -o "$DIST_DIR/umbra-$VERSION-windows-x86_64.zip"
        # Move files from windows subdirectory if they exist
        if [ -d "windows" ]; then
            mv windows/* . 2>/dev/null || true
            rmdir windows 2>/dev/null || true
        fi
        cd - > /dev/null
    fi

    # Clean and create structure
    rm -rf "$nsis_dir"
    mkdir -p "$nsis_dir"

    # Create simple NSIS installer script
    cat > "$nsis_dir/umbra-installer.nsi" << 'EOF'
; Umbra Programming Language Installer
Name "Umbra Programming Language 1.0.1"
OutFile "umbra-1.0.1-windows-x86_64-installer.exe"
InstallDir "$PROGRAMFILES64\Umbra"
RequestExecutionLevel admin

Page directory
Page instfiles

Section "Install"
    SetOutPath $INSTDIR
    File "umbra.exe"
    File "LICENSE"
    File "README.md"
    File /r "examples"

    ; Create shortcuts
    CreateDirectory "$SMPROGRAMS\Umbra Programming Language"
    CreateShortCut "$SMPROGRAMS\Umbra Programming Language\Umbra.lnk" "$INSTDIR\umbra.exe"
    CreateShortCut "$DESKTOP\Umbra.lnk" "$INSTDIR\umbra.exe"

    ; Write uninstaller
    WriteUninstaller "$INSTDIR\uninstall.exe"

    ; Add to registry
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\Umbra" "DisplayName" "Umbra Programming Language"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\Umbra" "UninstallString" "$INSTDIR\uninstall.exe"
SectionEnd

Section "Uninstall"
    Delete "$INSTDIR\umbra.exe"
    Delete "$INSTDIR\LICENSE"
    Delete "$INSTDIR\README.md"
    RMDir /r "$INSTDIR\examples"
    Delete "$INSTDIR\uninstall.exe"
    RMDir "$INSTDIR"

    Delete "$SMPROGRAMS\Umbra Programming Language\Umbra.lnk"
    RMDir "$SMPROGRAMS\Umbra Programming Language"
    Delete "$DESKTOP\Umbra.lnk"

    DeleteRegKey HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\Umbra"
SectionEnd
EOF

    # Copy files to NSIS directory
    if [ -f "$windows_dir/umbra.exe" ]; then
        cp "$windows_dir/umbra.exe" "$nsis_dir/"
        cp "$MAIN_LICENSE" "$nsis_dir/"
        cp "$windows_dir/README.md" "$nsis_dir/"
        cp -r "$windows_dir/examples" "$nsis_dir/" 2>/dev/null || true

        # Build installer
        cd "$nsis_dir"
        makensis umbra-installer.nsi

        # Move installer to packages directory
        mv "umbra-$VERSION-windows-x86_64-installer.exe" "$DIST_DIR/packages/exe/" 2>/dev/null || true

        log_success "Windows .exe installer created"
    else
        log_warning "Windows binary not found, skipping installer creation"
    fi
}

# Build packages
if [ -f "$DIST_DIR/linux/umbra" ] || [ -f "$DIST_DIR/umbra-$VERSION-linux-x86_64.tar.gz" ]; then
    create_debian_package
    create_rpm_package
else
    log_warning "Skipping Linux packages (Linux binary not found)"
fi

if [ -f "$DIST_DIR/windows/umbra.exe" ] || [ -f "$DIST_DIR/umbra-$VERSION-windows-x86_64.zip" ]; then
    create_windows_installer
else
    log_warning "Skipping Windows installer (Windows binary not found)"
fi

# Summary
echo ""
log_success "🎉 Installer packages built successfully!"
echo ""
echo "Packages created:"
ls -la "$DIST_DIR"/packages/*/* 2>/dev/null || echo "No packages found"
