#!/bin/bash

# Umbra Programming Language - Offline Installer Builder
# This script builds complete offline installers for multiple platforms

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
UMBRA_DIR="$SCRIPT_DIR/umbra-compiler"
DIST_DIR="$SCRIPT_DIR/distribution"
VERSION="1.0.1"
MAIN_LICENSE="$SCRIPT_DIR/LICENSE"

echo "🚀 Building Umbra Programming Language Offline Installers"
echo "=========================================================="
echo "Version: $VERSION"
echo "Source: $UMBRA_DIR"
echo "Output: $DIST_DIR"
echo ""

# Create distribution directory structure
mkdir -p "$DIST_DIR"/{linux,windows,macos,source,logos}
mkdir -p "$DIST_DIR"/packages/{deb,rpm,pkg,exe}
mkdir -p "$DIST_DIR"/temp/{deb,rpm,nsis,pkg}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to build for a specific target
build_target() {
    local target=$1
    local output_dir=$2

    log_info "Building for target: $target"

    cd "$UMBRA_DIR"

    # Install target if not already installed
    if ! rustup target list --installed | grep -q "$target"; then
        log_info "Installing target $target..."
        rustup target add "$target"
    fi

    # Configure cross-compilation environment for Windows
    if [[ "$target" == *"windows"* ]]; then
        log_info "Configuring Windows cross-compilation environment..."

        # Set up environment variables for Windows cross-compilation (Parrot OS style)
        export CC_x86_64_pc_windows_gnu=x86_64-w64-mingw32-gcc
        export CXX_x86_64_pc_windows_gnu=x86_64-w64-mingw32-g++
        export AR_x86_64_pc_windows_gnu=x86_64-w64-mingw32-ar
        export CARGO_TARGET_X86_64_PC_WINDOWS_GNU_LINKER=x86_64-w64-mingw32-gcc

        # Set library paths
        export PKG_CONFIG_ALLOW_CROSS=1
        export PKG_CONFIG_PATH="/usr/x86_64-w64-mingw32/lib/pkgconfig:/usr/lib/x86_64-linux-gnu/pkgconfig"
        export MINGW_PREFIX="/usr/x86_64-w64-mingw32"

        # Enhanced library linking (following Parrot OS approach)
        export RUSTFLAGS="-L /usr/x86_64-w64-mingw32/lib -L /usr/lib/x86_64-linux-gnu -C link-args=-Wl,--allow-multiple-definition -C link-args=-static-libgcc"

        # Set up comprehensive library search paths
        export LIBRARY_PATH="/usr/x86_64-w64-mingw32/lib:/usr/lib/x86_64-linux-gnu:$LIBRARY_PATH"
        export LD_LIBRARY_PATH="/usr/x86_64-w64-mingw32/lib:/usr/lib/x86_64-linux-gnu:$LD_LIBRARY_PATH"

        # Additional Windows-specific environment
        export WINEARCH=win64
        export WINEPREFIX="$HOME/.wine"

        # Ensure static linking for better compatibility
        export CARGO_TARGET_X86_64_PC_WINDOWS_GNU_RUSTFLAGS="-C target-feature=+crt-static"
    fi

    # Build release binary with appropriate features
    if [[ "$target" == *"windows"* ]]; then
        log_info "Compiling Windows release binary with full features..."
        # Use the new Windows platform support for full feature compatibility
        export UMBRA_WINDOWS_PLATFORM=1
        cargo build --release --target "$target"
    else
        log_info "Compiling release binary for $target..."
        cargo build --release --target "$target"
    fi
    
    # Copy binary to distribution directory
    local binary_name="umbra"
    if [[ "$target" == *"windows"* ]]; then
        binary_name="umbra.exe"
    fi

    mkdir -p "$output_dir"
    cp "target/$target/release/$binary_name" "$output_dir/"
    log_success "Binary built and copied to $output_dir/"
}

# Function to create Linux distribution
create_linux_dist() {
    log_info "Creating Linux distribution..."
    
    local linux_dir="$DIST_DIR/linux"
    
    # Build for Linux x86_64
    build_target "x86_64-unknown-linux-gnu" "$linux_dir"
    
    # Copy additional files
    cp "$UMBRA_DIR/README.md" "$linux_dir/"
    cp "$MAIN_LICENSE" "$linux_dir/"
    
    # Create examples directory
    mkdir -p "$linux_dir/examples"
    cp -r "$UMBRA_DIR/examples/"* "$linux_dir/examples/" 2>/dev/null || true
    
    # Create installation script
    cat > "$linux_dir/install.sh" << 'EOF'
#!/bin/bash

# Umbra Programming Language Installer for Linux
set -e

INSTALL_DIR="/usr/local/bin"
UMBRA_HOME="/usr/local/share/umbra"

echo "🔧 Installing Umbra Programming Language..."

# Check if running as root for system-wide installation
if [[ $EUID -eq 0 ]]; then
    echo "Installing system-wide to $INSTALL_DIR"
    
    # Create directories
    mkdir -p "$INSTALL_DIR"
    mkdir -p "$UMBRA_HOME"
    
    # Copy binary
    cp umbra "$INSTALL_DIR/"
    chmod +x "$INSTALL_DIR/umbra"
    
    # Copy examples and documentation
    cp -r examples "$UMBRA_HOME/" 2>/dev/null || true
    cp README.md "$UMBRA_HOME/" 2>/dev/null || true
    cp LICENSE "$UMBRA_HOME/" 2>/dev/null || true
    
    echo "✅ Umbra installed successfully!"
    echo "Run 'umbra --version' to verify installation"
    
else
    echo "Installing to user directory ~/.local/bin"
    
    # Create user directories
    mkdir -p "$HOME/.local/bin"
    mkdir -p "$HOME/.local/share/umbra"
    
    # Copy binary
    cp umbra "$HOME/.local/bin/"
    chmod +x "$HOME/.local/bin/umbra"
    
    # Copy examples and documentation
    cp -r examples "$HOME/.local/share/umbra/" 2>/dev/null || true
    cp README.md "$HOME/.local/share/umbra/" 2>/dev/null || true
    cp LICENSE "$HOME/.local/share/umbra/" 2>/dev/null || true
    
    # Add to PATH if not already there
    if ! echo "$PATH" | grep -q "$HOME/.local/bin"; then
        echo 'export PATH="$HOME/.local/bin:$PATH"' >> "$HOME/.bashrc"
        echo "⚠️  Added ~/.local/bin to PATH in ~/.bashrc"
        echo "   Please run 'source ~/.bashrc' or restart your terminal"
    fi
    
    echo "✅ Umbra installed successfully!"
    echo "Run 'umbra --version' to verify installation"
fi
EOF
    chmod +x "$linux_dir/install.sh"
    
    # Create tarball
    cd "$DIST_DIR"
    tar -czf "umbra-$VERSION-linux-x86_64.tar.gz" -C linux .
    
    log_success "Linux distribution created: umbra-$VERSION-linux-x86_64.tar.gz"
}

# Function to create Windows distribution
create_windows_dist() {
    log_info "Creating Windows distribution..."
    
    local windows_dir="$DIST_DIR/windows"
    
    # Build for Windows x86_64
    build_target "x86_64-pc-windows-gnu" "$windows_dir"
    
    # Copy additional files
    cp "$UMBRA_DIR/README.md" "$windows_dir/"
    cp "$MAIN_LICENSE" "$windows_dir/"
    
    # Create examples directory
    mkdir -p "$windows_dir/examples"
    cp -r "$UMBRA_DIR/examples/"* "$windows_dir/examples/" 2>/dev/null || true
    
    # Create installation batch script
    cat > "$windows_dir/install.bat" << 'EOF'
@echo off
echo Installing Umbra Programming Language for Windows...

set INSTALL_DIR=%ProgramFiles%\Umbra
set USER_INSTALL_DIR=%LOCALAPPDATA%\Umbra

:: Check for admin privileges
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Installing system-wide to %INSTALL_DIR%
    
    :: Create directories
    if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"
    if not exist "%INSTALL_DIR%\examples" mkdir "%INSTALL_DIR%\examples"
    
    :: Copy files
    copy umbra.exe "%INSTALL_DIR%\"
    xcopy examples "%INSTALL_DIR%\examples\" /E /I /Q
    copy README.md "%INSTALL_DIR%\" >nul 2>&1
    copy LICENSE "%INSTALL_DIR%\" >nul 2>&1
    
    :: Add to system PATH
    setx PATH "%PATH%;%INSTALL_DIR%" /M
    
    echo Umbra installed successfully!
    echo Run 'umbra --version' to verify installation
    
) else (
    echo Installing to user directory %USER_INSTALL_DIR%
    
    :: Create directories
    if not exist "%USER_INSTALL_DIR%" mkdir "%USER_INSTALL_DIR%"
    if not exist "%USER_INSTALL_DIR%\examples" mkdir "%USER_INSTALL_DIR%\examples"
    
    :: Copy files
    copy umbra.exe "%USER_INSTALL_DIR%\"
    xcopy examples "%USER_INSTALL_DIR%\examples\" /E /I /Q
    copy README.md "%USER_INSTALL_DIR%\" >nul 2>&1
    copy LICENSE "%USER_INSTALL_DIR%\" >nul 2>&1
    
    :: Add to user PATH
    setx PATH "%PATH%;%USER_INSTALL_DIR%"
    
    echo Umbra installed successfully!
    echo Please restart your command prompt and run 'umbra --version' to verify installation
)

pause
EOF
    
    # Create zip archive
    cd "$DIST_DIR"
    zip -r "umbra-$VERSION-windows-x86_64.zip" windows/
    
    log_success "Windows distribution created: umbra-$VERSION-windows-x86_64.zip"
}

# Function to create source distribution
create_source_dist() {
    log_info "Creating source distribution..."
    
    local source_dir="$DIST_DIR/source"
    
    # Copy source code (excluding target directory and other build artifacts)
    rsync -av --exclude='target' --exclude='.git' --exclude='*.log' \
          "$UMBRA_DIR/" "$source_dir/"
    
    # Create source tarball
    cd "$DIST_DIR"
    tar -czf "umbra-$VERSION-source.tar.gz" -C source .
    
    log_success "Source distribution created: umbra-$VERSION-source.tar.gz"
}

# Function to create checksums
create_checksums() {
    log_info "Creating checksums..."
    
    cd "$DIST_DIR"
    
    # Create checksums for all distribution files
    find . -name "*.tar.gz" -o -name "*.zip" | while read file; do
        sha256sum "$file" >> "umbra-$VERSION-checksums.txt"
    done
    
    log_success "Checksums created: umbra-$VERSION-checksums.txt"
}

# Function to create Windows .exe installer using NSIS
create_windows_exe_installer() {
    log_info "Creating Windows .exe installer..."

    local nsis_dir="$DIST_DIR/temp/nsis"
    local windows_dir="$DIST_DIR/windows"

    mkdir -p "$nsis_dir"

    # Verify Windows binary exists and is executable
    if [[ ! -f "$windows_dir/umbra.exe" ]]; then
        log_error "Windows binary not found: $windows_dir/umbra.exe"
        return 1
    fi

    # Test the Windows binary (basic validation)
    log_info "Validating Windows binary..."
    if ! file "$windows_dir/umbra.exe" | grep -q "PE32+.*executable.*Windows"; then
        log_warning "Windows binary may not be properly formatted"
    fi

    # Create EnvVarUpdate.nsh for PATH manipulation
    cat > "$nsis_dir/EnvVarUpdate.nsh" << 'ENVEOF'
; EnvVarUpdate.nsh
; Environment Variable Update Script for NSIS
; Based on the work by Diego Pedroso

!ifndef ENVVARUPDATE_FUNCTION
!define ENVVARUPDATE_FUNCTION
!verbose push
!verbose 3
!include "LogicLib.nsh"
!include "WinMessages.nsh"
!include "SendMessage.nsh"

!insertmacro _EnvVarUpdateConstructor EnvVarUpdate
!insertmacro _EnvVarUpdateConstructor un.EnvVarUpdate

!macro _EnvVarUpdateConstructor FUNCTION_NAME
!define ${FUNCTION_NAME}
!verbose push
!verbose 3

; AddToPath - Adds the given dir to the search path.
;        Input - head of the stack
;        Note - Win9x systems requires reboot

Function ${FUNCTION_NAME}
  Exch $0
  Exch
  Exch $1
  Exch
  Exch 2
  Exch $2
  Exch 2
  Exch 3
  Exch $3
  Exch 3
  Exch 4
  Exch $4
  Exch 4
  Push $5
  Push $6
  Push $7
  Push $8
  Push $9
  Push $R0

  ; $0 = ResultVar
  ; $1 = Action
  ; $2 = RegLoc
  ; $3 = RegKey
  ; $4 = PathString

  StrCpy $5 $4 1 -1 # copy last char
  ${If} $5 != "\"
    StrCpy $4 "$4\" # ensure string ends with back slash
  ${EndIf}

  ReadRegStr $5 $2 $3

  ${If} $1 == "A"
    ; Append
    ${If} $5 != ""
      StrCpy $0 "$5;$4"
    ${Else}
      StrCpy $0 $4
    ${EndIf}
  ${ElseIf} $1 == "P"
    ; Prepend
    ${If} $5 != ""
      StrCpy $0 "$4;$5"
    ${Else}
      StrCpy $0 $4
    ${EndIf}
  ${ElseIf} $1 == "R"
    ; Remove
    StrCpy $0 $5
    ${StrRep} $0 $0 "$4;" ""
    ${StrRep} $0 $0 ";$4" ""
    ${StrRep} $0 $0 $4 ""
  ${EndIf}

  WriteRegExpandStr $2 $3 $0
  SendMessage ${HWND_BROADCAST} ${WM_WININICHANGE} 0 "STR:Environment" /TIMEOUT=5000

  Pop $R0
  Pop $9
  Pop $8
  Pop $7
  Pop $6
  Pop $5
  Pop $4
  Pop $3
  Pop $2
  Pop $1
  Exch $0
FunctionEnd

!macroend
!verbose pop
!endif
ENVEOF

    # Create NSIS installer script
    cat > "$nsis_dir/umbra-installer.nsi" << 'EOF'
; Umbra Programming Language Installer
; Generated by Umbra build system

!define PRODUCT_NAME "Umbra Programming Language"
!define PRODUCT_VERSION "1.0.1"
!define PRODUCT_PUBLISHER "Eclipse Softworks"
!define PRODUCT_WEB_SITE "https://github.com/umbra-lang"
!define PRODUCT_DIR_REGKEY "Software\Microsoft\Windows\CurrentVersion\App Paths\umbra.exe"
!define PRODUCT_UNINST_KEY "Software\Microsoft\Windows\CurrentVersion\Uninstall\${PRODUCT_NAME}"
!define PRODUCT_UNINST_ROOT_KEY "HKLM"

; Constants for PATH manipulation
!define HWND_BROADCAST 0xffff
!define WM_WININICHANGE 0x001A

SetCompressor lzma

; Include required headers
!include "MUI.nsh"
!include "EnvVarUpdate.nsh"
!include "x64.nsh"

; MUI Settings
!define MUI_ABORTWARNING
!define MUI_ICON "${NSISDIR}\Contrib\Graphics\Icons\modern-install.ico"
!define MUI_UNICON "${NSISDIR}\Contrib\Graphics\Icons\modern-uninstall.ico"

; Welcome page
!insertmacro MUI_PAGE_WELCOME
; License page
!insertmacro MUI_PAGE_LICENSE "LICENSE"
; Directory page
!insertmacro MUI_PAGE_DIRECTORY
; Instfiles page
!insertmacro MUI_PAGE_INSTFILES
; Finish page
!define MUI_FINISHPAGE_RUN "$INSTDIR\umbra.exe"
!define MUI_FINISHPAGE_RUN_PARAMETERS "--version"
!insertmacro MUI_PAGE_FINISH

; Uninstaller pages
!insertmacro MUI_UNPAGE_INSTFILES

; Language files
!insertmacro MUI_LANGUAGE "English"

; Reserve files
!insertmacro MUI_RESERVEFILE_INSTALLOPTIONS

; MUI end ------

Name "${PRODUCT_NAME} ${PRODUCT_VERSION}"
OutFile "umbra-${PRODUCT_VERSION}-windows-x86_64-installer.exe"
InstallDir "$PROGRAMFILES64\Umbra"
InstallDirRegKey HKLM "${PRODUCT_DIR_REGKEY}" ""
ShowInstDetails show
ShowUnInstDetails show

Section "MainSection" SEC01
  SetOutPath "$INSTDIR"
  SetOverwrite ifnewer
  File "umbra.exe"
  File "LICENSE"
  File "README.md"

  ; Install examples
  SetOutPath "$INSTDIR\examples"
  File /r "examples\*.*"

  ; Create shortcuts
  CreateDirectory "$SMPROGRAMS\Umbra Programming Language"
  CreateShortCut "$SMPROGRAMS\Umbra Programming Language\Umbra.lnk" "$INSTDIR\umbra.exe"
  CreateShortCut "$SMPROGRAMS\Umbra Programming Language\Umbra Command Prompt.lnk" "$SYSDIR\cmd.exe" "/k cd /d $\"$INSTDIR$\""
  CreateShortCut "$DESKTOP\Umbra.lnk" "$INSTDIR\umbra.exe"

  ; Add to PATH using proper Windows method
  DetailPrint "Adding Umbra to system PATH..."
  \${EnvVarUpdate} $0 "PATH" "A" "HKLM" "$INSTDIR"

  ; Notify system of environment changes
  SendMessage \${HWND_BROADCAST} \${WM_WININICHANGE} 0 "STR:Environment" /TIMEOUT=5000
SectionEnd

Section -AdditionalIcons
  WriteIniStr "$INSTDIR\${PRODUCT_NAME}.url" "InternetShortcut" "URL" "${PRODUCT_WEB_SITE}"
  CreateShortCut "$SMPROGRAMS\Umbra Programming Language\Website.lnk" "$INSTDIR\${PRODUCT_NAME}.url"
  CreateShortCut "$SMPROGRAMS\Umbra Programming Language\Uninstall.lnk" "$INSTDIR\uninst.exe"
SectionEnd

Section -Post
  WriteUninstaller "$INSTDIR\uninst.exe"
  WriteRegStr HKLM "${PRODUCT_DIR_REGKEY}" "" "$INSTDIR\umbra.exe"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "DisplayName" "$(^Name)"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "UninstallString" "$INSTDIR\uninst.exe"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "DisplayIcon" "$INSTDIR\umbra.exe"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "DisplayVersion" "${PRODUCT_VERSION}"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "URLInfoAbout" "${PRODUCT_WEB_SITE}"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "Publisher" "${PRODUCT_PUBLISHER}"
SectionEnd

Function un.onUninstSuccess
  HideWindow
  MessageBox MB_ICONINFORMATION|MB_OK "$(^Name) was successfully removed from your computer."
FunctionEnd

Function un.onInit
  MessageBox MB_ICONQUESTION|MB_YESNO|MB_DEFBUTTON2 "Are you sure you want to completely remove $(^Name) and all of its components?" IDYES +2
  Abort
FunctionEnd

Section Uninstall
  Delete "$INSTDIR\${PRODUCT_NAME}.url"
  Delete "$INSTDIR\uninst.exe"
  Delete "$INSTDIR\umbra.exe"
  Delete "$INSTDIR\LICENSE"
  Delete "$INSTDIR\README.md"

  RMDir /r "$INSTDIR\examples"

  Delete "$SMPROGRAMS\Umbra Programming Language\Uninstall.lnk"
  Delete "$SMPROGRAMS\Umbra Programming Language\Website.lnk"
  Delete "$SMPROGRAMS\Umbra Programming Language\Umbra.lnk"
  Delete "$DESKTOP\Umbra.lnk"

  RMDir "$SMPROGRAMS\Umbra Programming Language"

  ; Remove from PATH
  DetailPrint "Removing Umbra from system PATH..."
  \${un.EnvVarUpdate} $0 "PATH" "R" "HKLM" "$INSTDIR"

  ; Notify system of environment changes
  SendMessage \${HWND_BROADCAST} \${WM_WININICHANGE} 0 "STR:Environment" /TIMEOUT=5000

  RMDir "$INSTDIR"

  DeleteRegKey ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}"
  DeleteRegKey HKLM "${PRODUCT_DIR_REGKEY}"
  SetAutoClose true
SectionEnd
EOF

    # Copy files to NSIS directory
    cp "$windows_dir/umbra.exe" "$nsis_dir/"
    cp "$windows_dir/LICENSE" "$nsis_dir/"
    cp "$windows_dir/README.md" "$nsis_dir/"
    cp -r "$windows_dir/examples" "$nsis_dir/" 2>/dev/null || true

    # Build installer
    cd "$nsis_dir"
    makensis umbra-installer.nsi

    # Move installer to packages directory
    mv "umbra-$VERSION-windows-x86_64-installer.exe" "$DIST_DIR/packages/exe/"

    log_success "Windows .exe installer created"
}

# Function to create Debian .deb package
create_debian_package() {
    log_info "Creating Debian .deb package..."

    local deb_dir="$DIST_DIR/temp/deb"
    local linux_dir="$DIST_DIR/linux"

    mkdir -p "$deb_dir/DEBIAN"
    mkdir -p "$deb_dir/usr/bin"
    mkdir -p "$deb_dir/usr/share/umbra"
    mkdir -p "$deb_dir/usr/share/doc/umbra"
    mkdir -p "$deb_dir/usr/share/man/man1"

    # Create control file
    cat > "$deb_dir/DEBIAN/control" << EOF
Package: umbra
Version: $VERSION
Section: devel
Priority: optional
Architecture: amd64
Depends: libc6 (>= 2.17)
Maintainer: Eclipse Softworks <<EMAIL>>
Description: Umbra Programming Language Compiler
 Umbra is a modern programming language designed for AI/ML applications,
 systems programming, and general-purpose development. It features:
 .
  * Native AI/ML support with built-in training and inference
  * Memory safety without garbage collection
  * High-performance compilation with LLVM backend
  * Comprehensive standard library
  * Python interoperability
  * Advanced type system with generics and traits
Homepage: https://github.com/umbra-lang
EOF

    # Create postinst script
    cat > "$deb_dir/DEBIAN/postinst" << 'EOF'
#!/bin/bash
set -e

echo "Configuring Umbra Programming Language..."

# Update PATH for all users
if ! grep -q "/usr/bin" /etc/environment; then
    echo "PATH already includes /usr/bin"
fi

# Create symbolic link if needed
if [ ! -L /usr/local/bin/umbra ]; then
    ln -sf /usr/bin/umbra /usr/local/bin/umbra 2>/dev/null || true
fi

echo "Umbra Programming Language installed successfully!"
echo "Run 'umbra --version' to verify installation"

exit 0
EOF

    # Create prerm script
    cat > "$deb_dir/DEBIAN/prerm" << 'EOF'
#!/bin/bash
set -e

echo "Removing Umbra Programming Language..."

# Remove symbolic link
rm -f /usr/local/bin/umbra 2>/dev/null || true

exit 0
EOF

    chmod 755 "$deb_dir/DEBIAN/postinst"
    chmod 755 "$deb_dir/DEBIAN/prerm"

    # Copy files
    cp "$linux_dir/umbra" "$deb_dir/usr/bin/"
    chmod 755 "$deb_dir/usr/bin/umbra"

    cp "$linux_dir/LICENSE" "$deb_dir/usr/share/doc/umbra/"
    cp "$linux_dir/README.md" "$deb_dir/usr/share/doc/umbra/"

    # Copy examples
    cp -r "$linux_dir/examples" "$deb_dir/usr/share/umbra/" 2>/dev/null || true

    # Create man page
    cat > "$deb_dir/usr/share/man/man1/umbra.1" << 'EOF'
.TH UMBRA 1 "2025-01-18" "1.0.1" "Umbra Programming Language"
.SH NAME
umbra \- Umbra Programming Language Compiler
.SH SYNOPSIS
.B umbra
[\fIOPTION\fR]... [\fIFILE\fR]...
.SH DESCRIPTION
Umbra is a modern programming language designed for AI/ML applications,
systems programming, and general-purpose development.
.SH OPTIONS
.TP
.B \-\-version
Show version information
.TP
.B \-\-help
Show help information
.TP
.B \-o \fIFILE\fR
Specify output file
.SH EXAMPLES
.TP
umbra hello.umbra
Compile hello.umbra
.TP
umbra --version
Show version
.SH AUTHOR
Eclipse Softworks
.SH SEE ALSO
Full documentation at: https://github.com/umbra-lang
EOF

    gzip "$deb_dir/usr/share/man/man1/umbra.1"

    # Build package
    fakeroot dpkg-deb --build "$deb_dir" "$DIST_DIR/packages/deb/umbra-$VERSION-amd64.deb"

    log_success "Debian .deb package created"
}

# Function to create RPM package
create_rpm_package() {
    log_info "Creating RPM package..."

    local rpm_dir="$DIST_DIR/temp/rpm"
    local linux_dir="$DIST_DIR/linux"

    mkdir -p "$rpm_dir"/{BUILD,RPMS,SOURCES,SPECS,SRPMS}
    mkdir -p "$rpm_dir/BUILD/umbra-$VERSION"

    # Create spec file
    cat > "$rpm_dir/SPECS/umbra.spec" << EOF
Name:           umbra
Version:        $VERSION
Release:        1%{?dist}
Summary:        Umbra Programming Language Compiler
Group:          Development/Languages
License:        MIT
URL:            https://github.com/umbra-lang
Source0:        umbra-%{version}.tar.gz
BuildRoot:      %{_tmppath}/%{name}-%{version}-%{release}-root-%(%{__id_u} -n)
Requires:       glibc >= 2.17

%description
Umbra is a modern programming language designed for AI/ML applications,
systems programming, and general-purpose development. It features:

* Native AI/ML support with built-in training and inference
* Memory safety without garbage collection
* High-performance compilation with LLVM backend
* Comprehensive standard library
* Python interoperability
* Advanced type system with generics and traits

%prep
%setup -q

%build
# Binary is pre-compiled

%install
rm -rf %{buildroot}
mkdir -p %{buildroot}%{_bindir}
mkdir -p %{buildroot}%{_datadir}/umbra
mkdir -p %{buildroot}%{_docdir}/umbra
mkdir -p %{buildroot}%{_mandir}/man1

install -m 755 umbra %{buildroot}%{_bindir}/
install -m 644 LICENSE %{buildroot}%{_docdir}/umbra/
install -m 644 README.md %{buildroot}%{_docdir}/umbra/
cp -r examples %{buildroot}%{_datadir}/umbra/ || true

# Create man page
cat > %{buildroot}%{_mandir}/man1/umbra.1 << 'MANEOF'
.TH UMBRA 1 "2025-01-18" "$VERSION" "Umbra Programming Language"
.SH NAME
umbra \- Umbra Programming Language Compiler
.SH SYNOPSIS
.B umbra
[\fIOPTION\fR]... [\fIFILE\fR]...
.SH DESCRIPTION
Umbra is a modern programming language designed for AI/ML applications,
systems programming, and general-purpose development.
.SH OPTIONS
.TP
.B \-\-version
Show version information
.TP
.B \-\-help
Show help information
.TP
.B \-o \fIFILE\fR
Specify output file
.SH EXAMPLES
.TP
umbra hello.umbra
Compile hello.umbra
.TP
umbra --version
Show version
.SH AUTHOR
Eclipse Softworks
.SH SEE ALSO
Full documentation at: https://github.com/umbra-lang
MANEOF

gzip %{buildroot}%{_mandir}/man1/umbra.1

%clean
rm -rf %{buildroot}

%post
echo "Umbra Programming Language installed successfully!"
echo "Run 'umbra --version' to verify installation"

%files
%defattr(-,root,root,-)
%{_bindir}/umbra
%{_datadir}/umbra/
%{_docdir}/umbra/
%{_mandir}/man1/umbra.1.gz

%changelog
* $(date +'%a %b %d %Y') Eclipse Softworks <<EMAIL>> - $VERSION-1
- Initial RPM package for Umbra Programming Language
EOF

    # Create source tarball
    cd "$rpm_dir/BUILD"
    cp "$linux_dir/umbra" "umbra-$VERSION/"
    cp "$linux_dir/LICENSE" "umbra-$VERSION/"
    cp "$linux_dir/README.md" "umbra-$VERSION/"
    cp -r "$linux_dir/examples" "umbra-$VERSION/" 2>/dev/null || true

    tar -czf "../SOURCES/umbra-$VERSION.tar.gz" "umbra-$VERSION"

    # Build RPM
    cd "$rpm_dir"
    rpmbuild --define "_topdir $(pwd)" -ba SPECS/umbra.spec

    # Move RPM to packages directory
    find RPMS -name "*.rpm" -exec cp {} "$DIST_DIR/packages/rpm/" \;

    log_success "RPM package created"
}

# Function to create macOS .pkg installer
create_macos_package() {
    log_info "Creating macOS .pkg installer..."

    local pkg_dir="$DIST_DIR/temp/pkg"
    local macos_dir="$DIST_DIR/macos"

    # Note: This creates a basic structure for macOS packaging
    # Actual .pkg creation requires macOS tools (pkgbuild, productbuild)
    # This creates the structure that can be packaged on macOS

    mkdir -p "$pkg_dir/payload/usr/local/bin"
    mkdir -p "$pkg_dir/payload/usr/local/share/umbra"
    mkdir -p "$pkg_dir/scripts"

    # Copy files (would need macOS binary)
    if [ -f "$macos_dir/umbra" ]; then
        cp "$macos_dir/umbra" "$pkg_dir/payload/usr/local/bin/"
        chmod 755 "$pkg_dir/payload/usr/local/bin/umbra"

        cp "$MAIN_LICENSE" "$pkg_dir/payload/usr/local/share/umbra/"
        cp "$UMBRA_DIR/README.md" "$pkg_dir/payload/usr/local/share/umbra/"
        cp -r "$macos_dir/examples" "$pkg_dir/payload/usr/local/share/umbra/" 2>/dev/null || true
    fi

    # Create postinstall script
    cat > "$pkg_dir/scripts/postinstall" << 'EOF'
#!/bin/bash

echo "Configuring Umbra Programming Language for macOS..."

# Add to PATH in common shell profiles
for profile in /etc/profile /etc/bash.bashrc /etc/zsh/zshrc; do
    if [ -f "$profile" ] && ! grep -q "/usr/local/bin" "$profile"; then
        echo 'export PATH="/usr/local/bin:$PATH"' >> "$profile" 2>/dev/null || true
    fi
done

# Create symlink for user access
ln -sf /usr/local/bin/umbra /usr/bin/umbra 2>/dev/null || true

echo "Umbra Programming Language installed successfully!"
echo "Run 'umbra --version' to verify installation"
echo "You may need to restart your terminal or run 'source ~/.bash_profile'"

exit 0
EOF

    chmod 755 "$pkg_dir/scripts/postinstall"

    # Create package info
    cat > "$pkg_dir/PackageInfo" << EOF
<?xml version="1.0" encoding="utf-8"?>
<pkg-info format-version="2" identifier="com.eclipsesoftworks.umbra" version="$VERSION" install-location="/" auth="root">
    <payload installKBytes="$(du -k "$pkg_dir/payload" | tail -1 | cut -f1)" numberOfFiles="$(find "$pkg_dir/payload" -type f | wc -l | tr -d ' ')"/>
    <bundle-version>
        <bundle id="com.eclipsesoftworks.umbra" CFBundleShortVersionString="$VERSION" path="./payload/usr/local/bin/umbra"/>
    </bundle-version>
    <scripts>
        <postinstall file="./scripts/postinstall"/>
    </scripts>
</pkg-info>
EOF

    # Create Distribution file for productbuild
    cat > "$pkg_dir/Distribution" << EOF
<?xml version="1.0" encoding="utf-8"?>
<installer-gui-script minSpecVersion="1">
    <title>Umbra Programming Language $VERSION</title>
    <organization>com.eclipsesoftworks</organization>
    <domains enable_localSystem="true"/>
    <options customize="never" require-scripts="true" rootVolumeOnly="true" />
    <choices-outline>
        <line choice="default">
            <line choice="com.eclipsesoftworks.umbra"/>
        </line>
    </choices-outline>
    <choice id="default"/>
    <choice id="com.eclipsesoftworks.umbra" visible="false">
        <pkg-ref id="com.eclipsesoftworks.umbra"/>
    </choice>
    <pkg-ref id="com.eclipsesoftworks.umbra" version="$VERSION" onConclusion="none">umbra.pkg</pkg-ref>
</installer-gui-script>
EOF

    # Create README for macOS packaging
    cat > "$pkg_dir/README_MACOS_PACKAGING.md" << EOF
# macOS Package Creation

This directory contains the structure for creating a macOS .pkg installer.

## To create the actual .pkg file on macOS:

1. Build the Umbra binary for macOS (requires macOS system):
   \`\`\`bash
   cargo build --release --target x86_64-apple-darwin
   cargo build --release --target aarch64-apple-darwin
   \`\`\`

2. Create universal binary:
   \`\`\`bash
   lipo -create -output umbra target/x86_64-apple-darwin/release/umbra target/aarch64-apple-darwin/release/umbra
   \`\`\`

3. Copy binary to payload directory:
   \`\`\`bash
   cp umbra payload/usr/local/bin/
   \`\`\`

4. Build the package:
   \`\`\`bash
   pkgbuild --root payload --scripts scripts --identifier com.eclipsesoftworks.umbra --version $VERSION umbra.pkg
   productbuild --distribution Distribution --package-path . umbra-$VERSION-macos-universal.pkg
   \`\`\`

## Files included:
- payload/: Installation files
- scripts/: Pre/post install scripts
- PackageInfo: Package metadata
- Distribution: Installer GUI configuration
EOF

    log_success "macOS .pkg structure created (requires macOS to build actual .pkg)"
}

# Main execution
main() {
    log_info "Starting build process..."
    
    # Check if we're in the right directory
    if [[ ! -d "$UMBRA_DIR" ]]; then
        log_error "Umbra compiler directory not found: $UMBRA_DIR"
        exit 1
    fi
    
    # Check if Rust is installed
    if ! command -v rustc &> /dev/null; then
        log_error "Rust is not installed. Please install Rust first."
        exit 1
    fi
    
    # Check if cross-compilation tools are available
    if ! command -v x86_64-w64-mingw32-gcc &> /dev/null; then
        log_warning "Windows cross-compilation tools not found. Skipping Windows build."
        log_warning "Install mingw-w64 to enable Windows builds: sudo apt install mingw-w64"
    fi
    
    # Clean previous builds
    log_info "Cleaning previous builds..."
    rm -rf "$DIST_DIR"
    mkdir -p "$DIST_DIR"/{linux,windows,macos,source,logos}
    mkdir -p "$DIST_DIR"/packages/{deb,rpm,pkg,exe}
    mkdir -p "$DIST_DIR"/temp/{deb,rpm,nsis,pkg}

    # Copy logos for branding
    if [[ -d "$SCRIPT_DIR/distribution/logos" ]]; then
        cp -r "$SCRIPT_DIR/distribution/logos/"* "$DIST_DIR/logos/" 2>/dev/null || true
        log_info "Logos copied for branding"
    fi
    
    # Build distributions
    create_linux_dist
    
    # Skip Windows build for now due to complex linking issues
    # We'll create a separate Windows build process
    log_warning "Skipping Windows cross-compilation (complex linking issues)"
    log_info "Windows build will be handled separately"
    
    create_source_dist
    create_checksums

    # Build installer packages
    log_info "Building installer packages..."

    # Build Debian package (requires Linux binary)
    if [ -f "$DIST_DIR/linux/umbra" ]; then
        create_debian_package
    else
        log_warning "Skipping Debian package (Linux binary not available)"
    fi

    # Build RPM package (requires Linux binary)
    if [ -f "$DIST_DIR/linux/umbra" ]; then
        create_rpm_package
    else
        log_warning "Skipping RPM package (Linux binary not available)"
    fi

    # Build Windows installer (requires Windows binary)
    if [ -f "$DIST_DIR/windows/umbra.exe" ]; then
        create_windows_exe_installer
    else
        log_warning "Skipping Windows installer (Windows binary not available)"
    fi

    # Build macOS package structure
    create_macos_package

    # Summary
    echo ""
    log_success "🎉 All distributions and installer packages built successfully!"
    echo ""
    echo "Distribution files created in: $DIST_DIR"
    echo ""
    echo "Basic distributions:"
    ls -la "$DIST_DIR"/*.tar.gz "$DIST_DIR"/*.zip 2>/dev/null || true
    echo ""
    echo "Installer packages:"
    ls -la "$DIST_DIR"/packages/*/* 2>/dev/null || true
    echo ""
    echo "Checksums:"
    cat "$DIST_DIR/umbra-$VERSION-checksums.txt" 2>/dev/null || true
}

# Run main function
main "$@"
