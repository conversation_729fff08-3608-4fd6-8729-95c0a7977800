#!/bin/bash
# Enhanced Windows Installer Builder for Umbra Programming Language
# This script creates a Windows installer using NSIS on Linux (Parrot OS style)

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
VERSION="1.0.1"
DIST_DIR="$SCRIPT_DIR/distribution"
WINDOWS_DIR="$DIST_DIR/windows"
NSIS_DIR="$DIST_DIR/temp/nsis"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if NSIS is available
check_nsis() {
    if ! command -v makensis &> /dev/null; then
        log_error "NSIS (makensis) not found. Please install it:"
        log_info "sudo apt-get install nsis"
        exit 1
    fi
    log_info "NSIS found: $(makensis -VERSION)"
}

# Create Windows binary using Docker (alternative approach)
create_windows_binary_docker() {
    log_info "Creating Windows binary using Docker cross-compilation..."
    
    # Create Dockerfile for Windows cross-compilation
    cat > "$SCRIPT_DIR/Dockerfile.windows" << 'EOF'
FROM rust:1.75-slim

# Install Windows cross-compilation tools
RUN apt-get update && apt-get install -y \
    gcc-mingw-w64-x86-64 \
    pkg-config \
    libssl-dev \
    && rm -rf /var/lib/apt/lists/*

# Add Windows target
RUN rustup target add x86_64-pc-windows-gnu

# Set up cross-compilation environment
ENV CC_x86_64_pc_windows_gnu=x86_64-w64-mingw32-gcc
ENV CXX_x86_64_pc_windows_gnu=x86_64-w64-mingw32-g++
ENV AR_x86_64_pc_windows_gnu=x86_64-w64-mingw32-ar
ENV CARGO_TARGET_X86_64_PC_WINDOWS_GNU_LINKER=x86_64-w64-mingw32-gcc

WORKDIR /app
COPY . .

# Build for Windows
RUN cargo build --release --target x86_64-pc-windows-gnu

# Copy the binary to output
RUN mkdir -p /output && cp target/x86_64-pc-windows-gnu/release/umbra.exe /output/
EOF

    # Build using Docker
    if command -v docker &> /dev/null; then
        log_info "Building Windows binary with Docker..."
        docker build -f Dockerfile.windows -t umbra-windows-builder .
        docker run --rm -v "$WINDOWS_DIR:/output" umbra-windows-builder cp /output/umbra.exe /output/
        rm -f Dockerfile.windows
        
        if [[ -f "$WINDOWS_DIR/umbra.exe" ]]; then
            log_success "Windows binary created successfully"
            return 0
        fi
    fi
    
    return 1
}

# Create Windows installer using pre-built binary approach
create_windows_installer_prebuilt() {
    log_info "Creating Windows installer with placeholder for pre-built binary..."
    
    mkdir -p "$NSIS_DIR" "$WINDOWS_DIR"
    
    # Create a placeholder batch file that downloads the real binary
    cat > "$WINDOWS_DIR/umbra.exe" << 'EOF'
@echo off
echo This is a placeholder. The real Umbra binary should be built on Windows.
echo Please run: cargo build --release --target x86_64-pc-windows-gnu
echo Or download from: https://github.com/umbra-lang/umbra/releases
pause
EOF
    
    # Make it executable (for demonstration)
    chmod +x "$WINDOWS_DIR/umbra.exe"
    
    create_nsis_installer
}

# Create the NSIS installer script
create_nsis_installer() {
    log_info "Creating NSIS installer script..."
    
    # Create the main NSIS script
    cat > "$NSIS_DIR/umbra-installer.nsi" << 'EOF'
; Umbra Programming Language Windows Installer
; Enhanced version with proper PATH management and modern UI

!define PRODUCT_NAME "Umbra Programming Language"
!define PRODUCT_VERSION "1.0.1"
!define PRODUCT_PUBLISHER "Eclipse Softworks"
!define PRODUCT_WEB_SITE "https://umbra-lang.org"
!define PRODUCT_DIR_REGKEY "Software\Microsoft\Windows\CurrentVersion\App Paths\umbra.exe"
!define PRODUCT_UNINST_KEY "Software\Microsoft\Windows\CurrentVersion\Uninstall\${PRODUCT_NAME}"
!define PRODUCT_UNINST_ROOT_KEY "HKLM"

; Modern UI
!include "MUI2.nsh"
!include "x64.nsh"

; Installer settings
Name "${PRODUCT_NAME} ${PRODUCT_VERSION}"
OutFile "umbra-${PRODUCT_VERSION}-windows-x64-installer.exe"
InstallDir "$PROGRAMFILES64\Umbra"
InstallDirRegKey HKLM "${PRODUCT_DIR_REGKEY}" ""
ShowInstDetails show
ShowUnInstDetails show
RequestExecutionLevel admin

; Modern UI Configuration
!define MUI_ABORTWARNING
!define MUI_ICON "${NSISDIR}\Contrib\Graphics\Icons\modern-install.ico"
!define MUI_UNICON "${NSISDIR}\Contrib\Graphics\Icons\modern-uninstall.ico"

; Welcome page
!insertmacro MUI_PAGE_WELCOME

; License page
!insertmacro MUI_PAGE_LICENSE "LICENSE"

; Directory page
!insertmacro MUI_PAGE_DIRECTORY

; Components page
!insertmacro MUI_PAGE_COMPONENTS

; Instfiles page
!insertmacro MUI_PAGE_INSTFILES

; Finish page
!define MUI_FINISHPAGE_RUN "$INSTDIR\umbra.exe"
!define MUI_FINISHPAGE_RUN_PARAMETERS "--version"
!insertmacro MUI_PAGE_FINISH

; Uninstaller pages
!insertmacro MUI_UNPAGE_INSTFILES

; Language files
!insertmacro MUI_LANGUAGE "English"

; Version information
VIProductVersion "*******"
VIAddVersionKey "ProductName" "${PRODUCT_NAME}"
VIAddVersionKey "Comments" "Modern programming language with AI/ML capabilities"
VIAddVersionKey "CompanyName" "${PRODUCT_PUBLISHER}"
VIAddVersionKey "LegalTrademarks" "${PRODUCT_NAME} is a trademark of ${PRODUCT_PUBLISHER}"
VIAddVersionKey "LegalCopyright" "© ${PRODUCT_PUBLISHER}"
VIAddVersionKey "FileDescription" "${PRODUCT_NAME} Installer"
VIAddVersionKey "FileVersion" "${PRODUCT_VERSION}"

; Main installer section
Section "Umbra Compiler (Required)" SEC01
  SectionIn RO
  SetOutPath "$INSTDIR"
  SetOverwrite ifnewer
  
  ; Install main executable
  File "umbra.exe"
  
  ; Install documentation
  File "LICENSE"
  File "README.md"
  
  ; Create application data directory
  CreateDirectory "$APPDATA\Umbra"
  
  ; Register application
  WriteRegStr HKLM "${PRODUCT_DIR_REGKEY}" "" "$INSTDIR\umbra.exe"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "DisplayName" "$(^Name)"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "UninstallString" "$INSTDIR\uninst.exe"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "DisplayIcon" "$INSTDIR\umbra.exe"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "DisplayVersion" "${PRODUCT_VERSION}"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "URLInfoAbout" "${PRODUCT_WEB_SITE}"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "Publisher" "${PRODUCT_PUBLISHER}"
  
  ; Add to PATH
  DetailPrint "Adding Umbra to system PATH..."
  Call AddToPath
  
  ; Create uninstaller
  WriteUninstaller "$INSTDIR\uninst.exe"
SectionEnd

; Optional components
Section "Desktop Shortcut" SEC02
  CreateShortCut "$DESKTOP\Umbra.lnk" "$INSTDIR\umbra.exe"
SectionEnd

Section "Start Menu Shortcuts" SEC03
  CreateDirectory "$SMPROGRAMS\Umbra Programming Language"
  CreateShortCut "$SMPROGRAMS\Umbra Programming Language\Umbra.lnk" "$INSTDIR\umbra.exe"
  CreateShortCut "$SMPROGRAMS\Umbra Programming Language\Umbra Command Prompt.lnk" "$SYSDIR\cmd.exe" "/k cd /d $\"$INSTDIR$\""
  CreateShortCut "$SMPROGRAMS\Umbra Programming Language\Uninstall.lnk" "$INSTDIR\uninst.exe"
SectionEnd

Section "Examples and Documentation" SEC04
  SetOutPath "$INSTDIR\examples"
  File /r "examples\*.*"
  SetOutPath "$INSTDIR\docs"
  File /r "docs\*.*"
SectionEnd

; Section descriptions
!insertmacro MUI_FUNCTION_DESCRIPTION_BEGIN
  !insertmacro MUI_DESCRIPTION_TEXT ${SEC01} "Core Umbra compiler and runtime (required)"
  !insertmacro MUI_DESCRIPTION_TEXT ${SEC02} "Create a desktop shortcut for easy access"
  !insertmacro MUI_DESCRIPTION_TEXT ${SEC03} "Add Umbra to the Start Menu"
  !insertmacro MUI_DESCRIPTION_TEXT ${SEC04} "Install example programs and documentation"
!insertmacro MUI_FUNCTION_DESCRIPTION_END

; Add to PATH function
Function AddToPath
  Push $R0
  Push $R1
  Push $R2
  Push $R3
  
  ReadRegStr $R0 HKLM "SYSTEM\CurrentControlSet\Control\Session Manager\Environment" "PATH"
  StrCpy $R1 "$INSTDIR"
  StrLen $R2 "$R1"
  StrCpy $R3 $R0 $R2
  StrCmp $R3 $R1 +3
    StrCmp $R0 "" AddToPath_NTPath
      StrCpy $R0 "$R0;$R1"
  Goto AddToPath_NTPath
  AddToPath_NTPath:
    WriteRegExpandStr HKLM "SYSTEM\CurrentControlSet\Control\Session Manager\Environment" "PATH" $R0
    SendMessage ${HWND_BROADCAST} ${WM_WININICHANGE} 0 "STR:Environment" /TIMEOUT=5000
  
  Pop $R3
  Pop $R2
  Pop $R1
  Pop $R0
FunctionEnd

; Remove from PATH function
Function un.RemoveFromPath
  Push $R0
  Push $R1
  Push $R2
  Push $R3
  Push $R4
  Push $R5
  Push $R6
  
  ReadRegStr $R0 HKLM "SYSTEM\CurrentControlSet\Control\Session Manager\Environment" "PATH"
  StrCpy $R1 "$INSTDIR"
  StrLen $R2 "$R1"
  StrLen $R3 $R0
  StrCpy $R4 0
  
  loop:
    StrCpy $R5 $R0 $R2 $R4
    StrCmp $R5 $R1 found
    StrCmp $R4 $R3 done
    IntOp $R4 $R4 + 1
    Goto loop
  
  found:
    StrCpy $R5 $R0 $R4
    IntOp $R4 $R4 + $R2
    StrCpy $R6 $R0 "" $R4
    StrCpy $R0 "$R5$R6"
    WriteRegExpandStr HKLM "SYSTEM\CurrentControlSet\Control\Session Manager\Environment" "PATH" $R0
    SendMessage ${HWND_BROADCAST} ${WM_WININICHANGE} 0 "STR:Environment" /TIMEOUT=5000
  
  done:
  Pop $R6
  Pop $R5
  Pop $R4
  Pop $R3
  Pop $R2
  Pop $R1
  Pop $R0
FunctionEnd

; Uninstaller
Section Uninstall
  ; Remove from PATH
  Call un.RemoveFromPath
  
  ; Remove files
  Delete "$INSTDIR\umbra.exe"
  Delete "$INSTDIR\LICENSE"
  Delete "$INSTDIR\README.md"
  Delete "$INSTDIR\uninst.exe"
  
  ; Remove directories
  RMDir /r "$INSTDIR\examples"
  RMDir /r "$INSTDIR\docs"
  RMDir "$INSTDIR"
  
  ; Remove shortcuts
  Delete "$DESKTOP\Umbra.lnk"
  RMDir /r "$SMPROGRAMS\Umbra Programming Language"
  
  ; Remove registry entries
  DeleteRegKey ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}"
  DeleteRegKey HKLM "${PRODUCT_DIR_REGKEY}"
  
  SetAutoClose true
SectionEnd

; Constants for PATH manipulation (if not already defined)
!ifndef HWND_BROADCAST
!define HWND_BROADCAST 0xffff
!endif
!ifndef WM_WININICHANGE
!define WM_WININICHANGE 0x001A
!endif
EOF

    # Copy required files to NSIS directory
    cp "$SCRIPT_DIR/LICENSE" "$NSIS_DIR/" 2>/dev/null || echo "# Umbra Programming Language License" > "$NSIS_DIR/LICENSE"
    cp "$SCRIPT_DIR/README.md" "$NSIS_DIR/" 2>/dev/null || echo "# Umbra Programming Language" > "$NSIS_DIR/README.md"
    cp "$WINDOWS_DIR/umbra.exe" "$NSIS_DIR/"
    
    # Copy examples and docs if they exist
    if [[ -d "$SCRIPT_DIR/examples" ]]; then
        cp -r "$SCRIPT_DIR/examples" "$NSIS_DIR/"
    else
        mkdir -p "$NSIS_DIR/examples"
        echo 'show("Hello, Umbra!")' > "$NSIS_DIR/examples/hello.umbra"
    fi
    
    if [[ -d "$SCRIPT_DIR/docs" ]]; then
        cp -r "$SCRIPT_DIR/docs" "$NSIS_DIR/"
    else
        mkdir -p "$NSIS_DIR/docs"
        echo "# Umbra Documentation" > "$NSIS_DIR/docs/README.md"
    fi
    
    # Build the installer
    log_info "Building Windows installer with NSIS..."
    cd "$NSIS_DIR"
    
    if makensis umbra-installer.nsi; then
        # Move the installer to the packages directory
        mkdir -p "$DIST_DIR/packages/exe"
        mv umbra-*.exe "$DIST_DIR/packages/exe/"
        log_success "Windows installer created successfully!"
        return 0
    else
        log_error "Failed to build Windows installer"
        return 1
    fi
}

# Main execution
main() {
    log_info "🚀 Building Umbra Windows Installer (Enhanced)"
    log_info "=============================================="
    
    check_nsis
    
    # Try Docker approach first, fallback to prebuilt approach
    if ! create_windows_binary_docker; then
        log_warning "Docker approach failed, using prebuilt binary approach"
        create_windows_installer_prebuilt
    else
        create_nsis_installer
    fi
    
    # Show results
    if [[ -f "$DIST_DIR/packages/exe/umbra-"*".exe" ]]; then
        log_success "🎉 Windows installer created successfully!"
        echo
        echo "Installer location:"
        ls -la "$DIST_DIR/packages/exe/"
        echo
        log_info "Note: For production use, build the Windows binary on Windows using:"
        log_info "  cargo build --release --target x86_64-pc-windows-gnu"
        log_info "Then replace the placeholder binary and rebuild the installer."
    else
        log_error "Failed to create Windows installer"
        exit 1
    fi
}

# Run main function
main "$@"
