#!/bin/bash

# Complete Umbra Distribution Packager
# Packs everything together for user download

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
UMBRA_DIR="$SCRIPT_DIR"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_step() { echo -e "${PURPLE}[STEP]${NC} $1"; }

# Create complete distribution package
create_distribution_package() {
    log_step "Creating Complete Umbra Distribution Package"
    
    local dist_dir="$UMBRA_DIR/umbra-complete-distribution"
    rm -rf "$dist_dir"
    mkdir -p "$dist_dir"
    
    # Create directory structure
    mkdir -p "$dist_dir/binaries"
    mkdir -p "$dist_dir/documentation"
    mkdir -p "$dist_dir/examples"
    mkdir -p "$dist_dir/tools"
    mkdir -p "$dist_dir/source"
    
    log_info "Copying binaries..."
    
    # Copy Windows compiler
    if [[ -f "$UMBRA_DIR/umbra-compiler/target/x86_64-pc-windows-gnu/release/umbra.exe" ]]; then
        cp "$UMBRA_DIR/umbra-compiler/target/x86_64-pc-windows-gnu/release/umbra.exe" "$dist_dir/binaries/umbra-windows.exe"
        log_success "Windows compiler added"
    fi
    
    # Copy Linux compiler
    if [[ -f "$UMBRA_DIR/umbra-compiler/target/release/umbra" ]]; then
        cp "$UMBRA_DIR/umbra-compiler/target/release/umbra" "$dist_dir/binaries/umbra-linux"
        log_success "Linux compiler added"
    fi
    
    # Copy Windows compilation tools
    if [[ -d "$UMBRA_DIR/windows-compilation" ]]; then
        cp -r "$UMBRA_DIR/windows-compilation" "$dist_dir/tools/"
        log_success "Windows compilation tools added"
    fi
    
    # Copy example executables
    if [[ -f "$UMBRA_DIR/test_simple.exe" ]]; then
        cp "$UMBRA_DIR/test_simple.exe" "$dist_dir/examples/"
        log_success "Example Windows executable added"
    fi
    
    log_info "Copying documentation..."
    
    # Copy documentation
    if [[ -f "$UMBRA_DIR/Umbra_Programming_Language_Complete_Reference_v1.2.1_with_Logo.pdf" ]]; then
        cp "$UMBRA_DIR/Umbra_Programming_Language_Complete_Reference_v1.2.1_with_Logo.pdf" "$dist_dir/documentation/"
        log_success "Complete reference guide added"
    fi
    
    # Copy all markdown documentation
    find "$UMBRA_DIR" -name "*.md" -maxdepth 1 -exec cp {} "$dist_dir/documentation/" \;
    log_success "Documentation files added"
    
    log_info "Copying examples..."
    
    # Copy example programs
    find "$UMBRA_DIR" -name "*.umbra" -maxdepth 1 -exec cp {} "$dist_dir/examples/" \;
    log_success "Example programs added"
    
    # Copy logos
    if [[ -d "$UMBRA_DIR/distribution/logos" ]]; then
        cp -r "$UMBRA_DIR/distribution/logos" "$dist_dir/"
        log_success "Logos added"
    fi
    
    log_info "Copying source code..."
    
    # Copy essential source code (without build artifacts)
    if [[ -d "$UMBRA_DIR/umbra-compiler/src" ]]; then
        cp -r "$UMBRA_DIR/umbra-compiler/src" "$dist_dir/source/"
        cp "$UMBRA_DIR/umbra-compiler/Cargo.toml" "$dist_dir/source/" 2>/dev/null || true
        log_success "Source code added"
    fi
    
    log_success "Distribution package created: $dist_dir"
}

# Create comprehensive README
create_main_readme() {
    log_step "Creating Main README"
    
    local dist_dir="$UMBRA_DIR/umbra-complete-distribution"
    
    cat > "$dist_dir/README.md" << 'EOF'
# 🚀 Umbra Programming Language - Complete Distribution

Welcome to the **Umbra Programming Language** complete distribution package! This package contains everything you need to start programming in Umbra on Windows, Linux, and macOS.

## 📦 What's Included

### 🔧 Compilers & Tools
- **`binaries/umbra-windows.exe`** - Windows compiler (6.6MB)
- **`binaries/umbra-linux`** - Linux compiler
- **`tools/windows-compilation/`** - Complete Windows development environment (100MB)
  - MinGW GCC toolchain
  - Windows runtime libraries
  - Compilation scripts

### 📚 Documentation
- **`documentation/Umbra_Programming_Language_Complete_Reference_v1.2.1_with_Logo.pdf`** - Complete reference guide (540KB)
- **`documentation/*.md`** - Technical documentation and guides
- **`logos/`** - Official Umbra logos and branding

### 💡 Examples
- **`examples/*.umbra`** - Example Umbra programs
- **`examples/test_simple.exe`** - Compiled Windows example

### 🛠️ Source Code
- **`source/`** - Umbra compiler source code (Rust)

---

## 🚀 Quick Start

### Windows Users

1. **Download the Windows compiler:**
   ```
   binaries/umbra-windows.exe
   ```

2. **For full development environment:**
   - Copy `tools/windows-compilation/` to your Windows machine
   - Open Command Prompt in that directory
   - Run: `compile.bat your_program.umbra`

3. **Example:**
   ```batch
   compile.bat examples\hello.umbra
   ```

### Linux Users

1. **Use the Linux compiler:**
   ```bash
   chmod +x binaries/umbra-linux
   ./binaries/umbra-linux build examples/hello.umbra
   ```

2. **Cross-compile for Windows:**
   ```bash
   ./tools/windows-compilation/compile_windows.sh examples/hello.umbra
   ```

---

## 📖 Learning Umbra

### Basic Syntax
```umbra
// Hello World
fn main() {
    show("Hello, World!");
    
    let name: String = "Umbra";
    let version: String = "1.2.1";
    
    show("Welcome to ", name, " v", version);
}
```

### Variables and Types
```umbra
fn main() {
    let x: Integer = 42;
    let y: Float = 3.14;
    let message: String = "Hello";
    let active: Boolean = true;
    
    show("Integer: ", x);
    show("Float: ", y);
    show("String: ", message);
    show("Boolean: ", active);
}
```

### Functions
```umbra
fn add(a: Integer, b: Integer) -> Integer {
    return a + b;
}

fn main() {
    let result: Integer = add(10, 20);
    show("Result: ", result);
}
```

### AI/ML Features
```umbra
fn main() {
    let data: Array<Float> = [1.0, 2.0, 3.0, 4.0, 5.0];
    
    // Train a simple model
    train model with data;
    
    // Make predictions
    let prediction: Float = predict model with 6.0;
    show("Prediction: ", prediction);
}
```

---

## 🛠️ Installation Guide

### Windows Installation

1. **Simple Installation:**
   - Download `binaries/umbra-windows.exe`
   - Place it in a folder (e.g., `C:\Umbra\`)
   - Add the folder to your Windows PATH
   - Open Command Prompt and run: `umbra-windows --version`

2. **Complete Development Environment:**
   - Extract `tools/windows-compilation/` to `C:\Umbra\`
   - Open Command Prompt in that directory
   - Run: `compile.bat examples\hello.umbra`
   - Your program will be compiled to `hello.exe`

### Linux Installation

1. **Download and install:**
   ```bash
   chmod +x binaries/umbra-linux
   sudo cp binaries/umbra-linux /usr/local/bin/umbra
   ```

2. **Verify installation:**
   ```bash
   umbra --version
   ```

3. **Compile programs:**
   ```bash
   umbra build your_program.umbra
   ```

---

## 📋 System Requirements

### Windows
- **OS:** Windows 10/11 (64-bit)
- **RAM:** 4GB minimum, 8GB recommended
- **Storage:** 200MB for compiler, 1GB for full development environment
- **Dependencies:** None (self-contained)

### Linux
- **OS:** Ubuntu 20.04+, Debian 11+, or equivalent
- **RAM:** 4GB minimum, 8GB recommended
- **Storage:** 100MB for compiler
- **Dependencies:** GCC, LLVM (auto-installed)

---

## 🔧 Advanced Usage

### Command Line Options
```bash
umbra --help                    # Show help
umbra --version                 # Show version
umbra build program.umbra       # Compile program
umbra build --verbose program.umbra  # Verbose compilation
umbra build --output myapp program.umbra  # Custom output name
```

### Project Structure
```
my-umbra-project/
├── src/
│   ├── main.umbra
│   └── utils.umbra
├── examples/
│   └── demo.umbra
└── README.md
```

### Building Projects
```bash
umbra build src/main.umbra --output my-app
```

---

## 🌐 Resources

- **Official Website:** https://umbra-lang.org (coming soon)
- **Documentation:** See `documentation/` folder
- **Examples:** See `examples/` folder
- **Source Code:** See `source/` folder

---

## 🤝 Support

### Getting Help
1. Check the complete reference guide in `documentation/`
2. Look at examples in `examples/` folder
3. Review the technical documentation

### Reporting Issues
- Describe your problem clearly
- Include your operating system
- Provide the Umbra code that's causing issues
- Include any error messages

---

## 📄 License

Umbra Programming Language is released under the MIT License.

---

## 🎉 Welcome to Umbra!

You're now ready to start programming in Umbra! Begin with the examples in the `examples/` folder, then explore the complete reference guide for advanced features.

**Happy coding!** 🚀

---

*Umbra Programming Language v1.2.1 - Eclipse Softworks*
EOF

    log_success "Main README created"
}

# Create Windows-specific README
create_windows_readme() {
    log_step "Creating Windows README"
    
    local dist_dir="$UMBRA_DIR/umbra-complete-distribution"
    
    cat > "$dist_dir/WINDOWS_SETUP.md" << 'EOF'
# 🪟 Umbra for Windows - Setup Guide

## 🚀 Quick Setup (Recommended)

### Option 1: Simple Compiler Only
1. Download `binaries/umbra-windows.exe`
2. Create folder: `C:\Umbra\`
3. Copy `umbra-windows.exe` to `C:\Umbra\`
4. Add `C:\Umbra\` to Windows PATH
5. Test: Open Command Prompt, run `umbra-windows --version`

### Option 2: Complete Development Environment
1. Extract `tools/windows-compilation/` to `C:\Umbra\`
2. Open Command Prompt in `C:\Umbra\`
3. Test: Run `compile.bat examples\hello.umbra`
4. Your program compiles to `hello.exe`

## 📋 Step-by-Step Instructions

### Adding to Windows PATH
1. Press `Win + R`, type `sysdm.cpl`, press Enter
2. Click "Environment Variables"
3. Under "System Variables", find "Path", click "Edit"
4. Click "New", add `C:\Umbra\`
5. Click "OK" on all dialogs
6. Restart Command Prompt

### First Program
1. Create `hello.umbra`:
```umbra
fn main() {
    show("Hello from Umbra on Windows!");
}
```

2. Compile:
```batch
compile.bat hello.umbra
```

3. Run:
```batch
hello.exe
```

## 🛠️ Troubleshooting

### "Command not found"
- Make sure you added Umbra to your PATH
- Restart Command Prompt after changing PATH
- Use full path: `C:\Umbra\umbra-windows.exe`

### "Missing DLL"
- Use the complete development environment
- All dependencies are included in `tools/windows-compilation/`

### Compilation Errors
- Make sure your `.umbra` file syntax is correct
- Check the examples in `examples/` folder
- Use `--verbose` flag for detailed error messages

## ✅ Verification

Test your installation:
```batch
umbra-windows --version
compile.bat examples\test_simple.umbra
test_simple.exe
```

You should see:
```
umbra 1.0.1
HellofromUmbracompiledonWindows!
Sum:10+20=30
```

🎉 **Success! Umbra is working on Windows!**
EOF

    log_success "Windows README created"
}

# Update file server with complete distribution
update_file_server() {
    log_step "Updating File Server with Complete Distribution"
    
    local dist_dir="$UMBRA_DIR/umbra-complete-distribution"
    local server_dir="$UMBRA_DIR/file-server"
    
    # Copy distribution to file server
    cp -r "$dist_dir" "$server_dir/"
    
    # Create ZIP packages
    log_info "Creating ZIP packages..."
    
    cd "$UMBRA_DIR"
    
    # Complete distribution
    if command -v zip &> /dev/null; then
        zip -r "umbra-complete-distribution.zip" "umbra-complete-distribution/" > /dev/null
        cp "umbra-complete-distribution.zip" "$server_dir/"
        log_success "Complete distribution ZIP created"
    fi
    
    # Windows-only package
    mkdir -p "umbra-windows-only"
    cp "$dist_dir/binaries/umbra-windows.exe" "umbra-windows-only/"
    cp -r "$dist_dir/tools/windows-compilation" "umbra-windows-only/"
    cp "$dist_dir/WINDOWS_SETUP.md" "umbra-windows-only/README.md"
    cp -r "$dist_dir/examples" "umbra-windows-only/"
    
    if command -v zip &> /dev/null; then
        zip -r "umbra-windows-only.zip" "umbra-windows-only/" > /dev/null
        cp "umbra-windows-only.zip" "$server_dir/"
        log_success "Windows-only package created"
    fi
    
    # Documentation package
    if command -v zip &> /dev/null; then
        zip -r "umbra-documentation.zip" "$dist_dir/documentation/" > /dev/null
        cp "umbra-documentation.zip" "$server_dir/"
        log_success "Documentation package created"
    fi
    
    log_success "File server updated with all packages"
}

# Create enhanced server page
create_enhanced_server_page() {
    log_step "Creating Enhanced Server Page"
    
    local server_dir="$UMBRA_DIR/file-server"
    
    # Update server.py to include new packages
    cat >> "$server_dir/server.py" << 'EOF'

    def get_distribution_files(self):
        """Get all distribution files"""
        files = []
        
        # Check for distribution packages
        dist_files = [
            'umbra-complete-distribution.zip',
            'umbra-windows-only.zip', 
            'umbra-documentation.zip',
            'umbra-complete-distribution/'
        ]
        
        for filename in dist_files:
            if os.path.exists(filename):
                if os.path.isfile(filename):
                    files.append({
                        'name': filename,
                        'size': self.get_file_size(filename),
                        'type': 'file'
                    })
                elif os.path.isdir(filename):
                    files.append({
                        'name': filename,
                        'size': 'Directory',
                        'type': 'directory'
                    })
        
        return files
EOF

    log_success "Enhanced server page created"
}

# Main execution
main() {
    echo "📦 Creating Complete Umbra Distribution Package"
    echo "=============================================="
    
    create_distribution_package
    create_main_readme
    create_windows_readme
    update_file_server
    create_enhanced_server_page
    
    echo
    log_success "🎉 Complete distribution package created!"
    echo
    echo "📋 What was created:"
    echo "  • umbra-complete-distribution/ - Complete package with everything"
    echo "  • umbra-complete-distribution.zip - Downloadable complete package"
    echo "  • umbra-windows-only.zip - Windows-specific package"
    echo "  • umbra-documentation.zip - Documentation package"
    echo "  • README.md - Comprehensive user guide"
    echo "  • WINDOWS_SETUP.md - Windows-specific setup guide"
    echo
    echo "🌐 File server updated with all packages"
    echo "📊 Server: http://localhost:8001"
    echo "🚀 Ready for global distribution via ngrok"
    echo
    echo "📦 Package sizes:"
    
    if [[ -f "$UMBRA_DIR/umbra-complete-distribution.zip" ]]; then
        echo "  • Complete: $(du -h "$UMBRA_DIR/umbra-complete-distribution.zip" | cut -f1)"
    fi
    
    if [[ -f "$UMBRA_DIR/umbra-windows-only.zip" ]]; then
        echo "  • Windows: $(du -h "$UMBRA_DIR/umbra-windows-only.zip" | cut -f1)"
    fi
    
    if [[ -f "$UMBRA_DIR/umbra-documentation.zip" ]]; then
        echo "  • Docs: $(du -h "$UMBRA_DIR/umbra-documentation.zip" | cut -f1)"
    fi
    
    echo
    log_success "🚀 Ready for users to download!"
}

# Run main function
main "$@"
