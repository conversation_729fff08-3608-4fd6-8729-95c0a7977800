Package: umbra
Version: 1.0.1
Section: devel
Priority: optional
Architecture: amd64
Essential: no
Depends: libc6 (>= 2.31), libgcc-s1 (>= 3.0), libssl3 (>= 3.0.0), python3 (>= 3.8), python3-pip
Recommends: git, code, python3-numpy, python3-pandas, python3-sklearn
Suggests: jupyter-notebook
Installed-Size: 80
Maintainer: Eclipse Softworks <<EMAIL>>
Description: Modern programming language with AI/ML capabilities
 Umbra is a modern, statically-typed programming language designed specifically
 for AI/ML development. It features built-in machine learning primitives,
 seamless Python integration, and a comprehensive development environment.
 .
 Key features:
  * Native AI/ML training and inference
  * Built-in LSP server for IDE integration
  * Interactive REPL with syntax highlighting
  * Comprehensive standard library
  * Cross-platform compatibility
  * VS Code extension support
Homepage: https://umbra-lang.org
Bugs: https://github.com/umbra-lang/umbra/issues
