#!/bin/bash

# Comprehensive Windows Compiler Solutions
# Multiple approaches to make Umbra work on Windows

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
UMBRA_DIR="$SCRIPT_DIR"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_step() { echo -e "${PURPLE}[STEP]${NC} $1"; }

# Solution 1: Minimal Windows Build (Recommended)
solution_1_minimal_build() {
    log_step "Solution 1: Creating Minimal Windows Build"
    
    cd "$UMBRA_DIR/umbra-compiler"
    
    # Create Windows-specific Cargo.toml
    log_info "Creating Windows-specific configuration..."
    
    cat > Cargo-windows.toml << 'EOF'
[package]
name = "umbra-compiler"
version = "1.2.1"
edition = "2021"
authors = ["Eclipse Softworks <<EMAIL>>"]
description = "Umbra programming language compiler - Windows minimal build"
license = "MIT"

[features]
default = ["windows-minimal"]
windows-minimal = []
windows-basic = ["basic-llvm"]
windows-full = ["basic-llvm", "python-interop"]
basic-llvm = ["inkwell"]
python-interop = ["pyo3"]

[dependencies]
# Core dependencies (no linking issues)
clap = { version = "4.4", features = ["derive"] }
thiserror = "1.0"
anyhow = "1.0"
logos = "0.14"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
toml = "0.8"
uuid = { version = "1.0", features = ["v4"] }
colored = "2.0"
crossterm = "0.27"
tempfile = "3.8"
walkdir = "2.4"
unicode-segmentation = "1.10"
base64 = "0.21"
urlencoding = "2.1"
lazy_static = "1.4"
chrono = { version = "0.4", features = ["serde"] }
fastrand = "2.0"
log = "0.4"
which = "4.4"
num_cpus = "1.16"
glob = "0.3"
regex = "1.0"
rand = "0.8"

# Optional dependencies (disabled by default for Windows)
inkwell = { version = "0.4", features = ["llvm18-0"], optional = true }
pyo3 = { version = "0.20", features = ["abi3-py38"], optional = true }

# Windows-specific dependencies
[target.'cfg(windows)'.dependencies]
winapi = { version = "0.3", features = ["winuser", "wincon", "processenv", "libloaderapi"] }

[build-dependencies]
chrono = "0.4"
EOF

    # Build minimal Windows version
    log_info "Building minimal Windows version..."
    
    export CC_x86_64_pc_windows_gnu=x86_64-w64-mingw32-gcc
    export CARGO_TARGET_X86_64_PC_WINDOWS_GNU_LINKER=x86_64-w64-mingw32-gcc
    export RUSTFLAGS="-C target-feature=+crt-static"
    
    if cargo build --release --target x86_64-pc-windows-gnu --manifest-path Cargo-windows.toml --no-default-features --features "windows-minimal"; then
        log_success "Minimal Windows build successful!"
        
        local binary_path="target/x86_64-pc-windows-gnu/release/umbra.exe"
        if [[ -f "$binary_path" ]]; then
            local size=$(du -h "$binary_path" | cut -f1)
            log_success "Windows binary created: $binary_path ($size)"
            
            # Test with Wine if available
            if command -v wine &> /dev/null; then
                log_info "Testing with Wine..."
                if wine "$binary_path" --version; then
                    log_success "Windows binary works with Wine!"
                fi
            fi
        fi
    else
        log_error "Minimal Windows build failed"
        return 1
    fi
    
    cd "$SCRIPT_DIR"
}

# Solution 2: Install Windows Libraries
solution_2_install_libraries() {
    log_step "Solution 2: Installing Windows-Compatible Libraries"
    
    log_info "Installing MinGW libraries..."
    
    # Install Windows-compatible libraries
    sudo apt update
    sudo apt install -y \
        mingw-w64 \
        gcc-mingw-w64-x86-64 \
        g++-mingw-w64-x86-64 \
        wine64 \
        libffi-dev \
        zlib1g-dev
    
    # Try to install Windows FFI library
    if apt-cache search libffi | grep -q mingw; then
        sudo apt install -y libffi-dev:mingw-w64-x86-64 || log_warning "Could not install MinGW FFI library"
    fi
    
    # Create Windows library symlinks
    log_info "Creating Windows library symlinks..."
    
    local mingw_lib="/usr/x86_64-w64-mingw32/lib"
    if [[ -d "$mingw_lib" ]]; then
        # Create missing library symlinks
        sudo ln -sf libmsvcrt.a "$mingw_lib/librt.a" 2>/dev/null || true
        sudo ln -sf libkernel32.a "$mingw_lib/libdl.a" 2>/dev/null || true
        sudo ln -sf libz.a "$mingw_lib/libz.a" 2>/dev/null || true
    fi
    
    log_success "Windows libraries installed"
}

# Solution 3: Static Linking Build
solution_3_static_linking() {
    log_step "Solution 3: Creating Static Linking Build"
    
    cd "$UMBRA_DIR/umbra-compiler"
    
    # Set up static linking environment
    export CC_x86_64_pc_windows_gnu=x86_64-w64-mingw32-gcc
    export CXX_x86_64_pc_windows_gnu=x86_64-w64-mingw32-g++
    export AR_x86_64_pc_windows_gnu=x86_64-w64-mingw32-ar
    export CARGO_TARGET_X86_64_PC_WINDOWS_GNU_LINKER=x86_64-w64-mingw32-gcc
    
    # Enhanced static linking flags
    export RUSTFLAGS="-C target-feature=+crt-static -C link-args=-static -C link-args=-static-libgcc -C link-args=-static-libstdc++"
    export CARGO_TARGET_X86_64_PC_WINDOWS_GNU_RUSTFLAGS="-C target-feature=+crt-static"
    
    # Disable problematic features
    log_info "Building with static linking..."
    
    if cargo build --release --target x86_64-pc-windows-gnu --no-default-features --features "windows-simple"; then
        log_success "Static linking build successful!"
        
        local binary_path="target/x86_64-pc-windows-gnu/release/umbra.exe"
        if [[ -f "$binary_path" ]]; then
            local size=$(du -h "$binary_path" | cut -f1)
            log_success "Static Windows binary: $binary_path ($size)"
        fi
    else
        log_error "Static linking build failed"
        return 1
    fi
    
    cd "$SCRIPT_DIR"
}

# Solution 4: Bundled LLVM Build
solution_4_bundled_llvm() {
    log_step "Solution 4: Creating Bundled LLVM Build"
    
    # Use the bundled LLVM compiler
    if [[ -d "$UMBRA_DIR/bundled-llvm-compiler" ]]; then
        cd "$UMBRA_DIR/bundled-llvm-compiler"
        
        log_info "Building bundled LLVM version for Windows..."
        
        # Set up environment
        export CC_x86_64_pc_windows_gnu=x86_64-w64-mingw32-gcc
        export CARGO_TARGET_X86_64_PC_WINDOWS_GNU_LINKER=x86_64-w64-mingw32-gcc
        export RUSTFLAGS="-C target-feature=+crt-static"
        
        # Build with bundled LLVM
        if cargo build --release --target x86_64-pc-windows-gnu --no-default-features --features "bundled-llvm"; then
            log_success "Bundled LLVM build successful!"
            
            local binary_path="target/x86_64-pc-windows-gnu/release/umbra.exe"
            if [[ -f "$binary_path" ]]; then
                local size=$(du -h "$binary_path" | cut -f1)
                log_success "Bundled Windows binary: $binary_path ($size)"
            fi
        else
            log_error "Bundled LLVM build failed"
            return 1
        fi
        
        cd "$SCRIPT_DIR"
    else
        log_warning "Bundled LLVM compiler not found"
        return 1
    fi
}

# Solution 5: Docker Cross-Compilation
solution_5_docker_build() {
    log_step "Solution 5: Docker Cross-Compilation Build"
    
    if ! command -v docker &> /dev/null; then
        log_warning "Docker not found. Skipping Docker build."
        return 1
    fi
    
    log_info "Creating Docker build environment..."
    
    cat > "$UMBRA_DIR/Dockerfile.windows" << 'EOF'
FROM rust:1.75

# Install Windows cross-compilation tools
RUN apt-get update && apt-get install -y \
    mingw-w64 \
    gcc-mingw-w64-x86-64 \
    g++-mingw-w64-x86-64 \
    wine64 \
    && rm -rf /var/lib/apt/lists/*

# Add Windows target
RUN rustup target add x86_64-pc-windows-gnu

# Set up environment
ENV CC_x86_64_pc_windows_gnu=x86_64-w64-mingw32-gcc
ENV CARGO_TARGET_X86_64_PC_WINDOWS_GNU_LINKER=x86_64-w64-mingw32-gcc
ENV RUSTFLAGS="-C target-feature=+crt-static"

WORKDIR /app
COPY . .

# Build Umbra for Windows
RUN cd umbra-compiler && \
    cargo build --release --target x86_64-pc-windows-gnu --no-default-features --features "windows-simple"

CMD ["cp", "umbra-compiler/target/x86_64-pc-windows-gnu/release/umbra.exe", "/output/"]
EOF

    log_info "Building with Docker..."
    
    mkdir -p "$UMBRA_DIR/docker-output"
    
    if docker build -f Dockerfile.windows -t umbra-windows .; then
        if docker run --rm -v "$UMBRA_DIR/docker-output:/output" umbra-windows; then
            log_success "Docker build successful!"
            
            if [[ -f "$UMBRA_DIR/docker-output/umbra.exe" ]]; then
                local size=$(du -h "$UMBRA_DIR/docker-output/umbra.exe" | cut -f1)
                log_success "Docker Windows binary: docker-output/umbra.exe ($size)"
            fi
        fi
    else
        log_error "Docker build failed"
        return 1
    fi
}

# Solution 6: Native Windows Build Script
solution_6_native_windows() {
    log_step "Solution 6: Creating Native Windows Build Script"
    
    log_info "Creating Windows batch script for native building..."
    
    cat > "$UMBRA_DIR/build-windows-native.bat" << 'EOF'
@echo off
echo Building Umbra for Windows (Native)
echo ===================================

REM Check if Rust is installed
where rustc >nul 2>nul
if %errorlevel% neq 0 (
    echo Error: Rust is not installed
    echo Please install Rust from https://rustup.rs/
    exit /b 1
)

REM Check if LLVM is installed
where llc >nul 2>nul
if %errorlevel% neq 0 (
    echo Warning: LLVM not found in PATH
    echo Install LLVM from https://releases.llvm.org/
)

REM Build Umbra
cd umbra-compiler
echo Building Umbra compiler...
cargo build --release --no-default-features --features "windows-simple"

if %errorlevel% equ 0 (
    echo.
    echo ✓ Build successful!
    echo Binary location: target\release\umbra.exe
    
    REM Test the binary
    echo Testing binary...
    target\release\umbra.exe --version
    
    if %errorlevel% equ 0 (
        echo ✓ Umbra is working correctly!
    ) else (
        echo ✗ Binary test failed
    )
) else (
    echo ✗ Build failed
    exit /b 1
)

pause
EOF

    log_success "Native Windows build script created: build-windows-native.bat"
    log_info "Copy this file to a Windows machine and run it to build natively"
}

# Test all solutions
test_solutions() {
    log_step "Testing All Solutions"
    
    local success_count=0
    local total_solutions=6
    
    echo "🧪 Testing Windows Compilation Solutions"
    echo "========================================"
    
    # Test Solution 1: Minimal Build
    if solution_1_minimal_build; then
        ((success_count++))
        log_success "✓ Solution 1: Minimal Build - SUCCESS"
    else
        log_error "✗ Solution 1: Minimal Build - FAILED"
    fi
    
    # Test Solution 2: Install Libraries
    if solution_2_install_libraries; then
        ((success_count++))
        log_success "✓ Solution 2: Install Libraries - SUCCESS"
    else
        log_error "✗ Solution 2: Install Libraries - FAILED"
    fi
    
    # Test Solution 3: Static Linking
    if solution_3_static_linking; then
        ((success_count++))
        log_success "✓ Solution 3: Static Linking - SUCCESS"
    else
        log_error "✗ Solution 3: Static Linking - FAILED"
    fi
    
    # Test Solution 4: Bundled LLVM
    if solution_4_bundled_llvm; then
        ((success_count++))
        log_success "✓ Solution 4: Bundled LLVM - SUCCESS"
    else
        log_error "✗ Solution 4: Bundled LLVM - FAILED"
    fi
    
    # Test Solution 5: Docker
    if solution_5_docker_build; then
        ((success_count++))
        log_success "✓ Solution 5: Docker Build - SUCCESS"
    else
        log_error "✗ Solution 5: Docker Build - FAILED"
    fi
    
    # Solution 6: Native Windows (always succeeds - just creates script)
    solution_6_native_windows
    ((success_count++))
    log_success "✓ Solution 6: Native Windows Script - SUCCESS"
    
    echo
    echo "📊 Results Summary:"
    echo "=================="
    echo "Successful solutions: $success_count/$total_solutions"
    
    if [[ $success_count -gt 0 ]]; then
        log_success "🎉 At least one solution worked! Umbra can now compile for Windows!"
    else
        log_error "😞 No solutions worked. Manual intervention may be required."
    fi
}

# Main execution
main() {
    echo "🔧 Comprehensive Windows Compiler Solutions"
    echo "==========================================="
    echo
    
    log_info "This script provides 6 different approaches to make Umbra work on Windows:"
    echo "  1. Minimal Build (Recommended) - Disable problematic features"
    echo "  2. Install Libraries - Install Windows-compatible libraries"
    echo "  3. Static Linking - Use static linking to avoid library issues"
    echo "  4. Bundled LLVM - Use bundled LLVM compiler"
    echo "  5. Docker Build - Use Docker for cross-compilation"
    echo "  6. Native Windows - Create script for native Windows building"
    echo
    
    # Check prerequisites
    if [[ ! -d "$UMBRA_DIR/umbra-compiler" ]]; then
        log_error "Umbra compiler directory not found: $UMBRA_DIR/umbra-compiler"
        exit 1
    fi
    
    if ! command -v rustc &> /dev/null; then
        log_error "Rust is not installed. Please install Rust first."
        exit 1
    fi
    
    if ! command -v x86_64-w64-mingw32-gcc &> /dev/null; then
        log_warning "MinGW not found. Installing..."
        sudo apt update
        sudo apt install -y mingw-w64 gcc-mingw-w64-x86-64
    fi
    
    # Add Windows target if not present
    if ! rustup target list --installed | grep -q "x86_64-pc-windows-gnu"; then
        log_info "Adding Windows target..."
        rustup target add x86_64-pc-windows-gnu
    fi
    
    # Run all solutions
    test_solutions
    
    echo
    log_success "🚀 Windows compiler solutions completed!"
    echo
    echo "📋 Next Steps:"
    echo "  • Use any successful solution to build Umbra for Windows"
    echo "  • Test the Windows binary with Wine: wine umbra.exe --version"
    echo "  • Copy the binary to Windows and test natively"
    echo "  • For native Windows building, use build-windows-native.bat"
}

# Run main function
main "$@"
