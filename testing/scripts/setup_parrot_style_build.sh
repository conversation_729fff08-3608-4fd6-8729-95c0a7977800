#!/bin/bash
# Setup Parrot OS-Style Cross-Platform Build Environment
# Configures Ubuntu/Linux to build Windows installers like Parrot OS

set -euo pipefail

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_step() { echo -e "${PURPLE}[STEP]${NC} $1"; }

# Check if running as root
check_permissions() {
    if [[ $EUID -eq 0 ]]; then
        log_error "Don't run this script as root. It will use sudo when needed."
        exit 1
    fi
}

# Setup Wine environment for Windows builds
setup_wine_environment() {
    log_step "Setting up Wine environment for Windows builds..."
    
    # Install Wine and dependencies
    log_info "Installing Wine and dependencies..."
    sudo apt-get update
    sudo apt-get install -y wine wine64 wine32 winetricks
    
    # Configure Wine
    log_info "Configuring Wine environment..."
    export WINEPREFIX="$HOME/.wine-build"
    export WINEARCH=win64
    
    # Initialize Wine prefix
    if [[ ! -d "$WINEPREFIX" ]]; then
        log_info "Initializing Wine prefix..."
        winecfg /v win10 2>/dev/null || true
        
        # Install essential Windows components
        log_info "Installing Windows components via Winetricks..."
        winetricks -q vcrun2019 vcrun2022 dotnet48 || log_warning "Some components failed to install"
    fi
    
    log_success "Wine environment configured"
}

# Install NSIS with proper Wine integration
install_nsis_properly() {
    log_step "Installing NSIS with Wine integration..."
    
    # Install Linux-native NSIS first
    log_info "Installing Linux-native NSIS..."
    sudo apt-get install -y nsis nsis-common nsis-pluginapi
    
    # Download and install Windows NSIS for better compatibility
    local nsis_dir="$HOME/.wine-build/drive_c/Program Files (x86)/NSIS"
    if [[ ! -d "$nsis_dir" ]]; then
        log_info "Downloading Windows NSIS for Wine..."
        cd /tmp
        wget -q "https://downloads.sourceforge.net/project/nsis/NSIS%203/3.09/nsis-3.09-setup.exe"
        
        log_info "Installing Windows NSIS in Wine..."
        export WINEPREFIX="$HOME/.wine-build"
        wine nsis-3.09-setup.exe /S || log_warning "Wine NSIS installation may have issues"
        
        rm -f nsis-3.09-setup.exe
    fi
    
    # Create wrapper script for makensis
    cat > "$HOME/.local/bin/makensis-wine" << 'EOF'
#!/bin/bash
export WINEPREFIX="$HOME/.wine-build"
export WINEARCH=win64
wine "$WINEPREFIX/drive_c/Program Files (x86)/NSIS/makensis.exe" "$@"
EOF
    chmod +x "$HOME/.local/bin/makensis-wine"
    
    # Test both versions
    log_info "Testing NSIS installations..."
    if makensis -VERSION 2>/dev/null; then
        log_success "Linux NSIS working: $(makensis -VERSION)"
    else
        log_warning "Linux NSIS has issues"
    fi
    
    if "$HOME/.local/bin/makensis-wine" -VERSION 2>/dev/null; then
        log_success "Wine NSIS working"
    else
        log_warning "Wine NSIS has issues"
    fi
}

# Install MinGW-w64 cross-compilation toolchain
install_mingw_toolchain() {
    log_step "Installing MinGW-w64 cross-compilation toolchain..."
    
    # Install MinGW packages
    log_info "Installing MinGW-w64 packages..."
    sudo apt-get install -y \
        gcc-mingw-w64 \
        g++-mingw-w64 \
        mingw-w64-tools \
        mingw-w64-common \
        mingw-w64-x86-64-dev \
        binutils-mingw-w64-x86-64
    
    # Configure alternatives for cross-compilation
    sudo update-alternatives --install /usr/bin/x86_64-w64-mingw32-gcc x86_64-w64-mingw32-gcc /usr/bin/x86_64-w64-mingw32-gcc-posix 100
    sudo update-alternatives --install /usr/bin/x86_64-w64-mingw32-g++ x86_64-w64-mingw32-g++ /usr/bin/x86_64-w64-mingw32-g++-posix 100
    
    # Test cross-compilation
    log_info "Testing MinGW cross-compilation..."
    cat > /tmp/test.c << 'EOF'
#include <stdio.h>
int main() { printf("Hello from MinGW!\n"); return 0; }
EOF
    
    if x86_64-w64-mingw32-gcc /tmp/test.c -o /tmp/test.exe; then
        log_success "MinGW cross-compilation working"
        rm -f /tmp/test.c /tmp/test.exe
    else
        log_error "MinGW cross-compilation failed"
    fi
}

# Setup Rust for Windows cross-compilation
setup_rust_windows_target() {
    log_step "Setting up Rust for Windows cross-compilation..."
    
    # Ensure Rust is available
    if ! command -v rustc &> /dev/null; then
        log_info "Installing Rust..."
        curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
        source "$HOME/.cargo/env"
    fi
    
    # Add Windows target
    log_info "Adding Windows target to Rust..."
    rustup target add x86_64-pc-windows-gnu
    
    # Configure Cargo for cross-compilation
    mkdir -p "$HOME/.cargo"
    cat >> "$HOME/.cargo/config.toml" << 'EOF'

[target.x86_64-pc-windows-gnu]
linker = "x86_64-w64-mingw32-gcc"
ar = "x86_64-w64-mingw32-ar"

[env]
CC_x86_64_pc_windows_gnu = "x86_64-w64-mingw32-gcc"
CXX_x86_64_pc_windows_gnu = "x86_64-w64-mingw32-g++"
AR_x86_64_pc_windows_gnu = "x86_64-w64-mingw32-ar"
CARGO_TARGET_X86_64_PC_WINDOWS_GNU_LINKER = "x86_64-w64-mingw32-gcc"
EOF
    
    log_success "Rust Windows cross-compilation configured"
}

# Install additional packaging tools
install_packaging_tools() {
    log_step "Installing additional packaging tools..."
    
    # Linux package tools
    log_info "Installing Linux packaging tools..."
    sudo apt-get install -y \
        dpkg-dev \
        rpm \
        rpmbuild \
        fakeroot \
        alien \
        debhelper \
        dh-make
    
    # Signing tools
    log_info "Installing signing tools..."
    sudo apt-get install -y \
        osslsigncode \
        gnupg \
        gpg-agent
    
    # Try to install dpkg-sig (may not be available in all repos)
    sudo apt-get install -y dpkg-sig || {
        log_warning "dpkg-sig not available, will use alternative signing methods"
    }
    
    # Archive and compression tools
    log_info "Installing archive tools..."
    sudo apt-get install -y \
        zip \
        unzip \
        p7zip-full \
        tar \
        gzip \
        bzip2 \
        xz-utils
    
    log_success "Packaging tools installed"
}

# Fix common library compatibility issues
fix_library_compatibility() {
    log_step "Fixing library compatibility issues..."
    
    # Update libstdc++ and related libraries
    log_info "Updating system libraries..."
    sudo apt-get install -y \
        libc6-dev \
        libstdc++6 \
        libgcc-s1 \
        build-essential
    
    # Fix potential GLIBC issues
    log_info "Checking GLIBC compatibility..."
    ldd --version || log_warning "GLIBC version check failed"
    
    # Install additional development libraries
    sudo apt-get install -y \
        libssl-dev \
        pkg-config \
        libffi-dev \
        zlib1g-dev
    
    log_success "Library compatibility improved"
}

# Create comprehensive build script
create_parrot_style_builder() {
    log_step "Creating Parrot OS-style build script..."
    
    cat > "$HOME/.local/bin/build-windows-installer" << 'EOF'
#!/bin/bash
# Parrot OS-Style Windows Installer Builder
# Usage: build-windows-installer <nsis-script>

set -euo pipefail

SCRIPT_FILE="$1"
OUTPUT_DIR="${2:-./dist}"

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log_info() { echo -e "${GREEN}[BUILD]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Setup environment
export WINEPREFIX="$HOME/.wine-build"
export WINEARCH=win64

# Try Linux NSIS first, fallback to Wine
build_with_linux_nsis() {
    log_info "Attempting build with Linux NSIS..."
    if makensis "$SCRIPT_FILE"; then
        return 0
    else
        return 1
    fi
}

build_with_wine_nsis() {
    log_info "Attempting build with Wine NSIS..."
    if wine "$WINEPREFIX/drive_c/Program Files (x86)/NSIS/makensis.exe" "$SCRIPT_FILE"; then
        return 0
    else
        return 1
    fi
}

# Main build logic
main() {
    if [[ ! -f "$SCRIPT_FILE" ]]; then
        log_error "NSIS script not found: $SCRIPT_FILE"
        exit 1
    fi
    
    log_info "Building Windows installer from: $SCRIPT_FILE"
    
    # Try Linux NSIS first
    if build_with_linux_nsis; then
        log_info "✅ Built successfully with Linux NSIS"
    elif build_with_wine_nsis; then
        log_info "✅ Built successfully with Wine NSIS"
    else
        log_error "❌ Both Linux and Wine NSIS failed"
        exit 1
    fi
    
    # Move output if specified
    if [[ "$OUTPUT_DIR" != "./dist" ]]; then
        mkdir -p "$OUTPUT_DIR"
        mv *.exe "$OUTPUT_DIR/" 2>/dev/null || true
    fi
    
    log_info "🎉 Windows installer build complete!"
}

main "$@"
EOF
    
    chmod +x "$HOME/.local/bin/build-windows-installer"
    
    # Add to PATH if not already there
    if [[ ":$PATH:" != *":$HOME/.local/bin:"* ]]; then
        echo 'export PATH="$HOME/.local/bin:$PATH"' >> "$HOME/.bashrc"
        export PATH="$HOME/.local/bin:$PATH"
    fi
    
    log_success "Parrot-style build script created: build-windows-installer"
}

# Test the complete setup
test_complete_setup() {
    log_step "Testing complete Parrot OS-style setup..."
    
    # Create test NSIS script
    cat > /tmp/test-installer.nsi << 'EOF'
!define PRODUCT_NAME "Test Installer"
!define PRODUCT_VERSION "1.0.0"

Name "${PRODUCT_NAME} ${PRODUCT_VERSION}"
OutFile "test-installer.exe"
InstallDir "$PROGRAMFILES64\TestApp"
RequestExecutionLevel admin

Section "Main"
  SetOutPath "$INSTDIR"
  WriteUninstaller "$INSTDIR\uninstall.exe"
SectionEnd

Section "Uninstall"
  Delete "$INSTDIR\uninstall.exe"
  RMDir "$INSTDIR"
SectionEnd
EOF
    
    # Test build
    log_info "Testing installer build..."
    cd /tmp
    
    if build-windows-installer test-installer.nsi; then
        if [[ -f "test-installer.exe" ]]; then
            local size=$(du -h test-installer.exe | cut -f1)
            log_success "✅ Test installer created successfully ($size)"
            rm -f test-installer.exe test-installer.nsi
        else
            log_warning "Build reported success but no installer found"
        fi
    else
        log_error "Test build failed"
        return 1
    fi
    
    # Test cross-compilation
    log_info "Testing Rust Windows cross-compilation..."
    if command -v rustc &> /dev/null; then
        echo 'fn main() { println!("Hello from Rust Windows!"); }' > /tmp/test.rs
        if rustc --target x86_64-pc-windows-gnu /tmp/test.rs -o /tmp/test.exe; then
            log_success "✅ Rust Windows cross-compilation working"
            rm -f /tmp/test.rs /tmp/test.exe
        else
            log_warning "Rust Windows cross-compilation failed"
        fi
    fi
    
    log_success "🎉 Complete setup test passed!"
}

# Main setup function
main() {
    log_info "🚀 Setting up Parrot OS-Style Cross-Platform Build Environment"
    log_info "============================================================="
    log_info "This will configure Ubuntu/Linux to build Windows installers like Parrot OS"
    echo
    
    check_permissions
    
    # Execute setup steps
    setup_wine_environment
    install_nsis_properly
    install_mingw_toolchain
    setup_rust_windows_target
    install_packaging_tools
    fix_library_compatibility
    create_parrot_style_builder
    test_complete_setup
    
    # Show final summary
    echo
    log_success "🎉 Parrot OS-Style Build Environment Setup Complete!"
    echo
    echo "📋 What's Now Available:"
    echo "  ✅ Wine environment configured for Windows builds"
    echo "  ✅ NSIS (Linux + Wine versions) for installer creation"
    echo "  ✅ MinGW-w64 cross-compilation toolchain"
    echo "  ✅ Rust Windows target configured"
    echo "  ✅ Linux packaging tools (DEB, RPM)"
    echo "  ✅ Code signing tools (osslsigncode, GPG)"
    echo "  ✅ Library compatibility fixes applied"
    echo
    echo "🛠️  Usage:"
    echo "  • build-windows-installer script.nsi"
    echo "  • cargo build --target x86_64-pc-windows-gnu"
    echo "  • makensis script.nsi (Linux version)"
    echo "  • makensis-wine script.nsi (Wine version)"
    echo
    echo "🔄 Restart your terminal or run: source ~/.bashrc"
    echo
    log_info "Your system now has Parrot OS-level cross-platform build capabilities!"
}

# Run main function
main "$@"
